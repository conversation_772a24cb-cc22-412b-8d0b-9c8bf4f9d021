version: '3.8'

# Monitoring Stack for SpheroSeg
# Includes: Prometheus, Grafana, AlertManager, Node Exporter, cAdvisor

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

networks:
  monitoring:
    driver: bridge
    internal: true
  spheroseg-network:
    external: true

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:

services:
  # ============================================
  # PROMETHEUS - Metrics Collection
  # ============================================
  prometheus:
    image: prom/prometheus:v2.47.2
    container_name: spheroseg-prometheus
    restart: unless-stopped
    user: "65534:65534"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    ports:
      - "9090:9090"
    networks:
      - monitoring
      - spheroseg-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # GRAFANA - Visualization
  # ============================================
  grafana:
    image: grafana/grafana:10.2.2
    container_name: spheroseg-grafana
    restart: unless-stopped
    user: "472:472"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,alexanderzobnin-zabbix-app,grafana-piechart-panel
      - GF_SERVER_ROOT_URL=https://monitoring.spherosegapp.utia.cas.cz
      - GF_SECURITY_COOKIE_SECURE=true
      - GF_SECURITY_STRICT_TRANSPORT_SECURITY=true
      - GF_SECURITY_X_CONTENT_TYPE_OPTIONS=true
      - GF_SECURITY_X_XSS_PROTECTION=true
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
      - GF_LOG_MODE=console
      - GF_LOG_LEVEL=warn
      - GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH=/var/lib/grafana/dashboards/home.json
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3003:3000"
    networks:
      - monitoring
      - spheroseg-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # ALERTMANAGER - Alert Routing
  # ============================================
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: spheroseg-alertmanager
    restart: unless-stopped
    user: "65534:65534"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=https://alerts.spherosegapp.utia.cas.cz'
      - '--log.level=warn'
    ports:
      - "9093:9093"
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9093/-/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # NODE EXPORTER - Host Metrics
  # ============================================
  node-exporter:
    image: prom/node-exporter:v1.7.0
    container_name: spheroseg-node-exporter
    restart: unless-stopped
    pid: host
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--path.rootfs=/rootfs'
      - '--collector.filesystem.mount-points-exclude=^/(dev|proc|sys|var/lib/docker/.+|var/lib/kubelet/pods/.+)($|/)'
      - '--collector.netclass.ignored-devices=^(veth.*)$'
      - '--collector.processes'
      - '--collector.systemd'
      - '--collector.tcpstat'
      - '--web.disable-exporter-metrics'
    ports:
      - "9100:9100"
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9100/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_TIME

  # ============================================
  # CADVISOR - Container Metrics
  # ============================================
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: spheroseg-cadvisor
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
      - /cgroup:/cgroup:ro
    command:
      - '--housekeeping_interval=30s'
      - '--docker_only=true'
      - '--disable_metrics=percpu,sched,tcp,udp,disk,diskIO,accelerator,hugetlb,referenced_memory,cpu_topology,resctrl'
      - '--store_container_labels=false'
      - '--whitelisted_container_labels=com.docker.compose.project,com.docker.compose.service'
    ports:
      - "8080:8080"
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging

  # ============================================
  # POSTGRES EXPORTER - Database Metrics
  # ============================================
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.15.0
    container_name: spheroseg-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "postgresql://spheroseg:${DB_PASSWORD}@postgres:5432/spheroseg?sslmode=require"
      PG_EXPORTER_DISABLE_DEFAULT_METRICS: "false"
      PG_EXPORTER_DISABLE_SETTINGS_METRICS: "false"
      PG_EXPORTER_AUTO_DISCOVER_DATABASES: "true"
      PG_EXPORTER_EXCLUDE_DATABASES: "template0,template1"
    ports:
      - "9187:9187"
    networks:
      - monitoring
      - spheroseg-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9187/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # REDIS EXPORTER - Cache Metrics
  # ============================================
  redis-exporter:
    image: oliver006/redis_exporter:v1.55.0
    container_name: spheroseg-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_EXPORTER_CHECK_KEYS: "spheroseg:*"
      REDIS_EXPORTER_CHECK_SINGLE_KEYS: "spheroseg:sessions"
      REDIS_EXPORTER_LOG_FORMAT: "json"
    ports:
      - "9121:9121"
    networks:
      - monitoring
      - spheroseg-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9121/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # RABBITMQ EXPORTER - Queue Metrics
  # ============================================
  rabbitmq-exporter:
    image: kbudde/rabbitmq-exporter:v1.0.0
    container_name: spheroseg-rabbitmq-exporter
    restart: unless-stopped
    environment:
      RABBIT_URL: "http://rabbitmq:15672"
      RABBIT_USER: admin
      RABBIT_PASSWORD: ${RABBITMQ_PASSWORD}
      PUBLISH_PORT: 9419
      LOG_LEVEL: warning
      SKIP_QUEUES: "^$"
      INCLUDE_QUEUES: ".*"
      RABBIT_CAPABILITIES: "bert,no_sort"
    ports:
      - "9419:9419"
    networks:
      - monitoring
      - spheroseg-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9419/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # BLACKBOX EXPORTER - Endpoint Monitoring
  # ============================================
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: spheroseg-blackbox-exporter
    restart: unless-stopped
    volumes:
      - ./monitoring/blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml:ro
    command:
      - '--config.file=/etc/blackbox_exporter/config.yml'
      - '--log.level=warn'
    ports:
      - "9115:9115"
    networks:
      - monitoring
      - spheroseg-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9115/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # LOKI - Log Aggregation
  # ============================================
  loki:
    image: grafana/loki:2.9.3
    container_name: spheroseg-loki
    restart: unless-stopped
    user: "10001:10001"
    volumes:
      - ./monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
      - ./logs:/var/log/spheroseg:ro
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3100/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # PROMTAIL - Log Collector
  # ============================================
  promtail:
    image: grafana/promtail:2.9.3
    container_name: spheroseg-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log/spheroseg:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    depends_on:
      - loki
    logging: *default-logging
    security_opt:
      - no-new-privileges:true