version: '3.8'

networks:
  test-network:
    driver: bridge

services:
  test-db:
    image: postgres:16-alpine
    container_name: spheroseg-test-db
    environment:
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass
      POSTGRES_DB: spheroseg_test
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    networks:
      - test-network
    tmpfs:
      - /var/lib/postgresql/data
    command: >
      postgres
      -c fsync=off
      -c synchronous_commit=off
      -c full_page_writes=off
      -c checkpoint_segments=32
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c shared_buffers=256MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d spheroseg_test"]
      interval: 5s
      timeout: 5s
      retries: 10

  test-redis:
    image: redis:7-alpine
    container_name: spheroseg-test-redis
    ports:
      - "6380:6379"
    networks:
      - test-network
    command: >
      redis-server
      --save ""
      --appendonly no
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10

  test-rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: spheroseg-test-rabbitmq
    ports:
      - "5673:5672"
      - "15673:15672"
    environment:
      RABBITMQ_DEFAULT_USER: testuser
      RABBITMQ_DEFAULT_PASS: testpass
      RABBITMQ_DEFAULT_VHOST: test
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10