# Error System Migration Guide

## Overview

The SpheroSeg project now has a unified error handling system that provides consistency across frontend and backend while following the SSOT (Single Source of Truth) principle.

## Architecture

### Unified Error System Structure

```
packages/shared/src/utils/unifiedErrorSystem.ts  (Core error definitions)
├── packages/backend/src/utils/UnifiedApiError.ts  (Backend integration)
└── packages/frontend/src/utils/error/UnifiedErrorHandler.ts  (Frontend integration)
```

## Key Components

### 1. Shared Error System (`packages/shared/src/utils/unifiedErrorSystem.ts`)

**Core Features:**
- Standardized error types, codes, and severity levels
- Type-safe error definitions shared across frontend and backend
- Mapping functions for HTTP status codes
- Error factory functions
- Validation utilities

**Error Types:**
```typescript
enum ErrorType {
  // Client Errors (4xx)
  VALIDATION, AUTHENTICATION, AUTHORIZATION, NOT_FOUND, CONFLICT, RATE_LIMIT,
  
  // Server Errors (5xx)
  INTERNAL_SERVER, DATABASE, EXTERNAL_SERVICE, SERVICE_UNAVAILABLE, STORAGE,
  
  // Network Errors
  NETWORK, TIMEOUT,
  
  // Application-specific
  IMAGE_OPERATION, PROJECT_OPERATION, SEGMENTATION_OPERATION,
  
  // Unknown
  UNKNOWN
}
```

### 2. Backend Integration (`packages/backend/src/utils/UnifiedApiError.ts`)

**Features:**
- Extends shared error system with backend-specific functionality
- Provides static factory methods for common error types
- Maintains backward compatibility with existing `ApiError` class
- Automatically maps error types to appropriate HTTP status codes

**Usage Example:**
```typescript
import { UnifiedApiError, ErrorType } from '../utils/UnifiedApiError';

// Create specific error types
const validationError = UnifiedApiError.validation('Invalid input data', details);
const authError = UnifiedApiError.authentication('Token expired');
const dbError = UnifiedApiError.database('Connection failed', originalError);

// Generic error creation
const customError = new UnifiedApiError(
  ErrorType.PROJECT_OPERATION,
  'Project access denied',
  { requestId, userId, context }
);
```

### 3. Frontend Integration (`packages/frontend/src/utils/error/UnifiedErrorHandler.ts`)

**Features:**
- Normalizes various error types (Axios, native Error, strings) to unified format
- Provides intelligent toast notification system with deduplication
- Integrates with error monitoring service
- Supports access denied error suppression to prevent notification spam

**Usage Example:**
```typescript
import { handleError, safeAsync, isAuthError } from '@/utils/error/UnifiedErrorHandler';

// Handle any error type
try {
  await apiCall();
} catch (error) {
  const errorInfo = handleError(error, {
    context: { operation: 'project-upload' },
    customMessage: 'Failed to upload project'
  });
}

// Safe async wrapper
const { data, error } = await safeAsync(() => fetchProjects());
if (error) {
  // Error already handled and logged
}

// Check error type
if (isAuthError(error)) {
  redirectToLogin();
}
```

## Migration Path

### For Backend Code

1. **Replace existing ApiError imports:**
   ```typescript
   // Old
   import { ApiError } from '../utils/ApiError';
   
   // New
   import { UnifiedApiError } from '../utils/UnifiedApiError';
   ```

2. **Update error creation:**
   ```typescript
   // Old
   throw new ApiError('Not found', 404);
   
   // New
   throw UnifiedApiError.notFound('Resource not found');
   ```

### For Frontend Code

1. **Replace existing error handlers:**
   ```typescript
   // Old
   import { handleError } from '@/utils/error/unifiedErrorHandler';
   
   // New
   import { handleError } from '@/utils/error/UnifiedErrorHandler';
   ```

2. **Update error handling patterns:**
   ```typescript
   // Old - manual error processing
   if (axios.isAxiosError(error)) {
     const message = error.response?.data?.message || 'Network error';
     toast.error(message);
   }
   
   // New - automatic normalization
   handleError(error, { context: { operation: 'data-fetch' } });
   ```

## Benefits

### 1. **Consistency**
- Unified error types, codes, and messages across all layers
- Standardized HTTP status code mapping
- Consistent error response format

### 2. **Type Safety**
- Full TypeScript support with proper type inference
- Compile-time validation of error codes and types
- Intellisense support for error properties

### 3. **Maintainability**
- Single source of truth for all error definitions
- Centralized error message templates
- Simplified testing with standardized error formats

### 4. **User Experience**
- Intelligent toast deduplication prevents notification spam
- Context-aware error suppression for related operations
- Consistent error messaging across the application

### 5. **Monitoring & Debugging**
- Structured error information for better logging
- Request ID tracking for distributed debugging
- Context information for better error analysis

## Configuration Files Consolidation

### Linting and Formatting

The project now has a unified configuration structure:

```
/.eslintrc.cjs                    (Root ESLint config)
/.prettierrc                      (Root Prettier config)
├── packages/backend/.eslintrc.js    (Extends root, backend-specific rules)
├── packages/backend/.prettierrc     (Uses root config)
├── packages/frontend/.eslintrc.json (Extends root, frontend-specific rules)  
├── packages/shared/.eslintrc.json   (Extends root, shared package rules)
└── packages/shared/.prettierrc      (Uses root config)
```

**Key Improvements:**
- **Eliminated Duplication:** Common rules defined once in root config
- **Package-Specific Overrides:** Each package extends root and adds only necessary overrides
- **Consistent Formatting:** All packages use same Prettier settings (printWidth: 100, etc.)
- **Simplified Maintenance:** Rule updates only need to be made in one place

## Migration Checklist

- [x] Create unified error system in shared package
- [x] Implement backend integration with UnifiedApiError
- [x] Implement frontend integration with UnifiedErrorHandler
- [x] Update shared package exports
- [x] Consolidate ESLint configurations across packages
- [x] Consolidate Prettier configurations across packages
- [x] Create migration documentation

## Next Steps

1. **Gradual Migration:** Replace existing error handling code incrementally
2. **Update Tests:** Modify tests to expect unified error formats
3. **Documentation:** Update API documentation to reflect new error responses
4. **Monitoring:** Configure error monitoring to work with unified error structure

## Backward Compatibility

The new system maintains backward compatibility:
- Existing `ApiError` usage continues to work
- Existing frontend error handlers will still function
- HTTP status codes and response formats remain consistent
- Existing error monitoring integration preserved

This allows for gradual migration without breaking existing functionality.