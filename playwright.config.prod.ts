import { defineConfig, devices } from '@playwright/test';

/**
 * Production E2E test configuration
 */
export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: 0,
  workers: 2,
  reporter: 'list',
  
  use: {
    /* Base URL for production environment */
    baseURL: 'http://localhost',
    
    /* Ignore HTTPS errors since we're using self-signed certs locally */
    ignoreHTTPSErrors: true,
    
    /* Collect trace on retry */
    trace: 'on-first-retry',
    
    /* Run in headless mode */
    headless: true,
  },

  /* Shorter timeout for testing */
  timeout: 30 * 1000,
  
  /* Expect timeout */
  expect: {
    timeout: 5000
  },

  /* Only test with Chromium for now */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  /* No web server since we're testing production */
  webServer: undefined,
});