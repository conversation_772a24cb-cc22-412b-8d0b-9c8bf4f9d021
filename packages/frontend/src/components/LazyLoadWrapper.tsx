import { lazy, Suspense, ComponentType } from "react";
import { Loader2 } from "lucide-react";

/**
 * Loading fallback component
 */
export const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

/**
 * Error fallback component
 */
export const ErrorFallback = ({ error }: { error: Error }) => (
  <div className="flex flex-col items-center justify-center min-h-[200px] p-4">
    <p className="text-destructive mb-2">Failed to load component</p>
    <p className="text-sm text-muted-foreground">{error.message}</p>
  </div>
);

/**
 * Wrapper for lazy-loaded components with error boundary
 */
export function LazyLoadWrapper<T extends ComponentType<any>>({
  loader,
  fallback = <LoadingFallback />,
}: {
  loader: () => Promise<{ default: T }>;
  fallback?: React.ReactNode;
}) {
  const Component = lazy(loader);

  return (props: any) => (
    <Suspense fallback={fallback}>
      <Component {...props} />
    </Suspense>
  );
}

/**
 * Preload a lazy component
 */
export function preloadComponent(loader: () => Promise<any>) {
  // Start loading the component in the background
  loader().catch((error) => {
    console.error("Failed to preload component:", error);
  });
}

/**
 * Create lazy-loaded route components
 */
export const lazyRoute = (
  importFn: () => Promise<{ default: ComponentType<any> }>,
  options?: {
    fallback?: React.ReactNode;
    preload?: boolean;
  },
) => {
  const Component = LazyLoadWrapper({
    loader: importFn,
    fallback: options?.fallback,
  });

  // Optionally preload the component
  if (options?.preload) {
    setTimeout(() => preloadComponent(importFn), 100);
  }

  return Component;
};
