import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { Polygon, Point } from '@spheroseg/shared';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileJson, 
  FileText, 
  Image as ImageIcon, 
  Database,
  AlertCircle,
  CheckCircle,
  Loader2,
  Eye,
  EyeOff
} from 'lucide-react';
import { segmentationImportService, ImportResult, ImportOptions } from '@/services/segmentationImportService';
import { useLanguage } from '@/contexts/LanguageContext';

interface SegmentationImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (polygons: Polygon[], replaceExisting: boolean) => void;
  imageWidth: number;
  imageHeight: number;
  currentPolygonCount: number;
}

interface PreviewPolygon extends Polygon {
  visible: boolean;
}

type ImportFormat = 'json' | 'coco' | 'csv' | 'binary-mask';
type ImportMode = 'merge' | 'replace';

export const SegmentationImportDialog: React.FC<SegmentationImportDialogProps> = ({
  open,
  onOpenChange,
  onImport,
  imageWidth,
  imageHeight,
  currentPolygonCount
}) => {
  const { t } = useLanguage();
  
  // State
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importFormat, setImportFormat] = useState<ImportFormat>('json');
  const [importMode, setImportMode] = useState<ImportMode>('merge');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [previewPolygons, setPreviewPolygons] = useState<PreviewPolygon[]>([]);
  const [validatePolygons, setValidatePolygons] = useState(true);
  const [showPreview, setShowPreview] = useState(false);

  // File handling
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setImportResult(null);
      setPreviewPolygons([]);
      setShowPreview(false);
      
      // Auto-detect format based on extension
      const extension = file.name.toLowerCase().split('.').pop();
      switch (extension) {
        case 'json':
          setImportFormat('json');
          break;
        case 'csv':
          setImportFormat('csv');
          break;
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'bmp':
          setImportFormat('binary-mask');
          break;
        default:
          setImportFormat('json');
      }
    }
  }, []);

  const clearFile = useCallback(() => {
    setSelectedFile(null);
    setImportResult(null);
    setPreviewPolygons([]);
    setShowPreview(false);
  }, []);

  // Import processing
  const processImport = useCallback(async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setProgress(10);

    try {
      const options: ImportOptions = {
        mergeWithExisting: importMode === 'merge',
        replaceExisting: importMode === 'replace',
        validatePolygons,
        imageWidth,
        imageHeight
      };

      setProgress(30);
      const result = await segmentationImportService.importFromFile(selectedFile, options);
      setProgress(80);

      setImportResult(result);
      
      if (result.success && result.polygons.length > 0) {
        const previewPolys: PreviewPolygon[] = result.polygons.map(poly => ({
          ...poly,
          visible: true
        }));
        setPreviewPolygons(previewPolys);
        setShowPreview(true);
      }

      setProgress(100);
    } catch (error) {
      setImportResult({
        success: false,
        polygons: [],
        errors: [`Import failed: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      });
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [selectedFile, importMode, validatePolygons, imageWidth, imageHeight]);

  // Preview controls
  const togglePolygonVisibility = useCallback((polygonId: string) => {
    setPreviewPolygons(prev => prev.map(poly => 
      poly.id === polygonId ? { ...poly, visible: !poly.visible } : poly
    ));
  }, []);

  const toggleAllVisibility = useCallback((visible: boolean) => {
    setPreviewPolygons(prev => prev.map(poly => ({ ...poly, visible })));
  }, []);

  // Final import
  const executeImport = useCallback(() => {
    if (!importResult?.success || previewPolygons.length === 0) return;

    const visiblePolygons = previewPolygons
      .filter(poly => poly.visible)
      .map(({ visible, ...poly }) => poly);

    onImport(visiblePolygons, importMode === 'replace');
    onOpenChange(false);
    
    // Reset state
    setSelectedFile(null);
    setImportResult(null);
    setPreviewPolygons([]);
    setShowPreview(false);
  }, [importResult, previewPolygons, importMode, onImport, onOpenChange]);

  // Format descriptions
  const formatDescriptions = useMemo(() => ({
    'json': {
      title: 'JSON Polygons',
      description: 'Standard JSON format with polygon arrays',
      example: '{ "polygons": [{ "points": [[x,y], ...], "type": "external" }] }',
      icon: FileJson
    },
    'coco': {
      title: 'COCO Dataset',
      description: 'COCO annotation format with segmentation data',
      example: '{ "annotations": [{ "segmentation": [[x1,y1,x2,y2,...]] }] }',
      icon: Database
    },
    'csv': {
      title: 'CSV Coordinates',
      description: 'Comma-separated values with x,y coordinates',
      example: 'polygon_id,x,y,type\\npolygon_1,100,200,external',
      icon: FileText
    },
    'binary-mask': {
      title: 'Binary Mask',
      description: 'PNG/JPG image with white foreground objects',
      example: 'Binary mask image with white objects on black background',
      icon: ImageIcon
    }
  }), []);

  const visiblePolygonCount = previewPolygons.filter(poly => poly.visible).length;
  const totalNewPolygons = importMode === 'replace' ? visiblePolygonCount : currentPolygonCount + visiblePolygonCount;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            {t('segmentation.import.title') || 'Import Segmentation Data'}
          </DialogTitle>
          <DialogDescription>
            {t('segmentation.import.description') || 'Import polygons from various formats including JSON, COCO, CSV, and binary masks.'}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="upload" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Upload & Settings</TabsTrigger>
              <TabsTrigger value="preview" disabled={!showPreview}>Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="flex-1 space-y-4 overflow-auto">
              {/* File Upload Section */}
              <div className="space-y-2">
                <Label htmlFor="file-upload">Select File</Label>
                <div className="flex gap-2">
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".json,.csv,.png,.jpg,.jpeg,.bmp"
                    onChange={handleFileSelect}
                    className="flex-1"
                  />
                  {selectedFile && (
                    <Button variant="outline" onClick={clearFile}>
                      Clear
                    </Button>
                  )}
                </div>
                {selectedFile && (
                  <div className="text-sm text-muted-foreground">
                    Selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                  </div>
                )}
              </div>

              {/* Format Selection */}
              <div className="space-y-3">
                <Label>File Format</Label>
                <RadioGroup value={importFormat} onValueChange={(value) => setImportFormat(value as ImportFormat)}>
                  {Object.entries(formatDescriptions).map(([format, info]) => {
                    const IconComponent = info.icon;
                    return (
                      <div key={format} className="flex items-start space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value={format} id={format} className="mt-1" />
                        <div className="flex-1">
                          <Label htmlFor={format} className="flex items-center gap-2 cursor-pointer">
                            <IconComponent className="w-4 h-4" />
                            {info.title}
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">{info.description}</p>
                          <code className="text-xs bg-muted p-1 rounded mt-1 block">{info.example}</code>
                        </div>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>

              {/* Import Settings */}
              <div className="space-y-3">
                <Label>Import Settings</Label>
                
                <RadioGroup value={importMode} onValueChange={(value) => setImportMode(value as ImportMode)}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="merge" id="merge" />
                    <Label htmlFor="merge">
                      Merge with existing polygons ({currentPolygonCount} current)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="replace" id="replace" />
                    <Label htmlFor="replace" className="text-amber-600">
                      Replace all existing polygons
                    </Label>
                  </div>
                </RadioGroup>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="validate"
                    checked={validatePolygons}
                    onCheckedChange={(checked) => setValidatePolygons(Boolean(checked))}
                  />
                  <Label htmlFor="validate">
                    Validate polygons (check for errors and self-intersections)
                  </Label>
                </div>
              </div>

              {/* Processing Progress */}
              {isProcessing && (
                <div className="space-y-2">
                  <Label>Processing...</Label>
                  <Progress value={progress} />
                  <p className="text-sm text-muted-foreground flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Importing {formatDescriptions[importFormat].title}
                  </p>
                </div>
              )}

              {/* Import Results */}
              {importResult && (
                <div className="space-y-2">
                  <Alert variant={importResult.success ? "default" : "destructive"}>
                    {importResult.success ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertTitle>
                      {importResult.success ? 'Import Successful' : 'Import Failed'}
                    </AlertTitle>
                    <AlertDescription>
                      {importResult.success ? (
                        `Successfully imported ${importResult.polygons.length} polygons`
                      ) : (
                        `Failed to import: ${importResult.errors.join(', ')}`
                      )}
                    </AlertDescription>
                  </Alert>

                  {importResult.warnings.length > 0 && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Warnings</AlertTitle>
                      <AlertDescription>
                        <ul className="list-disc list-inside text-sm">
                          {importResult.warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}

                  {importResult.errors.length > 0 && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Errors</AlertTitle>
                      <AlertDescription>
                        <ul className="list-disc list-inside text-sm">
                          {importResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="preview" className="flex-1 space-y-4 overflow-hidden">
              {showPreview && (
                <>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Preview Polygons</h3>
                      <p className="text-sm text-muted-foreground">
                        {visiblePolygonCount} of {previewPolygons.length} polygons selected
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleAllVisibility(true)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Show All
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleAllVisibility(false)}
                      >
                        <EyeOff className="w-4 h-4 mr-1" />
                        Hide All
                      </Button>
                    </div>
                  </div>

                  <ScrollArea className="flex-1 border rounded-lg p-4">
                    <div className="space-y-2">
                      {previewPolygons.map((polygon, index) => (
                        <div
                          key={polygon.id}
                          className={`flex items-center justify-between p-3 border rounded-lg ${
                            polygon.visible ? 'bg-background' : 'bg-muted opacity-50'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <Checkbox
                              checked={polygon.visible}
                              onCheckedChange={() => togglePolygonVisibility(polygon.id)}
                            />
                            <div>
                              <p className="font-medium">Polygon {index + 1}</p>
                              <div className="flex gap-2 text-sm text-muted-foreground">
                                <Badge variant="outline">{polygon.type}</Badge>
                                <span>{polygon.points.length} points</span>
                                {polygon.class && <Badge variant="secondary">{polygon.class}</Badge>}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-muted-foreground">
              {importResult?.success && (
                <>
                  Final result: {totalNewPolygons} total polygons
                  {importMode === 'replace' && currentPolygonCount > 0 && (
                    <span className="text-amber-600 ml-1">
                      ({currentPolygonCount} existing will be removed)
                    </span>
                  )}
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={selectedFile && !importResult ? processImport : executeImport}
                disabled={!selectedFile || isProcessing || (importResult && !importResult.success)}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : selectedFile && !importResult ? (
                  'Process File'
                ) : (
                  `Import ${visiblePolygonCount} Polygons`
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};