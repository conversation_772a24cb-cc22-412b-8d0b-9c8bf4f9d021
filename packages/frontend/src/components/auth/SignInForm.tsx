import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  EmailField,
  PasswordField,
  SubmitButton,
} from "@/utils/validation/components";
// import { useAuthContext } from '@/contexts/AuthContext';

const SignInForm: React.FC = () => {
  const { t } = useTranslation();
  // Placeholder form implementation
  const form = {} as any;
  const isLoading = false;
  const onSubmit = () => {};

  // Create footer with sign up and forgot password links
  const footer = (
    <>
      <div className="text-sm text-center">
        <span>{t("auth.noAccount")} </span>
        <Button variant="link" className="p-0" asChild>
          <Link to="/signup">{t("auth.signUp")}</Link>
        </Button>
      </div>
      <div className="text-sm text-center">
        <Button variant="link" className="p-0" asChild>
          <Link to="/forgot-password">{t("auth.forgotPassword")}</Link>
        </Button>
      </div>
    </>
  );

  return (
    <div className="auth-form-layout">
      <h1>{t("auth.signInTitle")}</h1>
      <p>{t("auth.signInDescription")}</p>
      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          <EmailField form={form} {...({} as any)} />
          <PasswordField form={form} {...({} as any)} />
          <SubmitButton
            isSubmitting={isLoading}
            label={t("auth.signIn")}
            loadingLabel={t("common.loading")}
          />
        </form>
      </Form>
      {footer}
    </div>
  );
};

export default SignInForm;
