import React, { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';
import { websocketService } from '@/services/websocketService';
import { cn } from '@/lib/utils';
import type { SegmentationProgressEvent } from '@spheroseg/shared/src/types/entities';

interface RealTimeProgressIndicatorProps {
  imageId: string;
  projectId: string;
  initialStatus?: 'pending' | 'processing' | 'completed' | 'failed' | 'queued';
  initialProgress?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
  onStatusChange?: (status: string, progress: number) => void;
}

const RealTimeProgressIndicator: React.FC<RealTimeProgressIndicatorProps> = ({
  imageId,
  projectId,
  initialStatus = 'pending',
  initialProgress = 0,
  className,
  size = 'md',
  showDetails = true,
  onStatusChange,
}) => {
  const [progress, setProgress] = useState(initialProgress);
  const [status, setStatus] = useState<'queued' | 'processing' | 'completed' | 'failed' | 'cancelled'>(
    initialStatus as any
  );
  const [stage, setStage] = useState<string | undefined>();
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Size variants
  const sizeConfig = {
    sm: {
      progress: 'h-1',
      text: 'text-xs',
      icon: 'h-3 w-3',
      badge: 'text-xs px-1 py-0.5',
    },
    md: {
      progress: 'h-2',
      text: 'text-sm',
      icon: 'h-4 w-4',
      badge: 'text-xs px-2 py-1',
    },
    lg: {
      progress: 'h-3',
      text: 'text-base',
      icon: 'h-5 w-5',
      badge: 'text-sm px-3 py-1.5',
    },
  };

  const config = sizeConfig[size];

  // Format estimated time remaining
  const formatTimeRemaining = (ms: number): string => {
    if (ms < 1000) return 'Less than 1s';
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
    return `${Math.round(ms / 3600000)}h`;
  };

  // Get status icon and color
  const getStatusInfo = (currentStatus: string) => {
    switch (currentStatus) {
      case 'queued':
        return {
          icon: <Clock className={cn(config.icon, 'text-yellow-500')} />,
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          label: 'Queued',
        };
      case 'processing':
        return {
          icon: <Loader2 className={cn(config.icon, 'text-blue-500 animate-spin')} />,
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Processing',
        };
      case 'completed':
        return {
          icon: <CheckCircle className={cn(config.icon, 'text-green-500')} />,
          color: 'bg-green-100 text-green-800 border-green-200',
          label: 'Complete',
        };
      case 'failed':
        return {
          icon: <XCircle className={cn(config.icon, 'text-red-500')} />,
          color: 'bg-red-100 text-red-800 border-red-200',
          label: 'Failed',
        };
      case 'cancelled':
        return {
          icon: <XCircle className={cn(config.icon, 'text-gray-500')} />,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Cancelled',
        };
      default:
        return {
          icon: <Clock className={cn(config.icon, 'text-gray-500')} />,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Pending',
        };
    }
  };

  // Subscribe to WebSocket events
  useEffect(() => {
    if (!projectId || !imageId) return;

    let isSubscribed = true;

    // Initialize WebSocket connection
    const initializeWebSocket = async () => {
      try {
        await websocketService.connect();
        await websocketService.joinProjectRoom(projectId);
      } catch (error) {
        console.error('Failed to initialize WebSocket for progress indicator:', error);
      }
    };

    // Handle progress events
    const handleProgressEvent = (data: SegmentationProgressEvent) => {
      if (!isSubscribed || data.imageId !== imageId) return;

      console.log('Received progress event:', data);

      setProgress(data.progress);
      setStatus(data.status);
      setStage(data.stage);
      setEstimatedTimeRemaining(data.estimatedTimeRemaining);
      setError(data.error);
      setLastUpdate(new Date());

      // Call status change callback
      if (onStatusChange) {
        onStatusChange(data.status, data.progress);
      }
    };

    // Handle legacy segmentation update events for backward compatibility
    const handleLegacySegmentationUpdate = (data: any) => {
      if (!isSubscribed || data.imageId !== imageId) return;

      console.log('Received legacy segmentation update:', data);

      // Map legacy status to new progress format
      let newProgress = 0;
      let newStatus: typeof status = 'pending';

      switch (data.status) {
        case 'queued':
        case 'pending':
          newStatus = 'queued';
          newProgress = 0;
          break;
        case 'processing':
          newStatus = 'processing';
          newProgress = data.progress || 50; // Default to 50% if no progress provided
          break;
        case 'completed':
          newStatus = 'completed';
          newProgress = 100;
          break;
        case 'failed':
          newStatus = 'failed';
          newProgress = progress; // Keep current progress
          setError(data.error || 'Processing failed');
          break;
      }

      setProgress(newProgress);
      setStatus(newStatus);
      setLastUpdate(new Date());

      if (onStatusChange) {
        onStatusChange(newStatus, newProgress);
      }
    };

    initializeWebSocket();

    // Subscribe to new progress events
    const progressEventId = websocketService.on('segmentation-progress', handleProgressEvent);
    const startedEventId = websocketService.on('segmentation-started', handleProgressEvent);
    const completedEventId = websocketService.on('segmentation-completed', handleProgressEvent);
    const failedEventId = websocketService.on('segmentation-failed', handleProgressEvent);
    const cancelledEventId = websocketService.on('segmentation-cancelled', handleProgressEvent);

    // Subscribe to legacy events for backward compatibility
    const legacyUpdateId = websocketService.on('segmentation_update', handleLegacySegmentationUpdate);
    const legacyUpdateLegacyId = websocketService.on('segmentation_update_legacy', handleLegacySegmentationUpdate);

    return () => {
      isSubscribed = false;
      
      // Unsubscribe from all events
      websocketService.off('segmentation-progress', progressEventId);
      websocketService.off('segmentation-started', startedEventId);
      websocketService.off('segmentation-completed', completedEventId);
      websocketService.off('segmentation-failed', failedEventId);
      websocketService.off('segmentation-cancelled', cancelledEventId);
      websocketService.off('segmentation_update', legacyUpdateId);
      websocketService.off('segmentation_update_legacy', legacyUpdateLegacyId);
    };
  }, [projectId, imageId, onStatusChange]);

  const statusInfo = getStatusInfo(status);

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      {/* Status badge with icon */}
      <Badge
        variant="outline"
        className={cn(
          statusInfo.color,
          config.badge,
          'flex items-center space-x-1'
        )}
      >
        {statusInfo.icon}
        <span>{statusInfo.label}</span>
        {showDetails && progress > 0 && progress < 100 && (
          <span className="ml-1">{progress}%</span>
        )}
      </Badge>

      {/* Progress bar for processing status */}
      {status === 'processing' && (
        <div className="flex-1 min-w-0">
          <Progress 
            value={progress} 
            className={cn('w-full', config.progress)}
          />
          {showDetails && stage && (
            <div className={cn('mt-1 truncate', config.text, 'text-muted-foreground')}>
              {stage}
            </div>
          )}
        </div>
      )}

      {/* Estimated time remaining */}
      {showDetails && status === 'processing' && estimatedTimeRemaining && estimatedTimeRemaining > 0 && (
        <div className={cn(config.text, 'text-muted-foreground whitespace-nowrap')}>
          {formatTimeRemaining(estimatedTimeRemaining)}
        </div>
      )}

      {/* Error message */}
      {status === 'failed' && error && showDetails && (
        <div className={cn(config.text, 'text-red-600 truncate')} title={error}>
          {error}
        </div>
      )}
    </div>
  );
};

export default RealTimeProgressIndicator;