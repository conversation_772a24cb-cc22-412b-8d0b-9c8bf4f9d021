import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { Button } from "../button";
import "@testing-library/jest-dom";

describe("Button Component", () => {
  it("renders with default variant and size", () => {
    render(<Button>Click me</Button>);

    const button = screen.getByRole("button", { name: "Click me" });
    expect(button).toBeInTheDocument();

    const className = button.className;
    expect(className).toContain("bg-primary");
    expect(className).toContain("text-primary-foreground");
    expect(className).toContain("h-10");
  });

  it("applies custom className", () => {
    render(<Button className="custom-class">Click me</Button>);

    const button = screen.getByRole("button", { name: "Click me" });
    expect(button.className).toContain("custom-class");
  });

  it("renders with different variants", () => {
    const { rerender } = render(
      <Button variant="destructive">Destructive</Button>,
    );

    let button = screen.getByRole("button", { name: "Destructive" });
    let className = button.className;
    expect(className).toContain("bg-destructive");
    expect(className).toContain("text-destructive-foreground");

    rerender(<Button variant="outline">Outline</Button>);
    button = screen.getByRole("button", { name: "Outline" });
    className = button.className;
    expect(className).toContain("border");
    expect(className).toContain("bg-background");

    rerender(<Button variant="secondary">Secondary</Button>);
    button = screen.getByRole("button", { name: "Secondary" });
    className = button.className;
    expect(className).toContain("bg-secondary");
    expect(className).toContain("text-secondary-foreground");

    rerender(<Button variant="ghost">Ghost</Button>);
    button = screen.getByRole("button", { name: "Ghost" });
    className = button.className;
    expect(className).toContain("hover:bg-accent");

    rerender(<Button variant="link">Link</Button>);
    button = screen.getByRole("button", { name: "Link" });
    className = button.className;
    expect(className).toContain("text-primary");
    expect(className).toContain("underline-offset-4");
  });

  it("renders with different sizes", () => {
    const { rerender } = render(<Button size="default">Default</Button>);

    let button = screen.getByRole("button", { name: "Default" });
    let className = button.className;
    expect(className).toContain("h-10");
    expect(className).toContain("px-");
    expect(className).toContain("py-");

    rerender(<Button size="sm">Small</Button>);
    button = screen.getByRole("button", { name: "Small" });
    className = button.className;
    expect(className).toContain("h-9");
    expect(className).toContain("px-");

    rerender(<Button size="lg">Large</Button>);
    button = screen.getByRole("button", { name: "Large" });
    className = button.className;
    expect(className).toContain("h-11");
    expect(className).toContain("px-");

    rerender(<Button size="icon">Icon</Button>);
    button = screen.getByRole("button", { name: "Icon" });
    className = button.className;
    expect(className).toContain("h-10");
    expect(className).toContain("w-10");
  });

  it("handles click events", () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    const button = screen.getByRole("button", { name: "Click me" });
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("renders as disabled when disabled prop is true", () => {
    render(<Button disabled>Disabled</Button>);

    const button = screen.getByRole("button", { name: "Disabled" });
    expect(button).toBeDisabled();
    expect(button.className).toContain("disabled:opacity-50");
  });

  it("renders with children", () => {
    render(
      <Button>
        <span>Child Element</span>
      </Button>,
    );

    expect(screen.getByText("Child Element")).toBeInTheDocument();
  });
});
