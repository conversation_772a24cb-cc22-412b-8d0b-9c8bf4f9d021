import { render } from "@testing-library/react";
import { Badge } from "../badge";
import "@testing-library/jest-dom";

describe("Badge Component", () => {
  it("renders with default variant", () => {
    const { container } = render(<Badge>Default Badge</Badge>);
    const badge = container.firstChild as HTMLElement;

    expect(badge).toBeInTheDocument();
    const className = badge.className;
    expect(className).toContain("bg-primary");
    expect(className).toContain("text-primary-foreground");
    expect(className).toContain("border-transparent");
    expect(badge).toHaveTextContent("Default Badge");
  });

  it("applies custom className", () => {
    const { container } = render(<Badge className="custom-class">Badge</Badge>);
    const badge = container.firstChild as HTMLElement;

    const className = badge.className;
    expect(className).toContain("custom-class");
    expect(className).toContain("bg-primary"); // Still has default classes
  });

  it("renders with secondary variant", () => {
    const { container } = render(
      <Badge variant="secondary">Secondary Badge</Badge>,
    );
    const badge = container.firstChild as HTMLElement;

    const className = badge.className;
    expect(className).toContain("bg-secondary");
    expect(className).toContain("text-secondary-foreground");
    expect(className).toContain("border-transparent");
  });

  it("renders with destructive variant", () => {
    const { container } = render(
      <Badge variant="destructive">Destructive Badge</Badge>,
    );
    const badge = container.firstChild as HTMLElement;

    const className = badge.className;
    expect(className).toContain("bg-destructive");
    expect(className).toContain("text-destructive-foreground");
    expect(className).toContain("border-transparent");
  });

  it("renders with outline variant", () => {
    const { container } = render(
      <Badge variant="outline">Outline Badge</Badge>,
    );
    const badge = container.firstChild as HTMLElement;

    const className = badge.className;
    expect(className).toContain("text-foreground");
    expect(className).not.toContain("bg-primary");
    expect(className).not.toContain("bg-secondary");
    expect(className).not.toContain("bg-destructive");
  });

  it("passes additional props to the div element", () => {
    const { container } = render(
      <Badge data-testid="badge-test" aria-label="Badge Label">
        Badge
      </Badge>,
    );
    const badge = container.firstChild as HTMLElement;

    expect(badge).toHaveAttribute("data-testid", "badge-test");
    expect(badge).toHaveAttribute("aria-label", "Badge Label");
  });
});
