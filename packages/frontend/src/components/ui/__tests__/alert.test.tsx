import { render } from "@testing-library/react";
import { Alert, AlertTitle, AlertDescription } from "../alert";
import "@testing-library/jest-dom";

describe("Alert Component", () => {
  it("renders with default variant", () => {
    const { container } = render(
      <Alert>
        <AlertTitle>Alert Title</AlertTitle>
        <AlertDescription>Alert Description</AlertDescription>
      </Alert>,
    );

    const alert = container.firstChild as HTMLElement;
    expect(alert).toBeInTheDocument();
    expect(alert).toHaveAttribute("role", "alert");
    const className = alert.className;
    expect(className).toContain("bg-background");
    expect(className).toContain("text-foreground");

    // Check title and description
    expect(alert.querySelector("h5")).toHaveTextContent("Alert Title");
    expect(alert.querySelector("div")).toHaveTextContent("Alert Description");
  });

  it("applies custom className", () => {
    const { container } = render(
      <Alert className="custom-class">Alert Content</Alert>,
    );

    const alert = container.firstChild as HTMLElement;
    const className = alert.className;
    expect(className).toContain("custom-class");
    expect(className).toContain("bg-background"); // Still has default classes
  });

  it("renders with destructive variant", () => {
    const { container } = render(
      <Alert variant="destructive">Destructive Alert</Alert>,
    );

    const alert = container.firstChild as HTMLElement;
    const className = alert.className;
    expect(className).toContain("border-destructive/50");
    expect(className).toContain("text-destructive");
    expect(className).toContain("dark:border-destructive");
  });

  it("renders AlertTitle with custom className", () => {
    const { getByText } = render(
      <AlertTitle className="custom-title-class">Custom Title</AlertTitle>,
    );

    const title = getByText("Custom Title");
    const className = title.className;
    expect(className).toContain("custom-title-class");
    expect(className).toContain("mb-1");
    expect(className).toContain("font-medium");
  });

  it("renders AlertDescription with custom className", () => {
    const { getByText } = render(
      <AlertDescription className="custom-desc-class">
        Custom Description
      </AlertDescription>,
    );

    const description = getByText("Custom Description");
    const className = description.className;
    expect(className).toContain("custom-desc-class");
    expect(className).toContain("text-sm");
  });

  it("passes additional props to the alert element", () => {
    const { container } = render(
      <Alert data-testid="alert-test" aria-label="Important Alert">
        Alert Content
      </Alert>,
    );

    const alert = container.firstChild as HTMLElement;
    expect(alert).toHaveAttribute("data-testid", "alert-test");
    expect(alert).toHaveAttribute("aria-label", "Important Alert");
  });
});
