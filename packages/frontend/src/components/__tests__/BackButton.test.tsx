import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import BackButton from "../BackButton";

// Create a mock function for navigation
const mockNavigate = vi.fn();

// Mock dependencies
vi.mock("react-router-dom", () => ({
  useNavigate: () => mockNavigate,
}));

vi.mock("@/contexts/LanguageContext", () => ({
  useLanguage: () => ({
    t: (key: string) => {
      // Simple mock translation function
      const translations: Record<string, string> = {
        "common.backToHome": "Back to Home",
        "navbar.home": "Home",
      };
      return translations[key] || key;
    },
  }),
}));

describe("BackButton Component", () => {
  it("renders correctly", () => {
    render(<BackButton />);

    // Check that the button is rendered with the correct aria-label
    const button = screen.getByLabelText("Back to Home");
    expect(button).toBeInTheDocument();
  });

  it("navigates to homepage when clicked", () => {
    // Clear any previous calls
    mockNavigate.mockClear();

    render(<BackButton />);

    // Get the button and click it
    const button = screen.getByLabelText("Back to Home");
    fireEvent.click(button);

    // Confirm navigation was attempted
    expect(mockNavigate).toHaveBeenCalledWith("/");
  });

  it("has the correct styling", () => {
    render(<BackButton />);

    const button = screen.getByLabelText("Back to Home");

    // Check that the button element exists and contains expected classes in its className
    const className = button.className;
    expect(className).toContain("text-gray-600");
    expect(className).toContain("hover:text-gray-900");
    expect(className).toContain("dark:text-gray-400");
    expect(className).toContain("dark:hover:text-gray-100");
  });
});
