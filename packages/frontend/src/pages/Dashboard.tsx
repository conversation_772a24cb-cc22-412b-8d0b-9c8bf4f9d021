import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

import DashboardHeader from "@/components/DashboardHeader";
import StatsOverview from "@/components/dashboard/StatsOverview";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import DashboardTabs from "@/components/dashboard/DashboardTabs";
import ProjectsTab from "@/components/dashboard/ProjectsTab";
import { useDirectProjects } from "@/hooks/useDirectProjects";
import { useProjectDelete } from "@/hooks/useProjectDelete";
import { useCacheManager } from "@/hooks/useUnifiedCache";
import ErrorBoundary from "@/components/ErrorBoundary";

const Dashboard = () => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortField, setSortField] = useState<string>("updated_at");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const navigate = useNavigate();
  const location = useLocation();

  const { user, loading: authLoading } = useAuth();

  // Safety check - if user is not available, don't render
  if (!user && !authLoading) {
    return null;
  }

  // Show loading state while auth is loading
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        <div className="container mx-auto px-4 py-8 relative z-10">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-300">
                Loading...
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { t } = useLanguage();

  const queryClient = useQueryClient();
  const { clearByTag } = useCacheManager();

  // const { projects, loading, error, fetchProjects } = useDashboardProjects();
  const { projects, loading, error, fetchProjects } = useDirectProjects();

  // Removed debug logging to reduce console noise

  // Store fetchProjects in a ref to avoid dependency issues
  const fetchProjectsRef = useRef(fetchProjects);
  fetchProjectsRef.current = fetchProjects;

  // Initial fetch when dashboard is loaded - removed to avoid duplicate fetching
  // The useDirectProjects hook already handles initial fetch

  // Use refs to store current sort parameters to avoid recreating event handlers
  const sortFieldRef = useRef(sortField);
  const sortDirectionRef = useRef(sortDirection);

  // Update refs when sort parameters change
  useEffect(() => {
    sortFieldRef.current = sortField;
    sortDirectionRef.current = sortDirection;
  }, [sortField, sortDirection]);

  // Create stable event handlers that don't change
  const handleProjectCreated = useCallback(() => {
    fetchProjects(10, 0, sortFieldRef.current, sortDirectionRef.current);
  }, [fetchProjects]);

  const handleProjectDeleted = useCallback(() => {
    fetchProjects(10, 0, sortFieldRef.current, sortDirectionRef.current);
  }, [fetchProjects]);

  const handleImagesUploaded = useCallback(() => {
    // Refresh projects to update image counts
    fetchProjects(10, 0, sortFieldRef.current, sortDirectionRef.current);
  }, [fetchProjects]);

  // Removed duplicate initial fetch - useDirectProjects already handles this

  // Check if we need to refresh projects (e.g., after accepting invitation)
  useEffect(() => {
    if (location.state?.refreshProjects) {
      fetchProjects();
      // Clear the state to prevent repeated refreshes
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, fetchProjects, navigate, location.pathname]);

  useEffect(() => {
    // Poslouchej události pro aktualizaci seznamu projektů
    window.addEventListener("project-created", handleProjectCreated);
    window.addEventListener("project-deleted", handleProjectDeleted);
    window.addEventListener("images-uploaded", handleImagesUploaded);

    return () => {
      window.removeEventListener("project-created", handleProjectCreated);
      window.removeEventListener("project-deleted", handleProjectDeleted);
      window.removeEventListener("images-uploaded", handleImagesUploaded);
    };
  }, [handleProjectCreated, handleProjectDeleted, handleImagesUploaded]); // Dependencies on the memoized handlers

  const handleOpenProject = (id: string) => {
    navigate(`/project/${id}`);
  };

  // We don't actually need this hook instance here since we're using DeleteProjectDialog
  // which already handles the deletion. Keep it only for direct API access if needed.
  const { deleteProject: deleteProjectHook, isDeleting } = useProjectDelete();

  const handleDeleteProject = async (
    projectId: string,
    projectName: string,
  ) => {
    if (!projectId) {
      toast.error("Cannot delete project: missing project identifier");
      return;
    }

    // The actual deletion is handled by DeleteProjectDialog through its own hook instance
    // We just need to refresh the project list when notified of deletion
    fetchProjects(10, 0, sortField, sortDirection);
  };

  const handleSort = (field: "name" | "updatedAt" | "segmentationStatus") => {
    let dbField = sortField;

    // Map field names to database fields
    if (field === "name") dbField = "title";
    else if (field === "updatedAt") dbField = "updated_at";
    else if (field === "segmentationStatus") dbField = "status";

    // Toggle direction if same field
    const newDirection =
      dbField === sortField
        ? sortDirection === "asc"
          ? "desc"
          : "asc"
        : "desc";

    // Log sorting to debug

    setSortField(dbField);
    setSortDirection(newDirection);

    // Refresh projects with new sort parameters
    fetchProjects(10, 0, dbField, newDirection);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10 pointer-events-none">
          <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float" />
          <div
            className="absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float"
            style={{ animationDelay: "-2s" }}
          />
          <div
            className="absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float"
            style={{ animationDelay: "-4s" }}
          />
        </div>

        <DashboardHeader />
        <div className="container mx-auto px-4 py-8 relative z-10">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-red-200 dark:border-red-800 text-center shadow-md">
            <p className="text-red-500 dark:text-red-400 mb-4">{error}</p>
            <button
              onClick={() => fetchProjects()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
            >
              {t("common.tryAgain")}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Create a sorted copy of projects to display using useMemo to prevent re-renders
  const sortedProjects = React.useMemo(() => {
    try {
      return [...projects].sort((a, b) => {
        // Ensure both objects exist
        if (!a || !b) return 0;

        // Use the current sort field and direction
        let valueA: any = a[sortField as keyof typeof a];
        let valueB: any = b[sortField as keyof typeof b];

        // For date fields, convert to Date objects
        if (sortField === "updated_at" || sortField === "created_at") {
          // Handle undefined or invalid dates
          const dateA = valueA ? new Date(valueA) : null;
          const dateB = valueB ? new Date(valueB) : null;
          valueA = dateA && !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
          valueB = dateB && !isNaN(dateB.getTime()) ? dateB.getTime() : 0;
        } else {
          // For other fields, convert to string
          valueA = valueA?.toString().toLowerCase() || "";
          valueB = valueB?.toString().toLowerCase() || "";
        }

        // Apply sort direction
        if (sortDirection === "asc") {
          return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
        } else {
          return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
        }
      });
    } catch (_error) {
      console.error("[Dashboard] Error sorting projects:", _error);
      // Use unsorted projects if sorting fails
      return [...projects];
    }
  }, [projects, sortField, sortDirection]);

  // Log the sorted projects for debugging
  //

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float" />
        <div
          className="absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float"
          style={{ animationDelay: "-2s" }}
        />
        <div
          className="absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float"
          style={{ animationDelay: "-4s" }}
        />
      </div>

      <DashboardHeader />

      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold mb-1 dark:text-gray-100">
              {t("common.dashboard")}
            </h1>
            <p className="text-gray-500 dark:text-gray-300">
              {t("dashboard.manageProjects")}
            </p>
          </div>
        </div>

        <div className="mb-8 animate-fade-in">
          {/* Wrap StatsOverview in error boundary to isolate potential issues */}
          <ErrorBoundary
            componentName="StatsOverview"
            level="component"
            isolate={true}
            fallback={
              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <p className="text-yellow-800 dark:text-yellow-200">
                  {t("errors.statsLoadingError") || "Unable to load statistics"}
                </p>
              </div>
            }
          >
            <StatsOverview />
          </ErrorBoundary>
        </div>

        <div className="animate-fade-in" style={{ animationDelay: "0.1s" }}>
          <DashboardTabs
            viewMode={viewMode}
            setViewMode={setViewMode}
            onSort={handleSort}
            sortField={
              sortField === "title"
                ? "name"
                : sortField === "updated_at"
                  ? "updatedAt"
                  : sortField === "status"
                    ? "segmentationStatus"
                    : "name"
            }
            sortDirection={sortDirection}
          >
            <ProjectsTab
              projects={sortedProjects}
              viewMode={viewMode}
              loading={loading}
              onOpenProject={handleOpenProject}
              onDeleteProject={handleDeleteProject}
            />
          </DashboardTabs>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
