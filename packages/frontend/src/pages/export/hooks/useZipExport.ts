/**
 * ZIP Export Hook
 * Handles ZIP archive export functionality
 */

import J<PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import { format } from "date-fns";
import { toast } from "sonner";
import logger from "@/utils/logging/unifiedLogger";
import type { ProjectImage } from "@/types";

export interface ZipExportOptions {
  includeImages?: boolean;
  includeMasks?: boolean;
  includeMetadata?: boolean;
  includeAnnotations?: boolean;
  format?: "png" | "jpg";
}

export function useZipExport() {
  /**
   * Export project as ZIP archive
   */
  const exportToZip = async (
    images: ProjectImage[],
    projectName: string,
    options: ZipExportOptions = {},
  ) => {
    try {
      const zip = new JSZip();

      // Create folders
      const imagesFolder = zip.folder("images");
      const masksFolder = zip.folder("masks");
      const annotationsFolder = zip.folder("annotations");

      // Add project metadata
      if (options.includeMetadata) {
        const metadata = {
          project: projectName,
          exportDate: new Date().toISOString(),
          imageCount: images.length,
          exportOptions: options,
        };
        zip.file("metadata.json", JSON.stringify(metadata, null, 2));
      }

      // Process each image
      for (const image of images) {
        // Add original image (if requested and available)
        if (options.includeImages && image.imageUrl) {
          try {
            const response = await fetch(image.imageUrl);
            const blob = await response.blob();
            imagesFolder?.file(image.filename, blob);
          } catch (error) {
            logger.warn(`Failed to fetch image ${image.filename}:`, error);
          }
        }

        // Add segmentation mask (if available)
        if (options.includeMasks && image.segmentation?.maskUrl) {
          try {
            const response = await fetch(image.segmentation.maskUrl);
            const blob = await response.blob();
            const maskFilename = `${image.filename.split(".")[0]}_mask.${options.format || "png"}`;
            masksFolder?.file(maskFilename, blob);
          } catch (error) {
            logger.warn(`Failed to fetch mask for ${image.filename}:`, error);
          }
        }

        // Add annotations (if available)
        if (options.includeAnnotations && image.segmentation) {
          const annotationFilename = `${image.filename.split(".")[0]}_annotations.json`;
          const annotationData = {
            imageId: image.id,
            filename: image.filename,
            polygons: image.segmentation.polygons,
            cellCount: image.segmentation.cellCount,
            timestamp: image.segmentation.timestamp,
          };
          annotationsFolder?.file(
            annotationFilename,
            JSON.stringify(annotationData, null, 2),
          );
        }
      }

      // Generate ZIP file
      const content = await zip.generateAsync({
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: { level: 6 },
      });

      // Save ZIP file
      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_export_${timestamp}.zip`;
      saveAs(content, filename);

      toast.success(`Exported ${images.length} images to ZIP archive`);
      logger.info("ZIP export completed", {
        imageCount: images.length,
        options,
      });
    } catch (error) {
      logger.error("ZIP export failed:", error);
      toast.error("Failed to create ZIP archive");
      throw error;
    }
  };

  /**
   * Export selected images as ZIP
   */
  const exportSelectedToZip = async (
    selectedImages: ProjectImage[],
    projectName: string,
    options: ZipExportOptions = {},
  ) => {
    if (selectedImages.length === 0) {
      toast.warning("No images selected for export");
      return;
    }

    return exportToZip(selectedImages, projectName, options);
  };

  return {
    exportToZip,
    exportSelectedToZip,
  };
}
