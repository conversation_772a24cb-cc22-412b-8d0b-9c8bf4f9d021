/**
 * JSON Export Hook
 * Handles JSON and COCO format export functionality
 */

import { format } from "date-fns";
import { toast } from "sonner";
import { saveAs } from "file-saver";
import logger from "@/utils/logging/unifiedLogger";
import type { ProjectImage, Polygon } from "@/types";

export interface JsonExportOptions {
  format: "json" | "coco" | "yolo";
  includeMetadata?: boolean;
  prettyPrint?: boolean;
}

export function useJsonExport() {
  /**
   * Export to JSON format
   */
  const exportToJson = (
    images: ProjectImage[],
    projectName: string,
    options: JsonExportOptions = { format: "json" },
  ) => {
    try {
      let data: any;

      switch (options.format) {
        case "coco":
          data = convertToCOCO(images, projectName);
          break;
        case "yolo":
          data = convertToYOLO(images);
          break;
        default:
          data = {
            project: projectName,
            exportDate: new Date().toISOString(),
            imageCount: images.length,
            images: images.map((img) => ({
              id: img.id,
              filename: img.filename,
              status: img.segmentationStatus,
              uploadDate: img.uploadDate,
              segmentation: img.segmentation,
              ...(options.includeMetadata && {
                metadata: {
                  width: img.width,
                  height: img.height,
                  fileSize: img.fileSize,
                },
              }),
            })),
          };
      }

      const jsonString = options.prettyPrint
        ? JSON.stringify(data, null, 2)
        : JSON.stringify(data);

      const blob = new Blob([jsonString], { type: "application/json" });
      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_${options.format}_${timestamp}.json`;

      saveAs(blob, filename);

      toast.success(
        `Exported ${images.length} images to ${options.format.toUpperCase()}`,
      );
      logger.info("JSON export completed", {
        format: options.format,
        imageCount: images.length,
      });
    } catch (error) {
      logger.error("JSON export failed:", error);
      toast.error("Failed to export JSON file");
      throw error;
    }
  };

  /**
   * Convert to COCO format
   */
  const convertToCOCO = (images: ProjectImage[], projectName: string) => {
    const cocoData = {
      info: {
        description: projectName,
        version: "1.0",
        year: new Date().getFullYear(),
        date_created: new Date().toISOString(),
      },
      licenses: [],
      images: [] as any[],
      annotations: [] as any[],
      categories: [
        {
          id: 1,
          name: "cell",
          supercategory: "biological",
        },
      ],
    };

    let annotationId = 1;

    images.forEach((image, imgIndex) => {
      // Add image info
      cocoData.images.push({
        id: imgIndex + 1,
        file_name: image.filename,
        width: image.width || 0,
        height: image.height || 0,
        date_captured: image.uploadDate,
      });

      // Add annotations
      if (image.segmentation?.polygons) {
        image.segmentation.polygons.forEach((polygon: Polygon) => {
          const points = polygon.points.flat();
          const area = calculatePolygonArea(polygon.points);
          const bbox = calculateBoundingBox(polygon.points);

          cocoData.annotations.push({
            id: annotationId++,
            image_id: imgIndex + 1,
            category_id: 1,
            segmentation: [points],
            area,
            bbox,
            iscrowd: 0,
          });
        });
      }
    });

    return cocoData;
  };

  /**
   * Convert to YOLO format
   */
  const convertToYOLO = (images: ProjectImage[]) => {
    return images.map((image) => ({
      filename: image.filename,
      annotations:
        image.segmentation?.polygons?.map((polygon: Polygon) => {
          const bbox = calculateBoundingBox(polygon.points);
          const centerX = (bbox[0] + bbox[2] / 2) / (image.width || 1);
          const centerY = (bbox[1] + bbox[3] / 2) / (image.height || 1);
          const width = bbox[2] / (image.width || 1);
          const height = bbox[3] / (image.height || 1);

          return {
            class: 0, // Cell class
            x_center: centerX,
            y_center: centerY,
            width,
            height,
          };
        }) || [],
    }));
  };

  /**
   * Calculate polygon area
   */
  const calculatePolygonArea = (points: number[][]): number => {
    let area = 0;
    const n = points.length;

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      area += points[i][0] * points[j][1];
      area -= points[j][0] * points[i][1];
    }

    return Math.abs(area / 2);
  };

  /**
   * Calculate bounding box [x, y, width, height]
   */
  const calculateBoundingBox = (points: number[][]): number[] => {
    const xs = points.map((p) => p[0]);
    const ys = points.map((p) => p[1]);
    const minX = Math.min(...xs);
    const minY = Math.min(...ys);
    const maxX = Math.max(...xs);
    const maxY = Math.max(...ys);

    return [minX, minY, maxX - minX, maxY - minY];
  };

  return {
    exportToJson,
    convertToCOCO,
    convertToYOLO,
  };
}
