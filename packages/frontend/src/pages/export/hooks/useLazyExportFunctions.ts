/**
 * Lazy Export Functions Hook
 * Dynamically imports export functionality to reduce initial bundle size
 */

import { useState, useCallback } from "react";
import { toast } from "sonner";
import { useLanguage } from "@/contexts/LanguageContext";
import type { ProjectImage } from "@/types";
import logger from "@/utils/logging/unifiedLogger";

export interface ExportOptions {
  format: "excel" | "csv" | "json" | "coco" | "yolo" | "zip";
  includeMetadata?: boolean;
  includeImages?: boolean;
  includeMasks?: boolean;
  includeAnnotations?: boolean;
}

export function useLazyExportFunctions() {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const { t } = useLanguage();

  /**
   * Lazy-loaded Excel export
   */
  const exportToExcel = useCallback(async (images: ProjectImage[], projectName: string) => {
    setIsExporting(true);
    try {
      const { useExcelExport } = await import("./useExcelExport");
      const hook = useExcelExport();
      await hook.exportToExcel(images, projectName);
    } catch (error) {
      logger.error("Failed to load Excel export", { error });
      toast.error(t("export.error"));
    } finally {
      setIsExporting(false);
    }
  }, [t]);

  /**
   * Lazy-loaded CSV export
   */
  const exportToCsv = useCallback(async (images: ProjectImage[], projectName: string) => {
    setIsExporting(true);
    try {
      const { useCsvExport } = await import("./useCsvExport");
      const hook = useCsvExport();
      await hook.exportToCsv(images, projectName);
    } catch (error) {
      logger.error("Failed to load CSV export", { error });
      toast.error(t("export.error"));
    } finally {
      setIsExporting(false);
    }
  }, [t]);

  /**
   * Lazy-loaded JSON export
   */
  const exportToJson = useCallback(async (images: ProjectImage[], projectName: string) => {
    setIsExporting(true);
    try {
      const { useJsonExport } = await import("./useJsonExport");
      const hook = useJsonExport();
      await hook.exportToJson(images, projectName);
    } catch (error) {
      logger.error("Failed to load JSON export", { error });
      toast.error(t("export.error"));
    } finally {
      setIsExporting(false);
    }
  }, [t]);

  /**
   * Lazy-loaded ZIP export
   */
  const exportToZip = useCallback(async (images: ProjectImage[], projectName: string) => {
    setIsExporting(true);
    try {
      const { useZipExport } = await import("./useZipExport");
      const hook = useZipExport();
      await hook.exportToZip(images, projectName);
    } catch (error) {
      logger.error("Failed to load ZIP export", { error });
      toast.error(t("export.error"));
    } finally {
      setIsExporting(false);
    }
  }, [t]);

  /**
   * Main export function with dynamic loading
   */
  const exportData = useCallback(async (
    images: ProjectImage[],
    projectName: string,
    options: ExportOptions,
  ) => {
    if (images.length === 0) {
      toast.warning(t("export.noImages"));
      return;
    }

    switch (options.format) {
      case "excel":
        await exportToExcel(images, projectName);
        break;
      case "csv":
        await exportToCsv(images, projectName);
        break;
      case "json":
        await exportToJson(images, projectName);
        break;
      case "zip":
        await exportToZip(images, projectName);
        break;
      default:
        toast.error(t("export.unsupportedFormat"));
    }
  }, [exportToExcel, exportToCsv, exportToJson, exportToZip, t]);

  return {
    isExporting,
    exportProgress,
    exportData,
    exportToExcel,
    exportToCsv,
    exportToJson,
    exportToZip,
  };
}