import { Point } from "@/lib/segmentation";
import {
  getStrokeWidth,
  getColors,
  getPointRadius,
} from "./visualizationUtils";
import { useCoordinateTransform } from "@/pages/segmentation/hooks/polygonInteraction/useCoordinateTransform";

interface HighlightedSegmentProps {
  hoveredSegment: {
    polygonId: string | null;
    segmentIndex: number | null;
    projectedPoint: Point | null;
  };
  polygonPoints: Point[] | null;
  zoom: number;
  offset: { x: number; y: number };
}

const HighlightedSegment = ({
  hoveredSegment,
  polygonPoints,
  zoom,
  offset,
}: HighlightedSegmentProps) => {
  const { imageToScreenCoordinates } = useCoordinateTransform(zoom, offset);

  if (hoveredSegment.segmentIndex === null || !polygonPoints) return null;

  const segmentIndex = hoveredSegment.segmentIndex;
  if (segmentIndex < 0 || segmentIndex >= polygonPoints.length) return null;
  const p1 = polygonPoints[segmentIndex];
  const nextIndex = (segmentIndex + 1) % polygonPoints.length;
  if (nextIndex >= polygonPoints.length) return null;
  const p2 = polygonPoints[nextIndex];

  const projectedPoint = hoveredSegment.projectedPoint;

  if (!p1 || !p2) return null;

  const canvasP1 = imageToScreenCoordinates(p1);
  const canvasP2 = imageToScreenCoordinates(p2);
  const canvasProjectedPoint = projectedPoint
    ? imageToScreenCoordinates(projectedPoint)
    : null;

  const strokeWidth = getStrokeWidth();
  const colors = getColors();
  const projectedPointRadius = getPointRadius();

  return (
    <g>
      <line
        x1={canvasP1.x}
        y1={canvasP1.y}
        x2={canvasP2.x}
        y2={canvasP2.y}
        stroke={colors.line.hoveredColor}
        strokeWidth={strokeWidth * 1.5}
        style={{ pointerEvents: "none" }}
      />

      {canvasProjectedPoint && (
        <circle
          cx={canvasProjectedPoint.x}
          cy={canvasProjectedPoint.y}
          r={projectedPointRadius * 1.1}
          fill={colors.hoverPoint.fill}
          stroke={colors.hoverPoint.stroke}
          strokeWidth={strokeWidth * 0.8}
          style={{ pointerEvents: "none" }}
        />
      )}
    </g>
  );
};

export default HighlightedSegment;
