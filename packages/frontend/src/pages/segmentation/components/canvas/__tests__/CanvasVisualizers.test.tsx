import { render } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import TemporaryEditPath from "../TemporaryEditPath";
import SlicingModeVisualizer from "../SlicingModeVisualizer";
import PointAddingVisualizer from "../PointAddingVisualizer";

// Mock useCoordinateTransform hook
vi.mock(
  "@/pages/segmentation/hooks/polygonInteraction/useCoordinateTransform",
  () => ({
    useCoordinateTransform: (
      zoom: number,
      offset: { x: number; y: number },
    ) => ({
      getScreenCoordinates: (x: number, y: number) => ({
        screenX: x * zoom + offset.x,
        screenY: y * zoom + offset.y,
      }),
      getImageCoordinates: (screenX: number, screenY: number) => ({
        x: (screenX - offset.x) / zoom,
        y: (screenY - offset.y) / zoom,
      }),
    }),
  }),
);

// Mock sub-components for PointAddingVisualizer
vi.mock("../pointAddingVisualizer/HighlightedSegment", () => ({
  default: () => null,
}));

vi.mock("../pointAddingVisualizer/TempPointsPath", () => ({
  default: () => null,
}));

vi.mock("../pointAddingVisualizer/CursorLineConnector", () => ({
  default: () => null,
}));

describe("Canvas Visualizer Components", () => {
  describe("TemporaryEditPath", () => {
    it("should render without crashing", () => {
      const props = {
        tempPoints: {
          points: [
            { x: 10, y: 10 },
            { x: 20, y: 20 },
            { x: 30, y: 30 },
          ],
        },
        cursorPosition: null,
        zoom: 1,
        offset: { x: 0, y: 0 },
        isShiftPressed: false,
      };

      const { container } = render(
        <svg>
          <TemporaryEditPath {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should handle empty path", () => {
      const props = {
        tempPoints: {
          points: [],
        },
        cursorPosition: null,
        zoom: 1,
        offset: { x: 0, y: 0 },
        isShiftPressed: false,
      };

      const { container } = render(
        <svg>
          <TemporaryEditPath {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should render with scaling and offset", () => {
      const props = {
        tempPoints: {
          points: [
            { x: 10, y: 10 },
            { x: 20, y: 20 },
          ],
        },
        cursorPosition: { x: 30, y: 30 },
        zoom: 2,
        offset: { x: 50, y: 100 },
        isShiftPressed: true,
      };

      const { container } = render(
        <svg>
          <TemporaryEditPath {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });
  });

  describe("SlicingModeVisualizer", () => {
    it("should render without crashing", () => {
      const props = {
        sliceStartPoint: { x: 0, y: 0 },
        cursorPosition: { x: 100, y: 100 },
      };

      const { container } = render(
        <svg>
          <defs>
            <filter id="line-glow">
              <feGaussianBlur stdDeviation="2" />
            </filter>
            <filter id="point-glow">
              <feGaussianBlur stdDeviation="2" />
            </filter>
          </defs>
          <SlicingModeVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should handle null slicing start point", () => {
      const props = {
        sliceStartPoint: null,
        cursorPosition: { x: 100, y: 100 },
      };

      const { container } = render(
        <svg>
          <SlicingModeVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should render with cursor position", () => {
      const props = {
        sliceStartPoint: { x: 10, y: 10 },
        cursorPosition: { x: 50, y: 50 },
      };

      const { container } = render(
        <svg>
          <defs>
            <filter id="line-glow">
              <feGaussianBlur stdDeviation="2" />
            </filter>
            <filter id="point-glow">
              <feGaussianBlur stdDeviation="2" />
            </filter>
          </defs>
          <SlicingModeVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });
  });

  describe("PointAddingVisualizer", () => {
    it("should render without crashing", () => {
      const props = {
        hoveredSegment: {
          polygonId: null,
          segmentIndex: null,
          projectedPoint: null,
        },
        tempPoints: [
          { x: 10, y: 10 },
          { x: 20, y: 20 },
          { x: 30, y: 30 },
        ],
        selectedVertexIndex: 0,
        sourcePolygonId: "poly-1",
        polygonPoints: [
          { x: 0, y: 0 },
          { x: 50, y: 0 },
          { x: 50, y: 50 },
          { x: 0, y: 50 },
        ],
        cursorPosition: { x: 40, y: 40 },
      };

      const { container } = render(
        <svg>
          <PointAddingVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should render with hover segment", () => {
      const props = {
        hoveredSegment: {
          polygonId: "poly-1",
          segmentIndex: 1,
          projectedPoint: { x: 15, y: 15 },
        },
        tempPoints: [],
        selectedVertexIndex: null,
        sourcePolygonId: null,
        polygonPoints: null,
        cursorPosition: null,
      };

      const { container } = render(
        <svg>
          <PointAddingVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should handle empty temp points array", () => {
      const props = {
        hoveredSegment: {
          polygonId: null,
          segmentIndex: null,
          projectedPoint: null,
        },
        tempPoints: [],
        selectedVertexIndex: 0,
        sourcePolygonId: "poly-1",
        polygonPoints: [
          { x: 0, y: 0 },
          { x: 50, y: 0 },
          { x: 50, y: 50 },
          { x: 0, y: 50 },
        ],
        cursorPosition: null,
      };

      const { container } = render(
        <svg>
          <PointAddingVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });

    it("should render with selected vertex and temp points", () => {
      const props = {
        hoveredSegment: {
          polygonId: "poly-1",
          segmentIndex: 2,
          projectedPoint: { x: 50, y: 25 },
        },
        tempPoints: [
          { x: 100, y: 100 },
          { x: 200, y: 200 },
        ],
        selectedVertexIndex: 1,
        sourcePolygonId: "poly-1",
        polygonPoints: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
        ],
        cursorPosition: { x: 150, y: 150 },
      };

      const { container } = render(
        <svg>
          <PointAddingVisualizer {...props} />
        </svg>,
      );
      expect(container).toBeTruthy();
    });
  });
});
