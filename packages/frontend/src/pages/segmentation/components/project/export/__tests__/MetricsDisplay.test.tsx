import "@testing-library/jest-dom";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import MetricsDisplay from "../MetricsDisplay";
import { vi } from "vitest";

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  CheckCircle: () => null,
  Clipboard: () => null,
  DownloadCloud: () => null,
}));

// Mock the metric calculations module
vi.mock("../../../utils/metricCalculations", () => ({
  calculateMetrics: vi.fn(() => ({
    area: 1234.56,
    perimeter: 123.45,
    circularity: 0.89,
    elongation: 1.23,
    solidity: 0.95,
  })),
  formatNumber: vi.fn((num) => num.toFixed(2)),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(() => Promise.resolve()),
  },
});

describe("MetricsDisplay Component", () => {
  const mockSegmentation = {
    polygons: [
      {
        type: "external",
        points: [
          [0, 0],
          [100, 0],
          [100, 100],
          [0, 100],
        ],
        id: "1",
      },
      {
        type: "internal",
        points: [
          [25, 25],
          [75, 25],
          [75, 75],
          [25, 75],
        ],
        id: "2",
      },
    ],
    metadata: {
      imageId: "test-image",
      timestamp: new Date().toISOString(),
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render metrics for external polygons", () => {
    render(<MetricsDisplay segmentation={mockSegmentation} />);

    // Should show "Sféroid #1" for the first external polygon
    expect(screen.getByText("Sféroid #1")).toBeInTheDocument();
  });

  it("should handle clipboard copy functionality", async () => {
    render(<MetricsDisplay segmentation={mockSegmentation} />);

    // Find all buttons (copy buttons should be present)
    const allButtons = screen.getAllByRole("button");
    // Filter for copy buttons - they should have specific text or be in a certain position
    const copyButtons = allButtons.filter((button) => {
      const text = button.textContent?.toLowerCase() || "";
      return text.includes("kopírovat") || text.includes("copy");
    });

    expect(copyButtons.length).toBeGreaterThan(0);

    // Click the first copy button
    fireEvent.click(copyButtons[0]);

    // Verify clipboard writeText was called
    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
    });
  });

  it("should handle download functionality", () => {
    // Mock document methods
    const createElementSpy = vi.spyOn(document, "createElement");
    const appendChildSpy = vi.spyOn(document.body, "appendChild");
    const removeChildSpy = vi.spyOn(document.body, "removeChild");

    // Mock URL methods
    const createObjectURLSpy = vi
      .spyOn(URL, "createObjectURL")
      .mockReturnValue("blob:test");
    const revokeObjectURLSpy = vi.spyOn(URL, "revokeObjectURL");

    render(<MetricsDisplay segmentation={mockSegmentation} />);

    // Find all buttons and filter for download buttons
    const allButtons = screen.getAllByRole("button");
    const downloadButtons = allButtons.filter((button) => {
      const text = button.textContent?.toLowerCase() || "";
      return text.includes("stáhnout") || text.includes("download");
    });

    expect(downloadButtons.length).toBeGreaterThan(0);

    // Click download button
    fireEvent.click(downloadButtons[0]);

    // Verify download was triggered
    expect(createElementSpy).toHaveBeenCalledWith("a");
    expect(createObjectURLSpy).toHaveBeenCalled();
    expect(appendChildSpy).toHaveBeenCalled();
    expect(removeChildSpy).toHaveBeenCalled();
    expect(revokeObjectURLSpy).toHaveBeenCalled();

    // Cleanup
    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
    removeChildSpy.mockRestore();
    createObjectURLSpy.mockRestore();
    revokeObjectURLSpy.mockRestore();
  });
});
