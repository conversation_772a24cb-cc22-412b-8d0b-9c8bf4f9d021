import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Circle } from "lucide-react";

const PolygonLegend: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="bg-background border border-border rounded-lg p-3 mb-2">
      <h4 className="text-xs font-medium text-foreground mb-2">
        {t("segmentation.legend.title") || "Legend"}
      </h4>
      <div className="space-y-2">
        <div className="flex items-center text-xs text-muted-foreground">
          <Circle className="w-3 h-3 mr-2 fill-red-500 text-red-500" />
          <span>{t("segmentation.legend.external") || "External Boundaries"}</span>
        </div>
        <div className="flex items-center text-xs text-muted-foreground">
          <Circle className="w-3 h-3 mr-2 fill-blue-500 text-blue-500" />
          <span>{t("segmentation.legend.internal") || "Internal Boundaries"}</span>
        </div>
      </div>
    </div>
  );
};

export default PolygonLegend;