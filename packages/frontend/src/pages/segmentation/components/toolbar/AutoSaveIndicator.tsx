import React from 'react';
import { Check, Clock, AlertCircle, Wifi, WifiOff, RotateCcw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/LanguageContext';
import { AutoSaveStatus, AutoSaveState } from '../../hooks/segmentation/useAutoSave';

interface AutoSaveIndicatorProps {
  autoSaveState: AutoSaveState;
  autoSaveEnabled: boolean;
  className?: string;
  compact?: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  autoSaveState,
  autoSaveEnabled,
  className,
  compact = false
}) => {
  const { t } = useLanguage();

  const getStatusConfig = () => {
    if (!autoSaveEnabled) {
      return {
        icon: Clock,
        text: compact ? 'Disabled' : (t('autoSave.disabled') || 'Auto-save disabled'),
        textColor: 'text-gray-500',
        iconColor: 'text-gray-400',
        bgColor: 'bg-gray-50',
        animate: false
      };
    }

    switch (autoSaveState.status) {
      case AutoSaveStatus.Idle:
        return {
          icon: Clock,
          text: compact ? 'Ready' : (t('autoSave.ready') || 'Ready to auto-save'),
          textColor: 'text-gray-600',
          iconColor: 'text-gray-400',
          bgColor: 'bg-gray-50',
          animate: false
        };
      
      case AutoSaveStatus.Saving:
        return {
          icon: RotateCcw,
          text: compact ? 'Saving...' : (t('autoSave.saving') || 'Auto-saving...'),
          textColor: 'text-blue-600',
          iconColor: 'text-blue-500',
          bgColor: 'bg-blue-50',
          animate: true
        };
      
      case AutoSaveStatus.Saved:
        const timeString = autoSaveState.lastSavedAt 
          ? autoSaveState.lastSavedAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          : '';
        return {
          icon: Check,
          text: compact 
            ? 'Saved' 
            : (t('autoSave.saved') || `All changes saved${timeString ? ` at ${timeString}` : ''}`),
          textColor: 'text-green-600',
          iconColor: 'text-green-500',
          bgColor: 'bg-green-50',
          animate: false
        };
      
      case AutoSaveStatus.Failed:
        return {
          icon: AlertCircle,
          text: compact 
            ? 'Failed' 
            : (t('autoSave.failed') || `Save failed${autoSaveState.retryCount > 0 ? ` (${autoSaveState.retryCount} retries)` : ''}`),
          textColor: 'text-red-600',
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50',
          animate: false
        };
      
      case AutoSaveStatus.Offline:
        return {
          icon: WifiOff,
          text: compact 
            ? 'Offline' 
            : (t('autoSave.offline') || 'Offline - changes queued'),
          textColor: 'text-orange-600',
          iconColor: 'text-orange-500',
          bgColor: 'bg-orange-50',
          animate: false
        };
      
      default:
        return {
          icon: Clock,
          text: compact ? 'Unknown' : (t('autoSave.unknown') || 'Unknown status'),
          textColor: 'text-gray-600',
          iconColor: 'text-gray-400',
          bgColor: 'bg-gray-50',
          animate: false
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  if (compact) {
    return (
      <div className={cn(
        'flex items-center gap-1 px-2 py-1 rounded text-xs',
        config.bgColor,
        className
      )}>
        <IconComponent 
          size={12} 
          className={cn(
            config.iconColor,
            config.animate && 'animate-spin'
          )} 
        />
        <span className={config.textColor}>
          {config.text}
        </span>
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200',
      config.bgColor,
      'border-gray-200',
      className
    )}>
      <IconComponent 
        size={16} 
        className={cn(
          config.iconColor,
          config.animate && 'animate-spin'
        )} 
      />
      
      <div className="flex flex-col">
        <span className={cn('text-sm font-medium', config.textColor)}>
          {config.text}
        </span>
        
        {autoSaveState.pendingChanges && (
          <span className="text-xs text-gray-500">
            {t('autoSave.pendingChanges') || 'Unsaved changes'}
          </span>
        )}
        
        {autoSaveState.status === AutoSaveStatus.Failed && autoSaveState.errorMessage && (
          <span className="text-xs text-red-500" title={autoSaveState.errorMessage}>
            {autoSaveState.errorMessage.length > 30 
              ? `${autoSaveState.errorMessage.substring(0, 30)}...`
              : autoSaveState.errorMessage
            }
          </span>
        )}
        
        {autoSaveState.lastSavedAt && (
          <span className="text-xs text-gray-500">
            {t('autoSave.lastSaved') || 'Last saved:'} {autoSaveState.lastSavedAt.toLocaleString()}
          </span>
        )}
      </div>
      
      {!navigator.onLine && (
        <WifiOff size={14} className="text-orange-500 ml-auto" title="Offline" />
      )}
    </div>
  );
};

export default AutoSaveIndicator;