import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import EditorCanvas from "../canvas/CanvasV2";
import { EditMode } from "@/pages/segmentation/hooks/segmentation";
import { vi } from "vitest";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { MemoryRouter } from "react-router-dom";

// Mock the child components to simplify testing
vi.mock("../canvas/CanvasContainer", () => ({
  default: ({
    children,
    onMouseDown,
    onMouseMove,
    onMouseUp,
    onMouseLeave,
    ref,
  }: any) => (
    <div
      data-testid="canvas-container"
      ref={ref}
      onClick={onMouseDown}
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
      onMouseLeave={onMouseLeave}
    >
      {children}
    </div>
  ),
}));

vi.mock("../canvas/CanvasContent", () => ({
  default: ({ children }: any) => (
    <div data-testid="canvas-content">{children}</div>
  ),
}));

vi.mock("../canvas/CanvasImage", () => ({
  default: ({ src }: any) => (
    <img data-testid="canvas-image" src={src} alt="mock" />
  ),
}));

vi.mock("../canvas/CanvasPolygonLayer", () => ({
  default: ({ segmentation, selectedPolygonId, onSelectPolygon }: any) => (
    <div
      data-testid="canvas-polygon-layer"
      data-segmentation-id={segmentation?.id}
      data-selected-polygon-id={selectedPolygonId}
      onClick={() => onSelectPolygon && onSelectPolygon("test-polygon-id")}
    >
      Polygon Layer
    </div>
  ),
}));

vi.mock("../canvas/CanvasLoadingOverlay", () => ({
  default: ({ loading }: any) => (
    <div data-testid="canvas-loading-overlay" data-loading={loading}>
      {loading ? "Loading..." : "Not Loading"}
    </div>
  ),
}));

// Mock the KeyboardShortcutsHelp component
vi.mock("../KeyboardShortcutsHelp", () => ({
  default: () => (
    <div data-testid="keyboard-shortcuts-help">Keyboard Shortcuts</div>
  ),
}));

// These tests validate the EditorCanvas component
describe("EditorCanvas", () => {
  // Default props for the component
  const defaultProps = {
    imageData: {
      width: 800,
      height: 600,
      src: "test-image.jpg",
    },
    segmentationData: {
      polygons: [
        {
          id: "polygon-1",
          type: "external" as const,
          points: [
            { x: 100, y: 100 },
            { x: 200, y: 100 },
            { x: 200, y: 200 },
            { x: 100, y: 200 },
          ],
        },
      ],
    },
    transform: {
      zoom: 1,
      translateX: 0,
      translateY: 0,
    },
    selectedPolygonId: null,
    hoveredVertex: null,
    setHoveredVertex: vi.fn(),
    tempPoints: [],
    editMode: "View" as EditMode,
    canvasRef: React.createRef<HTMLDivElement>(),
    interactionState: null,
    onMouseDown: vi.fn(),
    onMouseMove: vi.fn(),
    onMouseUp: vi.fn(),
  };

  it("renders the canvas component with required elements", () => {
    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...defaultProps} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    // Check that the main canvas container is rendered
    expect(screen.getByRole("presentation")).toBeInTheDocument();
  });

  it("renders without image data", () => {
    const propsWithoutImage = {
      ...defaultProps,
      imageData: null,
    };

    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...propsWithoutImage} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    // Should still render the canvas container
    expect(screen.getByRole("presentation")).toBeInTheDocument();
  });

  it("renders without segmentation data", () => {
    const propsWithoutSegmentation = {
      ...defaultProps,
      segmentationData: null,
    };

    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...propsWithoutSegmentation} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    // Should still render the canvas container
    expect(screen.getByRole("presentation")).toBeInTheDocument();
  });

  it("calls onMouseDown when canvas is clicked", () => {
    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...defaultProps} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    const canvas = screen.getByRole("presentation");
    fireEvent.mouseDown(canvas);
    expect(defaultProps.onMouseDown).toHaveBeenCalled();
  });

  it("calls onMouseMove when mouse moves over canvas", () => {
    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...defaultProps} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    const canvas = screen.getByRole("presentation");
    fireEvent.mouseMove(canvas);
    expect(defaultProps.onMouseMove).toHaveBeenCalled();
  });

  it("calls onMouseUp when mouse is released", () => {
    render(
      <MemoryRouter>
        <AuthProvider>
          <LanguageProvider>
            <EditorCanvas {...defaultProps} />
          </LanguageProvider>
        </AuthProvider>
      </MemoryRouter>,
    );

    const canvas = screen.getByRole("presentation");
    fireEvent.mouseUp(canvas);
    expect(defaultProps.onMouseUp).toHaveBeenCalled();
  });
});
