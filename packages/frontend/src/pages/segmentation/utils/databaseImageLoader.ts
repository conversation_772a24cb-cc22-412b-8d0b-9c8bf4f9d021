/**
 * Utility for loading images directly from the database
 */

import apiClient from '@/services/api/client';

/**
 * Attempts to load image data directly from the database
 * @param projectId Project ID
 * @param imageId Image ID
 * @returns Promise resolving to image data if found, null otherwise
 */
export const loadImageFromDatabase = async (projectId: string, imageId: string): Promise<any | null> => {
  // Try all possible endpoints in sequence
  const endpoints = [
    // 1. Direct image by ID
    {
      url: `/projects/${projectId}/images/${imageId}`,
      method: 'get',
      description: 'direct by ID',
    },
    // 2. All images, then filter
    {
      url: `/projects/${projectId}/images`,
      method: 'get',
      description: 'all images',
      process: (data: unknown[]) => {
        if (!Array.isArray(data)) return null;

        // Try to find by ID first
        let match = data.find((img) => (img as any).id === imageId);

        // If not found by ID, try by name
        if (!match) {
          match = data.find((img) => (img as any).name === imageId);
        }

        // If still not found, try by partial match on name or ID
        if (!match) {
          match = data.find((img) => ((img as any).id && (img as any).id.includes(imageId)) || ((img as any).name && (img as any).name.includes(imageId)));
        }

        return match;
      },
    },
    // 3. Query by name
    {
      url: `/projects/${projectId}/images?name=${encodeURIComponent(imageId)}`,
      method: 'get',
      description: 'by name',
      process: (data: unknown[]) => (Array.isArray(data) && data.length > 0 ? data[0] : null),
    },
    // 4. Alternative endpoint
    {
      url: `/images/${imageId}`,
      method: 'get',
      description: 'alternative endpoint',
    },
    // 5. Another alternative endpoint
    {
      url: `/projects/images/${imageId}`,
      method: 'get',
      description: 'another alternative',
    },
  ];

  // Try each endpoint in sequence
  for (const endpoint of endpoints) {
    try {
      // Add cache-busting query parameter
      const url = `${endpoint.url}${endpoint.url.includes('?') ? '&' : '?'}_=${Date.now()}`;

      const response = await apiClient[endpoint.method](url, {
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      });

      let imageData = response.data;

      // If we need to process the data (e.g., filter from a list)
      if (endpoint.process && typeof endpoint.process === 'function') {
        imageData = endpoint.process(response.data);
        if (!imageData) {
          continue; // Try next endpoint if no match found
        }
      }

      // Ensure we have width and height
      if (!imageData.width || !imageData.height) {
        imageData.width = imageData.width || 800;
        imageData.height = imageData.height || 600;
      }

      // Ensure we have a src property
      if (!imageData.src && imageData.storage_path) {
        imageData.src = imageData.storage_path;
      }

      return imageData;
    } catch (_error) {
      // Continue to next endpoint
    }
  }

  return null;
};

/**
 * Attempts to load all images for a project from the database
 * @param projectId Project ID
 * @returns Promise resolving to array of image data
 */
export const loadAllImagesFromDatabase = async (projectId: string): Promise<any[]> => {
  try {
    // Add cache-busting query parameter
    const url = `/projects/${projectId}/images?_=${Date.now()}`;

    const response = await apiClient.get(url, {
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });

    if (Array.isArray(response.data)) {
      return response.data;
    }

    return [];
  } catch (_error) {
    return [];
  }
};
