import { Point, Polygon } from '@/lib/segmentation';

/**
 * Calculate the intersection point between two line segments
 */
function lineIntersection(
  p1: Point, p2: Point, // First line segment
  p3: Point, p4: Point  // Second line segment
): Point | null {
  const x1 = p1.x, y1 = p1.y;
  const x2 = p2.x, y2 = p2.y;
  const x3 = p3.x, y3 = p3.y;
  const x4 = p4.x, y4 = p4.y;

  const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
  
  // Lines are parallel
  if (Math.abs(denom) < 1e-10) return null;

  const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
  const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

  // Check if intersection is within both line segments
  if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
    return {
      x: x1 + t * (x2 - x1),
      y: y1 + t * (y2 - y1)
    };
  }

  return null;
}

/**
 * Check which side of a line a point is on
 * Returns positive if point is on the left, negative if on the right, 0 if on the line
 */
function pointSideOfLine(point: Point, lineStart: Point, lineEnd: Point): number {
  return (lineEnd.x - lineStart.x) * (point.y - lineStart.y) - 
         (lineEnd.y - lineStart.y) * (point.x - lineStart.x);
}

/**
 * Slices a polygon along a line defined by two points
 * @param polygon The polygon to slice
 * @param sliceStart The start point of the slice line
 * @param sliceEnd The end point of the slice line
 * @returns An array of two new polygons if slicing was successful, or null if slicing failed
 */
export function slicePolygon(polygon: Polygon, sliceStart: Point, sliceEnd: Point): [Polygon, Polygon] | null {
  if (!polygon.points || polygon.points.length < 3) return null;

  const intersections: Array<{ point: Point; edgeIndex: number; t: number }> = [];
  const points = polygon.points;

  // Find all intersections between the slice line and polygon edges
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length;
    const intersection = lineIntersection(sliceStart, sliceEnd, points[i], points[j]);
    
    if (intersection) {
      // Calculate parameter t along the edge for sorting intersections
      const edgeLength = Math.sqrt(
        Math.pow(points[j].x - points[i].x, 2) + 
        Math.pow(points[j].y - points[i].y, 2)
      );
      const intersectionDist = Math.sqrt(
        Math.pow(intersection.x - points[i].x, 2) + 
        Math.pow(intersection.y - points[i].y, 2)
      );
      const t = edgeLength > 0 ? intersectionDist / edgeLength : 0;
      
      intersections.push({ point: intersection, edgeIndex: i, t });
    }
  }

  // Need exactly 2 intersections for a valid slice
  if (intersections.length !== 2) {
    console.warn(`Slice line intersects polygon at ${intersections.length} points, need exactly 2`);
    return null;
  }

  // Sort intersections by edge index and t parameter
  intersections.sort((a, b) => {
    if (a.edgeIndex !== b.edgeIndex) return a.edgeIndex - b.edgeIndex;
    return a.t - b.t;
  });

  // Build the two new polygons
  const polygon1Points: Point[] = [];
  const polygon2Points: Point[] = [];

  // Add first intersection point to both polygons
  polygon1Points.push(intersections[0].point);
  polygon2Points.push(intersections[0].point);

  // Traverse from first intersection to second, adding points to polygon1
  let currentIndex = intersections[0].edgeIndex + 1;
  while (currentIndex % points.length !== (intersections[1].edgeIndex + 1) % points.length) {
    polygon1Points.push(points[currentIndex % points.length]);
    currentIndex++;
  }

  // Add second intersection point to polygon1
  polygon1Points.push(intersections[1].point);

  // Traverse from second intersection back to first, adding points to polygon2
  currentIndex = intersections[1].edgeIndex + 1;
  while (currentIndex % points.length !== (intersections[0].edgeIndex + 1) % points.length) {
    polygon2Points.push(points[currentIndex % points.length]);
    currentIndex++;
  }

  // Add second intersection point to polygon2 (in reverse order)
  polygon2Points.unshift(intersections[1].point);

  // Ensure both polygons have at least 3 points
  if (polygon1Points.length < 3 || polygon2Points.length < 3) {
    console.warn('One of the resulting polygons has less than 3 points');
    return null;
  }

  // Create new polygon objects
  const newPolygon1: Polygon = {
    id: `polygon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    points: polygon1Points,
    color: polygon.color || '#ff0000',
    confidence: polygon.confidence || 1.0
  };

  const newPolygon2: Polygon = {
    id: `polygon_${Date.now() + 1}_${Math.random().toString(36).substr(2, 9)}`,
    points: polygon2Points,
    color: polygon.color || '#ff0000',
    confidence: polygon.confidence || 1.0
  };

  return [newPolygon1, newPolygon2];
}
