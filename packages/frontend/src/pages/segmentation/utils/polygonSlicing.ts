import { Point, Polygon } from '../types';
import { slicePolygonObject } from '@spheroseg/shared/utils/polygonUtils.unified';

interface LineSegment {
  start: Point;
  end: Point;
}

/**
 * Creates two new polygons by slicing an existing polygon along a line
 * Uses the shared polygon slicing algorithm
 */
export const slicePolygon = (polygon: Polygon, sliceLine: LineSegment): Polygon[] => {
  // Use shared polygon slicing implementation
  const result = slicePolygonObject(polygon, sliceLine.start, sliceLine.end);
  
  if (result.success && result.polygons.length > 0) {
    return result.polygons;
  }
  
  // Return original polygon if slicing failed
  return [polygon];
};
