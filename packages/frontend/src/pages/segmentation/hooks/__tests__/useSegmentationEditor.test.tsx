import { describe, it, expect } from "vitest";
import { useSegmentationEditor, EditMode } from "../useSegmentationEditor";

describe("useSegmentationEditor Hook", () => {
  it("should export the hook", () => {
    expect(useSegmentationEditor).toBeDefined();
    expect(typeof useSegmentationEditor).toBe("function");
  });

  it("should export EditMode enum", () => {
    expect(EditMode).toBeDefined();
    expect(EditMode.VIEW).toBe("VIEW");
    expect(EditMode.EDIT).toBe("EDIT");
    expect(EditMode.CREATE_POLYGON).toBe("CREATE_POLYGON");
    expect(EditMode.SLICE).toBe("SLICE");
    expect(EditMode.ADD_POINTS).toBe("ADD_POINTS");
  });
});
