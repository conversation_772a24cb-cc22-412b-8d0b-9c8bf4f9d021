import { v4 as uuidv4 } from "uuid";
import { Point, Polygon, SegmentationData } from "./types";
import usePolygonWorker from "../usePolygonWorker";
import { getLogger } from "@/utils/logging/unifiedLogger";

const logger = getLogger("segmentation:geometry.worker");
import { 
  calculatePolygonArea, 
  calculatePolygonPerimeter, 
  calculateBoundingBox, 
  perpendicularDistance, 
  createPolygon as createPolygonShared,
  slicePolygonObject,
  executePolygonWorkerOperation as executeWorkerOp 
} from '@spheroseg/shared/utils/polygonUtils.unified';
const calculatePolygonAreaAsync = async (points: Point[]): Promise<number> => {
  // Use shared polygon area calculation
  return calculatePolygonArea(points);
};

const calculatePolygonPerimeterAsync = async (
  points: Point[],
): Promise<number> => {
  // Use shared polygon perimeter calculation
  return calculatePolygonPerimeter(points);
};

const calculateBoundingBoxAsync = async (
  points: Point[],
): Promise<{ x: number; y: number; width: number; height: number }> => {
  // Use shared bounding box calculation
  const bbox = calculateBoundingBox(points);
  return { 
    x: bbox.minX, 
    y: bbox.minY, 
    width: bbox.maxX - bbox.minX, 
    height: bbox.maxY - bbox.minY 
  };
};

const executePolygonWorkerOperation = async (
  points: Point[],
  polygonWorker: any,
  operation: (pts: Point[]) => any,
  operationName: string,
  fallback: any,
): Promise<any> => {
  // Use shared polygon worker operation
  return executeWorkerOp(points, polygonWorker, operation, operationName, fallback);
};

const slicePolygonShared = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): Polygon[] => {
  // Use shared polygon slicing
  const result = slicePolygonObject(polygon, sliceStart, sliceEnd);
  return result.success ? result.polygons : [polygon];
};

const distanceToLineSegment = (p: Point, v: Point, w: Point): number => {
  // Use shared perpendicular distance calculation
  return perpendicularDistance(p, v, w);
};

const createPolygon = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  // Use shared polygon creation
  return createPolygonShared(points, type);
};


/**
 * Check if a point is inside a polygon using WebWorker
 */
export const isPointInPolygonAsync = async (
  x: number,
  y: number,
  points: Point[],
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<boolean> => {
  try {
    if (!polygonWorker.isReady) {
      logger.warn(
        "Polygon worker not ready, falling back to synchronous implementation",
      );
      return isPointInPolygonSync(x, y, points);
    }

    return await polygonWorker.isPointInPolygon({ x, y }, points);
  } catch (_error) {
    logger.error("Error in isPointInPolygonAsync:", _error);
    return isPointInPolygonSync(x, y, points);
  }
};

/**
 * Synchronous fallback for point in polygon check
 */
export const isPointInPolygonSync = (
  x: number,
  y: number,
  points: Point[],
): boolean => {
  let inside = false;
  for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
    const xi = points[i].x;
    const yi = points[i].y;
    const xj = points[j].x;
    const yj = points[j].y;

    const intersect =
      yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi;
    if (intersect) inside = !inside;
  }
  return inside;
};

/**
 * Calculate distance from a point to a line segment
 */
export const distanceToSegment = (p: Point, v: Point, w: Point): number => {
  return distanceToLineSegment(p, v, w);
};

/**
 * Slice a polygon with a line using WebWorker
 */
export const slicePolygonAsync = async (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<{ success: boolean; polygons: Polygon[] }> => {
  try {
    if (!polygonWorker.isReady) {
      logger.warn(
        "Polygon worker not ready, falling back to synchronous implementation",
      );
      return slicePolygonSync(polygon, sliceStart, sliceEnd);
    }

    const result = await polygonWorker.slicePolygon(
      polygon.points,
      sliceStart,
      sliceEnd,
    );

    if (!result || result.length < 2) {
      return { success: false, polygons: [] };
    }

    // Create new polygon objects from the result
    const newPolygons = result.map((points) => ({
      id: uuidv4(),
      points,
      type: polygon.type || "external",
    }));

    return { success: true, polygons: newPolygons };
  } catch (_error) {
    logger.error("Error in slicePolygonAsync:", _error);
    return slicePolygonSync(polygon, sliceStart, sliceEnd);
  }
};

/**
 * Synchronous fallback for slicing a polygon
 */
export const slicePolygonSync = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): { success: boolean; polygons: Polygon[] } => {
  const result = slicePolygonShared(polygon, sliceStart, sliceEnd);

  if (!result) {
    return { success: false, polygons: [] };
  }

  return { success: true, polygons: result };
};

/**
 * Create a new polygon
 */
export const createPolygonFn = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  return createPolygon(points, type);
};

/**
 * Update segmentation data with a new set of polygons
 */
export const updateSegmentationWithPolygons = (
  segmentationData: SegmentationData,
  polygons: Polygon[],
): SegmentationData => {
  return {
    ...segmentationData,
    polygons,
  };
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm with WebWorker
 */
export const simplifyPolygonAsync = async (
  points: Point[],
  epsilon: number,
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<Point[]> => {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) => polygonWorker.simplifyPolygon(pts, epsilon),
    "simplifyPolygonAsync",
    points,
  );
};

// Export shared utility functions
export {
  calculatePolygonAreaAsync,
  calculatePolygonPerimeterAsync,
  calculateBoundingBoxAsync,
};
