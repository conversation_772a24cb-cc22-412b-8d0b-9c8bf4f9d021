import { useEffect, useRef, useCallback, useState } from 'react';
import { toast } from 'sonner';
import { createNamespacedLogger } from '@/utils/logging/unifiedLogger';
import { SegmentationData } from './types';

const logger = createNamespacedLogger('useAutoSave');

// Auto-save status types
export enum AutoSaveStatus {
  Idle = 'idle',
  Saving = 'saving',
  Saved = 'saved',
  Failed = 'failed',
  Offline = 'offline'
}

export interface AutoSaveState {
  status: AutoSaveStatus;
  lastSavedAt: Date | null;
  pendingChanges: boolean;
  retryCount: number;
  errorMessage?: string;
}

interface UseAutoSaveOptions {
  debounceMs?: number;
  maxRetries?: number;
  retryDelayMs?: number;
  enabledStorageKey?: string;
  showToasts?: boolean; // Option to show toast notifications
}

interface UseAutoSaveProps {
  segmentationData: SegmentationData | null;
  isDirty: boolean;
  saveFunction: () => Promise<void>;
  isManualSaving: boolean;
  options?: UseAutoSaveOptions;
}

const DEFAULT_OPTIONS: Required<UseAutoSaveOptions> = {
  debounceMs: 3000,
  maxRetries: 3,
  retryDelayMs: 5000,
  enabledStorageKey: 'spheroseg-autosave-enabled',
  showToasts: false // Default to not showing toasts to avoid spam
};

/**
 * Auto-save hook with debouncing, retry logic, and offline queue support
 */
export const useAutoSave = ({
  segmentationData,
  isDirty,
  saveFunction,
  isManualSaving,
  options = {}
}: UseAutoSaveProps) => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Auto-save state
  const [autoSaveState, setAutoSaveState] = useState<AutoSaveState>({
    status: AutoSaveStatus.Idle,
    lastSavedAt: null,
    pendingChanges: false,
    retryCount: 0
  });

  // Auto-save enabled state (stored in localStorage)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState<boolean>(() => {
    try {
      const stored = localStorage.getItem(opts.enabledStorageKey);
      return stored !== null ? JSON.parse(stored) : true; // Default to enabled
    } catch {
      return true;
    }
  });

  // Refs for managing debounce and retries
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedDataRef = useRef<SegmentationData | null>(null);
  const isOnlineRef = useRef<boolean>(true);
  const pendingQueueRef = useRef<Array<{ data: SegmentationData; timestamp: number }>>([]);

  // Update localStorage when autoSaveEnabled changes
  useEffect(() => {
    try {
      localStorage.setItem(opts.enabledStorageKey, JSON.stringify(autoSaveEnabled));
    } catch (error) {
      logger.warn('Failed to save auto-save preference to localStorage:', error);
    }
  }, [autoSaveEnabled, opts.enabledStorageKey]);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => {
      logger.info('Connection restored, processing queued changes');
      isOnlineRef.current = true;
      setAutoSaveState(prev => ({ ...prev, status: AutoSaveStatus.Idle }));
      processOfflineQueue();
    };

    const handleOffline = () => {
      logger.info('Connection lost, queuing changes for later');
      isOnlineRef.current = false;
      setAutoSaveState(prev => ({ ...prev, status: AutoSaveStatus.Offline }));
      
      // Clear any pending save attempts
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial online state
    isOnlineRef.current = navigator.onLine;
    if (!navigator.onLine) {
      setAutoSaveState(prev => ({ ...prev, status: AutoSaveStatus.Offline }));
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Process offline queue when connection is restored
  const processOfflineQueue = useCallback(async () => {
    if (pendingQueueRef.current.length === 0 || !isOnlineRef.current) {
      return;
    }

    // Get the most recent queued change
    const latestChange = pendingQueueRef.current[pendingQueueRef.current.length - 1];
    pendingQueueRef.current = [];

    if (latestChange && hasDataChanged(latestChange.data, lastSavedDataRef.current)) {
      logger.info('Processing queued changes from offline period');
      await performSave();
    }
  }, []);

  // Check if segmentation data has actually changed
  const hasDataChanged = useCallback((newData: SegmentationData | null, oldData: SegmentationData | null): boolean => {
    if (!newData && !oldData) return false;
    if (!newData || !oldData) return true;

    // Compare polygon arrays
    const newPolygons = newData.polygons || [];
    const oldPolygons = oldData.polygons || [];

    if (newPolygons.length !== oldPolygons.length) return true;

    // Deep comparison of polygons
    return JSON.stringify(newPolygons.sort((a, b) => (a.id || '').localeCompare(b.id || ''))) !==
           JSON.stringify(oldPolygons.sort((a, b) => (a.id || '').localeCompare(b.id || '')));
  }, []);

  // Perform the actual save operation
  const performSave = useCallback(async (): Promise<void> => {
    if (!segmentationData || isManualSaving) {
      return;
    }

    try {
      setAutoSaveState(prev => ({ ...prev, status: AutoSaveStatus.Saving }));
      logger.info('Auto-saving segmentation data');

      await saveFunction();

      // Update state on successful save
      setAutoSaveState({
        status: AutoSaveStatus.Saved,
        lastSavedAt: new Date(),
        pendingChanges: false,
        retryCount: 0
      });

      // Store the saved data reference
      lastSavedDataRef.current = { ...segmentationData };
      
      logger.info('Auto-save completed successfully');

      // Show success toast if enabled
      if (opts.showToasts) {
        toast.success('Changes auto-saved', { 
          duration: 2000,
          position: 'bottom-right'
        });
      }

      // Clear saved status after a delay
      setTimeout(() => {
        setAutoSaveState(prev => 
          prev.status === AutoSaveStatus.Saved 
            ? { ...prev, status: AutoSaveStatus.Idle }
            : prev
        );
      }, 3000);

    } catch (error) {
      logger.error('Auto-save failed:', error);

      const newRetryCount = autoSaveState.retryCount + 1;
      const shouldRetry = newRetryCount < opts.maxRetries;

      setAutoSaveState(prev => ({
        ...prev,
        status: AutoSaveStatus.Failed,
        retryCount: newRetryCount,
        errorMessage: error instanceof Error ? error.message : 'Save failed'
      }));

      if (shouldRetry && isOnlineRef.current) {
        logger.info(`Auto-save failed, retrying in ${opts.retryDelayMs}ms (attempt ${newRetryCount}/${opts.maxRetries})`);
        
        retryTimeoutRef.current = setTimeout(() => {
          performSave();
        }, opts.retryDelayMs);
      } else {
        logger.error(`Auto-save failed after ${opts.maxRetries} attempts`);
        
        // Show error toast for final failure if enabled
        if (opts.showToasts) {
          toast.error(`Auto-save failed after ${opts.maxRetries} attempts`, {
            duration: 5000,
            position: 'bottom-right'
          });
        }
      }
    }
  }, [segmentationData, isManualSaving, saveFunction, autoSaveState.retryCount, opts.maxRetries, opts.retryDelayMs]);

  // Debounced save trigger
  const triggerAutoSave = useCallback(() => {
    if (!autoSaveEnabled || !segmentationData || isManualSaving) {
      return;
    }

    // Clear existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // If offline, queue the change
    if (!isOnlineRef.current) {
      pendingQueueRef.current.push({
        data: { ...segmentationData },
        timestamp: Date.now()
      });
      
      setAutoSaveState(prev => ({
        ...prev,
        pendingChanges: true,
        status: AutoSaveStatus.Offline
      }));
      return;
    }

    // Set pending changes state
    setAutoSaveState(prev => ({ ...prev, pendingChanges: true }));

    // Start debounced save
    debounceTimeoutRef.current = setTimeout(() => {
      performSave();
    }, opts.debounceMs);

    logger.debug(`Auto-save scheduled in ${opts.debounceMs}ms`);
  }, [autoSaveEnabled, segmentationData, isManualSaving, performSave, opts.debounceMs]);

  // Trigger auto-save when data changes and is dirty
  useEffect(() => {
    if (isDirty && hasDataChanged(segmentationData, lastSavedDataRef.current)) {
      triggerAutoSave();
    }
  }, [isDirty, segmentationData, triggerAutoSave, hasDataChanged]);

  // Reset retry count when manual save succeeds
  useEffect(() => {
    if (!isManualSaving && autoSaveState.retryCount > 0) {
      setAutoSaveState(prev => ({ ...prev, retryCount: 0 }));
    }
  }, [isManualSaving, autoSaveState.retryCount]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Toggle auto-save enabled state
  const toggleAutoSave = useCallback(() => {
    setAutoSaveEnabled(prev => !prev);
    
    if (!autoSaveEnabled) {
      // Cancel any pending saves when disabling
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
      
      setAutoSaveState(prev => ({ ...prev, status: AutoSaveStatus.Idle, pendingChanges: false }));
    }
  }, [autoSaveEnabled]);

  // Manual save trigger (for when user explicitly saves)
  const triggerManualSave = useCallback(() => {
    // Cancel auto-save if pending
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
    
    setAutoSaveState(prev => ({ ...prev, pendingChanges: false }));
  }, []);

  return {
    autoSaveState,
    autoSaveEnabled,
    toggleAutoSave,
    triggerManualSave,
    hasUnsavedChanges: autoSaveState.pendingChanges || (isDirty && hasDataChanged(segmentationData, lastSavedDataRef.current))
  };
};