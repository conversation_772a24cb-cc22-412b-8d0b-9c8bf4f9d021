import { useState, useEffect } from 'react';
import { SegmentationData } from './types';

// Type for the cache
interface SegmentationCache {
  [key: string]: {
    data: SegmentationData;
    timestamp: number;
  };
}

/**
 * Hook for caching segmentation data
 * This provides a simple in-memory cache for segmentation data
 * to avoid unnecessary API calls when switching between images
 */
export const useSegmentationCache = () => {
  // Initialize cache
  const [cache, setCache] = useState<SegmentationCache>({});

  // Function to get segmentation data from cache
  const getFromCache = (imageId: string): SegmentationData | null => {
    const cachedItem = cache[imageId];

    if (!cachedItem) {
      return null;
    }

    // Check if cache is still valid (10 minutes)
    const now = Date.now();
    const cacheAge = now - cachedItem.timestamp;
    const maxAge = 10 * 60 * 1000; // 10 minutes in milliseconds

    if (cacheAge > maxAge) {
      return null;
    }

    return cachedItem.data;
  };

  // Function to add segmentation data to cache
  const addToCache = (imageId: string, data: SegmentationData): void => {
    setCache((prevCache) => ({
      ...prevCache,
      [imageId]: {
        data,
        timestamp: Date.now(),
      },
    }));
  };

  // Function to clear cache
  const clearCache = (): void => {
    setCache({});
  };

  // Function to remove item from cache
  const removeFromCache = (imageId: string): void => {
    setCache((prevCache) => {
      const newCache = { ...prevCache };
      delete newCache[imageId];
      return newCache;
    });
  };

  // Function to get cache stats
  const getCacheStats = (): { size: number; keys: string[] } => {
    const keys = Object.keys(cache);
    return {
      size: keys.length,
      keys,
    };
  };

  // Clean up expired cache items every 5 minutes
  useEffect(() => {
    const cleanupInterval = setInterval(
      () => {
        const now = Date.now();
        const maxAge = 10 * 60 * 1000; // 10 minutes

        setCache((prevCache) => {
          const newCache = { ...prevCache };
          let removedCount = 0;

          Object.entries(newCache).forEach(([key, value]) => {
            if (now - value.timestamp > maxAge) {
              delete newCache[key];
              removedCount++;
            }
          });

          if (removedCount > 0) {
          }

          return newCache;
        });
      },
      5 * 60 * 1000,
    ); // Run every 5 minutes

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  return {
    getFromCache,
    addToCache,
    clearCache,
    removeFromCache,
    getCacheStats,
  };
};
