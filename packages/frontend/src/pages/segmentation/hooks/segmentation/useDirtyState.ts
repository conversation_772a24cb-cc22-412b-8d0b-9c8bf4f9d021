import { useEffect, useRef, useState } from 'react';
import { SegmentationData } from './types';
import { createNamespacedLogger } from '@/utils/logging/unifiedLogger';

const logger = createNamespacedLogger('useDirtyState');

/**
 * Hook to track if segmentation data has unsaved changes
 */
export const useDirtyState = (segmentationData: SegmentationData | null, isSaving: boolean) => {
  const [isDirty, setIsDirty] = useState(false);
  const lastSavedDataRef = useRef<SegmentationData | null>(null);
  const initialDataSetRef = useRef(false);

  // Create a stable serialization of polygon data for comparison
  const serializePolygonData = (data: SegmentationData | null): string => {
    if (!data) return '';
    
    const polygons = data.polygons || [];
    
    // Create a normalized version of polygons for comparison
    const normalized = polygons
      .map(polygon => ({
        id: polygon.id,
        type: polygon.type,
        points: polygon.points?.map(p => ({ x: p.x, y: p.y })) || []
      }))
      .sort((a, b) => (a.id || '').localeCompare(b.id || ''));
    
    return JSON.stringify(normalized);
  };

  // Reset dirty state when saving completes
  useEffect(() => {
    if (!isSaving && isDirty && segmentationData) {
      // Save completed, update our reference
      lastSavedDataRef.current = segmentationData;
      setIsDirty(false);
      logger.debug('Save completed, resetting dirty state');
    }
  }, [isSaving, isDirty, segmentationData]);

  // Track changes to segmentation data
  useEffect(() => {
    if (!segmentationData) {
      setIsDirty(false);
      return;
    }

    // Initialize the reference if this is the first time we have data
    if (!initialDataSetRef.current) {
      lastSavedDataRef.current = segmentationData;
      initialDataSetRef.current = true;
      setIsDirty(false);
      logger.debug('Initial segmentation data set, marking as clean');
      return;
    }

    // Compare current data with last saved data
    const currentSerialized = serializePolygonData(segmentationData);
    const lastSavedSerialized = serializePolygonData(lastSavedDataRef.current);

    const hasChanges = currentSerialized !== lastSavedSerialized;
    
    if (hasChanges !== isDirty) {
      setIsDirty(hasChanges);
      logger.debug(`Dirty state changed: ${hasChanges ? 'dirty' : 'clean'}`);
    }
  }, [segmentationData, isDirty]);

  // Function to manually mark as clean (e.g., after manual save)
  const markAsClean = () => {
    if (segmentationData) {
      lastSavedDataRef.current = segmentationData;
      setIsDirty(false);
      logger.debug('Manually marked as clean');
    }
  };

  // Function to manually mark as dirty (e.g., after programmatic change)
  const markAsDirty = () => {
    setIsDirty(true);
    logger.debug('Manually marked as dirty');
  };

  // Reset everything when segmentation data is null
  useEffect(() => {
    if (!segmentationData) {
      lastSavedDataRef.current = null;
      initialDataSetRef.current = false;
      setIsDirty(false);
    }
  }, [segmentationData]);

  return {
    isDirty,
    markAsClean,
    markAsDirty
  };
};