import { Point, Polygon, SegmentationData } from "./types";
import { 
  isPointInPolygon as isPointInPolygonShared, 
  perpendicularDistance,
  slicePolygonObject,
  createPolygon as createPolygonShared
} from '@spheroseg/shared/utils/polygonUtils.unified';

/**
 * Check if a point is inside a polygon
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean => {
  return isPointInPolygonShared(point, polygon);
};

/**
 * Calculate distance from a point to a line segment
 */
export const distanceToSegment = (p: Point, v: Point, w: Point): number => {
  return perpendicularDistance(p, v, w);
};


/**
 * Slice a polygon with a line
 */
export const slicePolygon = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): { success: boolean; polygons: Polygon[] } => {
  return slicePolygonObject(polygon, sliceStart, sliceEnd);
};

/**
 * Create a new polygon
 */
export const createPolygon = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  return createPolygonShared(points, type);
};

/**
 * Update segmentation data with a new set of polygons
 */
export const updateSegmentationWithPolygons = (
  segmentationData: SegmentationData,
  polygons: Polygon[],
): SegmentationData => {
  return {
    ...segmentationData,
    polygons,
  };
};
