import { useCallback } from "react";
import { SegmentationResult, Point } from "@/lib/segmentation";

/**
 * Hook pro základní operace s body polygonu (přidání, odebrání, duplikace)
 * s validací integrity (např. self-intersection).
 */
export const usePointOperations = (
  segmentation: SegmentationResult | null,
  setSegmentation: (seg: SegmentationResult | null) => void,
) => {
  // --- Helper Function for State Update ---
  const updatePolygonPoints = useCallback(
    (polygonId: string, newPoints: Point[]): boolean => {
      if (!segmentation) return false;

      // Validace integrity: kontrola self-intersection
      // Tato kontrola se provádí před voláním této funkce v jednotlivých operacích

      const updatedPolygons = segmentation.polygons.map((p) => {
        if (p.id === polygonId) {
          return {
            ...p,
            points: newPoints,
          };
        }
        return p;
      });

      setSegmentation({
        ...segmentation,
        polygons: updatedPolygons,
      });
      return true;
    },
    [segmentation, setSegmentation],
  );

  /**
   * Přidání bodu na nejbližší segment polygonu s validací integrity
   */
  const addPoint = useCallback(
    (polygonId: string, point: Point): boolean => {
      if (!segmentation) return false;

      // Manual point addition since addPointToPolygon method doesn't exist
      const polygon = segmentation.polygons.find((p) => p.id === polygonId);
      if (!polygon) return false;

      const newPoints = [...polygon.points, point];
      const result = {
        ...segmentation,
        polygons: segmentation.polygons.map((p) =>
          p.id === polygonId ? { ...p, points: newPoints } : p,
        ),
      };

      if (!result) {
        return false;
      }

      setSegmentation(result);
      return true;
    },
    [segmentation, setSegmentation],
  );

  /**
   * Odebrání bodu z polygonu s validací integrity
   */
  const removePoint = useCallback(
    (polygonId: string, pointIndex: number): boolean => {
      if (!segmentation) return false;

      // Use the consolidated polygonOperations utility
      // Manual point removal since removePointFromPolygon method doesn't exist
      const polygon = segmentation.polygons.find((p) => p.id === polygonId);
      if (!polygon || pointIndex < 0 || pointIndex >= polygon.points.length)
        return false;

      const newPoints = polygon.points.filter(
        (_, index) => index !== pointIndex,
      );
      const result = {
        ...segmentation,
        polygons: segmentation.polygons.map((p) =>
          p.id === polygonId ? { ...p, points: newPoints } : p,
        ),
      };

      if (!result) {
        return false;
      }

      setSegmentation(result);
      return true;
    },
    [segmentation, setSegmentation],
  );

  /**
   * Duplikace bodu polygonu s validací integrity
   */
  const duplicatePoint = useCallback(
    (polygonId: string, pointIndex: number): boolean => {
      if (!segmentation) return false;

      // Use the consolidated polygonOperations utility
      // Manual point duplication since duplicatePointInPolygon method doesn't exist
      const polygon = segmentation.polygons.find((p) => p.id === polygonId);
      if (!polygon || pointIndex < 0 || pointIndex >= polygon.points.length)
        return false;

      const pointToDuplicate = polygon.points[pointIndex];
      const newPoints = [
        ...polygon.points.slice(0, pointIndex + 1),
        { ...pointToDuplicate },
        ...polygon.points.slice(pointIndex + 1),
      ];
      const result = {
        ...segmentation,
        polygons: segmentation.polygons.map((p) =>
          p.id === polygonId ? { ...p, points: newPoints } : p,
        ),
      };

      if (!result) {
        return false;
      }

      setSegmentation(result);
      return true;
    },
    [segmentation, setSegmentation],
  );

  return {
    addPoint,
    removePoint,
    duplicatePoint,
  };
};
