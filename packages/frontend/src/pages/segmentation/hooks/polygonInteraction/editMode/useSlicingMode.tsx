import { useState, useCallback } from "react";
import { SegmentationResult, Point } from "@/lib/segmentation";
import { toast } from "sonner";
import { usePolygonSplitter } from "../geometry/usePolygonSplitter";
import { SliceOperation } from "../geometry/utils/polygonSplitter";

/**
 * Hook for polygon slicing mode
 */
export const useSlicingMode = (
  segmentation: SegmentationResult | null,
  setSegmentation: (seg: SegmentationResult | null) => void,
  selectedPolygonId: string | null,
) => {
  const [slicingMode, setSlicingMode] = useState(false);
  const [sliceStartPoint, setSliceStartPoint] = useState<Point | null>(null);
  const [cursorPosition, setCursorPosition] = useState<Point | null>(null);

  const { splitIntoTwoPolygons } = usePolygonSplitter(
    segmentation,
    setSegmentation,
  );

  /**
   * Toggle slicing mode on/off
   */
  const toggleSlicingMode = useCallback(() => {
    setSlicingMode((prev) => !prev);
    setSliceStartPoint(null);
    setCursorPosition(null);
  }, []);

  /**
   * Update cursor position for slicing mode
   */
  const updateCursorPosition = useCallback(
    (x: number, y: number) => {
      if (!slicingMode) return;
      // Debug log to help diagnose coordinate transformation issues
      console.log(`Cursor position: (${x.toFixed(2)}, ${y.toFixed(2)})`);
      setCursorPosition({ x, y });
    },
    [slicingMode],
  );

  /**
   * Handle clicks in slicing mode
   */
  const handleSlicingClick = useCallback(
    (x: number, y: number): boolean => {
      if (!slicingMode || !selectedPolygonId) {
        return false;
      }

      const clickPoint = { x, y };
      console.log(`Click at: (${x.toFixed(2)}, ${y.toFixed(2)})`);

      // Log the selected polygon for debugging
      if (segmentation) {
        const polygon = segmentation.polygons.find(
          (p) => p.id === selectedPolygonId,
        );
        if (polygon) {
          console.log(
            `Found polygon ${selectedPolygonId} with ${polygon.points.length} points`,
          );
        } else {
          console.log(`No polygon found with ID ${selectedPolygonId}`);
        }
      }

      // First click - set start point
      if (!sliceStartPoint) {
        setSliceStartPoint(clickPoint);
        console.log(
          `Slice start point set at: (${x.toFixed(2)}, ${y.toFixed(2)})`,
        );

        return true;
      }

      // Second click - complete slice and split into two polygons
      console.log(`Completing slice at: (${x.toFixed(2)}, ${y.toFixed(2)})`);
      console.log(
        `Slicing from (${sliceStartPoint.x.toFixed(2)}, ${sliceStartPoint.y.toFixed(2)}) to (${x.toFixed(2)}, ${y.toFixed(2)})`,
      );

      // Calculate line length for debugging
      const dx = x - sliceStartPoint.x;
      const dy = y - sliceStartPoint.y;
      const lineLength = Math.sqrt(dx * dx + dy * dy);
      console.log(`Slice line length: ${lineLength.toFixed(2)} pixels`);

      const operation: SliceOperation = {
        polygonId: selectedPolygonId,
        startPoint: sliceStartPoint,
        endPoint: clickPoint,
      };

      const success = splitIntoTwoPolygons(operation);

      if (success) {
        toast.success("Polygon successfully split into two");
        // Reset state and exit slicing mode automatically
        setSliceStartPoint(null);
        setSlicingMode(false);
      } else {
        console.log("Failed to split polygon");
        toast.error("Failed to split polygon");
        setSliceStartPoint(null);
      }

      return true;
    },
    [
      slicingMode,
      selectedPolygonId,
      sliceStartPoint,
      splitIntoTwoPolygons,
      segmentation,
    ],
  );

  return {
    slicingMode,
    sliceStartPoint,
    cursorPosition,
    toggleSlicingMode,
    handleSlicingClick,
    updateCursorPosition,
    setSlicingMode, // Export this to allow other components to directly change slicing mode
  };
};
