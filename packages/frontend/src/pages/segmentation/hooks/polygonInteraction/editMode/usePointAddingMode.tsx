import { useState } from "react";
import { SegmentationResult } from "@/lib/segmentation";

/**
 * Hook for managing point adding mode in polygon editing
 */
export const usePointAddingMode = (
  segmentation: SegmentationResult | null,
  setSegmentation: (seg: SegmentationResult | null) => void,
  selectedPolygonId: string | null,
) => {
  const [pointAddingMode, setPointAddingMode] = useState(false);
  const [hoveredSegment, setHoveredSegment] = useState<number | null>(null);
  const [tempPoints, setTempPoints] = useState<{ x: number; y: number }[]>([]);
  const [selectedVertexIndex, setSelectedVertexIndex] = useState<number | null>(
    null,
  );
  const [sourcePolygonId, setSourcePolygonId] = useState<string | null>(null);
  const [selectedPolygonPoints, setSelectedPolygonPoints] = useState<
    { x: number; y: number }[]
  >([]);

  const startPointAddingMode = () => {
    setPointAddingMode(true);
    setSourcePolygonId(selectedPolygonId);
  };

  const exitPointAddingMode = () => {
    setPointAddingMode(false);
    setHoveredSegment(null);
    setTempPoints([]);
    setSelectedVertexIndex(null);
    setSourcePolygonId(null);
    setSelectedPolygonPoints([]);
  };

  return {
    pointAddingMode,
    setPointAddingMode,
    hoveredSegment,
    setHoveredSegment,
    tempPoints,
    setTempPoints,
    selectedVertexIndex,
    setSelectedVertexIndex,
    sourcePolygonId,
    setSourcePolygonId,
    selectedPolygonPoints,
    setSelectedPolygonPoints,
    startPointAddingMode,
    exitPointAddingMode,
  };
};
