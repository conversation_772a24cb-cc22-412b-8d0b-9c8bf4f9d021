import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useDashboardProjects } from "@/hooks/useDashboardProjects";

const DebugDashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const { t } = useLanguage();
  const {
    projects,
    loading: projectsLoading,
    error,
    fetchProjects,
  } = useDashboardProjects();

  useEffect(() => {
    console.log("[DebugDashboard] Component mounted");
    console.log("[DebugDashboard] Auth state:", { user, authLoading });
    console.log("[DebugDashboard] Projects state:", {
      projects,
      projectsLoading,
      error,
    });
  }, [user, authLoading, projects, projectsLoading, error]);

  return (
    <div className="p-8 bg-white dark:bg-gray-900 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Debug Dashboard</h1>

      <div className="space-y-4">
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <h2 className="font-semibold mb-2">Auth State:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({ user, authLoading }, null, 2)}
          </pre>
        </div>

        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <h2 className="font-semibold mb-2">Projects State:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(
              {
                projectsCount: projects?.length || 0,
                projectsLoading,
                error,
              },
              null,
              2,
            )}
          </pre>
        </div>

        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <h2 className="font-semibold mb-2">Language Context:</h2>
          <p>
            Translation function available:{" "}
            {typeof t === "function" ? "Yes" : "No"}
          </p>
          <p>Sample translation: {t("common.dashboard")}</p>
        </div>

        <div className="p-4 bg-blue-100 dark:bg-blue-900 rounded">
          <h2 className="font-semibold mb-2">Instructions:</h2>
          <p>
            This debug page shows the current state without complex components.
          </p>
          <p>Check the browser console for additional logs.</p>
        </div>

        <button
          onClick={() => fetchProjects(10, 0)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Manually Fetch Projects
        </button>
      </div>
    </div>
  );
};

export default DebugDashboard;
