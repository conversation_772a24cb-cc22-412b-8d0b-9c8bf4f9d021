import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ProjectDetail from '../../pages/ProjectDetail';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock useParams
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: 'project-1' })
  };
});

// Mock API calls
vi.mock('../../lib/api', () => ({
  api: {
    get: vi.fn().mockImplementation((url) => {
      if (url === '/api/projects/project-1') {
        return Promise.resolve({
          id: 'project-1',
          title: 'Test Project',
          description: 'Test project description',
          tags: ['test', 'sample'],
          public: false,
          userId: 'user-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          imageCount: 3,
          segmentationCount: 2
        });
      }
      if (url === '/api/projects/project-1/images') {
        return Promise.resolve({
          images: [
            {
              id: 'image-1',
              name: 'cell1.jpg',
              thumbnailPath: '/thumbnails/cell1.jpg',
              segmentationStatus: 'completed',
              width: 512,
              height: 512,
              fileSize: 102400,
              createdAt: new Date().toISOString()
            },
            {
              id: 'image-2',
              name: 'cell2.jpg',
              thumbnailPath: '/thumbnails/cell2.jpg',
              segmentationStatus: 'processing',
              width: 512,
              height: 512,
              fileSize: 102400,
              createdAt: new Date().toISOString()
            },
            {
              id: 'image-3',
              name: 'cell3.jpg',
              thumbnailPath: '/thumbnails/cell3.jpg',
              segmentationStatus: 'pending',
              width: 512,
              height: 512,
              fileSize: 102400,
              createdAt: new Date().toISOString()
            }
          ],
          totalCount: 3,
          totalPages: 1
        });
      }
      if (url === '/api/projects/project-1/stats') {
        return Promise.resolve({
          totalImages: 3,
          completedSegmentations: 1,
          processingSegmentations: 1,
          pendingSegmentations: 1,
          averageProcessingTime: 2.5
        });
      }
      return Promise.resolve({});
    }),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock auth context
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: 'user-1', email: '<EMAIL>', name: 'Test User' },
    logout: vi.fn()
  })
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            <Route path="*" element={children} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('ProjectDetail Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render project details', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      expect(screen.getByText('Test project description')).toBeInTheDocument();
    });
  });

  it('should display project statistics', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument(); // Total images
      expect(screen.getByText('1')).toBeInTheDocument(); // Completed segmentations
      expect(screen.getByText('2.5s')).toBeInTheDocument(); // Avg processing time
    });
  });

  it('should render image grid', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('cell1.jpg')).toBeInTheDocument();
      expect(screen.getByText('cell2.jpg')).toBeInTheDocument();
      expect(screen.getByText('cell3.jpg')).toBeInTheDocument();
    });
  });

  it('should show segmentation status badges', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Completed')).toBeInTheDocument();
      expect(screen.getByText('Processing')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
    });
  });

  it('should handle image upload', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.post).mockResolvedValue({ success: true });
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByLabelText(/Upload Images/i);
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false
    });
    
    fireEvent.change(input);
    
    await waitFor(() => {
      expect(api.api.post).toHaveBeenCalledWith(
        '/api/projects/project-1/images/upload',
        expect.any(FormData)
      );
    });
  });

  it('should handle project deletion', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.delete).mockResolvedValue({});
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    const deleteButton = screen.getByRole('button', { name: /Delete Project/i });
    fireEvent.click(deleteButton);
    
    // Confirm deletion
    const confirmButton = await screen.findByText(/Confirm Delete/i);
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(api.api.delete).toHaveBeenCalledWith('/api/projects/project-1');
    });
  });

  it('should handle project update', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.put).mockResolvedValue({ success: true });
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    // Click edit button
    const editButton = screen.getByRole('button', { name: /Edit/i });
    fireEvent.click(editButton);
    
    // Update title
    const titleInput = screen.getByDisplayValue('Test Project');
    fireEvent.change(titleInput, { target: { value: 'Updated Project' } });
    
    // Save changes
    const saveButton = screen.getByRole('button', { name: /Save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(api.api.put).toHaveBeenCalledWith('/api/projects/project-1', {
        title: 'Updated Project',
        description: 'Test project description',
        tags: ['test', 'sample'],
        public: false
      });
    });
  });

  it('should handle image deletion', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.delete).mockResolvedValue({});
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('cell1.jpg')).toBeInTheDocument();
    });
    
    const deleteButtons = screen.getAllByRole('button', { name: /Delete Image/i });
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      expect(api.api.delete).toHaveBeenCalledWith('/api/images/image-1');
    });
  });

  it('should start segmentation for pending images', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.post).mockResolvedValue({ jobId: 'job-1' });
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('cell3.jpg')).toBeInTheDocument();
    });
    
    const segmentButtons = screen.getAllByRole('button', { name: /Start Segmentation/i });
    fireEvent.click(segmentButtons[0]);
    
    await waitFor(() => {
      expect(api.api.post).toHaveBeenCalledWith('/api/images/image-3/segment');
    });
  });

  it('should handle batch operations', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('cell1.jpg')).toBeInTheDocument();
    });
    
    // Select multiple images
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);
    fireEvent.click(checkboxes[1]);
    
    // Batch action button should appear
    expect(screen.getByText(/2 selected/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Batch Delete/i })).toBeInTheDocument();
  });

  it('should filter images by status', async () => {
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('cell1.jpg')).toBeInTheDocument();
      expect(screen.getByText('cell2.jpg')).toBeInTheDocument();
      expect(screen.getByText('cell3.jpg')).toBeInTheDocument();
    });
    
    // Filter by completed status
    const filterSelect = screen.getByRole('combobox', { name: /Filter by status/i });
    fireEvent.change(filterSelect, { target: { value: 'completed' } });
    
    await waitFor(() => {
      expect(screen.getByText('cell1.jpg')).toBeInTheDocument();
      expect(screen.queryByText('cell2.jpg')).not.toBeInTheDocument();
      expect(screen.queryByText('cell3.jpg')).not.toBeInTheDocument();
    });
  });

  it('should handle error loading project', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.get).mockRejectedValueOnce(new Error('Not found'));
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText(/Project not found/i)).toBeInTheDocument();
    });
  });

  it('should export project data', async () => {
    const api = await import('../../lib/api');
    const mockBlob = new Blob(['export data'], { type: 'application/json' });
    vi.mocked(api.api.get).mockImplementation((url) => {
      if (url === '/api/projects/project-1/export') {
        return Promise.resolve(mockBlob);
      }
      return Promise.resolve({});
    });
    
    const Wrapper = createWrapper();
    render(<ProjectDetail />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    const exportButton = screen.getByRole('button', { name: /Export/i });
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(api.api.get).toHaveBeenCalledWith('/api/projects/project-1/export');
    });
  });
});