import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import Navbar from '../../components/Navbar';
import { AuthProvider } from '../../contexts/AuthContext';
import { LanguageProvider } from '../../contexts/LanguageContext';

// Mock the auth context
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    isAuthenticated: true,
    user: { email: '<EMAIL>', name: 'Test User' },
    logout: vi.fn()
  })
}));

// Mock i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
      language: 'en'
    }
  })
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <LanguageProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </LanguageProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Navbar Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render navbar with logo', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    expect(screen.getByText('SpheroSeg')).toBeInTheDocument();
  });

  it('should show user menu when authenticated', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    expect(screen.getByText('Test User')).toBeInTheDocument();
  });

  it('should show login button when not authenticated', () => {
    vi.mock('../../contexts/AuthContext', () => ({
      useAuth: () => ({
        isAuthenticated: false,
        user: null,
        logout: vi.fn()
      })
    }));

    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    expect(screen.getByText('nav.signIn')).toBeInTheDocument();
  });

  it('should toggle mobile menu on hamburger click', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    const hamburger = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(hamburger);
    
    expect(screen.getByRole('navigation')).toHaveClass('open');
  });

  it('should navigate to dashboard on logo click', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    const logo = screen.getByText('SpheroSeg');
    fireEvent.click(logo);
    
    expect(window.location.pathname).toBe('/dashboard');
  });

  it('should call logout when logout button is clicked', async () => {
    const mockLogout = vi.fn();
    vi.mock('../../contexts/AuthContext', () => ({
      useAuth: () => ({
        isAuthenticated: true,
        user: { email: '<EMAIL>', name: 'Test User' },
        logout: mockLogout
      })
    }));

    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    const userMenu = screen.getByText('Test User');
    fireEvent.click(userMenu);
    
    await waitFor(() => {
      const logoutButton = screen.getByText('nav.logout');
      fireEvent.click(logoutButton);
      expect(mockLogout).toHaveBeenCalled();
    });
  });

  it('should change theme when theme toggle is clicked', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    const themeToggle = screen.getByRole('button', { name: /theme/i });
    fireEvent.click(themeToggle);
    
    expect(document.documentElement).toHaveClass('dark');
    
    fireEvent.click(themeToggle);
    expect(document.documentElement).not.toHaveClass('dark');
  });

  it('should display notification badge when there are notifications', () => {
    vi.mock('../../hooks/useNotifications', () => ({
      useNotifications: () => ({
        unreadCount: 5,
        notifications: []
      })
    }));

    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('should render search bar on desktop', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    expect(screen.getByPlaceholderText('nav.search')).toBeInTheDocument();
  });

  it('should handle search submission', () => {
    const Wrapper = createWrapper();
    render(<Navbar />, { wrapper: Wrapper });
    
    const searchInput = screen.getByPlaceholderText('nav.search');
    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.submit(searchInput);
    
    expect(window.location.pathname).toBe('/search');
    expect(window.location.search).toContain('q=test+query');
  });
});