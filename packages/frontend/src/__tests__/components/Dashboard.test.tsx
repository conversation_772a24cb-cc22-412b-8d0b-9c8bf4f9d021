import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON>Router } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import Dashboard from '../../pages/Dashboard';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock API calls
vi.mock('../../lib/api', () => ({
  api: {
    get: vi.fn().mockImplementation((url) => {
      if (url === '/api/users/stats') {
        return Promise.resolve({
          totalProjects: 5,
          totalImages: 25,
          totalSegmentations: 20,
          recentActivity: []
        });
      }
      if (url === '/api/projects') {
        return Promise.resolve({
          projects: [
            {
              id: 'project-1',
              title: 'Project 1',
              description: 'Description 1',
              imageCount: 10,
              createdAt: new Date().toISOString()
            },
            {
              id: 'project-2',
              title: 'Project 2',
              description: 'Description 2',
              imageCount: 15,
              createdAt: new Date().toISOString()
            }
          ],
          totalCount: 2,
          totalPages: 1
        });
      }
      return Promise.resolve({});
    })
  }
}));

// Mock auth context
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: 'user-1', email: '<EMAIL>', name: 'Test User' },
    logout: vi.fn()
  })
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          {children}
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render dashboard with stats', async () => {
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument(); // Total projects
      expect(screen.getByText('25')).toBeInTheDocument(); // Total images
      expect(screen.getByText('20')).toBeInTheDocument(); // Total segmentations
    });
  });

  it('should display welcome message with username', () => {
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    expect(screen.getByText(/Welcome back, Test User/i)).toBeInTheDocument();
  });

  it('should render project list', async () => {
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.getByText('Project 2')).toBeInTheDocument();
    });
  });

  it('should navigate to create project on button click', () => {
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    const createButton = screen.getByText(/Create New Project/i);
    fireEvent.click(createButton);
    
    expect(window.location.pathname).toBe('/projects/new');
  });

  it('should show loading state initially', () => {
    const Wrapper = createWrapper();
    const { container } = render(<Dashboard />, { wrapper: Wrapper });
    
    const loadingElements = container.querySelectorAll('.skeleton');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('should handle error state', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.get).mockRejectedValueOnce(new Error('Network error'));
    
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText(/Failed to load dashboard data/i)).toBeInTheDocument();
    });
  });

  it('should refresh data on refresh button click', async () => {
    const api = await import('../../lib/api');
    const mockGet = vi.mocked(api.api.get);
    
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    await waitFor(() => {
      expect(mockGet).toHaveBeenCalledTimes(4); // Initial + refresh calls
    });
  });

  it('should filter projects by search term', async () => {
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.getByText('Project 2')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/Search projects/i);
    fireEvent.change(searchInput, { target: { value: 'Project 1' } });
    
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.queryByText('Project 2')).not.toBeInTheDocument();
    });
  });

  it('should display empty state when no projects', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.get).mockImplementation((url) => {
      if (url === '/api/projects') {
        return Promise.resolve({
          projects: [],
          totalCount: 0,
          totalPages: 0
        });
      }
      return Promise.resolve({});
    });
    
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText(/No projects yet/i)).toBeInTheDocument();
      expect(screen.getByText(/Create your first project to get started/i)).toBeInTheDocument();
    });
  });

  it('should handle project deletion', async () => {
    const api = await import('../../lib/api');
    vi.mocked(api.api.delete).mockResolvedValue({});
    
    const Wrapper = createWrapper();
    render(<Dashboard />, { wrapper: Wrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
    });
    
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);
    
    // Confirm deletion in modal
    const confirmButton = await screen.findByText(/Confirm Delete/i);
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(api.api.delete).toHaveBeenCalledWith('/api/projects/project-1');
    });
  });
});