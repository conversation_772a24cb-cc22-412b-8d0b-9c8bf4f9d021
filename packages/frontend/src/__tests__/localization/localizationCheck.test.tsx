import { describe, it, expect } from "vitest";
import * as en from "../../translations/en";
import * as cs from "../../translations/cs";
import * as de from "../../translations/de";
import * as es from "../../translations/es";
import * as fr from "../../translations/fr";
import * as zh from "../../translations/zh";

describe("Localization Files", () => {
  // These tests ensure each translation file has a minimum structure
  // and contains at least some translations to be useful

  it("should have some translations in Czech file", () => {
    // Check that cs has some content
    expect(cs).toBeDefined();
    expect(typeof cs).toBe("object");
    expect(Object.keys(cs).length).toBeGreaterThan(0);

    // Check for at least one section with content
    const hasContent = Object.values(cs).some(
      (section) =>
        typeof section === "object" &&
        section !== null &&
        Object.keys(section).length > 0,
    );
    expect(hasContent).toBe(true);
  });

  it("should have some translations in German file", () => {
    // Check that de has some content
    expect(de).toBeDefined();
    expect(typeof de).toBe("object");
    expect(Object.keys(de).length).toBeGreaterThan(0);

    // Check for at least one section with content
    const hasContent = Object.values(de).some(
      (section) =>
        typeof section === "object" &&
        section !== null &&
        Object.keys(section).length > 0,
    );
    expect(hasContent).toBe(true);
  });

  it("should have some translations in Spanish file", () => {
    // Check that es has some content
    expect(es).toBeDefined();
    expect(typeof es).toBe("object");
    expect(Object.keys(es).length).toBeGreaterThan(0);

    // Check for at least one section with content
    const hasContent = Object.values(es).some(
      (section) =>
        typeof section === "object" &&
        section !== null &&
        Object.keys(section).length > 0,
    );
    expect(hasContent).toBe(true);
  });

  it("should have some translations in French file", () => {
    // Check that fr has some content
    expect(fr).toBeDefined();
    expect(typeof fr).toBe("object");
    expect(Object.keys(fr).length).toBeGreaterThan(0);

    // Check for at least one section with content
    const hasContent = Object.values(fr).some(
      (section) =>
        typeof section === "object" &&
        section !== null &&
        Object.keys(section).length > 0,
    );
    expect(hasContent).toBe(true);
  });

  it("should have some translations in Chinese file", () => {
    // Check that zh has some content
    expect(zh).toBeDefined();
    expect(typeof zh).toBe("object");
    expect(Object.keys(zh).length).toBeGreaterThan(0);

    // Check for at least one section with content
    const hasContent = Object.values(zh).some(
      (section) =>
        typeof section === "object" &&
        section !== null &&
        Object.keys(section).length > 0,
    );
    expect(hasContent).toBe(true);
  });

  // Add a passing test to ensure the test file itself is executed
  it("should properly import translation files", () => {
    expect(en).toBeDefined();
    expect(cs).toBeDefined();
    expect(de).toBeDefined();
    expect(es).toBeDefined();
    expect(fr).toBeDefined();
    expect(zh).toBeDefined();
  });
});
