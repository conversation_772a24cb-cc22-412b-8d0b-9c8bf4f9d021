import { vi, expect } from 'vitest';

// Import the actual EditMode enum from the segmentation types
import { EditMode } from '@/pages/segmentation/hooks/segmentation/types';

// Re-export EditMode for convenience
export { EditMode };

export const createKeyboardTestProps = () => ({
  editMode: EditMode.View,
  setEditMode: vi.fn(),
  canUndo: true,
  canRedo: true,
  onUndo: vi.fn(),
  onRedo: vi.fn(),
  onSave: vi.fn(),
  onDelete: vi.fn(),
  onZoomIn: vi.fn(),
  onZoomOut: vi.fn(),
  onResetView: vi.fn(),
});

export const setupInputElement = () => {
  const input = document.createElement('input');
  input.id = 'test-input';
  document.body.appendChild(input);
  return input;
};

export const simulateKeyPressWithTarget = (key: string, target: EventTarget, options: Partial<KeyboardEvent> = {}) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  Object.defineProperty(event, 'target', {
    value: target,
    enumerable: true,
  });
  window.dispatchEvent(event);
};

export const cleanupInputElement = () => {
  const input = document.getElementById('test-input');
  if (input) {
    input.remove();
  }
};

export const simulateKeyPress = (key: string, options: Partial<KeyboardEvent> = {}) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  window.dispatchEvent(event);
};

export const simulateKeyPressAndRelease = (key: string, options: Partial<KeyboardEvent> = {}) => {
  const downEvent = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  const upEvent = new KeyboardEvent('keyup', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  window.dispatchEvent(downEvent);
  window.dispatchEvent(upEvent);
};

export const createFocusedInput = () => {
  const input = document.createElement('input');
  input.id = 'test-input';
  document.body.appendChild(input);
  input.focus();
  return input;
};

export const verifyEditModeWasSet = (mockFn: any, expectedMode: any) => {
  expect(mockFn).toHaveBeenCalledWith(expectedMode);
};

export const verifyHandlerWasCalled = (mockFn: any) => {
  expect(mockFn).toHaveBeenCalled();
};
