import { useState, useEffect, useCallback } from 'react';
import { websocketService } from '@/services/websocketService';
import type { SegmentationProgressEvent } from '@spheroseg/shared/src/types/entities';

export interface SegmentationProgressState {
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'idle';
  progress: number;
  stage?: string;
  startedAt?: string;
  estimatedTimeRemaining?: number;
  error?: string;
  lastUpdate?: Date;
}

export interface UseSegmentationProgressOptions {
  imageId?: string;
  projectId?: string;
  onStatusChange?: (imageId: string, state: SegmentationProgressState) => void;
  onComplete?: (imageId: string, success: boolean) => void;
  onError?: (imageId: string, error: string) => void;
}

export const useSegmentationProgress = ({
  imageId,
  projectId,
  onStatusChange,
  onComplete,
  onError,
}: UseSegmentationProgressOptions = {}) => {
  const [progressStates, setProgressStates] = useState<Record<string, SegmentationProgressState>>({});
  const [isConnected, setIsConnected] = useState(false);

  // Get progress state for a specific image
  const getProgressState = useCallback((id: string): SegmentationProgressState => {
    return progressStates[id] || {
      status: 'idle',
      progress: 0,
    };
  }, [progressStates]);

  // Update progress state for an image
  const updateProgressState = useCallback((
    id: string,
    updates: Partial<SegmentationProgressState>
  ) => {
    setProgressStates(prev => {
      const currentState = prev[id] || { status: 'idle' as const, progress: 0 };
      const newState = {
        ...currentState,
        ...updates,
        lastUpdate: new Date(),
      };

      // Call status change callback
      if (onStatusChange) {
        onStatusChange(id, newState);
      }

      // Call completion callback
      if (onComplete && (newState.status === 'completed' || newState.status === 'failed')) {
        onComplete(id, newState.status === 'completed');
      }

      // Call error callback
      if (onError && newState.status === 'failed' && newState.error) {
        onError(id, newState.error);
      }

      return {
        ...prev,
        [id]: newState,
      };
    });
  }, [onStatusChange, onComplete, onError]);

  // Handle progress events from WebSocket
  const handleProgressEvent = useCallback((data: SegmentationProgressEvent) => {
    console.log('Received progress event:', data);

    // If we're filtering by specific image, only process matching events
    if (imageId && data.imageId !== imageId) {
      return;
    }

    updateProgressState(data.imageId, {
      status: data.status,
      progress: data.progress,
      stage: data.stage,
      startedAt: data.startedAt,
      estimatedTimeRemaining: data.estimatedTimeRemaining,
      error: data.error,
    });
  }, [imageId, updateProgressState]);

  // Handle legacy segmentation update events
  const handleLegacySegmentationUpdate = useCallback((data: any) => {
    console.log('Received legacy segmentation update:', data);

    // If we're filtering by specific image, only process matching events
    if (imageId && data.imageId !== imageId) {
      return;
    }

    // Map legacy status to new progress format
    let progress = 0;
    let status: SegmentationProgressState['status'] = 'idle';

    switch (data.status || data.newStatus) {
      case 'queued':
      case 'pending':
        status = 'queued';
        progress = 0;
        break;
      case 'processing':
        status = 'processing';
        progress = data.progress || 50; // Default to 50% if no progress provided
        break;
      case 'completed':
        status = 'completed';
        progress = 100;
        break;
      case 'failed':
        status = 'failed';
        progress = progressStates[data.imageId]?.progress || 0; // Keep current progress
        break;
      case 'cancelled':
        status = 'cancelled';
        progress = progressStates[data.imageId]?.progress || 0;
        break;
      default:
        return; // Don't update for unknown statuses
    }

    updateProgressState(data.imageId, {
      status,
      progress,
      error: data.error,
    });
  }, [imageId, progressStates, updateProgressState]);

  // Initialize WebSocket connection and event listeners
  useEffect(() => {
    let isSubscribed = true;

    const initializeWebSocket = async () => {
      try {
        await websocketService.connect();
        
        // Join project room if specified
        if (projectId) {
          await websocketService.joinProjectRoom(projectId);
        }
        
        if (isSubscribed) {
          setIsConnected(true);
        }
      } catch (error) {
        console.error('Failed to initialize WebSocket for segmentation progress:', error);
        if (isSubscribed) {
          setIsConnected(false);
        }
      }
    };

    // Subscribe to WebSocket connection state changes
    const unsubscribeStateChange = websocketService.onStateChange((state) => {
      if (isSubscribed) {
        setIsConnected(state.isConnected);
      }
    });

    // Subscribe to progress events
    const progressEventId = websocketService.on('segmentation-progress', handleProgressEvent);
    const startedEventId = websocketService.on('segmentation-started', handleProgressEvent);
    const completedEventId = websocketService.on('segmentation-completed', handleProgressEvent);
    const failedEventId = websocketService.on('segmentation-failed', handleProgressEvent);
    const cancelledEventId = websocketService.on('segmentation-cancelled', handleProgressEvent);

    // Subscribe to legacy events for backward compatibility
    const legacyUpdateId = websocketService.on('segmentation_update', handleLegacySegmentationUpdate);
    const legacyUpdateLegacyId = websocketService.on('segmentation_update_legacy', handleLegacySegmentationUpdate);

    initializeWebSocket();

    return () => {
      isSubscribed = false;
      
      // Unsubscribe from all events
      unsubscribeStateChange();
      websocketService.off('segmentation-progress', progressEventId);
      websocketService.off('segmentation-started', startedEventId);
      websocketService.off('segmentation-completed', completedEventId);
      websocketService.off('segmentation-failed', failedEventId);
      websocketService.off('segmentation-cancelled', cancelledEventId);
      websocketService.off('segmentation_update', legacyUpdateId);
      websocketService.off('segmentation_update_legacy', legacyUpdateLegacyId);
    };
  }, [projectId, handleProgressEvent, handleLegacySegmentationUpdate]);

  // Manual progress update function
  const setProgress = useCallback((
    id: string,
    status: SegmentationProgressState['status'],
    progress: number,
    options?: {
      stage?: string;
      error?: string;
      estimatedTimeRemaining?: number;
    }
  ) => {
    updateProgressState(id, {
      status,
      progress,
      ...options,
    });
  }, [updateProgressState]);

  // Clear progress state for an image
  const clearProgress = useCallback((id: string) => {
    setProgressStates(prev => {
      const newState = { ...prev };
      delete newState[id];
      return newState;
    });
  }, []);

  // Clear all progress states
  const clearAllProgress = useCallback(() => {
    setProgressStates({});
  }, []);

  return {
    // State
    progressStates,
    isConnected,
    
    // Methods
    getProgressState,
    setProgress,
    clearProgress,
    clearAllProgress,
    
    // Convenience getter for single image mode
    progress: imageId ? getProgressState(imageId) : null,
  };
};

export default useSegmentationProgress;