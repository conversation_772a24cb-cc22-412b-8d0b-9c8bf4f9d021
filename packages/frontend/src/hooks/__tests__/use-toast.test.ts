import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock the sonner module
vi.mock("sonner", () => {
  const mockToast = vi.fn() as any;
  mockToast.success = vi.fn();
  mockToast.error = vi.fn();
  mockToast.info = vi.fn();
  mockToast.warning = vi.fn();
  mockToast.dismiss = vi.fn();
  mockToast.promise = vi.fn();
  mockToast.custom = vi.fn();

  return {
    toast: mockToast,
  };
});

import { toast } from "../use-toast";
import { toast as sonnerToast } from "sonner";

describe("use-toast hook", () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  it("should directly export the sonner toast function", () => {
    // Verify that the exported toast is the same as sonner's toast
    expect(toast).toBe(sonnerToast);
  });

  it("should call sonner toast.success when used", () => {
    // Call the toast.success method
    toast.success("Test success message");

    // Verify that sonner's toast.success was called with the correct arguments
    expect(sonnerToast.success).toHaveBeenCalledWith("Test success message");
  });

  it("should call sonner toast.error when used", () => {
    // Call the toast.error method
    toast.error("Test error message");

    // Verify that sonner's toast.error was called with the correct arguments
    expect(sonnerToast.error).toHaveBeenCalledWith("Test error message");
  });

  it("should call sonner toast.dismiss when used", () => {
    // Call the toast.dismiss method
    toast.dismiss();

    // Verify that sonner's toast.dismiss was called
    expect(sonnerToast.dismiss).toHaveBeenCalled();
  });

  it("should call sonner toast function directly when used", () => {
    // Call the toast function directly
    toast("Direct toast message");

    // Verify that the mock toast function was called
    expect(sonnerToast).toHaveBeenCalledWith("Direct toast message");
  });

  it("should pass additional options to sonner toast", () => {
    // Call with additional options
    toast.success("Success with options", {
      duration: 5000,
      position: "top-center",
      description: "This is a description",
    });

    // Verify the options were passed through
    expect(sonnerToast.success).toHaveBeenCalledWith("Success with options", {
      duration: 5000,
      position: "top-center",
      description: "This is a description",
    });
  });
});
