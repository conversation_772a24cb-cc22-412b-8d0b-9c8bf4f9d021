import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useSegmentationProgress } from '../useSegmentationProgress';
import { websocketService } from '../../services/websocketService';

// Mock the websocket service
vi.mock('../../services/websocketService', () => ({
  websocketService: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn()
  }
}));

describe('useSegmentationProgress', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    expect(result.current.progress).toEqual({});
    expect(result.current.overallProgress).toBe(0);
    expect(result.current.activeSegmentations).toEqual([]);
    expect(result.current.completedCount).toBe(0);
    expect(result.current.failedCount).toBe(0);
  });

  it('should subscribe to websocket events on mount', () => {
    const { unmount } = renderHook(() => useSegmentationProgress('project-1'));

    expect(websocketService.on).toHaveBeenCalledWith('segmentation-progress', expect.any(Function));
    expect(websocketService.on).toHaveBeenCalledWith('segmentation-complete', expect.any(Function));
    expect(websocketService.on).toHaveBeenCalledWith('segmentation-error', expect.any(Function));
    
    unmount();
    
    expect(websocketService.off).toHaveBeenCalledWith('segmentation-progress', expect.any(Function));
    expect(websocketService.off).toHaveBeenCalledWith('segmentation-complete', expect.any(Function));
    expect(websocketService.off).toHaveBeenCalledWith('segmentation-error', expect.any(Function));
  });

  it('should update progress when receiving progress event', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    // Get the callback that was registered for progress events
    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];

    // Simulate progress update
    act(() => {
      progressCallback({
        imageId: 'image-1',
        progress: 50,
        status: 'processing',
        message: 'Processing image...'
      });
    });

    await waitFor(() => {
      expect(result.current.progress['image-1']).toEqual({
        progress: 50,
        status: 'processing',
        message: 'Processing image...'
      });
      expect(result.current.overallProgress).toBe(50);
      expect(result.current.activeSegmentations).toContain('image-1');
    });
  });

  it('should handle multiple image progress updates', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];

    act(() => {
      progressCallback({ imageId: 'image-1', progress: 30 });
      progressCallback({ imageId: 'image-2', progress: 60 });
      progressCallback({ imageId: 'image-3', progress: 90 });
    });

    await waitFor(() => {
      expect(result.current.overallProgress).toBe(60); // Average of 30, 60, 90
      expect(result.current.activeSegmentations).toHaveLength(3);
    });
  });

  it('should handle completion events', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];
    
    const completeCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-complete'
    )?.[1];

    // Start processing
    act(() => {
      progressCallback({ imageId: 'image-1', progress: 50 });
    });

    // Complete processing
    act(() => {
      completeCallback({
        imageId: 'image-1',
        result: { cellCount: 42, avgCellSize: 150 }
      });
    });

    await waitFor(() => {
      expect(result.current.progress['image-1']).toEqual({
        progress: 100,
        status: 'completed',
        result: { cellCount: 42, avgCellSize: 150 }
      });
      expect(result.current.completedCount).toBe(1);
      expect(result.current.activeSegmentations).not.toContain('image-1');
    });
  });

  it('should handle error events', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];
    
    const errorCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-error'
    )?.[1];

    // Start processing
    act(() => {
      progressCallback({ imageId: 'image-1', progress: 30 });
    });

    // Error occurs
    act(() => {
      errorCallback({
        imageId: 'image-1',
        error: 'Failed to process image'
      });
    });

    await waitFor(() => {
      expect(result.current.progress['image-1']).toEqual({
        progress: 0,
        status: 'failed',
        error: 'Failed to process image'
      });
      expect(result.current.failedCount).toBe(1);
      expect(result.current.activeSegmentations).not.toContain('image-1');
    });
  });

  it('should reset progress state', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];

    // Add some progress
    act(() => {
      progressCallback({ imageId: 'image-1', progress: 50 });
      progressCallback({ imageId: 'image-2', progress: 75 });
    });

    // Reset progress
    act(() => {
      result.current.resetProgress();
    });

    await waitFor(() => {
      expect(result.current.progress).toEqual({});
      expect(result.current.overallProgress).toBe(0);
      expect(result.current.activeSegmentations).toEqual([]);
      expect(result.current.completedCount).toBe(0);
      expect(result.current.failedCount).toBe(0);
    });
  });

  it('should handle batch progress updates', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    act(() => {
      result.current.updateBatchProgress([
        { imageId: 'image-1', progress: 25 },
        { imageId: 'image-2', progress: 50 },
        { imageId: 'image-3', progress: 75 },
        { imageId: 'image-4', progress: 100 }
      ]);
    });

    await waitFor(() => {
      expect(result.current.overallProgress).toBe(62.5); // Average
      expect(result.current.activeSegmentations).toHaveLength(3); // Not including completed
      expect(result.current.completedCount).toBe(1);
    });
  });

  it('should calculate ETA for ongoing segmentations', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const startTime = Date.now();
    
    act(() => {
      result.current.startSegmentation('image-1');
    });

    // Simulate progress over time
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];

    act(() => {
      progressCallback({ imageId: 'image-1', progress: 50 });
    });

    await waitFor(() => {
      const eta = result.current.getEstimatedTimeRemaining('image-1');
      expect(eta).toBeGreaterThan(0);
    });
  });

  it('should handle concurrent segmentations correctly', async () => {
    const { result } = renderHook(() => useSegmentationProgress('project-1'));

    const progressCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-progress'
    )?.[1];
    
    const completeCallback = (websocketService.on as any).mock.calls.find(
      (call: any[]) => call[0] === 'segmentation-complete'
    )?.[1];

    // Start multiple segmentations
    act(() => {
      for (let i = 1; i <= 5; i++) {
        progressCallback({ imageId: `image-${i}`, progress: i * 20 });
      }
    });

    expect(result.current.activeSegmentations).toHaveLength(5);

    // Complete some segmentations
    act(() => {
      completeCallback({ imageId: 'image-5' }); // Was at 100%
      completeCallback({ imageId: 'image-4' }); // Was at 80%
    });

    await waitFor(() => {
      expect(result.current.activeSegmentations).toHaveLength(3);
      expect(result.current.completedCount).toBe(2);
    });
  });
});