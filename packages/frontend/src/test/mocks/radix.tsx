import { vi } from 'vitest';

// Mock Checkbox components
export const CheckboxRoot = ({ children, ...props }: any) => (
  <div data-testid="checkbox-root" {...props}>
    {children}
  </div>
);

export const CheckboxIndicator = ({ children, ...props }: any) => (
  <div data-testid="checkbox-indicator" {...props}>
    {children}
  </div>
);

// Mock Dialog components
export const DialogRoot = ({ children, open, onOpenChange, ...props }: any) =>
  open ? (
    <div data-testid="dialog-root" {...props}>
      {children}
    </div>
  ) : null;

export const DialogTrigger = ({ children, ...props }: any) => (
  <button data-testid="dialog-trigger" {...props}>
    {children}
  </button>
);

export const DialogPortal = ({ children }: any) => children;

export const DialogOverlay = ({ ...props }: any) => <div data-testid="dialog-overlay" {...props} />;

export const DialogContent = ({ children, ...props }: any) => (
  <div data-testid="dialog-content" {...props}>
    {children}
  </div>
);

export const DialogTitle = ({ children, ...props }: any) => (
  <h2 data-testid="dialog-title" {...props}>
    {children}
  </h2>
);

export const DialogDescription = ({ children, ...props }: any) => (
  <p data-testid="dialog-description" {...props}>
    {children}
  </p>
);

export const DialogClose = ({ children, ...props }: any) => (
  <button data-testid="dialog-close" {...props}>
    {children}
  </button>
);

// Mock other Radix components
export const DropdownMenuRoot = ({ children }: any) => children;
export const DropdownMenuTrigger = ({ children, ...props }: any) => (
  <button data-testid="dropdown-trigger" {...props}>
    {children}
  </button>
);
export const DropdownMenuContent = ({ children, ...props }: any) => (
  <div data-testid="dropdown-content" {...props}>
    {children}
  </div>
);
export const DropdownMenuItem = ({ children, ...props }: any) => (
  <div data-testid="dropdown-item" {...props}>
    {children}
  </div>
);

// Setup module mock
vi.mock('@/lib/radix-optimized', () => ({
  CheckboxRoot,
  CheckboxIndicator,
  DialogRoot,
  DialogTrigger,
  DialogPortal,
  DialogOverlay,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogClose,
  DropdownMenuRoot,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
}));
