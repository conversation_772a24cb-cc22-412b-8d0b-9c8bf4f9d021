// Temporary local type definitions while @spheroseg/shared/monitoring is not available
export interface PerformanceMonitoring {
  startTimer: (name: string) => void;
  endTimer: (name: string) => void;
  recordMetric: (metric: any) => void;
}

export interface PerformanceMonitoringOptions {
  endpoint?: string;
  enabled?: boolean;
  globalLabels?: Record<string, string>;
}

export type MetricType =
  | "pageLoad"
  | "componentRender"
  | "apiRequest"
  | "resourceLoad"
  | "userInteraction"
  | "memoryUsage";

// Create an enum for runtime usage
export enum MetricTypeEnum {
  PAGE_LOAD = "pageLoad",
  COMPONENT_RENDER = "componentRender",
  API_REQUEST = "apiRequest",
  RESOURCE_LOAD = "resourceLoad",
  USER_INTERACTION = "userInteraction",
  MEMORY_USAGE = "memoryUsage",
}

// For backward compatibility
export const MetricType = MetricTypeEnum;
export type PageLoadMetric = any;
export type ComponentRenderMetric = any;
export type ApiRequestMetric = any;
export type ResourceLoadMetric = any;
export type UserInteractionMetric = any;
export type MemoryUsageMetric = any;
import apiClient from "@/services/api/client";

/**
 * Frontend implementation of performance monitoring
 */
export class FrontendPerformanceMonitoring {
  public startTimer: (name: string) => void;
  public endTimer: (name: string) => void;
  public recordMetric: (metric: any) => void;
  private static instance: FrontendPerformanceMonitoring | null = null;

  /**
   * Get singleton instance
   */
  public static getInstance(
    options?: Partial<PerformanceMonitoringOptions>,
  ): FrontendPerformanceMonitoring {
    if (!FrontendPerformanceMonitoring.instance) {
      FrontendPerformanceMonitoring.instance =
        new FrontendPerformanceMonitoring(options);
    } else if (options) {
      // Update options if provided
      (FrontendPerformanceMonitoring.instance as any).options = {
        ...(FrontendPerformanceMonitoring.instance as any).options,
        ...options,
      };
    }
    return FrontendPerformanceMonitoring.instance;
  }

  private options: PerformanceMonitoringOptions;
  private timers: Map<string, number> = new Map();
  private metrics: any[] = [];

  constructor(options: Partial<PerformanceMonitoringOptions> = {}) {
    this.options = {
      ...options,
      globalLabels: {
        ...options.globalLabels,
        app: "frontend",
        environment: import.meta.env.MODE || "development",
      },
    };

    // Initialize methods
    this.startTimer = this.startTimerImpl.bind(this);
    this.endTimer = this.endTimerImpl.bind(this);
    this.recordMetric = this.recordMetricImpl.bind(this);

    // Set up performance observers if in browser environment
    if (typeof window !== "undefined") {
      this.setupPerformanceObservers();
    }
  }

  /**
   * Set up performance observers to automatically collect metrics
   */
  private setupPerformanceObservers(): void {
    // Observe page load metrics
    if ("performance" in window) {
      // Record navigation timing metrics when page load completes
      window.addEventListener("load", () => {
        setTimeout(() => {
          this.recordPageLoadMetrics();
        }, 0);
      });

      // Observe resource timing
      if ("PerformanceObserver" in window) {
        try {
          // Resource timing
          const resourceObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              if (entry.entryType === "resource") {
                this.recordResourceLoadMetric(
                  entry as PerformanceResourceTiming,
                );
              }
            });
          });
          resourceObserver.observe({ entryTypes: ["resource"] });

          // Largest Contentful Paint
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
              (this as any).recordMetric({
                type: MetricType.PAGE_LOAD,
                timestamp: Date.now(),
                value: lastEntry.startTime,
                route: window.location.pathname,
                loadTime: performance.now(),
                largestContentfulPaint: lastEntry.startTime,
              });
            }
          });
          lcpObserver.observe({
            type: "largest-contentful-paint",
            buffered: true,
          });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              (this as any).recordMetric({
                type: MetricType.USER_INTERACTION,
                timestamp: Date.now(),
                value: entry.duration,
                action: "first-input",
                target: "document",
                duration: entry.duration,
              });
            });
          });
          fidObserver.observe({ type: "first-input", buffered: true });
        } catch (observerError) {
          // Ignore observer errors
        }
      }
    }

    // Monitor memory usage periodically
    if (performance && (performance as any).memory) {
      setInterval(() => {
        this.recordMemoryUsageMetric();
      }, 30000); // Every 30 seconds
    }
  }

  /**
   * Record page load metrics
   */
  public recordPageLoadMetrics(): void {
    if (
      !(this as any).options.enabled ||
      typeof window === "undefined" ||
      !performance
    )
      return;

    try {
      const navigationEntry = performance.getEntriesByType(
        "navigation",
      )[0] as PerformanceNavigationTiming;

      if (navigationEntry) {
        const metric: PageLoadMetric = {
          type: MetricType.PAGE_LOAD,
          timestamp: Date.now(),
          value: navigationEntry.duration,
          route: window.location.pathname,
          loadTime: navigationEntry.duration,
          domContentLoaded:
            navigationEntry.domContentLoadedEventEnd -
            navigationEntry.startTime,
          firstPaint: 0,
          firstContentfulPaint: 0,
        };

        // Get first paint and first contentful paint
        const paintEntries = performance.getEntriesByType("paint");
        paintEntries.forEach((entry) => {
          if (entry.name === "first-paint") {
            metric.firstPaint = entry.startTime;
          } else if (entry.name === "first-contentful-paint") {
            metric.firstContentfulPaint = entry.startTime;
          }
        });

        (this as any).recordMetric(metric);
      }
    } catch (_e) {}
  }

  /**
   * Record component render time
   */
  public recordComponentRenderMetric(
    component: string,
    renderTime: number,
  ): void {
    if (!(this as any).options.enabled) return;

    const metric: ComponentRenderMetric = {
      type: MetricType.COMPONENT_RENDER,
      timestamp: Date.now(),
      value: renderTime,
      component,
      renderTime,
    };

    (this as any).recordMetric(metric);
  }

  /**
   * Record API request metric
   */
  public recordApiRequestMetric(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    error?: string,
  ): void {
    if (!(this as any).options.enabled) return;

    const metric: ApiRequestMetric = {
      type: MetricType.API_REQUEST,
      timestamp: Date.now(),
      value: duration,
      endpoint,
      method,
      duration,
      status,
      error,
    };

    (this as any).recordMetric(metric);
  }

  /**
   * Record resource load metric
   */
  private recordResourceLoadMetric(entry: PerformanceResourceTiming): void {
    if (!(this as any).options.enabled) return;

    // Skip monitoring requests to avoid circular reporting
    if (entry.name.includes((this as any).options.endpoint || "/api/metrics")) {
      return;
    }

    const metric: ResourceLoadMetric = {
      type: MetricType.RESOURCE_LOAD,
      timestamp: Date.now(),
      value: entry.duration,
      resourceUrl: entry.name,
      resourceType: entry.initiatorType,
      loadTime: entry.duration,
      size: entry.transferSize,
    };

    (this as any).recordMetric(metric);
  }

  /**
   * Record user interaction metric
   */
  public recordUserInteractionMetric(
    action: string,
    target: string,
    duration: number,
  ): void {
    if (!(this as any).options.enabled) return;

    const metric: UserInteractionMetric = {
      type: MetricType.USER_INTERACTION,
      timestamp: Date.now(),
      value: duration,
      action,
      target,
      duration,
    };

    (this as any).recordMetric(metric);
  }

  /**
   * Record memory usage metric
   */
  private recordMemoryUsageMetric(): void {
    if (!(this as any).options.enabled || typeof window === "undefined") return;

    try {
      const memory = (performance as any).memory;
      if (memory) {
        const metric: MemoryUsageMetric = {
          type: MetricType.MEMORY_USAGE,
          timestamp: Date.now(),
          value: (memory as any).usedJSHeapSize,
          jsHeapSizeLimit: (memory as any).jsHeapSizeLimit,
          totalJSHeapSize: (memory as any).totalJSHeapSize,
          usedJSHeapSize: (memory as any).usedJSHeapSize,
        };

        (this as any).recordMetric(metric);
      }
    } catch (_e) {}
  }

  /**
   * Flush metrics to the server
   */
  protected async flushMetrics(): Promise<void> {
    if (!this.options.enabled || this.metrics.length === 0) return;

    const metrics = [...this.metrics];
    this.metrics = [];

    try {
      await apiClient.post(
        this.options.endpoint || "/api/metrics",
        { metrics },
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      // Axios throws an error for non-2xx responses, so no need to check response.ok
    } catch (_error) {
      // Put metrics back in the queue to try again later
      this.metrics = [...metrics, ...this.metrics];
    }
  }

  /**
   * Start a timer
   */
  private startTimerImpl(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * End a timer and record the duration
   */
  private endTimerImpl(name: string): void {
    const startTime = this.timers.get(name);
    if (startTime !== undefined) {
      const duration = performance.now() - startTime;
      this.timers.delete(name);
      this.recordMetric({
        type: MetricType.COMPONENT_RENDER,
        timestamp: Date.now(),
        value: duration,
        name,
        duration,
      });
    }
  }

  /**
   * Record a metric
   */
  private recordMetricImpl(metric: any): void {
    if (this.options.enabled) {
      this.metrics.push({
        ...metric,
        ...this.options.globalLabels,
      });
    }
  }
}

/**
 * Create a frontend performance monitoring instance
 */
export function createPerformanceMonitoring(
  options: Partial<PerformanceMonitoringOptions> = {},
): FrontendPerformanceMonitoring {
  return FrontendPerformanceMonitoring.getInstance(options);
}
