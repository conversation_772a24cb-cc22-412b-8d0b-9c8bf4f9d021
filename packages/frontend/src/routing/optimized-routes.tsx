import { RouteObject } from 'react-router-dom';
import { lazyRoute } from '@/components/LazyLoadWrapper';
import Layout from '@/components/Layout';
import ErrorBoundary from '@/components/ErrorBoundary';
import NotFound from '@/pages/NotFound';

/**
 * Optimized routes with code splitting and lazy loading
 */

// Critical routes - loaded immediately
import SignIn from '@/pages/sign-in';
import SignUp from '@/pages/sign-up';

// Lazy load all other routes for better initial load performance
const Dashboard = lazyRoute(() => import('@/pages/dashboard'), { preload: true });
const ProjectList = lazyRoute(() => import('@/pages/projects'));
const ProjectDetail = lazyRoute(() => import('@/pages/projects/[id]'));
const ProjectSharing = lazyRoute(() => import('@/pages/projects/[id]/sharing'));
const ProjectSettings = lazyRoute(() => import('@/pages/projects/[id]/settings'));
const ImageUpload = lazyRoute(() => import('@/pages/projects/[id]/upload'));
const ImageDetail = lazyRoute(() => import('@/pages/projects/[id]/images/[imageId]'));
const Profile = lazyRoute(() => import('@/pages/profile'));
const Settings = lazyRoute(() => import('@/pages/settings'));
const AdminDashboard = lazyRoute(() => import('@/pages/admin'));
const AdminUsers = lazyRoute(() => import('@/pages/admin/users'));
const AdminProjects = lazyRoute(() => import('@/pages/admin/projects'));
const AdminSystem = lazyRoute(() => import('@/pages/admin/system'));

// Feature modules - lazy loaded as chunks
const AnalyticsModule = lazyRoute(() => import('@/modules/analytics'));
const ExportModule = lazyRoute(() => import('@/modules/export'));
const CollaborationModule = lazyRoute(() => import('@/modules/collaboration'));
const VisualizationModule = lazyRoute(() => import('@/modules/visualization'));

export const optimizedRoutes: RouteObject[] = [
  {
    path: '/',
    element: <Layout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
      },
      {
        path: 'projects',
        children: [
          {
            index: true,
            element: <ProjectList />,
          },
          {
            path: ':id',
            children: [
              {
                index: true,
                element: <ProjectDetail />,
              },
              {
                path: 'sharing',
                element: <ProjectSharing />,
              },
              {
                path: 'settings',
                element: <ProjectSettings />,
              },
              {
                path: 'upload',
                element: <ImageUpload />,
              },
              {
                path: 'images/:imageId',
                element: <ImageDetail />,
              },
              {
                path: 'analytics',
                element: <AnalyticsModule />,
              },
              {
                path: 'export',
                element: <ExportModule />,
              },
              {
                path: 'collaboration',
                element: <CollaborationModule />,
              },
              {
                path: 'visualization',
                element: <VisualizationModule />,
              },
            ],
          },
        ],
      },
      {
        path: 'profile',
        element: <Profile />,
      },
      {
        path: 'settings',
        element: <Settings />,
      },
      {
        path: 'admin',
        children: [
          {
            index: true,
            element: <AdminDashboard />,
          },
          {
            path: 'users',
            element: <AdminUsers />,
          },
          {
            path: 'projects',
            element: <AdminProjects />,
          },
          {
            path: 'system',
            element: <AdminSystem />,
          },
        ],
      },
    ],
  },
  {
    path: '/sign-in',
    element: <SignIn />,
  },
  {
    path: '/sign-up',
    element: <SignUp />,
  },
  {
    path: '*',
    element: <NotFound />,
  },
];

/**
 * Route prefetching configuration
 */
export const routePrefetchConfig = {
  // Routes to prefetch after initial load
  prefetchOnIdle: ['/dashboard', '/projects'],
  
  // Routes to prefetch on hover
  prefetchOnHover: {
    '/': ['/dashboard', '/projects'],
    '/dashboard': ['/projects', '/profile'],
    '/projects': ['/projects/:id'],
  },
  
  // Routes to prefetch based on user role
  roleBased: {
    admin: ['/admin', '/admin/users', '/admin/system'],
    user: ['/projects', '/profile', '/settings'],
  },
};

/**
 * Chunk optimization configuration for webpack/vite
 */
export const chunkConfig = {
  // Vendor chunks
  vendor: {
    react: ['react', 'react-dom', 'react-router-dom'],
    ui: ['@radix-ui/*', 'lucide-react', 'tailwind-merge'],
    utils: ['axios', 'date-fns', 'lodash-es'],
    charts: ['recharts', 'd3'],
  },
  
  // Feature chunks
  features: {
    auth: ['/sign-in', '/sign-up'],
    projects: ['/projects/*'],
    admin: ['/admin/*'],
    analytics: ['/*/analytics', '/*/visualization'],
  },
  
  // Size thresholds
  thresholds: {
    maxAsyncRequests: 6,
    maxInitialRequests: 4,
    minChunkSize: 20000, // 20kb
    maxChunkSize: 244000, // 244kb for better caching
  },
};