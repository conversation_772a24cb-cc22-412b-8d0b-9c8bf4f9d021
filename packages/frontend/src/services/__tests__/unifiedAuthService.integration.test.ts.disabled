/**
 * Integration tests for UnifiedAuthService
 * 
 * Tests authentication flow with API mocking and localStorage
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import authService from '../unifiedAuthService';
import { api } from '../api/client';
import { authEndpoints } from '../api/endpoints';

// Mock API client
vi.mock('../api/client', () => ({
  api: {
    post: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    defaults: { headers: { common: {} } }
  }
}));

// Mock socket client
vi.mock('../socketClient', () => ({
  socketClient: {
    connect: vi.fn(),
    disconnect: vi.fn(),
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn()
  }
}));

describe('UnifiedAuthService Integration Tests', () => {
  const mockApi = api as any;

  beforeEach(() => {
    // Clear localStorage
    localStorage.clear();
    
    // Reset mocks
    vi.clearAllMocks();
    
    // Reset auth state
    authService.logout();
  });

  afterEach(() => {
    localStorage.clear();
  });

  describe('login', () => {
    it('should login successfully and store tokens', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 'user_123',
            email: '<EMAIL>',
            name: 'Test User'
          },
          accessToken: 'mock_access_token',
          refreshToken: 'mock_refresh_token'
        }
      };

      mockApi.post.mockResolvedValueOnce(mockResponse);

      const result = await authService.login('<EMAIL>', 'password123');

      expect(result).toEqual(mockResponse.data);
      expect(mockApi.post).toHaveBeenCalledWith(
        authEndpoints.login,
        { email: '<EMAIL>', password: 'password123' }
      );

      // Verify tokens stored
      expect(localStorage.getItem('access_token')).toBe('mock_access_token');
      expect(localStorage.getItem('refresh_token')).toBe('mock_refresh_token');
      
      // Verify auth header set
      expect(api.defaults.headers.common['Authorization']).toBe('Bearer mock_access_token');
      
      // Verify current user
      const currentUser = authService.getCurrentUser();
      expect(currentUser).toEqual(mockResponse.data.user);
    });

    it('should handle login errors', async () => {
      mockApi.post.mockRejectedValueOnce(new Error('Invalid credentials'));

      await expect(authService.login('<EMAIL>', 'wrong'))
        .rejects.toThrow('Invalid credentials');

      // Verify no tokens stored
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(authService.getCurrentUser()).toBeNull();
    });

    it('should emit auth state changes', async () => {
      const mockResponse = {
        data: {
          user: { id: 'user_123', email: '<EMAIL>', name: 'Test User' },
          accessToken: 'token',
          refreshToken: 'refresh'
        }
      };

      mockApi.post.mockResolvedValueOnce(mockResponse);

      const authStateChanges: boolean[] = [];
      const unsubscribe = authService.onAuthStateChange((isAuthenticated) => {
        authStateChanges.push(isAuthenticated);
      });

      await authService.login('<EMAIL>', 'password');

      expect(authStateChanges).toEqual([true]);
      
      unsubscribe();
    });
  });

  describe('register', () => {
    it('should register new user and auto-login', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 'user_123',
            email: '<EMAIL>',
            name: 'New User'
          },
          accessToken: 'mock_access_token',
          refreshToken: 'mock_refresh_token'
        }
      };

      mockApi.post.mockResolvedValueOnce(mockResponse);

      const result = await authService.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User'
      });

      expect(result).toEqual(mockResponse.data);
      expect(mockApi.post).toHaveBeenCalledWith(
        authEndpoints.register,
        {
          email: '<EMAIL>',
          password: 'password123',
          name: 'New User'
        }
      );

      // Verify auto-login
      expect(localStorage.getItem('access_token')).toBe('mock_access_token');
      expect(authService.getCurrentUser()).toEqual(mockResponse.data.user);
    });

    it('should handle registration errors', async () => {
      mockApi.post.mockRejectedValueOnce(new Error('Email already exists'));

      await expect(authService.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User'
      })).rejects.toThrow('Email already exists');

      expect(authService.getCurrentUser()).toBeNull();
    });
  });

  describe('logout', () => {
    beforeEach(async () => {
      // Setup authenticated state
      localStorage.setItem('access_token', 'mock_token');
      localStorage.setItem('refresh_token', 'mock_refresh');
      
      // Mock current user
      const mockUser = { id: 'user_123', email: '<EMAIL>', name: 'Test User' };
      mockApi.get.mockResolvedValueOnce({ data: mockUser });
      await authService.initializeAuth();
    });

    it('should logout and clear all data', async () => {
      mockApi.post.mockResolvedValueOnce({ data: { message: 'Logged out' } });

      await authService.logout();

      expect(mockApi.post).toHaveBeenCalledWith(authEndpoints.logout);
      
      // Verify tokens cleared
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(localStorage.getItem('refresh_token')).toBeNull();
      
      // Verify auth header cleared
      expect(api.defaults.headers.common['Authorization']).toBeUndefined();
      
      // Verify user cleared
      expect(authService.getCurrentUser()).toBeNull();
      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should logout even if API call fails', async () => {
      mockApi.post.mockRejectedValueOnce(new Error('Network error'));

      await authService.logout();

      // Should still clear local data
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(authService.getCurrentUser()).toBeNull();
    });
  });

  describe('token refresh', () => {
    it('should refresh token when expired', async () => {
      localStorage.setItem('refresh_token', 'old_refresh_token');
      
      const mockResponse = {
        data: {
          accessToken: 'new_access_token',
          refreshToken: 'new_refresh_token'
        }
      };

      mockApi.post.mockResolvedValueOnce(mockResponse);

      const result = await authService.refreshToken();

      expect(result).toBe(true);
      expect(mockApi.post).toHaveBeenCalledWith(
        authEndpoints.refreshToken,
        { refreshToken: 'old_refresh_token' }
      );

      // Verify new tokens stored
      expect(localStorage.getItem('access_token')).toBe('new_access_token');
      expect(localStorage.getItem('refresh_token')).toBe('new_refresh_token');
    });

    it('should handle refresh token failure', async () => {
      localStorage.setItem('refresh_token', 'invalid_token');
      
      mockApi.post.mockRejectedValueOnce(new Error('Invalid refresh token'));

      const result = await authService.refreshToken();

      expect(result).toBe(false);
      
      // Should clear auth state
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('initializeAuth', () => {
    it('should restore session from localStorage', async () => {
      localStorage.setItem('access_token', 'stored_token');
      localStorage.setItem('refresh_token', 'stored_refresh');
      
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      mockApi.get.mockResolvedValueOnce({ data: mockUser });

      await authService.initializeAuth();

      expect(mockApi.get).toHaveBeenCalledWith(authEndpoints.currentUser);
      expect(authService.getCurrentUser()).toEqual(mockUser);
      expect(authService.isAuthenticated()).toBe(true);
    });

    it('should handle invalid stored token', async () => {
      localStorage.setItem('access_token', 'invalid_token');
      
      mockApi.get.mockRejectedValueOnce(new Error('Unauthorized'));

      await authService.initializeAuth();

      // Should clear invalid tokens
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should not make API call if no token', async () => {
      await authService.initializeAuth();

      expect(mockApi.get).not.toHaveBeenCalled();
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('updateProfile', () => {
    beforeEach(async () => {
      // Setup authenticated state
      localStorage.setItem('access_token', 'mock_token');
      const mockUser = { id: 'user_123', email: '<EMAIL>', name: 'Test User' };
      mockApi.get.mockResolvedValueOnce({ data: mockUser });
      await authService.initializeAuth();
    });

    it('should update user profile', async () => {
      const updates = { name: 'Updated Name', bio: 'New bio' };
      const updatedUser = {
        id: 'user_123',
        email: '<EMAIL>',
        name: 'Updated Name',
        bio: 'New bio'
      };

      mockApi.put.mockResolvedValueOnce({ data: updatedUser });

      const result = await authService.updateProfile(updates);

      expect(result).toEqual(updatedUser);
      expect(mockApi.put).toHaveBeenCalledWith(authEndpoints.updateProfile, updates);
      
      // Verify current user updated
      expect(authService.getCurrentUser()).toEqual(updatedUser);
    });

    it('should handle profile update errors', async () => {
      mockApi.put.mockRejectedValueOnce(new Error('Update failed'));

      await expect(authService.updateProfile({ name: 'New' }))
        .rejects.toThrow('Update failed');

      // Current user should remain unchanged
      expect(authService.getCurrentUser()?.name).toBe('Test User');
    });
  });

  describe('password reset', () => {
    it('should request password reset', async () => {
      mockApi.post.mockResolvedValueOnce({ 
        data: { message: 'Reset email sent' } 
      });

      const result = await authService.requestPasswordReset('<EMAIL>');

      expect(result).toEqual({ message: 'Reset email sent' });
      expect(mockApi.post).toHaveBeenCalledWith(
        authEndpoints.requestPasswordReset,
        { email: '<EMAIL>' }
      );
    });

    it('should reset password with token', async () => {
      mockApi.post.mockResolvedValueOnce({ 
        data: { message: 'Password reset successful' } 
      });

      const result = await authService.resetPassword('reset_token', 'new_password');

      expect(result).toEqual({ message: 'Password reset successful' });
      expect(mockApi.post).toHaveBeenCalledWith(
        authEndpoints.resetPassword,
        { token: 'reset_token', newPassword: 'new_password' }
      );
    });
  });

  describe('permissions', () => {
    beforeEach(async () => {
      // Setup authenticated state with permissions
      localStorage.setItem('access_token', 'mock_token');
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin',
        permissions: ['create_project', 'delete_project', 'manage_users']
      };
      mockApi.get.mockResolvedValueOnce({ data: mockUser });
      await authService.initializeAuth();
    });

    it('should check user permissions', () => {
      expect(authService.hasPermission('create_project')).toBe(true);
      expect(authService.hasPermission('delete_project')).toBe(true);
      expect(authService.hasPermission('manage_billing')).toBe(false);
    });

    it('should check user role', () => {
      expect(authService.hasRole('admin')).toBe(true);
      expect(authService.hasRole('user')).toBe(false);
    });

    it('should return false for unauthenticated users', async () => {
      await authService.logout();
      
      expect(authService.hasPermission('create_project')).toBe(false);
      expect(authService.hasRole('admin')).toBe(false);
    });
  });

  describe('session management', () => {
    it('should handle session expiry', async () => {
      const authStateChanges: boolean[] = [];
      const unsubscribe = authService.onAuthStateChange((isAuthenticated) => {
        authStateChanges.push(isAuthenticated);
      });

      // Simulate session expiry
      authService.handleSessionExpired();

      expect(authService.isAuthenticated()).toBe(false);
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(authStateChanges).toEqual([false]);

      unsubscribe();
    });

    it('should track last activity', () => {
      const before = Date.now();
      authService.updateLastActivity();
      const after = Date.now();

      const lastActivity = authService.getLastActivity();
      expect(lastActivity).toBeGreaterThanOrEqual(before);
      expect(lastActivity).toBeLessThanOrEqual(after);
    });
  });
});