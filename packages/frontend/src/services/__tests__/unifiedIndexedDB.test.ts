/**
 * Tests for Unified IndexedDB Service
 * 
 * These tests verify that the database consolidation works correctly
 */

import { unifiedIndexedDB } from '../unifiedIndexedDBService';
import { migrationService } from '../databaseMigrationService';

// Mock logger to avoid imports during tests
jest.mock('@/utils/logging/unifiedLogger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('UnifiedIndexedDBService', () => {
  beforeEach(async () => {
    // Clear any existing data
    try {
      await unifiedIndexedDB.clearEntireDatabase();
    } catch (error) {
      // Database might not exist yet, which is fine
    }
  });

  afterEach(async () => {
    // Clean up after each test
    try {
      await unifiedIndexedDB.clearEntireDatabase();
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Images Store', () => {
    test('should store and retrieve image blobs', async () => {
      const imageId = 'test-image-1';
      const projectId = 'test-project-1';
      const testBlob = new Blob(['test image data'], { type: 'image/png' });

      // Store image
      await unifiedIndexedDB.storeImageBlob(imageId, projectId, testBlob);

      // Retrieve image
      const retrievedBlob = await unifiedIndexedDB.getImageBlob(imageId);

      expect(retrievedBlob).not.toBeNull();
      expect(retrievedBlob?.size).toBe(testBlob.size);
      expect(retrievedBlob?.type).toBe(testBlob.type);
    });

    test('should delete images correctly', async () => {
      const imageId = 'test-image-2';
      const projectId = 'test-project-1';
      const testBlob = new Blob(['test image data'], { type: 'image/png' });

      // Store and then delete
      await unifiedIndexedDB.storeImageBlob(imageId, projectId, testBlob);
      await unifiedIndexedDB.deleteImageFromDB(imageId);

      // Should return null after deletion
      const retrievedBlob = await unifiedIndexedDB.getImageBlob(imageId);
      expect(retrievedBlob).toBeNull();
    });

    test('should delete all project images', async () => {
      const projectId = 'test-project-2';
      const testBlob = new Blob(['test image data'], { type: 'image/png' });

      // Store multiple images for the same project
      await unifiedIndexedDB.storeImageBlob('image-1', projectId, testBlob);
      await unifiedIndexedDB.storeImageBlob('image-2', projectId, testBlob);

      // Delete all project images
      await unifiedIndexedDB.deleteProjectImages(projectId);

      // Both images should be gone
      expect(await unifiedIndexedDB.getImageBlob('image-1')).toBeNull();
      expect(await unifiedIndexedDB.getImageBlob('image-2')).toBeNull();
    });
  });

  describe('Secure Data Store', () => {
    test('should store and retrieve secure data', async () => {
      const key = 'test-secure-key';
      const value = { secret: 'test-data', number: 42 };

      // Store secure data
      await unifiedIndexedDB.setSecureItem(key, value);

      // Retrieve secure data
      const retrievedValue = await unifiedIndexedDB.getSecureItem(key);

      expect(retrievedValue).toEqual(value);
    });

    test('should handle non-existent secure data', async () => {
      const retrievedValue = await unifiedIndexedDB.getSecureItem('non-existent-key');
      expect(retrievedValue).toBeNull();
    });

    test('should remove secure data', async () => {
      const key = 'test-secure-key-2';
      const value = { test: 'data' };

      // Store and then remove
      await unifiedIndexedDB.setSecureItem(key, value);
      await unifiedIndexedDB.removeSecureItem(key);

      // Should return null after removal
      const retrievedValue = await unifiedIndexedDB.getSecureItem(key);
      expect(retrievedValue).toBeNull();
    });
  });

  describe('Settings Store', () => {
    test('should store and retrieve settings', async () => {
      const key = 'theme';
      const value = 'dark';

      // Store setting
      await unifiedIndexedDB.setSetting(key, value);

      // Retrieve setting
      const retrievedValue = await unifiedIndexedDB.getSetting(key);

      expect(retrievedValue).toBe(value);
    });

    test('should handle non-existent settings', async () => {
      const retrievedValue = await unifiedIndexedDB.getSetting('non-existent-setting');
      expect(retrievedValue).toBeNull();
    });
  });

  describe('Cache Store', () => {
    test('should store and retrieve cache entries', async () => {
      const key = 'test-cache-key';
      const entry = {
        key,
        value: { data: 'test-cache-data' },
        timestamp: Date.now(),
        expiresAt: Date.now() + 300000, // 5 minutes
        size: 100,
        hits: 0,
      };

      // Store cache entry
      await unifiedIndexedDB.setCacheEntry(key, entry);

      // Retrieve cache entry
      const retrievedEntry = await unifiedIndexedDB.getCacheEntry(key);

      expect(retrievedEntry).toEqual(entry);
    });

    test('should delete cache entries', async () => {
      const key = 'test-cache-key-2';
      const entry = {
        key,
        value: { data: 'test-cache-data' },
        timestamp: Date.now(),
        expiresAt: Date.now() + 300000,
        size: 100,
        hits: 0,
      };

      // Store and then delete
      await unifiedIndexedDB.setCacheEntry(key, entry);
      await unifiedIndexedDB.deleteCacheEntry(key);

      // Should return null after deletion
      const retrievedEntry = await unifiedIndexedDB.getCacheEntry(key);
      expect(retrievedEntry).toBeNull();
    });
  });

  describe('Database Statistics', () => {
    test('should provide database statistics', async () => {
      // Add some test data
      const testBlob = new Blob(['test'], { type: 'image/png' });
      await unifiedIndexedDB.storeImageBlob('test-img', 'test-proj', testBlob);
      await unifiedIndexedDB.setSecureItem('test-key', { test: 'data' });
      await unifiedIndexedDB.setSetting('test-setting', 'test-value');

      const stats = await unifiedIndexedDB.getDBStats();

      expect(stats.images.count).toBe(1);
      expect(stats.images.totalSize).toBeGreaterThan(0);
      expect(stats.secureData.count).toBe(1);
      expect(stats.settings.count).toBe(1);
    });
  });

  describe('Store Management', () => {
    test('should clear individual stores', async () => {
      // Add test data to images store
      const testBlob = new Blob(['test'], { type: 'image/png' });
      await unifiedIndexedDB.storeImageBlob('test-img', 'test-proj', testBlob);

      // Clear images store
      await unifiedIndexedDB.clearStore('IMAGES');

      // Image should be gone
      const retrievedBlob = await unifiedIndexedDB.getImageBlob('test-img');
      expect(retrievedBlob).toBeNull();
    });
  });

  describe('Migration Service', () => {
    test('should handle migration status', async () => {
      // Get initial migration status
      const initialStatus = await migrationService.getMigrationStatus();

      // Run migration
      const migrationResult = await migrationService.migrate();

      expect(migrationResult).toBeDefined();
      expect(migrationResult.timestamp).toBeGreaterThan(0);
      expect(Array.isArray(migrationResult.errors)).toBe(true);
    });
  });

  describe('Data Cleanup', () => {
    test('should cleanup expired cache entries', async () => {
      // Add expired cache entry
      const expiredEntry = {
        key: 'expired-key',
        value: { data: 'expired' },
        timestamp: Date.now() - 86400000, // 1 day ago
        expiresAt: Date.now() - 3600000, // 1 hour ago (expired)
        size: 50,
        hits: 0,
      };

      await unifiedIndexedDB.setCacheEntry('expired-key', expiredEntry);

      // Run cleanup
      await unifiedIndexedDB.cleanupExpiredCache();

      // Expired entry should be gone
      const retrievedEntry = await unifiedIndexedDB.getCacheEntry('expired-key');
      expect(retrievedEntry).toBeNull();
    });

    test('should cleanup old data', async () => {
      // This test verifies the cleanup method exists and runs without error
      await expect(unifiedIndexedDB.cleanupOldData()).resolves.not.toThrow();
    });
  });
});