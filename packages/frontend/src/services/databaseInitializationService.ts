/**
 * Database Initialization Service
 * 
 * This service handles the initialization and setup of the unified IndexedDB system.
 * It should be called early in the application lifecycle to ensure proper database
 * migration and setup.
 */

import logger from '@/utils/logging/unifiedLogger';
import { unifiedIndexedDB } from './unifiedIndexedDBService';
import { migrationService } from './databaseMigrationService';

interface InitializationStatus {
  initialized: boolean;
  migrationCompleted: boolean;
  timestamp: number;
  errors: string[];
  stats?: {
    images: { count: number; totalSize: number };
    secureData: { count: number; totalSize: number };
    settings: { count: number; totalSize: number };
    cache: { count: number; totalSize: number };
  };
}

class DatabaseInitializationService {
  private initializationPromise: Promise<InitializationStatus> | null = null;
  private initialized = false;

  /**
   * Initialize the unified database system
   * This method is idempotent and can be called multiple times safely
   */
  async initialize(): Promise<InitializationStatus> {
    // Return existing promise if already initializing
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Return success if already initialized
    if (this.initialized) {
      return {
        initialized: true,
        migrationCompleted: true,
        timestamp: Date.now(),
        errors: [],
      };
    }

    this.initializationPromise = this.performInitialization();
    
    try {
      const result = await this.initializationPromise;
      this.initialized = result.initialized && result.migrationCompleted;
      return result;
    } catch (error) {
      // Reset promise on error so we can retry
      this.initializationPromise = null;
      throw error;
    }
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(): Promise<InitializationStatus> {
    const status: InitializationStatus = {
      initialized: false,
      migrationCompleted: false,
      timestamp: Date.now(),
      errors: [],
    };

    try {
      logger.info('Starting unified database initialization...');

      // Step 1: Run data migration from old databases
      logger.debug('Running database migration...');
      const migrationResult = await migrationService.migrate();
      
      status.migrationCompleted = migrationResult.completed;
      if (migrationResult.errors.length > 0) {
        status.errors.push(...migrationResult.errors);
        logger.warn('Migration completed with errors:', migrationResult.errors);
      }

      // Log migration results
      if (migrationResult.completed) {
        logger.info('Database migration completed successfully:', {
          images: migrationResult.migratedCounts.images,
          secureData: migrationResult.migratedCounts.secureData,
          settings: migrationResult.migratedCounts.settings,
          cache: migrationResult.migratedCounts.cache,
        });
      }

      // Step 2: Verify unified database is working
      logger.debug('Verifying unified database functionality...');
      
      try {
        // Test database operations
        await this.verifyDatabaseOperations();
        logger.debug('Database verification successful');
      } catch (error) {
        const errorMsg = `Database verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        status.errors.push(errorMsg);
        logger.error(errorMsg);
      }

      // Step 3: Get database statistics
      try {
        status.stats = await unifiedIndexedDB.getDBStats();
        logger.info('Unified database statistics:', status.stats);
      } catch (error) {
        logger.warn('Failed to get database statistics:', error);
      }

      // Step 4: Schedule cleanup
      this.schedulePeriodicCleanup();

      // Mark as initialized if migration was successful (even with minor errors)
      status.initialized = status.migrationCompleted;

      if (status.initialized) {
        logger.info('Unified database initialization completed successfully');
      } else {
        logger.error('Unified database initialization failed', { errors: status.errors });
      }

      return status;

    } catch (error) {
      const errorMsg = `Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      status.errors.push(errorMsg);
      logger.error(errorMsg, error);
      throw error;
    }
  }

  /**
   * Verify basic database operations work correctly
   */
  private async verifyDatabaseOperations(): Promise<void> {
    const testKey = `__test_${Date.now()}`;
    
    try {
      // Test settings store
      await unifiedIndexedDB.setSetting(testKey, 'test-value');
      const settingValue = await unifiedIndexedDB.getSetting(testKey);
      if (settingValue !== 'test-value') {
        throw new Error('Settings store verification failed');
      }

      // Test secure data store
      await unifiedIndexedDB.setSecureItem(testKey, { test: 'secure-data' });
      const secureValue = await unifiedIndexedDB.getSecureItem(testKey);
      if (!secureValue || secureValue.test !== 'secure-data') {
        throw new Error('Secure data store verification failed');
      }

      // Test cache store
      const cacheEntry = {
        key: testKey,
        value: { test: 'cache-data' },
        timestamp: Date.now(),
        expiresAt: Date.now() + 300000,
        size: 50,
        hits: 0,
      };
      await unifiedIndexedDB.setCacheEntry(testKey, cacheEntry);
      const cacheValue = await unifiedIndexedDB.getCacheEntry(testKey);
      if (!cacheValue || cacheValue.value.test !== 'cache-data') {
        throw new Error('Cache store verification failed');
      }

      // Clean up test data
      await Promise.all([
        unifiedIndexedDB.setSetting(testKey, ''), // Clear setting
        unifiedIndexedDB.removeSecureItem(testKey),
        unifiedIndexedDB.deleteCacheEntry(testKey),
      ]);

      logger.debug('All database store verifications passed');
    } catch (error) {
      // Attempt cleanup even if tests failed
      try {
        await Promise.all([
          unifiedIndexedDB.removeSecureItem(testKey),
          unifiedIndexedDB.deleteCacheEntry(testKey),
        ]);
      } catch (cleanupError) {
        logger.warn('Failed to cleanup test data:', cleanupError);
      }
      throw error;
    }
  }

  /**
   * Schedule periodic database cleanup
   */
  private schedulePeriodicCleanup(): void {
    // Run cleanup every 4 hours
    const cleanupInterval = 4 * 60 * 60 * 1000; // 4 hours

    setInterval(async () => {
      try {
        logger.debug('Running periodic database cleanup...');
        
        await Promise.all([
          unifiedIndexedDB.cleanupExpiredCache(),
          unifiedIndexedDB.cleanupOldData(7 * 24 * 60 * 60 * 1000), // 7 days
        ]);
        
        logger.debug('Periodic database cleanup completed');
      } catch (error) {
        logger.warn('Periodic database cleanup failed:', error);
      }
    }, cleanupInterval);

    // Run initial cleanup after 30 seconds
    setTimeout(async () => {
      try {
        await unifiedIndexedDB.cleanupExpiredCache();
      } catch (error) {
        logger.warn('Initial cleanup failed:', error);
      }
    }, 30000);

    logger.debug('Scheduled periodic database cleanup');
  }

  /**
   * Get the current initialization status
   */
  getInitializationStatus(): { initialized: boolean; initializing: boolean } {
    return {
      initialized: this.initialized,
      initializing: this.initializationPromise !== null && !this.initialized,
    };
  }

  /**
   * Force re-initialization (for debugging or recovery)
   */
  async forceReinitialize(): Promise<InitializationStatus> {
    logger.warn('Forcing database re-initialization...');
    
    this.initialized = false;
    this.initializationPromise = null;
    
    return this.initialize();
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    healthy: boolean;
    issues: string[];
    stats?: InitializationStatus['stats'];
  }> {
    const issues: string[] = [];
    let healthy = true;

    try {
      // Check if initialized
      if (!this.initialized) {
        issues.push('Database not initialized');
        healthy = false;
      }

      // Test basic operations
      const testKey = `__health_${Date.now()}`;
      try {
        await unifiedIndexedDB.setSetting(testKey, 'health-check');
        const value = await unifiedIndexedDB.getSetting(testKey);
        if (value !== 'health-check') {
          issues.push('Basic database operations failing');
          healthy = false;
        }
      } catch (error) {
        issues.push(`Database operation error: ${error instanceof Error ? error.message : 'Unknown'}`);
        healthy = false;
      }

      // Get current stats
      const stats = await unifiedIndexedDB.getDBStats();

      return { healthy, issues, stats };
    } catch (error) {
      return {
        healthy: false,
        issues: [`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      };
    }
  }
}

// Export singleton instance
export const databaseInitializationService = new DatabaseInitializationService();
export default databaseInitializationService;