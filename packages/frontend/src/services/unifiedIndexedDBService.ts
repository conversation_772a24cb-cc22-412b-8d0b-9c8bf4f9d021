/**
 * Unified IndexedDB Service
 * 
 * This service consolidates all IndexedDB databases into a single unified database
 * with multiple object stores for different data types:
 * - images: Image storage (from indexedDBService.ts)
 * - secureData: Encrypted secure storage (from secureStorage.ts)
 * - settings: User settings (from userProfileService.ts)
 * - cache: Cache data (from unifiedCacheService.ts)
 */

import logger from '@/utils/logging/unifiedLogger';
import { v4 as uuidv4 } from 'uuid';

// ===========================
// Database Configuration
// ===========================

const DB_NAME = 'spheroseg-unified';
const DB_VERSION = 1;

// Object store names
const STORES = {
  IMAGES: 'images',
  SECURE_DATA: 'secureData',
  SETTINGS: 'settings',
  CACHE: 'cache',
} as const;

// ===========================
// Type Definitions
// ===========================

export interface ImageData {
  id: string;
  projectId: string;
  blob: Blob;
  timestamp: number;
}

export interface SecureStorageItem {
  id: string;
  key: string;
  value: string;
  timestamp: number;
  encrypted: boolean;
}

export interface SettingsItem {
  key: string;
  value: string;
  timestamp: number;
}

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  expiresAt: number;
  size: number;
  hits: number;
  compressed?: boolean;
  encrypted?: boolean;
  tags?: string[];
}

export interface DatabaseStats {
  images: { count: number; totalSize: number };
  secureData: { count: number; totalSize: number };
  settings: { count: number; totalSize: number };
  cache: { count: number; totalSize: number };
}

// ===========================
// Unified IndexedDB Service
// ===========================

class UnifiedIndexedDBService {
  private db: IDBDatabase | null = null;
  private dbPromise: Promise<IDBDatabase> | null = null;
  private encryptionKey: CryptoKey | null = null;

  constructor() {
    this.initializeDatabase();
  }

  /**
   * Initialize the unified database
   */
  private async initializeDatabase(): Promise<void> {
    if (this.dbPromise) {
      return this.dbPromise.then(() => {});
    }

    this.dbPromise = new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        logger.error('Failed to open unified IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        logger.info('Unified IndexedDB initialized successfully');
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create images object store
        if (!db.objectStoreNames.contains(STORES.IMAGES)) {
          const imagesStore = db.createObjectStore(STORES.IMAGES, { keyPath: 'id' });
          imagesStore.createIndex('projectId', 'projectId', { unique: false });
          imagesStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Create secure data object store
        if (!db.objectStoreNames.contains(STORES.SECURE_DATA)) {
          const secureStore = db.createObjectStore(STORES.SECURE_DATA, { keyPath: 'id' });
          secureStore.createIndex('key', 'key', { unique: true });
          secureStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Create settings object store
        if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
          const settingsStore = db.createObjectStore(STORES.SETTINGS, { keyPath: 'key' });
          settingsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Create cache object store
        if (!db.objectStoreNames.contains(STORES.CACHE)) {
          const cacheStore = db.createObjectStore(STORES.CACHE, { keyPath: 'key' });
          cacheStore.createIndex('expiresAt', 'expiresAt', { unique: false });
          cacheStore.createIndex('tags', 'tags', { multiEntry: true });
          cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        logger.info('Unified IndexedDB object stores created');
      };
    });

    await this.dbPromise;
  }

  /**
   * Get database instance
   */
  private async getDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.initializeDatabase();
    }
    return this.db!;
  }

  // ===========================
  // Images Store Methods
  // ===========================

  /**
   * Store an image blob
   */
  async storeImageBlob(imageId: string, projectId: string, blob: Blob): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.IMAGES], 'readwrite');
      const store = transaction.objectStore(STORES.IMAGES);

      const imageData: ImageData = {
        id: imageId,
        projectId,
        blob,
        timestamp: Date.now(),
      };

      await new Promise<void>((resolve, reject) => {
        const request = store.put(imageData);
        request.onsuccess = () => {
          logger.debug(`Stored image ${imageId} in unified IndexedDB`);
          resolve();
        };
        request.onerror = () => {
          logger.error('Error storing image:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      logger.error('Failed to store image:', error);
      throw error;
    }
  }

  /**
   * Retrieve an image blob
   */
  async getImageBlob(imageId: string): Promise<Blob | null> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.IMAGES], 'readonly');
      const store = transaction.objectStore(STORES.IMAGES);

      return new Promise((resolve, reject) => {
        const request = store.get(imageId);
        request.onsuccess = () => {
          const result = request.result as ImageData | undefined;
          if (result) {
            logger.debug(`Retrieved image ${imageId} from unified IndexedDB`);
            resolve(result.blob);
          } else {
            logger.debug(`Image ${imageId} not found in unified IndexedDB`);
            resolve(null);
          }
        };
        request.onerror = () => {
          logger.error('Error retrieving image:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      logger.error('Failed to retrieve image:', error);
      return null;
    }
  }

  /**
   * Delete an image
   */
  async deleteImageFromDB(imageId: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.IMAGES], 'readwrite');
      const store = transaction.objectStore(STORES.IMAGES);

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(imageId);
        request.onsuccess = () => {
          logger.debug(`Deleted image ${imageId} from unified IndexedDB`);
          resolve();
        };
        request.onerror = () => {
          logger.error('Error deleting image:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      logger.error('Failed to delete image:', error);
    }
  }

  /**
   * Delete all images for a project
   */
  async deleteProjectImages(projectId: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.IMAGES], 'readwrite');
      const store = transaction.objectStore(STORES.IMAGES);
      const index = store.index('projectId');

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(projectId));
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            logger.debug(`Deleted all images for project ${projectId}`);
            resolve();
          }
        };
        request.onerror = () => {
          logger.error('Error deleting project images:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      logger.error('Failed to delete project images:', error);
      throw error;
    }
  }

  // ===========================
  // Secure Data Store Methods
  // ===========================

  /**
   * Initialize encryption key
   */
  private async getOrCreateEncryptionKey(): Promise<CryptoKey> {
    if (this.encryptionKey) {
      return this.encryptionKey;
    }

    // In production, this should be derived from user session or server
    const keyMaterial = await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256,
      },
      true,
      ['encrypt', 'decrypt']
    );

    this.encryptionKey = keyMaterial;
    return keyMaterial;
  }

  /**
   * Encrypt data
   */
  private async encrypt(data: string): Promise<{ encrypted: ArrayBuffer; iv: Uint8Array }> {
    const key = await this.getOrCreateEncryptionKey();
    const encoder = new TextEncoder();
    const iv = crypto.getRandomValues(new Uint8Array(12));

    const encrypted = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv,
      },
      key,
      encoder.encode(data)
    );

    return { encrypted, iv };
  }

  /**
   * Decrypt data
   */
  private async decrypt(encrypted: ArrayBuffer, iv: Uint8Array): Promise<string> {
    const key = await this.getOrCreateEncryptionKey();

    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv,
      },
      key,
      encrypted
    );

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  }

  /**
   * Set secure item
   */
  async setSecureItem(key: string, value: unknown): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.SECURE_DATA], 'readwrite');
      const store = transaction.objectStore(STORES.SECURE_DATA);

      const stringValue = JSON.stringify(value);
      const { encrypted, iv } = await this.encrypt(stringValue);

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv, 0);
      combined.set(new Uint8Array(encrypted), iv.length);

      // Convert to base64 for storage
      const base64 = btoa(String.fromCharCode(...combined));

      const item: SecureStorageItem = {
        id: uuidv4(),
        key,
        value: base64,
        timestamp: Date.now(),
        encrypted: true,
      };

      // Delete existing item with the same key first
      const index = store.index('key');
      const deleteRequest = index.openCursor(IDBKeyRange.only(key));

      await new Promise<void>((resolve, reject) => {
        deleteRequest.onsuccess = () => {
          const cursor = deleteRequest.result;
          if (cursor) {
            cursor.delete();
          }

          // Add the new item
          const addRequest = store.add(item);
          addRequest.onsuccess = () => resolve();
          addRequest.onerror = () => reject(addRequest.error);
        };
        deleteRequest.onerror = () => reject(deleteRequest.error);
      });
    } catch (error) {
      logger.error('Failed to set secure item:', error);
      throw error;
    }
  }

  /**
   * Get secure item
   */
  async getSecureItem(key: string): Promise<any | null> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.SECURE_DATA], 'readonly');
      const store = transaction.objectStore(STORES.SECURE_DATA);
      const index = store.index('key');

      return new Promise((resolve, reject) => {
        const request = index.get(key);
        request.onsuccess = async () => {
          const item = request.result as SecureStorageItem | undefined;
          if (!item) {
            resolve(null);
            return;
          }

          try {
            // Decode base64
            const combined = Uint8Array.from(atob(item.value), (c) => c.charCodeAt(0));

            // Extract IV and encrypted data
            const iv = combined.slice(0, 12);
            const encrypted = combined.slice(12);

            // Decrypt
            const decrypted = await this.decrypt(encrypted.buffer, iv);
            resolve(JSON.parse(decrypted));
          } catch (error) {
            logger.error('Failed to decrypt secure item:', error);
            resolve(null);
          }
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to get secure item:', error);
      return null;
    }
  }

  /**
   * Remove secure item
   */
  async removeSecureItem(key: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.SECURE_DATA], 'readwrite');
      const store = transaction.objectStore(STORES.SECURE_DATA);
      const index = store.index('key');

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(key));
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            cursor.delete();
          }
          resolve();
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to remove secure item:', error);
      throw error;
    }
  }

  // ===========================
  // Settings Store Methods
  // ===========================

  /**
   * Set setting
   */
  async setSetting(key: string, value: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.SETTINGS], 'readwrite');
      const store = transaction.objectStore(STORES.SETTINGS);

      const item: SettingsItem = {
        key,
        value,
        timestamp: Date.now(),
      };

      await new Promise<void>((resolve, reject) => {
        const request = store.put(item);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to set setting:', error);
      throw error;
    }
  }

  /**
   * Get setting
   */
  async getSetting(key: string): Promise<string | null> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.SETTINGS], 'readonly');
      const store = transaction.objectStore(STORES.SETTINGS);

      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => {
          const result = request.result as SettingsItem | undefined;
          resolve(result ? result.value : null);
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to get setting:', error);
      return null;
    }
  }

  // ===========================
  // Cache Store Methods
  // ===========================

  /**
   * Set cache entry
   */
  async setCacheEntry<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.CACHE], 'readwrite');
      const store = transaction.objectStore(STORES.CACHE);

      await new Promise<void>((resolve, reject) => {
        const request = store.put(entry);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to set cache entry:', error);
      throw error;
    }
  }

  /**
   * Get cache entry
   */
  async getCacheEntry<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.CACHE], 'readonly');
      const store = transaction.objectStore(STORES.CACHE);

      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => {
          resolve(request.result || null);
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to get cache entry:', error);
      return null;
    }
  }

  /**
   * Delete cache entry
   */
  async deleteCacheEntry(key: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.CACHE], 'readwrite');
      const store = transaction.objectStore(STORES.CACHE);

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(key);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to delete cache entry:', error);
    }
  }

  // ===========================
  // Utility Methods
  // ===========================

  /**
   * Clear all data from a specific store
   */
  async clearStore(storeName: keyof typeof STORES): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES[storeName]], 'readwrite');
      const store = transaction.objectStore(STORES[storeName]);

      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => {
          logger.info(`Cleared ${storeName} store`);
          resolve();
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error(`Failed to clear ${storeName} store:`, error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getDBStats(): Promise<DatabaseStats> {
    try {
      const db = await this.getDB();
      const stats: DatabaseStats = {
        images: { count: 0, totalSize: 0 },
        secureData: { count: 0, totalSize: 0 },
        settings: { count: 0, totalSize: 0 },
        cache: { count: 0, totalSize: 0 },
      };

      // Get images stats
      const imagesTransaction = db.transaction([STORES.IMAGES], 'readonly');
      const imagesStore = imagesTransaction.objectStore(STORES.IMAGES);
      const imagesRequest = imagesStore.getAll();

      stats.images = await new Promise((resolve) => {
        imagesRequest.onsuccess = () => {
          const images = imagesRequest.result as ImageData[];
          const totalSize = images.reduce((sum, img) => sum + img.blob.size, 0);
          resolve({ count: images.length, totalSize });
        };
      });

      // Get other stores counts
      for (const [key, storeName] of Object.entries(STORES)) {
        if (key === 'IMAGES') continue;

        const transaction = db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const countRequest = store.count();

        stats[key.toLowerCase() as keyof Omit<DatabaseStats, 'images'>] = await new Promise((resolve) => {
          countRequest.onsuccess = () => {
            resolve({ count: countRequest.result, totalSize: 0 }); // Size calculation would require more complex logic
          };
        });
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get database stats:', error);
      return {
        images: { count: 0, totalSize: 0 },
        secureData: { count: 0, totalSize: 0 },
        settings: { count: 0, totalSize: 0 },
        cache: { count: 0, totalSize: 0 },
      };
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([STORES.CACHE], 'readwrite');
      const store = transaction.objectStore(STORES.CACHE);
      const index = store.index('expiresAt');
      const range = IDBKeyRange.upperBound(Date.now());

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(range);
        let deletedCount = 0;

        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            cursor.delete();
            deletedCount++;
            cursor.continue();
          } else {
            logger.debug(`Cleaned up ${deletedCount} expired cache entries`);
            resolve();
          }
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      logger.error('Failed to cleanup expired cache:', error);
    }
  }

  /**
   * Clean up old data based on timestamps
   */
  async cleanupOldData(maxAgeMs: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const cutoffTime = Date.now() - maxAgeMs;
      const db = await this.getDB();

      // Clean up old secure data
      const secureTransaction = db.transaction([STORES.SECURE_DATA], 'readwrite');
      const secureStore = secureTransaction.objectStore(STORES.SECURE_DATA);
      const secureIndex = secureStore.index('timestamp');
      const secureRange = IDBKeyRange.upperBound(cutoffTime);

      await new Promise<void>((resolve) => {
        const request = secureIndex.openCursor(secureRange);
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            resolve();
          }
        };
      });

      logger.debug('Cleaned up old data from unified database');
    } catch (error) {
      logger.error('Failed to cleanup old data:', error);
    }
  }

  /**
   * Delete the entire unified database
   */
  async clearEntireDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

      deleteRequest.onsuccess = () => {
        logger.info('Unified IndexedDB database completely cleared');
        this.dbPromise = null;
        resolve();
      };

      deleteRequest.onerror = () => {
        logger.error('Error clearing unified IndexedDB database:', deleteRequest.error);
        reject(new Error('Error clearing database'));
      };

      deleteRequest.onblocked = () => {
        logger.warn('Database deletion blocked - close all tabs and try again');
        reject(new Error('Database deletion blocked'));
      };
    });
  }
}

// ===========================
// Singleton Instance Export
// ===========================

export const unifiedIndexedDB = new UnifiedIndexedDBService();
export default unifiedIndexedDB;