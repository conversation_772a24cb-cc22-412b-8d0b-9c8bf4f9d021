# Frontend Cleanup Plan

## 🎯 Cíle
- Odstranit duplicitní komponenty
- Konsolidovat translations
- Vyčistit nepoužívané soubory
- Vytvořit jednotnou component library

## 📊 Analýza
- **<PERSON><PERSON><PERSON> soubor<PERSON>**: 721
- **Komponenty**: ~200+
- **Translations**: 10 souborů (v<PERSON><PERSON><PERSON><PERSON> duplicit)
- **Z<PERSON><PERSON> duplicity**:
  - ImageUploader.tsx vs ImageUploaderV2.tsx
  - en.ts, en-cleaned.ts, en-restructured.ts
  - zh.ts vs zh.ts.broken

## 🔧 Akce

### 1. Komponenty k konsolidaci
- [ ] ImageUploader - použít V2, odstranit V1
- [ ] ErrorBoundary vs AppErrorBoundary - sjednotit
- [ ] Dashboard komponenty - konsolidovat do dashboard/
- [ ] Auth komponenty - ověřit duplicity

### 2. Translations cleanup
- [ ] Konsolidovat en.ts varianty
- [ ] Odstranit zh.ts.broken
- [ ] Odstranit duplicates-report.md
- [ ] Sjednotit strukturu všech jazyků

### 3. Nepoužívané soubory
- [ ] Zkontrolovat examples/
- [ ] Zkontrolovat __mocks__/
- [ ] Odstranit test soubory cookies*.txt

### 4. Component Library
- [ ] Vytvořit UI komponenty
- [ ] Standardizovat styly
- [ ] Dokumentovat použití