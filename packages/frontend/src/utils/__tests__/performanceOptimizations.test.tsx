/**
 * Tests for performance optimization utilities
 */

import React from 'react';
import { render, act, renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import {
  arePropsEqual,
  withMemo,
  useDebounce,
  useThrottle,
  useVirtualList,
  useIntersectionObserver,
} from '../performanceOptimizations';

describe('Performance Optimizations', () => {
  describe('arePropsEqual', () => {
    it('should return true for identical primitive props', () => {
      const prev = { a: 1, b: 'test', c: true };
      const next = { a: 1, b: 'test', c: true };
      expect(arePropsEqual(prev, next)).toBe(true);
    });

    it('should return false for different primitive props', () => {
      const prev = { a: 1, b: 'test' };
      const next = { a: 2, b: 'test' };
      expect(arePropsEqual(prev, next)).toBe(false);
    });

    it('should skip function comparisons', () => {
      const fn1 = () => {};
      const fn2 = () => {};
      const prev = { onClick: fn1 };
      const next = { onClick: fn2 };
      expect(arePropsEqual(prev, next)).toBe(true);
    });

    it('should deep compare objects', () => {
      const prev = { data: { x: 1, y: 2 } };
      const next = { data: { x: 1, y: 2 } };
      expect(arePropsEqual(prev, next)).toBe(true);

      const different = { data: { x: 1, y: 3 } };
      expect(arePropsEqual(prev, different)).toBe(false);
    });

    it('should only compare specified props', () => {
      const prev = { a: 1, b: 2, c: 3 };
      const next = { a: 1, b: 3, c: 3 };
      expect(arePropsEqual(prev, next, ['a', 'c'])).toBe(true);
      expect(arePropsEqual(prev, next, ['b'])).toBe(false);
    });
  });

  describe('withMemo', () => {
    it('should memoize component and prevent unnecessary renders', () => {
      let renderCount = 0;

      const TestComponent: React.FC<{ value: number; callback: () => void }> = ({ value }) => {
        renderCount++;
        return <div>{value}</div>;
      };

      const MemoizedComponent = withMemo(TestComponent, ['value']);

      const { rerender } = render(<MemoizedComponent value={1} callback={() => {}} />);

      expect(renderCount).toBe(1);

      // Same value, different callback - should not re-render
      rerender(<MemoizedComponent value={1} callback={() => {}} />);
      expect(renderCount).toBe(1);

      // Different value - should re-render
      rerender(<MemoizedComponent value={2} callback={() => {}} />);
      expect(renderCount).toBe(2);
    });
  });

  describe('useDebounce', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should debounce value changes', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'initial', delay: 500 },
      });

      expect(result.current).toBe('initial');

      // Update value
      rerender({ value: 'updated', delay: 500 });
      expect(result.current).toBe('initial'); // Still initial

      // Fast forward time
      act(() => {
        vi.advanceTimersByTime(500);
      });

      expect(result.current).toBe('updated');
    });

    it('should cancel previous timeout on new value', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'initial', delay: 500 },
      });

      rerender({ value: 'first', delay: 500 });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      rerender({ value: 'second', delay: 500 });

      act(() => {
        vi.advanceTimersByTime(500);
      });

      expect(result.current).toBe('second');
    });
  });

  describe('useThrottle', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
      vi.restoreAllMocks();
    });

    it('should throttle value changes', () => {
      // Mock Date.now to control time
      let currentTime = 0;
      vi.spyOn(Date, 'now').mockImplementation(() => currentTime);

      const { result, rerender } = renderHook(({ value, interval }) => useThrottle(value, interval), {
        initialProps: { value: 'initial', interval: 1000 },
      });

      expect(result.current).toBe('initial');

      // Advance time to make first update go through
      currentTime = 1000;

      // First update after some time - should go through
      rerender({ value: 'first', interval: 1000 });

      act(() => {
        vi.runAllTimers();
      });

      expect(result.current).toBe('first');

      // Second update within interval - should be throttled
      currentTime = 1500; // Only 500ms later
      rerender({ value: 'second', interval: 1000 });
      expect(result.current).toBe('first'); // Still first, throttled

      // Advance time and run timers
      act(() => {
        vi.advanceTimersByTime(500); // Complete the throttle interval
      });

      // Should now show the throttled value
      expect(result.current).toBe('second');

      // Restore Date.now
      vi.mocked(Date.now).mockRestore();
    });
  });

  describe('useVirtualList', () => {
    it('should calculate visible items correctly', () => {
      const items = Array.from({ length: 100 }, (_, i) => i);

      const { result } = renderHook(() =>
        useVirtualList({
          items,
          itemHeight: 50,
          containerHeight: 200,
          overscan: 2,
        }),
      );

      // With containerHeight=200, itemHeight=50: 4 items visible (200/50=4)
      // With overscan=2: start at index max(0, 0-2)=0, end at index min(99, 4+2)=6
      // So items 0-6 inclusive = 7 items
      expect(result.current.visibleItems).toHaveLength(7);
      expect(result.current.visibleItems[0]).toBe(0);
      expect(result.current.visibleItems[6]).toBe(6);
      expect(result.current.totalHeight).toBe(5000); // 100 * 50
      expect(result.current.offsetY).toBe(0);
    });

    it('should update visible items on scroll', () => {
      const items = Array.from({ length: 100 }, (_, i) => i);

      const { result } = renderHook(() =>
        useVirtualList({
          items,
          itemHeight: 50,
          containerHeight: 200,
          overscan: 2,
        }),
      );

      // Simulate scroll
      act(() => {
        const mockEvent = {
          currentTarget: { scrollTop: 250 },
        } as React.UIEvent<HTMLElement>;
        result.current.handleScroll(mockEvent);
      });

      // Should show items starting from index 3 (250 / 50 = 5, minus overscan)
      expect(result.current.visibleItems[0]).toBe(3);
      expect(result.current.offsetY).toBe(150); // 3 * 50
    });
  });

  describe('useIntersectionObserver', () => {
    // Save original IntersectionObserver
    const originalIntersectionObserver = window.IntersectionObserver;

    // Mock IntersectionObserver
    const mockIntersectionObserver = vi.fn();

    beforeEach(() => {
      mockIntersectionObserver.mockReturnValue({
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      });
      window.IntersectionObserver = mockIntersectionObserver as unknown as typeof IntersectionObserver;
    });

    afterEach(() => {
      // Restore original IntersectionObserver
      window.IntersectionObserver = originalIntersectionObserver;
      mockIntersectionObserver.mockClear();
    });

    it('should observe element and report intersection', async () => {
      const ref = React.createRef<HTMLDivElement>();

      const { result } = renderHook(() => useIntersectionObserver(ref));

      // Initially not intersecting
      expect(result.current).toBe(false);

      // Create element
      const div = document.createElement('div');
      (ref as React.MutableRefObject<HTMLDivElement>).current = div;

      // Trigger observer callback
      const observerCallback = mockIntersectionObserver.mock.calls[0][0];
      act(() => {
        observerCallback([{ isIntersecting: true }]);
      });

      await waitFor(() => {
        expect(result.current).toBe(true);
      });
    });

    it('should accept custom options', () => {
      const ref = React.createRef<HTMLDivElement>();
      const options = { threshold: 0.5, rootMargin: '10px' };

      renderHook(() => useIntersectionObserver(ref, options));

      expect(mockIntersectionObserver).toHaveBeenCalledWith(expect.any(Function), options);
    });
  });
});
