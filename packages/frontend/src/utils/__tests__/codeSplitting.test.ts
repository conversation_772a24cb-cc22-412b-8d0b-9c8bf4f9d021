import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as React from 'react';

// Mock the lazy function from react
vi.mock('react', async () => {
  const actual = await vi.importActual<typeof React>('react');
  return {
    ...actual,
    lazy: vi.fn((fn) => ({ _importFn: fn })),
  };
});

import { lazyWithRetry, prefetchComponent, createCodeSplitComponent } from '../codeSplitting';

describe('codeSplitting', () => {
  let mockLazy: any;

  beforeEach(() => {
    vi.clearAllMocks();
    // Clear localStorage
    localStorage.clear();
    // Reset fetch mock
    global.fetch = vi.fn();

    // Clear module cache to ensure fresh imports
    vi.resetModules();

    // Get the mocked lazy function
    mockLazy = React.lazy;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('lazyWithRetry', () => {
    it('should create a lazy component with retry logic', async () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });
      const component = lazyWithRetry(mockImport);

      expect(mockLazy).toHaveBeenCalled();
      expect(component).toBeDefined();
    });

    it('should retry failed imports', async () => {
      // Import fresh to avoid cache
      const { lazyWithRetry: freshLazyWithRetry } = await import('../codeSplitting');

      const mockImport = vi
        .fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ default: () => null });

      // Call lazyWithRetry with a unique name to avoid cache
      const component = freshLazyWithRetry(mockImport, { chunkName: 'retry-test-' + Date.now() });

      // Now get the lazy function that was passed to React.lazy
      expect(mockLazy).toHaveBeenCalled();
      const lastCall = mockLazy.mock.calls[mockLazy.mock.calls.length - 1];
      const lazyFn = lastCall[0];

      // Execute the lazy function
      await lazyFn();

      // Should have been called 3 times (2 failures + 1 success)
      expect(mockImport).toHaveBeenCalledTimes(3);
    });

    it('should track failed imports in localStorage', async () => {
      const mockImport = vi.fn().mockRejectedValue(new Error('Import failed'));
      const chunkName = 'test-chunk';

      // Call lazyWithRetry first
      const component = lazyWithRetry(mockImport, { chunkName, retryAttempts: 1 });

      // Get the lazy function - need to get the latest call
      const calls = mockLazy.mock.calls;
      const lazyFn = calls[calls.length - 1][0];

      try {
        await lazyFn();
      } catch (_error) {
        // Expected to fail
      }

      const failedImports = JSON.parse(localStorage.getItem('failedImports') || '[]');
      expect(failedImports).toContain(chunkName);
    });

    it('should not retry if chunk previously failed', async () => {
      const chunkName = 'failed-chunk';
      localStorage.setItem('failedImports', JSON.stringify([chunkName]));

      const mockImport = vi.fn().mockRejectedValue(new Error('Import failed'));

      // Call lazyWithRetry first
      const component = lazyWithRetry(mockImport, { chunkName });

      // Get the lazy function - need to get the latest call
      const calls = mockLazy.mock.calls;
      const lazyFn = calls[calls.length - 1][0];

      try {
        await lazyFn();
      } catch (_error) {
        // Expected to fail
      }

      // Should only be called once (no retries)
      expect(mockImport).toHaveBeenCalledTimes(1);
    });
  });

  describe('prefetchComponent', () => {
    it('should prefetch component resources', async () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });
      const mockFetch = vi.fn().mockResolvedValue({ ok: true });
      global.fetch = mockFetch;

      await prefetchComponent(mockImport, 'test-component');

      expect(mockImport).toHaveBeenCalled();
    });

    it('should cache prefetched components', async () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });

      await prefetchComponent(mockImport, 'cached-component');
      await prefetchComponent(mockImport, 'cached-component');

      // Should only be called once due to caching
      expect(mockImport).toHaveBeenCalledTimes(1);
    });

    it('should handle prefetch errors gracefully', async () => {
      const mockImport = vi.fn().mockRejectedValue(new Error('Prefetch failed'));

      // Should not throw
      await expect(prefetchComponent(mockImport, 'error-component')).resolves.toBeUndefined();
    });
  });

  describe('createCodeSplitComponent', () => {
    it('should create a component with prefetching support', () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });
      const result = createCodeSplitComponent(mockImport, {
        chunkName: 'split-component',
        prefetch: true,
      });

      expect(result).toBeDefined();
      expect(result.Component).toBeDefined();
      expect(result.prefetch).toBeDefined();
      expect(result.preload).toBeDefined();
      expect(mockLazy).toHaveBeenCalled();
    });

    it('should provide prefetch function', async () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });
      const result = createCodeSplitComponent(mockImport, {
        chunkName: 'prefetch-component',
      });

      await result.prefetch();
      expect(mockImport).toHaveBeenCalled();
    });

    it('should provide preload function', async () => {
      const mockImport = vi.fn().mockResolvedValue({ default: () => null });
      const result = createCodeSplitComponent(mockImport, {
        chunkName: 'preload-component',
      });

      const module = await result.preload();
      expect(module).toHaveProperty('default');
      expect(mockImport).toHaveBeenCalled();
    });
  });

  describe('integration', () => {
    it('should handle full code splitting flow', async () => {
      const TestComponent = () => React.createElement('div', null, 'Test');
      const mockImport = vi.fn().mockResolvedValue({ default: TestComponent });

      // Create code split component
      const SplitComponent = createCodeSplitComponent(mockImport, {
        chunkName: 'integration-test',
        prefetch: true,
        retryAttempts: 2,
      });

      expect(SplitComponent).toBeDefined();
      expect(mockLazy).toHaveBeenCalled();

      // Verify component structure
      expect(SplitComponent.Component).toBeDefined();
      expect(typeof SplitComponent.prefetch).toBe('function');
      expect(typeof SplitComponent.preload).toBe('function');
    });

    it('should track loading performance', async () => {
      const mockImport = vi
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve({ default: () => null }), 100)));

      // Call lazyWithRetry first
      const component = lazyWithRetry(mockImport, { chunkName: 'perf-test' });

      // Get the lazy function - need to get the latest call
      const calls = mockLazy.mock.calls;
      const lazyFn = calls[calls.length - 1][0];

      const start = Date.now();
      await lazyFn();
      const duration = Date.now() - start;

      // Should take at least 100ms
      expect(duration).toBeGreaterThanOrEqual(100);
    });
  });
});
