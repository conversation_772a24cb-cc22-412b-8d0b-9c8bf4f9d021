/**
 * URL normalization and processing utilities for SpheroSeg
 * 
 * This module handles:
 * - Image URL processing and normalization
 * - Cache busting parameter generation
 * - Alternative URL generation for fallback loading
 * - Default dimension handling
 */

import { getLogger } from "@/utils/logging/unifiedLogger";

// Create a logger for this module
const logger = getLogger("url-normalization");

/**
 * Interface for image data with URL processing
 */
export interface ImageUrlData {
  id: string;
  src?: string;
  storage_path?: string;
  storage_path_full?: string;
  width?: number;
  height?: number;
  alternativeUrls?: string[];
  [key: string]: unknown;
}

/**
 * Generate a cache-busting parameter for preventing browser caching
 */
export const generateCacheBuster = (): string => {
  return `_cb=${Date.now()}`;
};

/**
 * Add cache buster to a URL
 */
export const addCacheBuster = (url: string): string => {
  const cacheBuster = generateCacheBuster();
  return url.includes("?")
    ? `${url}&${cacheBuster}`
    : `${url}?${cacheBuster}`;
};

/**
 * Add cache busters to multiple URLs
 */
export const addCacheBustersToUrls = (urls: string[]): string[] => {
  return urls.map(url => addCacheBuster(url));
};

/**
 * Add default dimensions to image data if missing
 */
export const addDefaultDimensions = <T extends ImageUrlData>(imageData: T): T => {
  if (!imageData.width || !imageData.height) {
    // Default dimensions for spheroid images
    imageData.width = imageData.width || 1024;
    imageData.height = imageData.height || 1024;
    logger.debug(`Added default dimensions: ${imageData.width}x${imageData.height}`);
  }
  return imageData;
};

/**
 * Generate comprehensive alternative URLs based on storage path
 */
export const generateAlternativeUrls = (storagePath: string): string[] => {
  const baseUrl = window.location.origin;
  const cleanStoragePath = storagePath.replace(/^\//, ""); // Remove leading slash if present

  // Add properly formed alternative URLs based on actual storage path
  // All uploaded files are served from /uploads/ directory
  const additionalUrls = [
    `${baseUrl}/uploads/${cleanStoragePath}`,
    `/uploads/${cleanStoragePath}`,
    `${baseUrl}/${cleanStoragePath}`,
    `/${cleanStoragePath}`,
  ];

  // Remove duplicates
  return [...new Set(additionalUrls)];
};

/**
 * Process image URL to ensure it's absolute and attempts multiple URL formats
 * This is the main URL processing function extracted from segmentation/api.ts
 */
export const processImageUrl = <T extends ImageUrlData>(imageData: T): T => {
  logger.debug(
    `Processing image URL for image ${imageData.id}, storage_path: ${imageData.storage_path}, src: ${imageData.src}`,
  );
  
  // Add default dimensions if missing
  addDefaultDimensions(imageData);

  // First try to use storage_path_full which should be a complete URL
  if (imageData.storage_path_full) {
    imageData.src = imageData.storage_path_full;
    imageData.alternativeUrls = [];
  }
  // Then try src which might already be set
  else if (imageData.src) {
    // Create alternative URLs
    imageData.alternativeUrls = [];

    // If URL is not absolute, add base URL
    if (!imageData.src.startsWith("http") && !imageData.src.startsWith("/")) {
      const baseUrl = window.location.origin;
      imageData.src = `${baseUrl}/${imageData.src}`;
      // Add original as alternative
      imageData.alternativeUrls.push(imageData.src);
    } else if (imageData.src.startsWith("/")) {
      const baseUrl = window.location.origin;
      imageData.src = `${baseUrl}${imageData.src}`;
      // Add original as alternative
      imageData.alternativeUrls.push(imageData.src);
    }
    // If it's already an absolute URL (http:// or https://), leave it as is
  }
  // Then try storage_path
  else if (imageData.storage_path) {
    const baseUrl = window.location.origin;
    imageData.alternativeUrls = [];

    // Check if storage_path is already a full URL
    if (
      imageData.storage_path.startsWith("http://") ||
      imageData.storage_path.startsWith("https://")
    ) {
      // Extract the path part from the URL to use relative paths
      try {
        const url = new URL(imageData.storage_path);
        const pathOnly = url.pathname;

        // Use the pathname directly without prepending baseUrl (nginx will handle routing)
        imageData.src = pathOnly;

        // Add alternatives
        imageData.alternativeUrls.push(`${baseUrl}${pathOnly}`);
        imageData.alternativeUrls.push(pathOnly);
      } catch (e) {
        // If URL parsing fails, try to extract path manually
        logger.warn("Failed to parse storage_path as URL:", e);
        const match = imageData.storage_path.match(/https?:\/\/[^/]+(\/.+)/);
        if (match && match[1]) {
          imageData.src = match[1];
        } else {
          imageData.src = imageData.storage_path;
        }
      }
    } else if (imageData.storage_path.startsWith("/")) {
      // If it starts with /, it's already an absolute path
      imageData.src = `${baseUrl}${imageData.storage_path}`;
      // Add variations as alternatives
      imageData.alternativeUrls.push(imageData.storage_path);
    } else {
      // For relative paths, we need to add /uploads/ prefix
      // All uploaded images are stored in the /uploads/ directory
      imageData.src = `${baseUrl}/uploads/${imageData.storage_path}`;
      // Add variations as alternatives
      imageData.alternativeUrls.push(`/uploads/${imageData.storage_path}`);
      imageData.alternativeUrls.push(`${baseUrl}/${imageData.storage_path}`);
      imageData.alternativeUrls.push(`/${imageData.storage_path}`);
    }
  }
  // If we still don't have a src, that's an error
  else {
    throw new Error(
      "Image data missing src, storage_path_full, and storage_path",
    );
  }

  // Add cache buster to avoid browser caching issues
  if (imageData.src) {
    imageData.src = addCacheBuster(imageData.src);
  }

  // Generate comprehensive alternative URLs, but only use storage_path based URLs
  // Don't generate URLs based on image ID as they won't match the actual storage structure
  if (imageData.storage_path) {
    const additionalUrls = generateAlternativeUrls(imageData.storage_path);

    // Merge with existing alternatives, removing duplicates
    const allUrls = [...(imageData.alternativeUrls || []), ...additionalUrls];
    imageData.alternativeUrls = [...new Set(allUrls)];
  }

  // Add cache busters to all alternative URLs
  if (imageData.alternativeUrls) {
    imageData.alternativeUrls = addCacheBustersToUrls(imageData.alternativeUrls);
  }

  logger.debug(
    `Processed image URL: ${imageData.src} with ${imageData.alternativeUrls?.length || 0} alternatives`,
  );
  return imageData;
};

/**
 * Create placeholder image data with proper URL structure
 */
export const createPlaceholderImageData = (imageId: string, projectId: string): ImageUrlData => {
  // Create a placeholder image data with a blank image
  const placeholderUrl = `/placeholder.svg`;

  const placeholderImageData: ImageUrlData = {
    id: imageId,
    name: `Image ${imageId}`,
    width: 800,
    height: 600,
    src: placeholderUrl,
    storage_path: placeholderUrl,
    project_id: projectId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    status: "completed",
    alternativeUrls: [
      placeholderUrl,
      `/api/placeholder.svg`,
      `/public/placeholder.svg`,
    ],
  };

  return processImageUrl(placeholderImageData);
};