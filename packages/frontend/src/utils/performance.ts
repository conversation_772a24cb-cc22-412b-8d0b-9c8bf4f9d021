import { useCallback, useEffect, useRef } from 'react';
import { getLogger } from '@/utils/logging/unifiedLogger';

// Get logger for performance monitoring
const logger = getLogger('performance');

// Web Vitals metric type
interface WebVitalsMetric {
  name: string;
  value: number;
  id: string;
  rating?: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
  entries?: PerformanceEntry[];
  navigationType?: 'navigate' | 'reload' | 'back-forward' | 'back-forward-cache' | 'prerender' | 'restore';
}

/**
 * Initialize performance monitoring
 */
export const initPerformanceMonitoring = () => {
  // Record navigation timing
  if (window.performance && window.performance.timing) {
    const timing = window.performance.timing;
    const navigationStart = timing.navigationStart;

    // Report basic metrics
    const metrics = {
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      tcpConnection: timing.connectEnd - timing.connectStart,
      requestTime: timing.responseEnd - timing.requestStart,
      domProcessing: timing.domComplete - timing.domLoading,
      totalPageLoad: timing.loadEventEnd - navigationStart,
    };

    logger.debug('Navigation timing metrics', metrics);

    // Send metrics to backend (with error handling)
    if (navigator.sendBeacon) {
      const metricsPayload = {
        ...metrics,
        timestamp: new Date().toISOString(),
      };

      try {
        // Check if performance metrics endpoint is available
        const performanceMetricsEnabled = import.meta.env.VITE_ENABLE_PERFORMANCE_METRICS === 'true';

        if (performanceMetricsEnabled) {
          // Use fetch instead of sendBeacon for better error handling
          fetch('/api/metrics/performance', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(metricsPayload),
            // Don't block page unload if this fails
            keepalive: true,
          }).catch((error) => {
            // Silently log performance metric failures to avoid noise
            logger.debug('Failed to send performance metrics to backend', { error: error.message });
          });
        } else {
          // Log metrics locally only
          logger.debug('Performance metrics (local only)', metricsPayload);
        }
      } catch (_error) {
        // Fallback - don't throw errors for performance metrics
        logger.debug('Performance metrics collection failed', { _error });
      }
    }
  }

  // Register performance observer if available
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          logger.debug(`Performance entry: ${entry.name}`, {
            startTime: entry.startTime.toFixed(2),
            duration: entry.duration.toFixed(2),
            entryType: entry.entryType,
          });
        });
      });

      observer.observe({ entryTypes: ['resource', 'paint', 'navigation'] });
    } catch (_e) {
      logger.error('Performance observer initialization failed', _e);
    }
  }
};

/**
 * Mark a performance event
 */
export const markPerformance = (name: string) => {
  if (window.performance && window.performance.mark) {
    window.performance.mark(name);
    logger.debug(`Performance mark created: ${name}`);
    return true;
  }
  return false;
};

/**
 * Measure time between two performance marks OR measure function execution time
 */
export function measurePerformance<T extends (...args: unknown[]) => unknown>(
  nameOrFunction: string | T,
  startMark?: string,
  endMark?: string,
): number | { result?: ReturnType<T>; executionTime: number; error?: Error } | null {
  // Function execution measurement
  if (typeof nameOrFunction === 'function') {
    const fn = nameOrFunction;
    const start = performance.now();

    try {
      const result = fn();
      const executionTime = performance.now() - start;

      logger.debug(`Function execution time`, { executionTime: executionTime.toFixed(2) });

      return {
        result: result as ReturnType<T>,
        executionTime,
      };
    } catch (_error) {
      const executionTime = performance.now() - start;

      logger.error(`Function execution failed`, { _error, executionTime: executionTime.toFixed(2) });

      return {
        executionTime,
        error: _error instanceof Error ? _error : new Error(String(_error)),
      };
    }
  }

  // Performance marks measurement
  if (typeof nameOrFunction === 'string' && startMark && endMark) {
    if (window.performance && window.performance.measure) {
      try {
        window.performance.measure(nameOrFunction, startMark, endMark);
        const measures = window.performance.getEntriesByName(nameOrFunction, 'measure');
        if (measures.length > 0) {
          const duration = measures[0].duration;
          logger.debug(`Performance measure: ${nameOrFunction}`, { duration: duration.toFixed(2) });
          return duration;
        }
      } catch (_e) {
        logger.error(`Performance measurement failed for ${nameOrFunction}`, _e);
      }
    }
  }

  return null;
}

/**
 * Utility to measure and report frontend performance metrics
 */
export const usePerformance = () => {
  const renderCount = useRef<number>(0);
  const lastRenderTime = useRef<number>(0);
  const initialRenderTime = useRef<number | null>(null);

  // Record initial load time
  useEffect(() => {
    if (initialRenderTime.current === null) {
      initialRenderTime.current = performance.now();

      // Report initial load time to backend
      const frontendMetricsEnabled = import.meta.env.VITE_ENABLE_FRONTEND_METRICS === 'true';

      if (frontendMetricsEnabled && navigator.sendBeacon) {
        const metrics = {
          initialLoadTime: initialRenderTime.current,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          screenWidth: window.innerWidth,
          screenHeight: window.innerHeight,
        };

        // Only send if endpoint exists
        navigator.sendBeacon('/api/metrics/frontend', JSON.stringify(metrics));
      } else {
        // Log metrics locally only
        logger.debug('Frontend initial load metrics (local only)', {
          initialLoadTime: initialRenderTime.current,
          userAgent: navigator.userAgent,
          screenWidth: window.innerWidth,
          screenHeight: window.innerHeight,
        });
      }

      // Record web vitals
      import('web-vitals')
        .then((webVitals) => {
          webVitals.onCLS?.(sendToAnalytics);
          webVitals.onFID?.(sendToAnalytics);
          webVitals.onLCP?.(sendToAnalytics);
        })
        .catch((error) => {
          logger.debug('Web vitals not available', { error });
        });
    }
  }, []);

  // Track render performance
  useEffect(() => {
    const now = performance.now();
    renderCount.current += 1;

    if (lastRenderTime.current > 0) {
      const timeSinceLastRender = now - lastRenderTime.current;

      // Log render times if they exceed a threshold (e.g., 50ms)
      if (timeSinceLastRender > 50) {
        logger.debug('Slow render detected', {
          duration: timeSinceLastRender.toFixed(2),
          renderCount: renderCount.current,
        });
      }
    }

    lastRenderTime.current = now;
  });

  // Helper to send metrics to analytics
  const sendToAnalytics = useCallback((metric: WebVitalsMetric) => {
    // You can implement any analytics service here
    logger.debug(`Web Vitals: ${metric.name}`, {
      value: metric.value,
      id: metric.id,
      rating: metric.rating,
    });

    // Optionally send to backend
    const webVitalsMetricsEnabled = import.meta.env.VITE_ENABLE_WEB_VITALS_METRICS === 'true';

    if (webVitalsMetricsEnabled && navigator.sendBeacon) {
      navigator.sendBeacon(
        '/api/metrics/vitals',
        JSON.stringify({
          name: metric.name,
          value: metric.value,
          id: metric.id,
          timestamp: new Date().toISOString(),
        }),
      );
    }
  }, []);

  return {
    renderCount: renderCount.current,
    initialLoadTime: initialRenderTime.current,
    measureOperation: (operation: () => void, label = 'Operation') => {
      const start = performance.now();
      operation();
      const duration = performance.now() - start;
      logger.debug(`Operation timing: ${label}`, { duration: duration.toFixed(2) });
      return duration;
    },
  };
};

/**
 * Utility to measure image loading performance
 */
export const useImageLoadPerformance = () => {
  const trackImageLoad = useCallback((imageUrl: string, onLoaded?: (duration: number) => void) => {
    const startTime = performance.now();

    return (event: React.SyntheticEvent<HTMLImageElement>) => {
      const loadTime = performance.now() - startTime;
      logger.debug('Image loaded', {
        url: imageUrl,
        loadTime: loadTime.toFixed(2),
      });

      // Report image load time
      const img = event.currentTarget;
      const metrics = {
        imageUrl,
        loadTime,
        width: img.naturalWidth,
        height: img.naturalHeight,
        timestamp: new Date().toISOString(),
      };

      // Send image load metrics if enabled
      const imageLoadMetricsEnabled = import.meta.env.VITE_ENABLE_IMAGE_METRICS === 'true';

      if (imageLoadMetricsEnabled && navigator.sendBeacon) {
        navigator.sendBeacon('/api/metrics/image-load', JSON.stringify(metrics));
      } else {
        logger.debug('Image load metrics (local only)', metrics);
      }

      if (onLoaded) {
        onLoaded(loadTime);
      }
    };
  }, []);

  return { trackImageLoad };
};

/**
 * Utility for lazily loading images
 */
export const useLazyLoading = () => {
  useEffect(() => {
    if ('IntersectionObserver' in window) {
      const lazyImages = document.querySelectorAll('img[data-src]');

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      });

      lazyImages.forEach((image) => {
        observer.observe(image);
      });

      return () => {
        observer.disconnect();
      };
    } else {
      // Fallback for browsers without IntersectionObserver
      const lazyImages = document.querySelectorAll('img[data-src]');
      lazyImages.forEach((img) => {
        if ((img as HTMLImageElement).dataset.src) {
          (img as HTMLImageElement).src = (img as HTMLImageElement).dataset.src!;
          (img as HTMLImageElement).removeAttribute('data-src');
        }
      });
    }
  }, []);
};

/**
 * Performance monitoring class for tracking multiple metrics
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private activeOperations: Map<string, number> = new Map();

  start(label: string): () => void {
    const startTime = performance.now();

    return () => {
      const duration = performance.now() - startTime;
      if (!this.metrics.has(label)) {
        this.metrics.set(label, []);
      }
      this.metrics.get(label)!.push(duration);
    };
  }

  startOperation(operationName: string): void {
    this.activeOperations.set(operationName, performance.now());
  }

  endOperation(operationName: string): number | null {
    const startTime = this.activeOperations.get(operationName);
    if (startTime === undefined) {
      return null;
    }

    const duration = performance.now() - startTime;
    this.activeOperations.delete(operationName);

    if (!this.metrics.has(operationName)) {
      this.metrics.set(operationName, []);
    }
    this.metrics.get(operationName)!.push(duration);

    return duration;
  }

  trackOperation<T extends (...args: unknown[]) => unknown>(operationName: string, fn: T): T {
    return ((...args: Parameters<T>) => {
      this.startOperation(operationName);
      try {
        const result = fn(...args);
        this.endOperation(operationName);
        return result;
      } catch (_error) {
        this.endOperation(operationName);
        throw _error;
      }
    }) as T;
  }

  getStats(): Record<
    string,
    {
      count: number;
      totalTime: number;
      averageTime: number;
      minTime: number;
      maxTime: number;
    }
  > {
    const stats: Record<
      string,
      {
        count: number;
        totalTime: number;
        averageTime: number;
        minTime: number;
        maxTime: number;
      }
    > = {};

    for (const [label, values] of this.metrics.entries()) {
      if (values.length > 0) {
        const totalTime = values.reduce((sum, val) => sum + val, 0);
        stats[label] = {
          count: values.length,
          totalTime,
          averageTime: totalTime / values.length,
          minTime: Math.min(...values),
          maxTime: Math.max(...values),
        };
      }
    }

    return stats;
  }

  getMetrics(label: string): {
    count: number;
    total: number;
    average: number;
    min: number;
    max: number;
  } | null {
    const values = this.metrics.get(label);
    if (!values || values.length === 0) {
      return null;
    }

    return {
      count: values.length,
      total: values.reduce((sum, val) => sum + val, 0),
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
    };
  }

  reset(label?: string): void {
    if (label) {
      this.metrics.delete(label);
      this.activeOperations.delete(label);
    } else {
      this.metrics.clear();
      this.activeOperations.clear();
    }
  }

  getReport(): string {
    const stats = this.getStats();
    const lines = ['Performance Report:', '=================='];

    for (const [operation, data] of Object.entries(stats)) {
      lines.push(
        `${operation}: ${data.count} calls, avg: ${data.averageTime.toFixed(2)}ms, total: ${data.totalTime.toFixed(2)}ms`,
      );
    }

    return lines.join('\n');
  }

  getAllMetrics(): Map<string, number[]> {
    return new Map(this.metrics);
  }
}

// Re-export debounce from the canonical implementation
export { debounce, simpleDebounce } from '@/utils/debounce';
// Import for use in the default export
import { debounce as debounceFn } from '@/utils/debounce';

// Re-export timing utilities from the canonical implementation
export { throttle, memoize } from '@/utils/timing';

/**
 * Track operation time with callbacks
 */
export function trackOperationTime<T extends (...args: unknown[]) => unknown>(
  operation: T,
  operationName: string,
  onComplete?: (name: string, duration: number) => void,
  onError?: (error: Error) => void,
): (...args: Parameters<T>) => ReturnType<T> {
  return ((...args: Parameters<T>) => {
    const start = performance.now();
    try {
      const result = operation(...args);
      const duration = performance.now() - start;

      logger.debug(`Operation completed: ${operationName}`, { duration: duration.toFixed(2) });

      if (onComplete) {
        onComplete(operationName, duration);
      }

      return result;
    } catch (_error) {
      const duration = performance.now() - start;

      logger.error(`Operation failed: ${operationName}`, { error: _error, duration: duration.toFixed(2) });

      if (onComplete) {
        onComplete(operationName, duration);
      }
      if (onError && _error instanceof Error) {
        onError(_error);
      }

      throw _error;
    }
  }) as (...args: Parameters<T>) => ReturnType<T>;
}

// Import timing utilities for default export
import { throttle as throttleFn, memoize as memoizeFn } from '@/utils/timing';

export default {
  usePerformance,
  useImageLoadPerformance,
  useLazyLoading,
  initPerformanceMonitoring,
  markPerformance,
  measurePerformance,
  PerformanceMonitor,
  debounce: debounceFn,
  throttle: throttleFn,
  memoize: memoizeFn,
  trackOperationTime,
};
