/**
 * Unified Logging System for Frontend
 *
 * Provides consistent logging with namespacing, levels, and server shipping.
 * This consolidates all logging patterns into a single, configurable system.
 */

import { safeAsync } from '@/utils/error/UnifiedErrorHandler';

// ===========================
// Types and Interfaces
// ===========================

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  namespace: string;
  message: string;
  data?: unknown;
  error?: Error;
  context?: Record<string, unknown>;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableMemoryStorage: boolean;
  enableServerShipping: boolean;
  maxMemoryLogs: number;
  serverEndpoint: string;
  shipInterval: number;
  namespace?: string;
}

// ===========================
// Default Configuration
// ===========================

// Parse log level from environment variable
const getLogLevelFromEnv = (): LogLevel => {
  const envLevel = import.meta.env.VITE_LOG_LEVEL?.toUpperCase();

  switch (envLevel) {
    case 'DEBUG':
      return LogLevel.DEBUG;
    case 'INFO':
      return LogLevel.INFO;
    case 'WARN':
      return LogLevel.WARN;
    case 'ERROR':
      return LogLevel.ERROR;
    case 'NONE':
      return LogLevel.NONE;
    default:
      // Default to INFO in production, DEBUG in development
      return import.meta.env.PROD ? LogLevel.INFO : LogLevel.DEBUG;
  }
};

const DEFAULT_CONFIG: LoggerConfig = {
  level: getLogLevelFromEnv(),
  enableConsole: import.meta.env.VITE_ENABLE_CONSOLE_LOGS !== 'false',
  enableMemoryStorage: true,
  enableServerShipping: false, // Disabled to prevent 403 errors
  maxMemoryLogs: parseInt(import.meta.env.VITE_MAX_MEMORY_LOGS || '1000', 10),
  serverEndpoint: import.meta.env.VITE_LOG_SERVER_ENDPOINT || '/logs', // Fixed endpoint (API prefix added by client)
  shipInterval: parseInt(import.meta.env.VITE_LOG_SHIP_INTERVAL || '30000', 10),
  namespace: 'app',
};

// ===========================
// Logger Implementation
// ===========================

class UnifiedLogger {
  private config: LoggerConfig;
  private memoryLogs: LogEntry[] = [];
  private shipTimer?: NodeJS.Timeout;
  private namespace: string;

  constructor(namespace?: string, config?: Partial<LoggerConfig>) {
    this.namespace = namespace || DEFAULT_CONFIG.namespace!;
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
      namespace: this.namespace,
    };

    if (this.config.enableServerShipping) {
      this.startShipping();
    }
  }

  // Core logging methods

  debug(message: string, data?: unknown, context?: Record<string, unknown>): void {
    this.log(LogLevel.DEBUG, message, data, context);
  }

  info(message: string, data?: unknown, context?: Record<string, unknown>): void {
    this.log(LogLevel.INFO, message, data, context);
  }

  warn(message: string, data?: unknown, context?: Record<string, unknown>): void {
    this.log(LogLevel.WARN, message, data, context);
  }

  error(message: string, error?: Error | unknown, context?: Record<string, unknown>): void {
    let errorData: Record<string, unknown> = {};

    if (error instanceof Error) {
      errorData = { error: error.message, stack: error.stack };
    } else if (error && typeof error === 'object') {
      // Handle objects (like Axios error responses)
      errorData = {
        error: (error as Record<string, any>).message || (error as any).toString?.() || JSON.stringify(error),
        stack: (error as Record<string, any>).stack || '',
        ...((error as Record<string, any>).response && { response: (error as Record<string, any>).response }),
        ...((error as Record<string, any>).status && { status: (error as Record<string, any>).status }),
        ...((error as Record<string, any>).code && { code: (error as Record<string, any>).code }),
      };
    } else if (error) {
      errorData = { error: String(error) };
    }

    this.log(LogLevel.ERROR, message, errorData, context);
  }

  // Main logging function

  private log(level: LogLevel, message: string, data?: unknown, context?: Record<string, unknown>): void {
    // Check if logging is enabled for this level
    if (level < this.config.level) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      namespace: this.namespace,
      message,
      data,
      context: {
        ...context,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
      },
    };

    // Console output
    if (this.config.enableConsole && typeof console !== 'undefined') {
      const prefix = `[${entry.timestamp}] [${this.namespace}]`;
      const logData = data ? [prefix, message, data] : [prefix, message];

      switch (level) {
        case LogLevel.DEBUG:
          break;
        case LogLevel.INFO:
          break;
        case LogLevel.WARN:
          break;
        case LogLevel.ERROR:
          break;
      }
    }

    // Memory storage
    if (this.config.enableMemoryStorage) {
      this.memoryLogs.push(entry);

      // Trim logs if exceeding max
      if (this.memoryLogs.length > this.config.maxMemoryLogs) {
        this.memoryLogs = this.memoryLogs.slice(-this.config.maxMemoryLogs);
      }
    }
  }

  // Server shipping

  private startShipping(): void {
    this.shipTimer = setInterval(() => {
      this.shipLogs();
    }, this.config.shipInterval);

    // Ship logs on page unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.shipLogs(true);
      });
    }
  }

  private async shipLogs(immediate = false): Promise<void> {
    if (this.memoryLogs.length === 0) {
      return;
    }

    const logsToShip = [...this.memoryLogs];
    this.memoryLogs = [];

    // Map LogLevel to levelName for backend compatibility
    const levelNames = {
      [LogLevel.DEBUG]: 'DEBUG',
      [LogLevel.INFO]: 'INFO',
      [LogLevel.WARN]: 'WARN',
      [LogLevel.ERROR]: 'ERROR',
    };

    // Transform logs to match backend schema
    const transformedLogs = logsToShip.map((log) => ({
      timestamp: log.timestamp,
      level: log.level,
      levelName: levelNames[log.level] || 'INFO',
      message: `[${log.namespace}] ${log.message}`,
      data: {
        ...(typeof log.data === 'object' && log.data !== null ? log.data : {}),
        context: log.context,
        namespace: log.namespace,
      },
    }));

    // Use safeAsync to handle errors gracefully
    await safeAsync(
      async () => {
        const response = await fetch(this.config.serverEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            logs: transformedLogs,
            source: 'frontend',
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
            timestamp: new Date().toISOString(),
          }),
          // Use keepalive for immediate shipping on page unload
          keepalive: immediate,
        });

        if (!response.ok) {
          throw new Error(`Failed to ship logs: ${response.statusText}`);
        }
      },
      {
        showToast: false,
        logError: false, // Avoid recursive logging
      },
    );
  }

  // Utility methods

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  getLevel(): LogLevel {
    return this.config.level;
  }

  getLogs(): LogEntry[] {
    return [...this.memoryLogs];
  }

  clearLogs(): void {
    this.memoryLogs = [];
  }

  destroy(): void {
    if (this.shipTimer) {
      clearInterval(this.shipTimer);
      this.shipTimer = undefined;
    }
    this.shipLogs(true);
  }

  // Create child logger with namespace
  child(namespace: string): UnifiedLogger {
    return new UnifiedLogger(`${this.namespace}:${namespace}`, this.config);
  }
}

// ===========================
// Global Logger Instance
// ===========================

const globalLogger = new UnifiedLogger();

// ===========================
// Factory Functions
// ===========================

/**
 * Create a namespaced logger
 */
export function createLogger(namespace: string, config?: Partial<LoggerConfig>): UnifiedLogger {
  return new UnifiedLogger(namespace, config);
}

/**
 * Create a namespaced logger (backward compatibility)
 */
export function createNamespacedLogger(namespace: string): UnifiedLogger {
  return createLogger(namespace);
}

/**
 * Get or create a logger for a module
 */
const loggerCache = new Map<string, UnifiedLogger>();

export function getLogger(namespace: string): UnifiedLogger {
  if (!loggerCache.has(namespace)) {
    loggerCache.set(namespace, createLogger(namespace));
  }
  return loggerCache.get(namespace)!;
}

// ===========================
// Configuration Functions
// ===========================

/**
 * Set global log level
 */
export function setGlobalLogLevel(level: LogLevel): void {
  globalLogger.setLevel(level);
  // Update all cached loggers
  loggerCache.forEach((logger) => logger.setLevel(level));
}

/**
 * Enable/disable console output globally
 */
export function setConsoleEnabled(enabled: boolean): void {
  DEFAULT_CONFIG.enableConsole = enabled;
}

/**
 * Enable/disable server shipping globally
 */
export function setServerShippingEnabled(enabled: boolean): void {
  DEFAULT_CONFIG.enableServerShipping = enabled;
}

// ===========================
// Console Override (Optional)
// ===========================

/**
 * Override console methods to use unified logger
 * This ensures all console usage goes through our logging system
 */
export function overrideConsole(): void {
  if (typeof console === 'undefined') return;

  const originalConsole = {
    log: console.log,
    debug: console.debug,
    info: console.info,
    warn: console.warn,
    error: console.error,
  };

  console.log = (...args: unknown[]) => {
    globalLogger.info(args.map((arg) => String(arg)).join(' '));
  };

  console.debug = (...args: unknown[]) => {
    globalLogger.debug(args.map((arg) => String(arg)).join(' '));
  };

  console.info = (...args: unknown[]) => {
    globalLogger.info(args.map((arg) => String(arg)).join(' '));
  };

  console.warn = (...args: unknown[]) => {
    globalLogger.warn(args.map((arg) => String(arg)).join(' '));
  };

  console.error = (...args: unknown[]) => {
    const message = args[0];
    const error = args[1];
    if (args.length > 1 && (error instanceof Error || (error && typeof error === 'object'))) {
      globalLogger.error(String(message), error);
    } else {
      globalLogger.error(
        args
          .map((arg) => {
            if (arg instanceof Error) return arg.message;
            if (arg && typeof arg === 'object') return JSON.stringify(arg);
            return String(arg);
          })
          .join(' '),
      );
    }
  };

  // Store reference to restore later if needed
  (window as Window & { __originalConsole?: unknown }).__originalConsole = originalConsole;
}

/**
 * Restore original console methods
 */
export function restoreConsole(): void {
  if (typeof window !== 'undefined' && (window as Window & { __originalConsole?: unknown }).__originalConsole) {
    const original = (window as Window & { __originalConsole?: unknown }).__originalConsole as {
      log: typeof console.log;
      debug: typeof console.debug;
      info: typeof console.info;
      warn: typeof console.warn;
      error: typeof console.error;
    };
    console.log = original.log;
    console.debug = original.debug;
    console.info = original.info;
    console.warn = original.warn;
    console.error = original.error;
  }
}

// ===========================
// Default Export
// ===========================

export default globalLogger;

// Named exports for convenience
export const logger = globalLogger;
export { LogLevel as LEVELS };
