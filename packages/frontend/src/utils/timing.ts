/**
 * Canonical timing utilities
 * This is the single source of truth for all timing-related utility functions
 */

/**
 * Simple throttle implementation
 * Limits function execution to once per specified time period
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  fn: T,
  limit: number,
  options: { leading?: boolean; trailing?: boolean } = {},
): (...args: Parameters<T>) => void {
  const { leading = true, trailing = false } = options;
  let inThrottle = false;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: unknown;
  let hasInvoked = false;

  return function (this: unknown, ...args: Parameters<T>) {
    lastArgs = args;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    lastThis = this;

    if (!inThrottle) {
      if (leading) {
        fn.apply(this, args);
        hasInvoked = true;
      }
      inThrottle = true;

      setTimeout(() => {
        inThrottle = false;
        if (trailing && lastArgs && (!leading || hasInvoked)) {
          fn.apply(lastThis, lastArgs);
        }
        if (!leading && !hasInvoked && lastArgs) {
          fn.apply(lastThis, lastArgs);
        }
        lastArgs = null;
        hasInvoked = false;
      }, limit) as unknown as number;
    } else if (trailing) {
      lastArgs = args;
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      lastThis = this;
    }
  };
}

/**
 * Simple memoize implementation
 * Caches function results based on arguments
 */
export function memoize<T extends (...args: unknown[]) => unknown>(
  fn: T,
  resolver?: (...args: Parameters<T>) => string,
): T {
  const cache = new Map<string, ReturnType<T>>();

  return ((...args: Parameters<T>) => {
    const key = resolver ? resolver(...args) : JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key)!;
    }

    const result = fn(...args) as ReturnType<T>;
    cache.set(key, result);
    return result;
  }) as T;
}

// Default export for convenience
export default {
  throttle,
  memoize,
};