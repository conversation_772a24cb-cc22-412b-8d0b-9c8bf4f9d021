/**
 * Tests for access denied notification suppression
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  handleError,
  clearAccessDeniedSuppression,
  FrontendError,
  ErrorType,
} from '../UnifiedErrorHandler';
import { toast } from 'sonner';

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(() => 'toast-id'),
    warning: vi.fn(() => 'toast-id'),
    info: vi.fn(() => 'toast-id'),
  },
}));

// Mock logger
vi.mock('@/utils/logging/unifiedLogger', () => ({
  default: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('Access Denied Notification Suppression', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    clearAccessDeniedSuppression();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should show access denied notification', () => {
    const error = new FrontendError(ErrorType.INTERNAL_SERVER, 'You do not have permission to delete images in this project');

    handleError(error, {
      context: { context: 'API DELETE /api/images/123' },
    });

    expect(toast.error).toHaveBeenCalledWith(
      'You do not have permission to delete images in this project',
      expect.any(Object),
    );
  });

  it('should suppress subsequent 404 errors after access denied', () => {
    // First, trigger an access denied error
    const accessDeniedError = new FrontendError(ErrorType.AUTHORIZATION, 'You do not have permission to resegment images in this project');

    handleError(accessDeniedError, {
      context: { operation: 'API POST /api/images/123/resegment' },
    });

    expect(toast.error).toHaveBeenCalledTimes(1);

    // Clear only toast mocks to see what happens with the second call
    vi.mocked(toast.error).mockClear();
    vi.mocked(toast.warning).mockClear();
    vi.mocked(toast.info).mockClear();

    // Now trigger a 404 error for a related operation
    // The key is extracted from the message "Image not found", which will give "image-operation"
    const notFoundError = new FrontendError(ErrorType.NOT_FOUND, 'Image not found');

    handleError(notFoundError, {
      context: { operation: 'API GET /api/segmentation-results/456' },
    });
    // The 404 error should be suppressed - no toasts at all after clearing
    expect(toast.error).not.toHaveBeenCalled();
    expect(toast.warning).not.toHaveBeenCalled();
    expect(toast.info).not.toHaveBeenCalled();
  });

  it('should show 404 errors when there is no recent access denied', () => {
    const notFoundError = new FrontendError(ErrorType.NOT_FOUND, 'Resource not found');

    handleError(notFoundError, {
      context: { context: 'API GET /api/projects/999' },
    });

    expect(toast.error).toHaveBeenCalledWith('Resource not found', expect.any(Object));
  });

  it('should stop suppressing after the suppression window expires', () => {
    vi.useFakeTimers();

    // Trigger an access denied error
    const accessDeniedError = new FrontendError(ErrorType.AUTHORIZATION, 'Access denied');

    handleError(accessDeniedError, {
      context: { context: 'API DELETE /api/images/123' },
    });

    expect(toast.error).toHaveBeenCalledTimes(1);

    // Advance time past the suppression window (30 seconds)
    vi.advanceTimersByTime(31000);

    // Now a 404 error should be shown
    const notFoundError = new FrontendError(ErrorType.NOT_FOUND, 'Not found');

    handleError(notFoundError, {
      context: { context: 'API GET /api/images/456' },
    });

    expect(toast.error).toHaveBeenCalledTimes(2);
  });

  it('should handle multiple access denied errors for different contexts', () => {
    // Access denied for images
    const imageAccessDenied = new FrontendError(ErrorType.AUTHORIZATION, 'No permission for images');

    handleError(imageAccessDenied, {
      context: { context: 'API DELETE /api/images/123' },
    });

    // Access denied for projects
    const projectAccessDenied = new FrontendError(ErrorType.AUTHORIZATION, 'No permission for projects');

    handleError(projectAccessDenied, {
      context: { context: 'API DELETE /api/projects/456' },
    });

    expect(toast.error).toHaveBeenCalledTimes(2);

    // 404 for image should be suppressed
    const imageNotFound = new FrontendError(ErrorType.NOT_FOUND, 'Image not found');

    handleError(imageNotFound, {
      context: { context: 'API GET /api/segmentation/image/123' },
    });

    expect(toast.error).toHaveBeenCalledTimes(2); // Still only the access denied errors
  });

  it('should clear suppression when clearAccessDeniedSuppression is called', () => {
    // Trigger an access denied error
    const accessDeniedError = new FrontendError(ErrorType.AUTHORIZATION, 'Access denied for images');

    handleError(accessDeniedError, {
      context: { operation: 'API DELETE /api/images/123' },
    });

    expect(toast.error).toHaveBeenCalledTimes(1);

    // Clear suppression
    clearAccessDeniedSuppression();

    // Clear only toast mocks to see the effect of the next call
    vi.mocked(toast.error).mockClear();
    vi.mocked(toast.warning).mockClear();
    vi.mocked(toast.info).mockClear();

    // Now a 404 error should be shown (with "image" in the message for related context)
    const notFoundError = new FrontendError(ErrorType.NOT_FOUND, 'Image not found');

    handleError(notFoundError, {
      context: { operation: 'API GET /api/images/456' },
    });

    expect(toast.error).toHaveBeenCalledTimes(1); // Should show the 404 error
  });
});
