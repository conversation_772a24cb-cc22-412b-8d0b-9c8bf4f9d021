/**
 * Token Migration Utility
 * Ensures consistent token storage across the application
 */

import logger from '@/utils/logging/unifiedLogger';

// Unified token storage keys
export const TOKEN_KEYS = {
  ACCESS_TOKEN: 'spheroseg_access_token',
  REFRESH_TOKEN: 'spheroseg_refresh_token',
  USER_DATA: 'spheroseg_user',
} as const;

// Legacy token keys that need to be migrated
const LEGACY_TOKEN_KEYS = [
  'authToken',
  'access_token',
  'token',
  'jwt_token',
];

const LEGACY_REFRESH_KEYS = [
  'refreshToken',
  'refresh_token',
];

/**
 * Migrate legacy tokens to new format
 * This should be called on app initialization
 */
export const migrateLegacyTokens = (): void => {
  try {
    // Migrate access tokens
    for (const legacyKey of LEGACY_TOKEN_KEYS) {
      const legacyToken = localStorage.getItem(legacyKey);
      if (legacyToken && !localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN)) {
        logger.info(`Migrating legacy token from ${legacyKey} to ${TOKEN_KEYS.ACCESS_TOKEN}`);
        localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, legacyToken);
        localStorage.removeItem(legacyKey);
      }
    }

    // Migrate refresh tokens
    for (const legacyKey of LEGACY_REFRESH_KEYS) {
      const legacyToken = localStorage.getItem(legacyKey);
      if (legacyToken && !localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)) {
        logger.info(`Migrating legacy refresh token from ${legacyKey} to ${TOKEN_KEYS.REFRESH_TOKEN}`);
        localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, legacyToken);
        localStorage.removeItem(legacyKey);
      }
    }

    // Clean up any duplicate keys
    cleanupDuplicateTokens();
  } catch (error) {
    logger.error('Error during token migration:', error);
  }
};

/**
 * Clean up duplicate token storage
 */
const cleanupDuplicateTokens = (): void => {
  // Keep only the unified keys, remove all legacy ones
  const allLegacyKeys = [...LEGACY_TOKEN_KEYS, ...LEGACY_REFRESH_KEYS];
  
  for (const key of allLegacyKeys) {
    if (localStorage.getItem(key)) {
      logger.debug(`Removing duplicate token key: ${key}`);
      localStorage.removeItem(key);
    }
  }
};

/**
 * Store tokens from auth response
 * Ensures consistent storage regardless of response format
 */
export const storeAuthTokens = (response: any): void => {
  try {
    // Handle different response formats
    const accessToken = response.accessToken || response.token || response.access_token;
    const refreshToken = response.refreshToken || response.refresh_token;

    if (accessToken) {
      localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
      logger.debug('Access token stored successfully');
    }

    if (refreshToken) {
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
      logger.debug('Refresh token stored successfully');
    }

    // Store user data if present
    if (response.user) {
      localStorage.setItem(TOKEN_KEYS.USER_DATA, JSON.stringify(response.user));
    }

    // Clean up any legacy keys
    cleanupDuplicateTokens();
  } catch (error) {
    logger.error('Error storing auth tokens:', error);
  }
};

/**
 * Get access token with fallback to legacy keys
 */
export const getAccessTokenWithFallback = (): string | null => {
  // Try unified key first
  let token = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
  
  if (!token) {
    // Try legacy keys
    for (const legacyKey of LEGACY_TOKEN_KEYS) {
      token = localStorage.getItem(legacyKey);
      if (token) {
        // Migrate to unified key
        localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, token);
        localStorage.removeItem(legacyKey);
        logger.info(`Migrated token from legacy key: ${legacyKey}`);
        break;
      }
    }
  }

  return token;
};

/**
 * Clear all auth tokens
 */
export const clearAllTokens = (): void => {
  // Clear unified keys
  Object.values(TOKEN_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });

  // Clear any remaining legacy keys
  [...LEGACY_TOKEN_KEYS, ...LEGACY_REFRESH_KEYS].forEach(key => {
    localStorage.removeItem(key);
  });

  logger.debug('All auth tokens cleared');
};