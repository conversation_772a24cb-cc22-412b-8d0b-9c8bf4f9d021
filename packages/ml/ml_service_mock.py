#!/usr/bin/env python3
"""
Mock ML Service for Development
Provides basic endpoints to simulate ML service functionality
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import uuid
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)

# Enable CORS for all origins in development
CORS(app, origins=["http://localhost:3000", "http://localhost:5001", "http://localhost"], 
     allow_headers=["Content-Type", "Authorization"],
     methods=["GET", "POST", "OPTIONS"])

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "ml-mock",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0-mock"
    })

@app.route('/api/segment', methods=['POST'])
def segment():
    """Mock segmentation endpoint"""
    data = request.get_json() if request.is_json else {}
    
    # Generate mock job ID
    job_id = f"mock-{uuid.uuid4().hex[:8]}"
    
    logger.info(f"Mock segmentation request received: job_id={job_id}")
    
    return jsonify({
        "status": "processing",
        "job_id": job_id,
        "message": "Mock segmentation started",
        "estimated_time": 5  # seconds
    })

@app.route('/api/status/<job_id>', methods=['GET'])
def get_status(job_id):
    """Mock status endpoint"""
    logger.info(f"Status check for job: {job_id}")
    
    # Always return completed for mock
    return jsonify({
        "job_id": job_id,
        "status": "completed",
        "progress": 100,
        "result": {
            "polygons": [],
            "cell_count": 0,
            "processing_time": 0.5
        }
    })

@app.route('/api/cancel/<job_id>', methods=['POST'])
def cancel_job(job_id):
    """Mock cancel endpoint"""
    logger.info(f"Cancel request for job: {job_id}")
    
    return jsonify({
        "job_id": job_id,
        "status": "cancelled",
        "message": "Job cancelled successfully"
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        "error": "Not found",
        "message": str(error)
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal error: {error}")
    return jsonify({
        "error": "Internal server error",
        "message": "An error occurred processing your request"
    }), 500

if __name__ == '__main__':
    logger.info("Starting Mock ML Service on port 5002...")
    logger.info("This is a development mock service - not for production use!")
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5002,
        debug=True,
        threaded=True
    )