{"models": {"resunet_v1": {"id": "resunet_v1", "name": "ResUNet Original", "path": "/ML/checkpoint_epoch_9.pth.tar", "type": "resunet", "version": "1.0", "description": "Original ResUNet model trained on cell images", "default": true, "input_size": [256, 256], "num_classes": 2, "confidence_threshold": 0.5, "preprocessing": {"normalize": true, "resize": true}, "performance": {"accuracy": 0.92, "inference_time_ms": 150, "memory_mb": 512}, "supported_formats": ["png", "jpg", "jpeg", "tiff"]}, "resunet_enhanced": {"id": "resunet_enhanced", "name": "ResUNet Enhanced", "path": "/ML/resunet_enhanced.pth.tar", "type": "resunet", "version": "2.0", "description": "Enhanced ResUNet with improved accuracy", "default": false, "input_size": [512, 512], "num_classes": 2, "confidence_threshold": 0.6, "preprocessing": {"normalize": true, "resize": true, "denoise": true}, "performance": {"accuracy": 0.95, "inference_time_ms": 300, "memory_mb": 1024}, "supported_formats": ["png", "jpg", "jpeg", "tiff"]}, "unet_lite": {"id": "unet_lite", "name": "U-Net Lite", "path": "/ML/unet_lite.pth.tar", "type": "unet", "version": "1.0", "description": "Lightweight U-Net for faster processing", "default": false, "input_size": [256, 256], "num_classes": 2, "confidence_threshold": 0.45, "preprocessing": {"normalize": true, "resize": true}, "performance": {"accuracy": 0.88, "inference_time_ms": 50, "memory_mb": 256}, "supported_formats": ["png", "jpg", "jpeg"]}}, "settings": {"max_loaded_models": 2, "model_cache_ttl_seconds": 3600, "gpu_memory_limit_mb": 4096, "fallback_model": "resunet_v1", "allow_dynamic_loading": true}}