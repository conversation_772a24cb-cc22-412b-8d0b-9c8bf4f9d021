"""
Optimized ML Model Loader with multi-model support and caching
"""
import os
import json
import hashlib
import pickle
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
import torch
import logging
from functools import lru_cache
import requests
from tqdm import tqdm

logger = logging.getLogger(__name__)

class ModelLoader:
    """Optimized model loader with multi-model support and caching"""
    
    def __init__(self, cache_dir: str = "/tmp/ml_models", config_file: str = "models_config.json"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._model_cache: Dict[str, Any] = {}
        self.config_file = Path(__file__).parent / config_file
        self.models_config = self._load_config()
        
        # Legacy support - map old model name to new config
        self.model_urls = {
            "checkpoint_epoch_9": os.getenv(
                "ML_MODEL_URL",
                "https://storage.googleapis.com/spheroseg-models/checkpoint_epoch_9.pth.tar"
            )
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """Load models configuration"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    logger.info(f"Loaded {len(config.get('models', {}))} model configurations")
                    return config
            except Exception as e:
                logger.error(f"Failed to load models config: {e}")
        
        # Fallback to default configuration
        return {
            "models": {
                "resunet_v1": {
                    "id": "resunet_v1",
                    "name": "ResUNet Original",
                    "path": os.getenv("MODEL_PATH", "/ML/checkpoint_epoch_9.pth.tar"),
                    "type": "resunet",
                    "version": "1.0",
                    "default": True
                }
            },
            "settings": {
                "max_loaded_models": 2,
                "fallback_model": "resunet_v1"
            }
        }
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        models = []
        for model_id, model_info in self.models_config.get("models", {}).items():
            models.append({
                "id": model_id,
                "name": model_info.get("name", model_id),
                "type": model_info.get("type", "unknown"),
                "version": model_info.get("version", "1.0"),
                "description": model_info.get("description", ""),
                "default": model_info.get("default", False),
                "performance": model_info.get("performance", {})
            })
        return models
    
    def get_default_model(self) -> str:
        """Get default model ID"""
        for model_id, model_info in self.models_config.get("models", {}).items():
            if model_info.get("default", False):
                return model_id
        # Fallback to first model or configured fallback
        fallback = self.models_config.get("settings", {}).get("fallback_model", "resunet_v1")
        return fallback
    
    def get_model_config(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get configuration for specific model"""
        # Support legacy model name
        if model_id == "checkpoint_epoch_9":
            model_id = "resunet_v1"
        
        return self.models_config.get("models", {}).get(model_id)
    
    def _get_cache_path(self, model_name: str) -> Path:
        """Get cache path for model"""
        return self.cache_dir / f"{model_name}.pth.tar"
    
    def _download_model(self, model_name: str, url: str) -> Path:
        """Download model from external storage with progress bar"""
        cache_path = self._get_cache_path(model_name)
        
        if cache_path.exists():
            # Verify checksum if available
            if self._verify_checksum(cache_path, model_name):
                logger.info(f"Model {model_name} found in cache")
                return cache_path
        
        logger.info(f"Downloading model {model_name} from {url}")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(cache_path, 'wb') as f:
                with tqdm(total=total_size, unit='B', unit_scale=True, desc=model_name) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            logger.info(f"Model {model_name} downloaded successfully")
            return cache_path
            
        except Exception as e:
            logger.error(f"Failed to download model {model_name}: {e}")
            # Fallback to local model if exists
            local_path = Path(__file__).parent / f"{model_name}.pth.tar"
            if local_path.exists():
                logger.warning(f"Using local model at {local_path}")
                return local_path
            raise
    
    def _verify_checksum(self, file_path: Path, model_name: str) -> bool:
        """Verify model checksum"""
        expected_checksums = {
            "checkpoint_epoch_9": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
            "resunet_v1": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        }
        
        if model_name not in expected_checksums:
            return True  # No checksum available
        
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        
        actual = sha256_hash.hexdigest()
        expected = expected_checksums[model_name]
        
        if actual != expected:
            logger.warning(f"Checksum mismatch for {model_name}: {actual} != {expected}")
            return False
        
        return True
    
    def _get_model_path(self, model_id: str) -> Path:
        """Get model path from configuration or legacy location"""
        config = self.get_model_config(model_id)
        
        if config:
            model_path = Path(config.get("path", ""))
            if model_path.exists():
                return model_path
        
        # Check for downloadable model
        if model_id in self.model_urls:
            return self._download_model(model_id, self.model_urls[model_id])
        
        # Legacy path support
        if model_id == "checkpoint_epoch_9" or model_id == "resunet_v1":
            legacy_path = Path(os.getenv("MODEL_PATH", "/ML/checkpoint_epoch_9.pth.tar"))
            if legacy_path.exists():
                return legacy_path
        
        # Try cache directory
        cache_path = self._get_cache_path(model_id)
        if cache_path.exists():
            return cache_path
        
        # Try local directory
        local_path = Path(__file__).parent / f"{model_id}.pth.tar"
        if local_path.exists():
            return local_path
        
        raise FileNotFoundError(f"Model {model_id} not found")
    
    def _create_model_instance(self, model_type: str, config: Dict[str, Any]):
        """Create model instance based on type"""
        if model_type == "resunet":
            from model import ResUNet
            return ResUNet(
                num_classes=config.get("num_classes", 2),
                input_channels=config.get("input_channels", 3)
            )
        elif model_type == "unet":
            # Import U-Net implementation when available
            from model import ResUNet  # Using ResUNet as fallback
            return ResUNet(
                num_classes=config.get("num_classes", 2),
                input_channels=config.get("input_channels", 3)
            )
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    @lru_cache(maxsize=4)  # Increased cache size for multiple models
    def load_model(self, model_id: str = None, device: str = "cuda") -> Any:
        """Load model with caching and lazy loading"""
        
        # Use default model if not specified
        if model_id is None:
            model_id = self.get_default_model()
        
        # Support legacy model name
        if model_id == "checkpoint_epoch_9":
            model_id = "resunet_v1"
        
        # Check in-memory cache first
        cache_key = f"{model_id}_{device}"
        if cache_key in self._model_cache:
            logger.info(f"Loading model {model_id} from memory cache")
            return self._model_cache[cache_key]
        
        # Check if we need to evict models due to memory limits
        max_models = self.models_config.get("settings", {}).get("max_loaded_models", 2)
        if len(self._model_cache) >= max_models:
            # Evict least recently used model
            oldest_key = next(iter(self._model_cache))
            del self._model_cache[oldest_key]
            logger.info(f"Evicted model {oldest_key} from cache")
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        start_time = time.time()
        
        # Get model configuration
        config = self.get_model_config(model_id)
        if not config:
            raise ValueError(f"Model {model_id} not found in configuration")
        
        # Get model path
        model_path = self._get_model_path(model_id)
        
        # Load model with optimizations
        logger.info(f"Loading model {model_id} from {model_path}")
        
        # Use map_location to load directly to target device
        if device == "cuda" and not torch.cuda.is_available():
            device = "cpu"
            logger.warning("CUDA not available, falling back to CPU")
        
        checkpoint = torch.load(
            model_path,
            map_location=device,
            weights_only=False  # Set to True if possible for security
        )
        
        # Extract model from checkpoint
        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                model = checkpoint['model']
            elif 'state_dict' in checkpoint:
                # Create model instance and load state dict
                model = self._create_model_instance(config.get("type", "resunet"), config)
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model = checkpoint
        else:
            model = checkpoint
        
        # Move to device and set to eval mode
        if hasattr(model, 'to'):
            model = model.to(device)
        if hasattr(model, 'eval'):
            model.eval()
        
        # Enable mixed precision if available and configured
        if device == "cuda" and config.get("use_fp16", True):
            try:
                model = model.half()  # Use FP16 for faster inference
                logger.info("Using FP16 precision for faster inference")
            except:
                logger.info("FP16 not supported, using FP32")
        
        # Cache the model
        self._model_cache[cache_key] = model
        
        load_time = time.time() - start_time
        logger.info(f"Model {model_id} loaded in {load_time:.2f} seconds")
        
        return model
    
    def preload_model(self, model_id: str = None, device: str = "cuda"):
        """Preload model into cache"""
        if model_id is None:
            model_id = self.get_default_model()
        self.load_model(model_id, device)
    
    def clear_cache(self):
        """Clear model cache to free memory"""
        self._model_cache.clear()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        logger.info("Model cache cleared")
    
    def get_model_info(self, model_id: str = None) -> Dict[str, Any]:
        """Get model information without loading it"""
        if model_id is None:
            model_id = self.get_default_model()
        
        config = self.get_model_config(model_id)
        if not config:
            return {"error": f"Model {model_id} not found"}
        
        try:
            model_path = self._get_model_path(model_id)
            file_exists = model_path.exists()
            
            info = {
                "id": model_id,
                "name": config.get("name", model_id),
                "type": config.get("type", "unknown"),
                "version": config.get("version", "1.0"),
                "description": config.get("description", ""),
                "path": str(model_path) if file_exists else "Not found",
                "exists": file_exists,
                "cached": f"{model_id}_cuda" in self._model_cache or f"{model_id}_cpu" in self._model_cache,
                "config": config
            }
            
            if file_exists:
                info.update({
                    "size_mb": model_path.stat().st_size / (1024 * 1024),
                    "last_modified": time.ctime(model_path.stat().st_mtime)
                })
            
            return info
            
        except Exception as e:
            return {
                "id": model_id,
                "name": config.get("name", model_id),
                "error": str(e)
            }
    
    def get_all_models_info(self) -> List[Dict[str, Any]]:
        """Get information about all configured models"""
        models_info = []
        for model_id in self.models_config.get("models", {}).keys():
            models_info.append(self.get_model_info(model_id))
        return models_info

# Global model loader instance
model_loader = ModelLoader()

# Preload default model on startup if configured
if os.getenv("PRELOAD_MODEL", "false").lower() == "true":
    default_model = model_loader.get_default_model()
    logger.info(f"Preloading default model: {default_model}")
    model_loader.preload_model(default_model)