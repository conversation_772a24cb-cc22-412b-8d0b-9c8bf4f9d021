/**
 * Export E2E Tests
 * Tests for data export functionality with new architecture
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import path from 'path';
import fs from 'fs/promises';
import AdmZip from 'adm-zip';
import { parse as parseCSV } from 'csv-parse/sync';
import * as XLSX from 'xlsx';
import { e2eConfig } from '../config/e2e.config';
import { setupTestDatabase, teardownTestDatabase } from '../helpers/database';
import { createTestUser, getAuthToken } from '../helpers/auth';
import { createTestProject, uploadTestImages } from '../helpers/projects';

const API_URL = e2eConfig.baseUrl;

describe('Export E2E Tests', () => {
  let authToken: string;
  let projectId: string;
  let imageIds: string[] = [];
  
  beforeAll(async () => {
    await setupTestDatabase();
    const user = await createTestUser(e2eConfig.auth.testUser);
    authToken = await getAuthToken(user);
    
    // Create project with test data
    projectId = await createTestProject(authToken, {
      name: 'Export Test Project',
      description: 'Project for export testing'
    });
    
    // Upload test images with mock segmentation
    imageIds = await uploadTestImages(authToken, projectId, 5);
  });
  
  afterAll(async () => {
    await teardownTestDatabase();
  });
  
  describe('CSV Export', () => {
    it('should export project data to CSV', async () => {
      const response = await request(API_URL)
        .get(`/api/export/csv/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect('Content-Type', /text\/csv/);
      
      expect(response.status).toBe(200);
      expect(response.headers['content-disposition']).toContain('.csv');
      
      // Parse CSV content
      const csvData = parseCSV(response.text, {
        columns: true,
        skip_empty_lines: true
      });
      
      expect(csvData).toHaveLength(5); // 5 images
      expect(csvData[0]).toHaveProperty('Image ID');
      expect(csvData[0]).toHaveProperty('Cell Count');
      expect(csvData[0]).toHaveProperty('Average Area');
    });
    
    it('should export filtered data to CSV', async () => {
      const response = await request(API_URL)
        .post(`/api/export/csv/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageIds: imageIds.slice(0, 2), // Only first 2 images
          includeMetrics: true
        })
        .expect('Content-Type', /text\/csv/);
      
      expect(response.status).toBe(200);
      
      const csvData = parseCSV(response.text, {
        columns: true,
        skip_empty_lines: true
      });
      
      expect(csvData).toHaveLength(2);
    });
  });
  
  describe('Excel Export', () => {
    it('should export project data to Excel', async () => {
      const response = await request(API_URL)
        .get(`/api/export/excel/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect('Content-Type', /spreadsheetml/);
      
      expect(response.status).toBe(200);
      expect(response.headers['content-disposition']).toContain('.xlsx');
      
      // Parse Excel content
      const workbook = XLSX.read(response.body, { type: 'buffer' });
      
      expect(workbook.SheetNames).toContain('Images');
      expect(workbook.SheetNames).toContain('Statistics');
      
      const worksheet = workbook.Sheets['Images'];
      const data = XLSX.utils.sheet_to_json(worksheet);
      
      expect(data).toHaveLength(5);
      expect(data[0]).toHaveProperty('Image ID');
    });
    
    it('should include charts in Excel export', async () => {
      const response = await request(API_URL)
        .post(`/api/export/excel/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          includeCharts: true,
          includeStatistics: true
        });
      
      expect(response.status).toBe(200);
      
      const workbook = XLSX.read(response.body, { type: 'buffer' });
      expect(workbook.SheetNames).toContain('Charts');
    });
  });
  
  describe('JSON Export', () => {
    it('should export to standard JSON format', async () => {
      const response = await request(API_URL)
        .get(`/api/export/json/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect('Content-Type', /application\/json/);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('project');
      expect(response.body).toHaveProperty('images');
      expect(response.body.images).toHaveLength(5);
      expect(response.body.images[0]).toHaveProperty('segmentation');
    });
    
    it('should export to COCO format', async () => {
      const response = await request(API_URL)
        .get(`/api/export/coco/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect('Content-Type', /application\/json/);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('images');
      expect(response.body).toHaveProperty('annotations');
      expect(response.body).toHaveProperty('categories');
      
      // COCO format validation
      expect(response.body.info).toHaveProperty('description');
      expect(response.body.categories).toHaveLength(1); // Cell category
      expect(response.body.annotations[0]).toHaveProperty('segmentation');
      expect(response.body.annotations[0]).toHaveProperty('bbox');
    });
    
    it('should export to YOLO format', async () => {
      const response = await request(API_URL)
        .get(`/api/export/yolo/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      
      // YOLO format returns a zip file
      expect(response.headers['content-type']).toContain('zip');
      
      // Save and extract zip
      const zipPath = path.join(e2eConfig.files.testDataPath, 'yolo-export.zip');
      await fs.mkdir(path.dirname(zipPath), { recursive: true });
      await fs.writeFile(zipPath, response.body);
      
      const zip = new AdmZip(zipPath);
      const entries = zip.getEntries();
      
      // Check for YOLO structure
      expect(entries.some(e => e.entryName === 'data.yaml')).toBe(true);
      expect(entries.some(e => e.entryName.includes('labels/'))).toBe(true);
      expect(entries.some(e => e.entryName.includes('images/'))).toBe(true);
      
      // Clean up
      await fs.unlink(zipPath).catch(() => {});
    });
  });
  
  describe('ZIP Export', () => {
    it('should export complete project as ZIP', async () => {
      const response = await request(API_URL)
        .post(`/api/export/zip/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          includeImages: true,
          includeMasks: true,
          includeMetadata: true,
          includeAnnotations: true
        });
      
      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('zip');
      
      // Save and extract zip
      const zipPath = path.join(e2eConfig.files.testDataPath, 'complete-export.zip');
      await fs.mkdir(path.dirname(zipPath), { recursive: true });
      await fs.writeFile(zipPath, response.body);
      
      const zip = new AdmZip(zipPath);
      const entries = zip.getEntries();
      
      // Verify ZIP contents
      expect(entries.some(e => e.entryName === 'metadata.json')).toBe(true);
      expect(entries.some(e => e.entryName.includes('images/'))).toBe(true);
      expect(entries.some(e => e.entryName.includes('masks/'))).toBe(true);
      expect(entries.some(e => e.entryName.includes('annotations/'))).toBe(true);
      
      // Check metadata
      const metadataEntry = zip.getEntry('metadata.json');
      const metadata = JSON.parse(metadataEntry!.getData().toString());
      expect(metadata).toHaveProperty('project');
      expect(metadata).toHaveProperty('exportDate');
      expect(metadata).toHaveProperty('imageCount');
      expect(metadata.imageCount).toBe(5);
      
      // Clean up
      await fs.unlink(zipPath).catch(() => {});
    });
    
    it('should export selected images only', async () => {
      const response = await request(API_URL)
        .post(`/api/export/zip/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageIds: imageIds.slice(0, 2),
          includeImages: true
        });
      
      expect(response.status).toBe(200);
      
      const zipPath = path.join(e2eConfig.files.testDataPath, 'partial-export.zip');
      await fs.writeFile(zipPath, response.body);
      
      const zip = new AdmZip(zipPath);
      const imageEntries = zip.getEntries().filter(e => e.entryName.includes('images/'));
      
      expect(imageEntries).toHaveLength(2);
      
      // Clean up
      await fs.unlink(zipPath).catch(() => {});
    });
  });
  
  describe('Export Progress Tracking', () => {
    it('should track export progress for large exports', async function() {
      this.timeout(e2eConfig.timeout.longRunning);
      
      // Start large export
      const startResponse = await request(API_URL)
        .post(`/api/export/async/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          format: 'zip',
          includeEverything: true
        });
      
      expect(startResponse.status).toBe(202);
      expect(startResponse.body).toHaveProperty('exportId');
      
      const exportId = startResponse.body.exportId;
      
      // Check progress
      let completed = false;
      let attempts = 0;
      
      while (!completed && attempts < 10) {
        const progressResponse = await request(API_URL)
          .get(`/api/export/progress/${exportId}`)
          .set('Authorization', `Bearer ${authToken}`);
        
        expect(progressResponse.status).toBe(200);
        expect(progressResponse.body).toHaveProperty('progress');
        expect(progressResponse.body).toHaveProperty('status');
        
        if (progressResponse.body.status === 'completed') {
          completed = true;
          expect(progressResponse.body).toHaveProperty('downloadUrl');
        }
        
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      expect(completed).toBe(true);
    });
  });
  
  describe('Export Formats Validation', () => {
    it('should validate export format compatibility', async () => {
      const response = await request(API_URL)
        .get('/api/export/formats')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      
      const formats = response.body;
      expect(formats).toContain('csv');
      expect(formats).toContain('excel');
      expect(formats).toContain('json');
      expect(formats).toContain('coco');
      expect(formats).toContain('yolo');
      expect(formats).toContain('zip');
    });
    
    it('should reject invalid export format', async () => {
      const response = await request(API_URL)
        .get(`/api/export/invalid-format/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('format');
    });
  });
});