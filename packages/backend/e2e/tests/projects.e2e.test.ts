/**
 * Projects E2E Tests
 * Tests for project management with new architecture
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import path from 'path';
import fs from 'fs/promises';
import { e2eConfig } from '../config/e2e.config';
import { setupTestDatabase, teardownTestDatabase } from '../helpers/database';
import { createTestUser, getAuthToken } from '../helpers/auth';

const API_URL = e2eConfig.baseUrl;

describe('Projects E2E Tests', () => {
  let authToken: string;
  let userId: string;
  let projectId: string;
  
  beforeAll(async () => {
    await setupTestDatabase();
    const user = await createTestUser(e2eConfig.auth.testUser);
    userId = user.id;
    authToken = await getAuthToken(user);
  });
  
  afterAll(async () => {
    await teardownTestDatabase();
  });
  
  beforeEach(async () => {
    // Clean up test projects
    if (projectId) {
      await request(API_URL)
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      projectId = '';
    }
  });
  
  describe('Project Creation', () => {
    it('should create a new project', async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Project',
          description: 'E2E test project',
          settings: {
            autoSegmentation: true,
            confidenceThreshold: 0.7
          }
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('Test Project');
      expect(response.body.userId).toBe(userId);
      
      projectId = response.body.id;
    });
    
    it('should reject project with invalid name', async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: '',
          description: 'Invalid project'
        });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('name');
    });
    
    it('should reject unauthorized project creation', async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .send({
          name: 'Unauthorized Project'
        });
      
      expect(response.status).toBe(401);
    });
  });
  
  describe('Project Retrieval', () => {
    beforeEach(async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Project for Retrieval',
          description: 'Test description'
        });
      projectId = response.body.id;
    });
    
    it('should get project by ID', async () => {
      const response = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(projectId);
      expect(response.body.name).toBe('Test Project for Retrieval');
    });
    
    it('should list user projects', async () => {
      const response = await request(API_URL)
        .get('/api/projects')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body.some((p: any) => p.id === projectId)).toBe(true);
    });
    
    it('should return 404 for non-existent project', async () => {
      const response = await request(API_URL)
        .get('/api/projects/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(404);
    });
  });
  
  describe('Project Update', () => {
    beforeEach(async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Original Name',
          description: 'Original description'
        });
      projectId = response.body.id;
    });
    
    it('should update project details', async () => {
      const response = await request(API_URL)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Updated Name',
          description: 'Updated description',
          settings: {
            autoSegmentation: false
          }
        });
      
      expect(response.status).toBe(200);
      expect(response.body.name).toBe('Updated Name');
      expect(response.body.description).toBe('Updated description');
    });
    
    it('should reject update with invalid data', async () => {
      const response = await request(API_URL)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: ''
        });
      
      expect(response.status).toBe(400);
    });
  });
  
  describe('Project Deletion', () => {
    beforeEach(async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Project to Delete'
        });
      projectId = response.body.id;
    });
    
    it('should delete project', async () => {
      const response = await request(API_URL)
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      
      // Verify deletion
      const getResponse = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(getResponse.status).toBe(404);
    });
    
    it('should cascade delete project images', async () => {
      // First upload an image
      const imagePath = path.join(e2eConfig.files.testImagesPath, 'test.jpg');
      await fs.mkdir(path.dirname(imagePath), { recursive: true });
      await fs.writeFile(imagePath, Buffer.from('fake-image-data'));
      
      const uploadResponse = await request(API_URL)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${authToken}`)
        .attach('image', imagePath);
      
      expect(uploadResponse.status).toBe(201);
      
      // Delete project
      const deleteResponse = await request(API_URL)
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(deleteResponse.status).toBe(200);
      
      // Clean up
      await fs.unlink(imagePath).catch(() => {});
    });
  });
  
  describe('Project Sharing', () => {
    let otherUserToken: string;
    
    beforeAll(async () => {
      const otherUser = await createTestUser({
        email: '<EMAIL>',
        password: 'OtherPass123!',
        name: 'Other User'
      });
      otherUserToken = await getAuthToken(otherUser);
    });
    
    beforeEach(async () => {
      const response = await request(API_URL)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Shared Project'
        });
      projectId = response.body.id;
    });
    
    it('should share project with another user', async () => {
      const response = await request(API_URL)
        .post(`/api/projects/${projectId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          permission: 'read'
        });
      
      expect(response.status).toBe(200);
      
      // Other user should be able to access
      const accessResponse = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${otherUserToken}`);
      
      expect(accessResponse.status).toBe(200);
    });
    
    it('should prevent unauthorized access to unshared project', async () => {
      const response = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${otherUserToken}`);
      
      expect(response.status).toBe(403);
    });
  });
});