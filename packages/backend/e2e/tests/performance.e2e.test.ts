/**
 * Performance E2E Tests
 * Tests for performance and scalability with new architecture
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { performance } from 'perf_hooks';
import { e2eConfig } from '../config/e2e.config';
import { setupTestDatabase, teardownTestDatabase } from '../helpers/database';
import { createTestUser, getAuthToken } from '../helpers/auth';
import { createTestProject, uploadTestImages } from '../helpers/projects';

const API_URL = e2eConfig.baseUrl;

describe('Performance E2E Tests', () => {
  let authToken: string;
  let projectId: string;
  
  beforeAll(async () => {
    await setupTestDatabase();
    const user = await createTestUser(e2eConfig.auth.testUser);
    authToken = await getAuthToken(user);
    projectId = await createTestProject(authToken, {
      name: 'Performance Test Project'
    });
  });
  
  afterAll(async () => {
    await teardownTestDatabase();
  });
  
  describe('API Response Times', () => {
    it('should respond to health check within 100ms', async () => {
      const start = performance.now();
      
      const response = await request(API_URL)
        .get('/api/health');
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(100);
    });
    
    it('should authenticate user within 500ms', async () => {
      const start = performance.now();
      
      const response = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: e2eConfig.auth.testUser.password
        });
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(500);
    });
    
    it('should list projects within 300ms', async () => {
      const start = performance.now();
      
      const response = await request(API_URL)
        .get('/api/projects')
        .set('Authorization', `Bearer ${authToken}`);
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(300);
    });
  });
  
  describe('Database Query Performance', () => {
    beforeAll(async () => {
      // Create test data
      for (let i = 0; i < 10; i++) {
        await createTestProject(authToken, {
          name: `Perf Test ${i}`
        });
      }
    });
    
    it('should handle pagination efficiently', async () => {
      const start = performance.now();
      
      const response = await request(API_URL)
        .get('/api/projects?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`);
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
    });
    
    it('should search projects efficiently', async () => {
      const start = performance.now();
      
      const response = await request(API_URL)
        .get('/api/projects/search?q=Perf')
        .set('Authorization', `Bearer ${authToken}`);
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(300);
    });
  });
  
  describe('File Upload Performance', () => {
    it('should handle single file upload within 1s', async function() {
      this.timeout(5000);
      
      const start = performance.now();
      
      // Create 1MB test file
      const buffer = Buffer.alloc(1024 * 1024);
      const response = await request(API_URL)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${authToken}`)
        .attach('images', buffer, 'test-1mb.jpg');
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(201);
      expect(duration).toBeLessThan(1000);
    });
    
    it('should handle batch upload efficiently', async function() {
      this.timeout(10000);
      
      const start = performance.now();
      
      // Upload 5 files simultaneously
      const uploadRequest = request(API_URL)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${authToken}`);
      
      for (let i = 0; i < 5; i++) {
        const buffer = Buffer.alloc(500 * 1024); // 500KB each
        uploadRequest.attach('images', buffer, `test-${i}.jpg`);
      }
      
      const response = await uploadRequest;
      const duration = performance.now() - start;
      
      expect(response.status).toBe(201);
      expect(response.body.images).toHaveLength(5);
      expect(duration).toBeLessThan(3000);
    });
  });
  
  describe('Concurrent Request Handling', () => {
    it('should handle 10 concurrent requests', async function() {
      this.timeout(5000);
      
      const requests = [];
      const start = performance.now();
      
      // Send 10 concurrent requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(API_URL)
            .get('/api/projects')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }
      
      const responses = await Promise.all(requests);
      const duration = performance.now() - start;
      
      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(2000);
    });
    
    it('should handle mixed concurrent operations', async function() {
      this.timeout(5000);
      
      const start = performance.now();
      
      const operations = [
        // Read operations
        request(API_URL).get('/api/projects').set('Authorization', `Bearer ${authToken}`),
        request(API_URL).get(`/api/projects/${projectId}`).set('Authorization', `Bearer ${authToken}`),
        
        // Write operations
        request(API_URL)
          .post('/api/projects')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ name: 'Concurrent Test 1' }),
        request(API_URL)
          .post('/api/projects')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ name: 'Concurrent Test 2' }),
        
        // Search operation
        request(API_URL).get('/api/projects/search?q=test').set('Authorization', `Bearer ${authToken}`)
      ];
      
      const responses = await Promise.all(operations);
      const duration = performance.now() - start;
      
      // Check all succeeded
      expect(responses[0].status).toBe(200); // List
      expect(responses[1].status).toBe(200); // Get
      expect(responses[2].status).toBe(201); // Create 1
      expect(responses[3].status).toBe(201); // Create 2
      expect(responses[4].status).toBe(200); // Search
      
      expect(duration).toBeLessThan(3000);
    });
  });
  
  describe('Memory Usage', () => {
    it('should not leak memory on repeated operations', async function() {
      this.timeout(30000);
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform 100 operations
      for (let i = 0; i < 100; i++) {
        await request(API_URL)
          .get('/api/projects')
          .set('Authorization', `Bearer ${authToken}`);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be minimal (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
  
  describe('Cache Performance', () => {
    it('should serve cached responses faster', async () => {
      // First request (cache miss)
      const firstStart = performance.now();
      const firstResponse = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      const firstDuration = performance.now() - firstStart;
      
      expect(firstResponse.status).toBe(200);
      
      // Second request (cache hit)
      const secondStart = performance.now();
      const secondResponse = await request(API_URL)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      const secondDuration = performance.now() - secondStart;
      
      expect(secondResponse.status).toBe(200);
      
      // Cached response should be at least 50% faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
  });
  
  describe('Export Performance', () => {
    beforeAll(async () => {
      // Create project with many images
      await uploadTestImages(authToken, projectId, 20);
    });
    
    it('should export CSV within 2s for 20 images', async function() {
      this.timeout(5000);
      
      const start = performance.now();
      
      const response = await request(API_URL)
        .get(`/api/export/csv/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(2000);
    });
    
    it('should stream large exports efficiently', async function() {
      this.timeout(10000);
      
      const start = performance.now();
      let bytesReceived = 0;
      
      const response = await request(API_URL)
        .get(`/api/export/zip/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .buffer(false)
        .parse((res, callback) => {
          res.on('data', (chunk) => {
            bytesReceived += chunk.length;
          });
          res.on('end', () => {
            callback(null, bytesReceived);
          });
        });
      
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(bytesReceived).toBeGreaterThan(0);
      
      // Calculate throughput (MB/s)
      const throughput = (bytesReceived / 1024 / 1024) / (duration / 1000);
      expect(throughput).toBeGreaterThan(1); // At least 1 MB/s
    });
  });
  
  describe('Load Testing', () => {
    it('should handle sustained load', async function() {
      this.timeout(60000);
      
      const duration = 10000; // 10 seconds
      const startTime = Date.now();
      let requestCount = 0;
      let errorCount = 0;
      const responseTimes: number[] = [];
      
      while (Date.now() - startTime < duration) {
        const requestStart = performance.now();
        
        try {
          const response = await request(API_URL)
            .get('/api/health');
          
          if (response.status !== 200) {
            errorCount++;
          }
          
          responseTimes.push(performance.now() - requestStart);
          requestCount++;
        } catch (error) {
          errorCount++;
        }
      }
      
      // Calculate statistics
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);
      
      // Performance assertions
      expect(errorCount).toBe(0);
      expect(requestCount).toBeGreaterThan(100); // At least 10 req/s
      expect(avgResponseTime).toBeLessThan(100);
      expect(maxResponseTime).toBeLessThan(500);
      
      console.log(`Load test results:
        Requests: ${requestCount}
        Errors: ${errorCount}
        Avg Response: ${avgResponseTime.toFixed(2)}ms
        Min Response: ${minResponseTime.toFixed(2)}ms
        Max Response: ${maxResponseTime.toFixed(2)}ms
        Throughput: ${(requestCount / (duration / 1000)).toFixed(2)} req/s
      `);
    });
  });
});