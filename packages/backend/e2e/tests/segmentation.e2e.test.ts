/**
 * Segmentation E2E Tests
 * Tests for ML segmentation pipeline with new architecture
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import path from 'path';
import fs from 'fs/promises';
import { e2eConfig } from '../config/e2e.config';
import { setupTestDatabase, teardownTestDatabase } from '../helpers/database';
import { createTestUser, getAuthToken } from '../helpers/auth';
import { waitForJobCompletion } from '../helpers/queue';

const API_URL = e2eConfig.baseUrl;

describe('Segmentation E2E Tests', () => {
  let authToken: string;
  let projectId: string;
  let imageId: string;
  
  beforeAll(async () => {
    await setupTestDatabase();
    const user = await createTestUser(e2eConfig.auth.testUser);
    authToken = await getAuthToken(user);
    
    // Create test project
    const projectResponse = await request(API_URL)
      .post('/api/projects')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Segmentation Test Project',
        settings: {
          autoSegmentation: true,
          modelType: 'resunet'
        }
      });
    projectId = projectResponse.body.id;
  });
  
  afterAll(async () => {
    await teardownTestDatabase();
  });
  
  describe('Image Upload and Processing', () => {
    it('should upload image and trigger segmentation', async () => {
      // Create test image
      const imagePath = path.join(e2eConfig.files.testImagesPath, 'cell-image.jpg');
      await fs.mkdir(path.dirname(imagePath), { recursive: true });
      
      // Create a simple test image buffer
      const imageBuffer = Buffer.from('fake-image-data-for-testing');
      await fs.writeFile(imagePath, imageBuffer);
      
      // Upload image
      const uploadResponse = await request(API_URL)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${authToken}`)
        .attach('images', imagePath);
      
      expect(uploadResponse.status).toBe(201);
      expect(uploadResponse.body).toHaveProperty('images');
      expect(uploadResponse.body.images).toHaveLength(1);
      
      imageId = uploadResponse.body.images[0].id;
      
      // Clean up
      await fs.unlink(imagePath).catch(() => {});
    });
    
    it('should get segmentation status', async () => {
      const response = await request(API_URL)
        .get(`/api/segmentation/status/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(['pending', 'processing', 'completed', 'failed']).toContain(response.body.status);
    });
    
    it('should process segmentation job', async function() {
      this.timeout(e2eConfig.timeout.longRunning);
      
      // Trigger segmentation
      const triggerResponse = await request(API_URL)
        .post(`/api/segmentation/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageId,
          modelType: 'resunet',
          parameters: {
            threshold: 0.5,
            minArea: 10
          }
        });
      
      expect(triggerResponse.status).toBe(202);
      expect(triggerResponse.body).toHaveProperty('jobId');
      
      const jobId = triggerResponse.body.jobId;
      
      // Wait for completion (mocked in test mode)
      if (e2eConfig.ml.mockMode) {
        await new Promise(resolve => setTimeout(resolve, e2eConfig.ml.mockResponseTime));
      } else {
        await waitForJobCompletion(jobId, e2eConfig.timeout.longRunning);
      }
      
      // Check results
      const resultResponse = await request(API_URL)
        .get(`/api/segmentation/results/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(resultResponse.status).toBe(200);
      expect(resultResponse.body).toHaveProperty('polygons');
      expect(Array.isArray(resultResponse.body.polygons)).toBe(true);
    });
  });
  
  describe('Batch Processing', () => {
    let imageIds: string[] = [];
    
    beforeEach(async () => {
      imageIds = [];
      
      // Upload multiple images
      for (let i = 0; i < 3; i++) {
        const imagePath = path.join(e2eConfig.files.testImagesPath, `batch-${i}.jpg`);
        await fs.mkdir(path.dirname(imagePath), { recursive: true });
        await fs.writeFile(imagePath, Buffer.from(`fake-image-${i}`));
        
        const response = await request(API_URL)
          .post(`/api/projects/${projectId}/images`)
          .set('Authorization', `Bearer ${authToken}`)
          .attach('images', imagePath);
        
        imageIds.push(response.body.images[0].id);
        
        await fs.unlink(imagePath).catch(() => {});
      }
    });
    
    it('should process batch segmentation', async function() {
      this.timeout(e2eConfig.timeout.longRunning);
      
      const response = await request(API_URL)
        .post('/api/segmentation/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageIds,
          modelType: 'resunet',
          parameters: {
            threshold: 0.5
          }
        });
      
      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('jobs');
      expect(response.body.jobs).toHaveLength(3);
      
      // Wait for all jobs to complete
      if (e2eConfig.ml.mockMode) {
        await new Promise(resolve => setTimeout(resolve, e2eConfig.ml.mockResponseTime));
      }
      
      // Check all results
      for (const imageId of imageIds) {
        const resultResponse = await request(API_URL)
          .get(`/api/segmentation/results/${imageId}`)
          .set('Authorization', `Bearer ${authToken}`);
        
        expect(resultResponse.status).toBe(200);
      }
    });
    
    it('should cancel batch processing', async () => {
      const response = await request(API_URL)
        .post('/api/segmentation/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageIds,
          modelType: 'resunet'
        });
      
      expect(response.status).toBe(202);
      
      const batchId = response.body.batchId;
      
      // Cancel batch
      const cancelResponse = await request(API_URL)
        .delete(`/api/segmentation/batch/${batchId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(cancelResponse.status).toBe(200);
      expect(cancelResponse.body.message).toContain('cancelled');
    });
  });
  
  describe('Manual Corrections', () => {
    beforeEach(async () => {
      // Create and process an image
      const imagePath = path.join(e2eConfig.files.testImagesPath, 'correction-test.jpg');
      await fs.mkdir(path.dirname(imagePath), { recursive: true });
      await fs.writeFile(imagePath, Buffer.from('fake-image'));
      
      const uploadResponse = await request(API_URL)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${authToken}`)
        .attach('images', imagePath);
      
      imageId = uploadResponse.body.images[0].id;
      
      await fs.unlink(imagePath).catch(() => {});
      
      // Add mock segmentation result
      await request(API_URL)
        .post(`/api/segmentation/mock-result`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          imageId,
          polygons: [
            { points: [[0, 0], [10, 0], [10, 10], [0, 10]], area: 100 },
            { points: [[20, 20], [30, 20], [30, 30], [20, 30]], area: 100 }
          ]
        });
    });
    
    it('should save manual corrections', async () => {
      const response = await request(API_URL)
        .put(`/api/segmentation/corrections/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          polygons: [
            { points: [[0, 0], [10, 0], [10, 10], [0, 10]], area: 100 },
            { points: [[20, 20], [35, 20], [35, 35], [20, 35]], area: 225 }, // Modified
            { points: [[40, 40], [50, 40], [50, 50], [40, 50]], area: 100 }  // Added
          ],
          editedBy: 'user',
          editedAt: new Date().toISOString()
        });
      
      expect(response.status).toBe(200);
      expect(response.body.polygons).toHaveLength(3);
    });
    
    it('should track correction history', async () => {
      // Make first correction
      await request(API_URL)
        .put(`/api/segmentation/corrections/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          polygons: [
            { points: [[0, 0], [15, 0], [15, 15], [0, 15]], area: 225 }
          ]
        });
      
      // Make second correction
      await request(API_URL)
        .put(`/api/segmentation/corrections/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          polygons: [
            { points: [[0, 0], [20, 0], [20, 20], [0, 20]], area: 400 }
          ]
        });
      
      // Get history
      const historyResponse = await request(API_URL)
        .get(`/api/segmentation/history/${imageId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(historyResponse.status).toBe(200);
      expect(historyResponse.body).toHaveProperty('versions');
      expect(historyResponse.body.versions.length).toBeGreaterThanOrEqual(2);
    });
  });
  
  describe('Model Management', () => {
    it('should list available models', async () => {
      const response = await request(API_URL)
        .get('/api/segmentation/models')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('type');
      expect(response.body[0]).toHaveProperty('version');
    });
    
    it('should get model details', async () => {
      const response = await request(API_URL)
        .get('/api/segmentation/models/resunet')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('parameters');
      expect(response.body).toHaveProperty('performance');
    });
  });
});