/**
 * Authentication E2E Tests
 * Tests for authentication flow with new architecture
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import { e2eConfig } from '../config/e2e.config';
import { setupTestDatabase, teardownTestDatabase } from '../helpers/database';
import { createTestUser, clearTestUsers } from '../helpers/auth';

const API_URL = e2eConfig.baseUrl;

describe('Authentication E2E Tests', () => {
  let authToken: string;
  
  beforeAll(async () => {
    await setupTestDatabase();
  });
  
  afterAll(async () => {
    await teardownTestDatabase();
  });
  
  beforeEach(async () => {
    await clearTestUsers();
  });
  
  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      const response = await request(API_URL)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          name: 'New User'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body).toHaveProperty('token');
    });
    
    it('should reject registration with invalid email', async () => {
      const response = await request(API_URL)
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          password: 'SecurePass123!',
          name: 'Test User'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('email');
    });
    
    it('should reject weak passwords', async () => {
      const response = await request(API_URL)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123',
          name: 'Test User'
        });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('password');
    });
    
    it('should prevent duplicate email registration', async () => {
      await createTestUser(e2eConfig.auth.testUser);
      
      const response = await request(API_URL)
        .post('/api/auth/register')
        .send(e2eConfig.auth.testUser);
      
      expect(response.status).toBe(409);
      expect(response.body.error).toContain('already exists');
    });
  });
  
  describe('User Login', () => {
    beforeEach(async () => {
      await createTestUser(e2eConfig.auth.testUser);
    });
    
    it('should login with valid credentials', async () => {
      const response = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: e2eConfig.auth.testUser.password
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(e2eConfig.auth.testUser.email);
      
      authToken = response.body.token;
    });
    
    it('should reject invalid password', async () => {
      const response = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: 'WrongPassword123!'
        });
      
      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid credentials');
    });
    
    it('should reject non-existent user', async () => {
      const response = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!'
        });
      
      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid credentials');
    });
  });
  
  describe('Protected Routes', () => {
    beforeEach(async () => {
      const user = await createTestUser(e2eConfig.auth.testUser);
      const loginResponse = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: e2eConfig.auth.testUser.password
        });
      authToken = loginResponse.body.token;
    });
    
    it('should access protected route with valid token', async () => {
      const response = await request(API_URL)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(e2eConfig.auth.testUser.email);
    });
    
    it('should reject request without token', async () => {
      const response = await request(API_URL)
        .get('/api/auth/me');
      
      expect(response.status).toBe(401);
      expect(response.body.error).toContain('authentication');
    });
    
    it('should reject request with invalid token', async () => {
      const response = await request(API_URL)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');
      
      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid token');
    });
  });
  
  describe('Token Refresh', () => {
    beforeEach(async () => {
      await createTestUser(e2eConfig.auth.testUser);
      const loginResponse = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: e2eConfig.auth.testUser.password
        });
      authToken = loginResponse.body.token;
    });
    
    it('should refresh valid token', async () => {
      const response = await request(API_URL)
        .post('/api/auth/refresh')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body.token).not.toBe(authToken);
    });
  });
  
  describe('Logout', () => {
    beforeEach(async () => {
      await createTestUser(e2eConfig.auth.testUser);
      const loginResponse = await request(API_URL)
        .post('/api/auth/login')
        .send({
          email: e2eConfig.auth.testUser.email,
          password: e2eConfig.auth.testUser.password
        });
      authToken = loginResponse.body.token;
    });
    
    it('should logout successfully', async () => {
      const response = await request(API_URL)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      
      // Token should be invalidated
      const meResponse = await request(API_URL)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(meResponse.status).toBe(401);
    });
  });
});