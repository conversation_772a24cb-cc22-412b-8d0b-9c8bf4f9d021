/**
 * Global Teardown for E2E Tests
 * Runs once after all test suites
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs/promises';
import { e2eConfig } from '../config/e2e.config';

export default async function globalTeardown() {
  console.log('\n🧹 Cleaning up E2E Test Environment...\n');
  
  try {
    // 1. Stop test server
    await stopTestServer();
    
    // 2. Stop Docker services if needed
    if (process.env.USE_DOCKER === 'true') {
      await stopDockerServices();
    }
    
    // 3. Clean up test data
    if (e2eConfig.cleanup.removeTestData) {
      await cleanupTestData();
    }
    
    // 4. Clean up test directories
    await cleanupTestDirectories();
    
    console.log('✅ E2E Test Environment Cleaned!\n');
    
  } catch (error) {
    console.error('❌ Failed to cleanup E2E test environment:', error);
    // Don't throw to allow tests to complete
  }
}

/**
 * Stop test server
 */
async function stopTestServer() {
  console.log('🛑 Stopping test server...');
  
  const serverProcess = (global as any).__SERVER_PROCESS__;
  
  if (serverProcess) {
    serverProcess.kill('SIGTERM');
    
    // Wait for graceful shutdown
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Force kill if still running
    try {
      serverProcess.kill('SIGKILL');
    } catch (error) {
      // Process already terminated
    }
  }
}

/**
 * Stop Docker services
 */
async function stopDockerServices() {
  console.log('🐳 Stopping Docker services...');
  
  return new Promise((resolve) => {
    const dockerDown = spawn('docker-compose', [
      '-f', 'docker-compose.test.yml',
      'down', '-v'
    ], {
      cwd: path.resolve(__dirname, '../../../'),
      stdio: 'inherit'
    });
    
    dockerDown.on('exit', () => {
      resolve(void 0);
    });
    
    // Set timeout to avoid hanging
    setTimeout(() => {
      try {
        dockerDown.kill('SIGKILL');
      } catch (error) {
        // Ignore
      }
      resolve(void 0);
    }, 10000);
  });
}

/**
 * Clean up test data from database
 */
async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');
  
  // This would typically connect to the test database
  // and clean up any remaining test data
  // For now, we'll rely on the database reset in tests
}

/**
 * Clean up test directories
 */
async function cleanupTestDirectories() {
  console.log('📁 Cleaning up test directories...');
  
  const dirs = [
    e2eConfig.files.testImagesPath,
    e2eConfig.files.testDataPath,
    e2eConfig.files.uploadDir
  ];
  
  for (const dir of dirs) {
    try {
      await fs.rm(dir, { recursive: true, force: true });
    } catch (error) {
      // Directory might not exist
    }
  }
}