/**
 * Global Setup for E2E Tests
 * Runs once before all test suites
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs/promises';
import { e2eConfig } from '../config/e2e.config';

let serverProcess: any;
let dockerProcess: any;

export default async function globalSetup() {
  console.log('\n🚀 Starting E2E Test Environment...\n');
  
  try {
    // 1. Create test directories
    await createTestDirectories();
    
    // 2. Start Docker services if needed
    if (process.env.USE_DOCKER === 'true') {
      await startDockerServices();
    }
    
    // 3. Setup test database
    await setupTestDatabase();
    
    // 4. Start test server
    await startTestServer();
    
    // 5. Wait for services to be ready
    await waitForServices();
    
    console.log('✅ E2E Test Environment Ready!\n');
    
  } catch (error) {
    console.error('❌ Failed to setup E2E test environment:', error);
    throw error;
  }
}

/**
 * Create test directories
 */
async function createTestDirectories() {
  const dirs = [
    e2eConfig.files.testImagesPath,
    e2eConfig.files.testDataPath,
    e2eConfig.files.uploadDir
  ];
  
  for (const dir of dirs) {
    await fs.mkdir(dir, { recursive: true });
  }
}

/**
 * Start Docker services
 */
async function startDockerServices() {
  console.log('🐳 Starting Docker services...');
  
  return new Promise((resolve, reject) => {
    dockerProcess = spawn('docker-compose', [
      '-f', 'docker-compose.test.yml',
      'up', '-d'
    ], {
      cwd: path.resolve(__dirname, '../../../'),
      stdio: 'inherit'
    });
    
    dockerProcess.on('exit', (code: number) => {
      if (code === 0) {
        resolve(void 0);
      } else {
        reject(new Error(`Docker compose failed with code ${code}`));
      }
    });
  });
}

/**
 * Setup test database
 */
async function setupTestDatabase() {
  console.log('🗄️ Setting up test database...');
  
  // Run Prisma migrations
  return new Promise((resolve, reject) => {
    const migrateProcess = spawn('npx', [
      'prisma', 'migrate', 'deploy'
    ], {
      env: {
        ...process.env,
        DATABASE_URL: e2eConfig.database.url
      },
      stdio: 'inherit'
    });
    
    migrateProcess.on('exit', (code: number) => {
      if (code === 0) {
        resolve(void 0);
      } else {
        reject(new Error(`Database migration failed with code ${code}`));
      }
    });
  });
}

/**
 * Start test server
 */
async function startTestServer() {
  console.log('🚀 Starting test server...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.PORT = '5001';
  process.env.DATABASE_URL = e2eConfig.database.url;
  process.env.REDIS_URL = e2eConfig.redis.url;
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.CORS_ORIGIN = '*';
  
  // Start server process
  serverProcess = spawn('node', [
    path.resolve(__dirname, '../../dist/server-production-modular.js')
  ], {
    env: process.env,
    stdio: 'pipe'
  });
  
  // Log server output
  serverProcess.stdout.on('data', (data: Buffer) => {
    if (process.env.DEBUG_E2E) {
      console.log(`[Server]: ${data.toString()}`);
    }
  });
  
  serverProcess.stderr.on('data', (data: Buffer) => {
    console.error(`[Server Error]: ${data.toString()}`);
  });
  
  // Store process reference for cleanup
  (global as any).__SERVER_PROCESS__ = serverProcess;
  (global as any).__DOCKER_PROCESS__ = dockerProcess;
}

/**
 * Wait for services to be ready
 */
async function waitForServices() {
  console.log('⏳ Waiting for services to be ready...');
  
  const maxAttempts = 30;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      // Check if server is responding
      const response = await fetch(`${e2eConfig.baseUrl}/api/health`);
      
      if (response.ok) {
        console.log('✅ Server is ready!');
        return;
      }
    } catch (error) {
      // Server not ready yet
    }
    
    attempts++;
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error('Services failed to start within timeout');
}