/**
 * Test Setup
 * Runs before each test file
 */

import { e2eConfig } from '../config/e2e.config';

// Extend Jest matchers if needed
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R;
    }
  }
}

// Custom matchers
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false
      };
    }
  }
});

// Set longer timeout for E2E tests
jest.setTimeout(e2eConfig.timeout.default);

// Suppress console logs in tests unless debugging
if (!process.env.DEBUG_E2E) {
  global.console.log = jest.fn();
  global.console.info = jest.fn();
  global.console.warn = jest.fn();
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
  console.error('Unhandled Promise Rejection:', error);
});

// Clean up after each test
afterEach(async () => {
  // Add any per-test cleanup here
});

// Export test utilities
export * from '../helpers/auth';
export * from '../helpers/database';
export * from '../helpers/projects';
export * from '../helpers/queue';