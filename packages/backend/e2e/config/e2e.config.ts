/**
 * E2E Test Configuration
 * Configuration for end-to-end testing of the refactored architecture
 */

export const e2eConfig = {
  baseUrl: process.env.E2E_BASE_URL || 'http://localhost:5001',
  frontendUrl: process.env.E2E_FRONTEND_URL || 'http://localhost:3000',
  
  timeout: {
    default: 30000,
    api: 10000,
    ui: 15000,
    longRunning: 60000
  },
  
  auth: {
    testUser: {
      email: '<EMAIL>',
      password: 'Test123!@#',
      name: 'Test User'
    },
    adminUser: {
      email: '<EMAIL>',
      password: 'Admin123!@#',
      name: 'Admin User'
    }
  },
  
  database: {
    url: process.env.TEST_DATABASE_URL || 'postgresql://spheroseg:testpass@localhost:5433/spheroseg_test',
    reset: true,
    seed: true
  },
  
  redis: {
    url: process.env.TEST_REDIS_URL || 'redis://localhost:6380',
    flushBeforeTests: true
  },
  
  ml: {
    mockMode: true,
    mockResponseTime: 2000,
    apiKey: 'test-api-key'
  },
  
  files: {
    testImagesPath: './e2e/fixtures/images',
    testDataPath: './e2e/fixtures/data',
    uploadDir: './e2e/temp/uploads'
  },
  
  cleanup: {
    afterEach: true,
    removeTestData: true,
    clearCache: true
  }
};