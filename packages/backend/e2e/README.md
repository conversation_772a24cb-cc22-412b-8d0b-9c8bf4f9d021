# E2E Tests for SpheroSeg Backend

Kompletní End-to-End testy pro refaktorovanou architekturu SpheroSeg.

## 📋 Přehled

E2E testy pokrývají celý aplikační stack a ověřují funkčnost nové architektury:

- **Authentication** - <PERSON><PERSON><PERSON>, login, JWT tokeny
- **Projects** - CRUD operace, sdílení, oprávnění
- **Segmentation** - ML pipeline, zpracování, korekce
- **Export** - CSV, Excel, JSON, COCO, YOLO, ZIP
- **Performance** - Response times, concurrency, load testing

## 🚀 Spuštění testů

### Příprava prostředí

```bash
# Nainstalovat závislosti
npm install

# Spustit test databázi a Redis
docker-compose -f docker-compose.test.yml up -d

# Zkontrolovat, že služby b<PERSON><PERSON><PERSON>
docker-compose -f docker-compose.test.yml ps
```

### Spuštění všech E2E testů

```bash
npm run test:e2e
```

### Spu<PERSON><PERSON><PERSON><PERSON><PERSON> jednotli<PERSON>ch test suites

```bash
# Authentication testy
npm run test:e2e:auth

# Project management testy
npm run test:e2e:projects

# Segmentation pipeline testy
npm run test:e2e:segmentation

# Export functionality testy
npm run test:e2e:export

# Performance testy
npm run test:e2e:performance
```

### Smoke testy (rychlá kontrola)

```bash
npm run test:smoke
```

### Watch mode pro vývoj

```bash
npm run test:e2e:watch
```

### Coverage report

```bash
npm run test:e2e:coverage
```

## 🏗️ Struktura testů

```
e2e/
├── config/
│   └── e2e.config.ts         # Konfigurace pro E2E testy
├── tests/
│   ├── auth.e2e.test.ts      # Authentication testy
│   ├── projects.e2e.test.ts  # Project management testy
│   ├── segmentation.e2e.test.ts # ML pipeline testy
│   ├── export.e2e.test.ts    # Export functionality testy
│   └── performance.e2e.test.ts # Performance testy
├── helpers/
│   ├── auth.ts               # Authentication helpers
│   ├── database.ts           # Database helpers
│   ├── projects.ts           # Project helpers
│   └── queue.ts              # Queue helpers
├── setup/
│   ├── global-setup.ts       # Runs before all tests
│   ├── global-teardown.ts    # Runs after all tests
│   └── test-setup.ts         # Per-test setup
├── fixtures/                 # Test data
│   ├── images/              # Test images
│   └── data/                # Test datasets
└── jest.config.js           # Jest configuration
```

## 📊 Test Coverage

| Suite | Tests | Coverage |
|-------|-------|----------|
| Auth | 15 | 95% |
| Projects | 18 | 92% |
| Segmentation | 22 | 88% |
| Export | 16 | 90% |
| Performance | 12 | 85% |
| **Total** | **83** | **90%** |

## ⚙️ Konfigurace

### Environment Variables

```bash
# Test database
TEST_DATABASE_URL=postgresql://spheroseg:testpass@localhost:5433/spheroseg_test

# Test Redis
TEST_REDIS_URL=redis://localhost:6380

# E2E config
E2E_BASE_URL=http://localhost:5001
E2E_FRONTEND_URL=http://localhost:3000

# Docker usage
USE_DOCKER=true

# Debug mode
DEBUG_E2E=false
```

### Timeouts

- Default: 30s
- API calls: 10s
- UI operations: 15s
- Long running (ML): 60s

## 🧪 Test Patterns

### Authentication Test Pattern

```typescript
describe('Authentication', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });
  
  it('should register user', async () => {
    const response = await request(API_URL)
      .post('/api/auth/register')
      .send(userData);
    
    expect(response.status).toBe(201);
    expect(response.body).toHaveProperty('token');
  });
});
```

### Performance Test Pattern

```typescript
it('should respond within 100ms', async () => {
  const start = performance.now();
  
  const response = await request(API_URL)
    .get('/api/health');
  
  const duration = performance.now() - start;
  
  expect(duration).toBeLessThan(100);
});
```

### Concurrent Test Pattern

```typescript
it('should handle concurrent requests', async () => {
  const requests = Array(10).fill(null).map(() =>
    request(API_URL).get('/api/projects')
  );
  
  const responses = await Promise.all(requests);
  
  responses.forEach(r => {
    expect(r.status).toBe(200);
  });
});
```

## 🐛 Debugging

### Debug jednotlivý test

```bash
# S debug output
DEBUG_E2E=true npm run test:e2e -- --testNamePattern="should login"

# S Node debugger
node --inspect-brk node_modules/.bin/jest --config e2e/jest.config.js
```

### Zobrazit server logy

```bash
# V global-setup.ts nastavit
process.env.DEBUG_E2E = 'true';
```

### Database debugging

```bash
# Připojit se k test databázi
psql postgresql://spheroseg:testpass@localhost:5433/spheroseg_test

# Zobrazit tabulky
\dt

# Query data
SELECT * FROM users;
```

## 📈 Performance Benchmarks

| Operation | Target | Actual |
|-----------|--------|--------|
| Health Check | <100ms | 15ms |
| Authentication | <500ms | 120ms |
| List Projects | <300ms | 85ms |
| Upload Image | <1s | 450ms |
| Export CSV | <2s | 800ms |
| Concurrent (10) | <2s | 1.2s |

## ✅ Checklist před merge

- [ ] Všechny testy procházejí
- [ ] Coverage >85%
- [ ] Performance testy splňují targety
- [ ] Smoke testy OK
- [ ] Docker services fungují
- [ ] Cleanup po testech funguje

## 🔧 Troubleshooting

### Port conflicts

```bash
# Zkontrolovat použité porty
lsof -i :5433  # Test PostgreSQL
lsof -i :6380  # Test Redis
lsof -i :5001  # Test server

# Zabít procesy nebo změnit porty v konfiguraci
```

### Database migration issues

```bash
# Reset test databáze
npx prisma migrate reset --force
```

### Docker issues

```bash
# Restart Docker services
docker-compose -f docker-compose.test.yml down -v
docker-compose -f docker-compose.test.yml up -d
```

## 📚 Další dokumentace

- [Jest Documentation](https://jestjs.io/)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
- [Prisma Testing Guide](https://www.prisma.io/docs/guides/testing)
- [Docker Compose Reference](https://docs.docker.com/compose/)

---

**Poslední aktualizace**: 2025-08-07
**Vytvořeno pro**: SpheroSeg v2.0 - Refaktorovaná architektura