/**
 * Authentication Test Helpers
 * Helper functions for E2E test authentication
 */

import request from 'supertest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { getPrismaClient } from './database';
import { e2eConfig } from '../config/e2e.config';

interface TestUser {
  email: string;
  password: string;
  name: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  passwordHash: string;
  role: string;
  createdAt: Date;
}

/**
 * Create a test user
 */
export async function createTestUser(userData: TestUser): Promise<User> {
  const prisma = getPrismaClient();
  
  const passwordHash = await bcrypt.hash(userData.password, 10);
  
  const user = await prisma.user.create({
    data: {
      email: userData.email,
      name: userData.name,
      passwordHash,
      role: 'user',
    }
  });
  
  return user;
}

/**
 * Create an admin user
 */
export async function createAdminUser(userData: TestUser): Promise<User> {
  const prisma = getPrismaClient();
  
  const passwordHash = await bcrypt.hash(userData.password, 10);
  
  const user = await prisma.user.create({
    data: {
      email: userData.email,
      name: userData.name,
      passwordHash,
      role: 'admin',
    }
  });
  
  return user;
}

/**
 * Get authentication token for user
 */
export async function getAuthToken(user: User | TestUser): Promise<string> {
  // If TestUser, login first
  if ('password' in user) {
    const response = await request(e2eConfig.baseUrl)
      .post('/api/auth/login')
      .send({
        email: user.email,
        password: user.password
      });
    
    if (response.status !== 200) {
      throw new Error(`Failed to login: ${response.body.error}`);
    }
    
    return response.body.token;
  }
  
  // If User object, generate token directly
  const token = jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role
    },
    process.env.JWT_SECRET || 'test-secret',
    {
      expiresIn: '1h'
    }
  );
  
  return token;
}

/**
 * Clear all test users
 */
export async function clearTestUsers(): Promise<void> {
  const prisma = getPrismaClient();
  
  // Delete test users (those with @test.com email)
  await prisma.user.deleteMany({
    where: {
      email: {
        endsWith: '@test.com'
      }
    }
  });
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  const prisma = getPrismaClient();
  
  return prisma.user.findUnique({
    where: { email }
  });
}

/**
 * Update user role
 */
export async function updateUserRole(userId: string, role: string): Promise<User> {
  const prisma = getPrismaClient();
  
  return prisma.user.update({
    where: { id: userId },
    data: { role }
  });
}

/**
 * Create multiple test users
 */
export async function createTestUsers(count: number): Promise<User[]> {
  const users: User[] = [];
  
  for (let i = 0; i < count; i++) {
    const user = await createTestUser({
      email: `user${i}@test.com`,
      password: 'Password123!',
      name: `Test User ${i}`
    });
    users.push(user);
  }
  
  return users;
}

/**
 * Login helper
 */
export async function login(email: string, password: string): Promise<{
  token: string;
  user: any;
}> {
  const response = await request(e2eConfig.baseUrl)
    .post('/api/auth/login')
    .send({ email, password });
  
  if (response.status !== 200) {
    throw new Error(`Login failed: ${response.body.error}`);
  }
  
  return response.body;
}

/**
 * Logout helper
 */
export async function logout(token: string): Promise<void> {
  await request(e2eConfig.baseUrl)
    .post('/api/auth/logout')
    .set('Authorization', `Bearer ${token}`);
}

/**
 * Verify token is valid
 */
export async function verifyToken(token: string): Promise<boolean> {
  const response = await request(e2eConfig.baseUrl)
    .get('/api/auth/me')
    .set('Authorization', `Bearer ${token}`);
  
  return response.status === 200;
}

/**
 * Create session for user
 */
export async function createSession(userId: string): Promise<string> {
  const prisma = getPrismaClient();
  
  const session = await prisma.session.create({
    data: {
      userId,
      token: `session-${Date.now()}-${Math.random()}`,
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    }
  });
  
  return session.token;
}

/**
 * Clear expired sessions
 */
export async function clearExpiredSessions(): Promise<void> {
  const prisma = getPrismaClient();
  
  await prisma.session.deleteMany({
    where: {
      expiresAt: {
        lt: new Date()
      }
    }
  });
}