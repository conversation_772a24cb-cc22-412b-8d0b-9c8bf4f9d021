/**
 * Database Test Helpers
 * Helper functions for E2E test database management
 */

import { PrismaClient } from '@prisma/client';
import { e2eConfig } from '../config/e2e.config';

let prisma: PrismaClient;

/**
 * Setup test database
 */
export async function setupTestDatabase(): Promise<void> {
  // Use test database URL
  process.env.DATABASE_URL = e2eConfig.database.url;
  
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: e2eConfig.database.url
      }
    }
  });
  
  try {
    // Connect to database
    await prisma.$connect();
    
    if (e2eConfig.database.reset) {
      // Clean all tables
      await cleanDatabase();
    }
    
    if (e2eConfig.database.seed) {
      // Seed initial data
      await seedDatabase();
    }
  } catch (error) {
    console.error('Failed to setup test database:', error);
    throw error;
  }
}

/**
 * Teardown test database
 */
export async function teardownTestDatabase(): Promise<void> {
  try {
    if (e2eConfig.cleanup.removeTestData) {
      await cleanDatabase();
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Failed to teardown test database:', error);
    throw error;
  }
}

/**
 * Clean all database tables
 */
async function cleanDatabase(): Promise<void> {
  // Delete in correct order to respect foreign keys
  await prisma.$transaction([
    prisma.segmentationResult.deleteMany(),
    prisma.projectImage.deleteMany(),
    prisma.projectShare.deleteMany(),
    prisma.project.deleteMany(),
    prisma.session.deleteMany(),
    prisma.user.deleteMany(),
  ]);
}

/**
 * Seed database with initial data
 */
async function seedDatabase(): Promise<void> {
  // Add any initial seed data if needed
  // For E2E tests, we typically start with empty database
}

/**
 * Get Prisma client instance
 */
export function getPrismaClient(): PrismaClient {
  if (!prisma) {
    throw new Error('Database not initialized. Call setupTestDatabase first.');
  }
  return prisma;
}

/**
 * Execute raw SQL query
 */
export async function executeRawQuery(query: string): Promise<any> {
  return prisma.$executeRawUnsafe(query);
}

/**
 * Transaction helper
 */
export async function withTransaction<T>(
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(async (tx) => {
    return callback(tx as PrismaClient);
  });
}

/**
 * Reset specific tables
 */
export async function resetTables(tables: string[]): Promise<void> {
  const resetOperations = tables.map(table => {
    switch (table) {
      case 'users':
        return prisma.user.deleteMany();
      case 'projects':
        return prisma.project.deleteMany();
      case 'images':
        return prisma.projectImage.deleteMany();
      case 'segmentations':
        return prisma.segmentationResult.deleteMany();
      case 'sessions':
        return prisma.session.deleteMany();
      default:
        throw new Error(`Unknown table: ${table}`);
    }
  });
  
  await prisma.$transaction(resetOperations);
}

/**
 * Count records in table
 */
export async function countRecords(table: string): Promise<number> {
  switch (table) {
    case 'users':
      return prisma.user.count();
    case 'projects':
      return prisma.project.count();
    case 'images':
      return prisma.projectImage.count();
    case 'segmentations':
      return prisma.segmentationResult.count();
    default:
      throw new Error(`Unknown table: ${table}`);
  }
}

/**
 * Wait for database to be ready
 */
export async function waitForDatabase(maxAttempts = 10): Promise<void> {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return;
    } catch (error) {
      attempts++;
      if (attempts >= maxAttempts) {
        throw new Error('Database not ready after maximum attempts');
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}