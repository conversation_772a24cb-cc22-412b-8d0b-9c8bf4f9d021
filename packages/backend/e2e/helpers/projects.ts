/**
 * Project Test Helpers
 * Helper functions for E2E test project management
 */

import request from 'supertest';
import path from 'path';
import fs from 'fs/promises';
import { e2eConfig } from '../config/e2e.config';
import { getPrismaClient } from './database';

interface ProjectData {
  name: string;
  description?: string;
  settings?: any;
}

/**
 * Create a test project
 */
export async function createTestProject(
  authToken: string,
  projectData: ProjectData
): Promise<string> {
  const response = await request(e2eConfig.baseUrl)
    .post('/api/projects')
    .set('Authorization', `Bearer ${authToken}`)
    .send(projectData);
  
  if (response.status !== 201) {
    throw new Error(`Failed to create project: ${response.body.error}`);
  }
  
  return response.body.id;
}

/**
 * Upload test images to project
 */
export async function uploadTestImages(
  authToken: string,
  projectId: string,
  count: number
): Promise<string[]> {
  const imageIds: string[] = [];
  
  for (let i = 0; i < count; i++) {
    const imagePath = path.join(e2eConfig.files.testImagesPath, `test-${i}.jpg`);
    
    // Create test image directory
    await fs.mkdir(path.dirname(imagePath), { recursive: true });
    
    // Create fake image data
    const imageBuffer = Buffer.from(`fake-image-data-${i}`);
    await fs.writeFile(imagePath, imageBuffer);
    
    // Upload image
    const response = await request(e2eConfig.baseUrl)
      .post(`/api/projects/${projectId}/images`)
      .set('Authorization', `Bearer ${authToken}`)
      .attach('images', imagePath);
    
    if (response.status !== 201) {
      throw new Error(`Failed to upload image: ${response.body.error}`);
    }
    
    imageIds.push(response.body.images[0].id);
    
    // Clean up temp file
    await fs.unlink(imagePath).catch(() => {});
  }
  
  return imageIds;
}

/**
 * Add mock segmentation results
 */
export async function addMockSegmentation(
  imageId: string,
  cellCount: number = 10
): Promise<void> {
  const prisma = getPrismaClient();
  
  const polygons = [];
  for (let i = 0; i < cellCount; i++) {
    const x = i * 20;
    const y = i * 20;
    polygons.push({
      points: [
        [x, y],
        [x + 15, y],
        [x + 15, y + 15],
        [x, y + 15]
      ],
      area: 225,
      perimeter: 60,
      centroid: [x + 7.5, y + 7.5]
    });
  }
  
  await prisma.segmentationResult.create({
    data: {
      imageId,
      polygons: JSON.stringify(polygons),
      cellCount,
      status: 'completed',
      modelType: 'resunet',
      parameters: JSON.stringify({
        threshold: 0.5,
        minArea: 10
      }),
      processingTime: 1500,
      confidence: 0.85
    }
  });
}

/**
 * Get project details
 */
export async function getProject(
  authToken: string,
  projectId: string
): Promise<any> {
  const response = await request(e2eConfig.baseUrl)
    .get(`/api/projects/${projectId}`)
    .set('Authorization', `Bearer ${authToken}`);
  
  if (response.status !== 200) {
    throw new Error(`Failed to get project: ${response.body.error}`);
  }
  
  return response.body;
}

/**
 * Delete project
 */
export async function deleteProject(
  authToken: string,
  projectId: string
): Promise<void> {
  const response = await request(e2eConfig.baseUrl)
    .delete(`/api/projects/${projectId}`)
    .set('Authorization', `Bearer ${authToken}`);
  
  if (response.status !== 200) {
    throw new Error(`Failed to delete project: ${response.body.error}`);
  }
}

/**
 * Share project with user
 */
export async function shareProject(
  authToken: string,
  projectId: string,
  email: string,
  permission: 'read' | 'write' | 'admin' = 'read'
): Promise<void> {
  const response = await request(e2eConfig.baseUrl)
    .post(`/api/projects/${projectId}/share`)
    .set('Authorization', `Bearer ${authToken}`)
    .send({ email, permission });
  
  if (response.status !== 200) {
    throw new Error(`Failed to share project: ${response.body.error}`);
  }
}

/**
 * Create projects with images
 */
export async function createProjectsWithImages(
  authToken: string,
  projectCount: number,
  imagesPerProject: number
): Promise<{ projectId: string; imageIds: string[] }[]> {
  const projects = [];
  
  for (let i = 0; i < projectCount; i++) {
    const projectId = await createTestProject(authToken, {
      name: `Test Project ${i}`,
      description: `Project for testing ${i}`
    });
    
    const imageIds = await uploadTestImages(authToken, projectId, imagesPerProject);
    
    // Add mock segmentation to each image
    for (const imageId of imageIds) {
      await addMockSegmentation(imageId, 5 + i);
    }
    
    projects.push({ projectId, imageIds });
  }
  
  return projects;
}

/**
 * Get project statistics
 */
export async function getProjectStats(
  authToken: string,
  projectId: string
): Promise<any> {
  const response = await request(e2eConfig.baseUrl)
    .get(`/api/projects/${projectId}/stats`)
    .set('Authorization', `Bearer ${authToken}`);
  
  if (response.status !== 200) {
    throw new Error(`Failed to get project stats: ${response.body.error}`);
  }
  
  return response.body;
}

/**
 * Clean up test files
 */
export async function cleanupTestFiles(): Promise<void> {
  try {
    // Clean test images directory
    await fs.rm(e2eConfig.files.testImagesPath, { recursive: true, force: true });
    
    // Clean test data directory
    await fs.rm(e2eConfig.files.testDataPath, { recursive: true, force: true });
    
    // Clean upload directory
    await fs.rm(e2eConfig.files.uploadDir, { recursive: true, force: true });
  } catch (error) {
    // Ignore errors if directories don't exist
  }
}