/**
 * Queue Test Helpers
 * Helper functions for E2E test queue management
 */

import Bull from 'bull';
import { e2eConfig } from '../config/e2e.config';

let segmentationQueue: Bull.Queue;

/**
 * Initialize test queue
 */
export function initializeTestQueue(): Bull.Queue {
  if (!segmentationQueue) {
    segmentationQueue = new Bull('segmentation-test', {
      redis: {
        port: parseInt(e2eConfig.redis.url.split(':')[2]) || 6380,
        host: 'localhost'
      }
    });
  }
  
  return segmentationQueue;
}

/**
 * Wait for job completion
 */
export async function waitForJobCompletion(
  jobId: string,
  timeout: number = 30000
): Promise<any> {
  const queue = initializeTestQueue();
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const job = await queue.getJob(jobId);
    
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }
    
    const state = await job.getState();
    
    if (state === 'completed') {
      return job.returnvalue;
    }
    
    if (state === 'failed') {
      throw new Error(`Job ${jobId} failed: ${job.failedReason}`);
    }
    
    // Wait before checking again
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  throw new Error(`Job ${jobId} timed out after ${timeout}ms`);
}

/**
 * Clear all jobs from queue
 */
export async function clearQueue(): Promise<void> {
  const queue = initializeTestQueue();
  
  await queue.empty();
  await queue.clean(0, 'completed');
  await queue.clean(0, 'failed');
  await queue.clean(0, 'delayed');
  await queue.clean(0, 'wait');
  await queue.clean(0, 'active');
}

/**
 * Get job by ID
 */
export async function getJob(jobId: string): Promise<Bull.Job | null> {
  const queue = initializeTestQueue();
  return queue.getJob(jobId);
}

/**
 * Get job state
 */
export async function getJobState(jobId: string): Promise<string> {
  const job = await getJob(jobId);
  
  if (!job) {
    throw new Error(`Job ${jobId} not found`);
  }
  
  return job.getState();
}

/**
 * Add mock job result
 */
export async function addMockJobResult(
  jobId: string,
  result: any
): Promise<void> {
  const job = await getJob(jobId);
  
  if (!job) {
    throw new Error(`Job ${jobId} not found`);
  }
  
  // Simulate job completion
  await job.moveToCompleted(result, true);
}

/**
 * Fail job with error
 */
export async function failJob(
  jobId: string,
  error: string
): Promise<void> {
  const job = await getJob(jobId);
  
  if (!job) {
    throw new Error(`Job ${jobId} not found`);
  }
  
  await job.moveToFailed(new Error(error), true);
}

/**
 * Get queue statistics
 */
export async function getQueueStats(): Promise<{
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
}> {
  const queue = initializeTestQueue();
  
  const [waiting, active, completed, failed, delayed] = await Promise.all([
    queue.getWaitingCount(),
    queue.getActiveCount(),
    queue.getCompletedCount(),
    queue.getFailedCount(),
    queue.getDelayedCount()
  ]);
  
  return {
    waiting,
    active,
    completed,
    failed,
    delayed
  };
}

/**
 * Process jobs in test mode
 */
export function processTestJobs(
  processor: (job: Bull.Job) => Promise<any>
): void {
  const queue = initializeTestQueue();
  
  queue.process(async (job) => {
    // In test mode, we can mock the processing
    if (e2eConfig.ml.mockMode) {
      // Simulate processing time
      await new Promise(resolve => 
        setTimeout(resolve, e2eConfig.ml.mockResponseTime)
      );
      
      // Return mock result
      return {
        polygons: generateMockPolygons(),
        cellCount: 10,
        confidence: 0.85,
        processingTime: e2eConfig.ml.mockResponseTime
      };
    }
    
    // Otherwise use the provided processor
    return processor(job);
  });
}

/**
 * Generate mock polygons for testing
 */
function generateMockPolygons(): any[] {
  const polygons = [];
  const cellCount = Math.floor(Math.random() * 20) + 5;
  
  for (let i = 0; i < cellCount; i++) {
    const x = Math.random() * 500;
    const y = Math.random() * 500;
    const size = Math.random() * 30 + 10;
    
    polygons.push({
      points: [
        [x, y],
        [x + size, y],
        [x + size, y + size],
        [x, y + size]
      ],
      area: size * size,
      perimeter: size * 4,
      centroid: [x + size / 2, y + size / 2],
      confidence: Math.random() * 0.3 + 0.7
    });
  }
  
  return polygons;
}

/**
 * Close queue connection
 */
export async function closeQueue(): Promise<void> {
  if (segmentationQueue) {
    await segmentationQueue.close();
  }
}