import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./src/__tests__/setup.ts'],
    include: [
      'src/**/*.{test,spec}.{ts,tsx}',
      'src/**/__tests__/**/*.{test,spec}.{ts,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      'generated',
      '**/*.d.ts',
      'src/routes/test.ts',
      'e2e/**'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov', 'json', 'json-summary', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        'generated/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
        '**/types/**',
        '**/migrations/**',
        '**/__tests__/**',
        '**/test-utils/**',
        'src/__tests__/setup.ts',
        'src/routes/test.ts'
      ],
      thresholds: {
        branches: 75,
        functions: 80,
        lines: 85,
        statements: 85
      }
    },
    testTimeout: 10000,
    // Pool configuration for better performance
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true, // Run all tests in a single worker for database consistency
      }
    },
    // Mock configuration
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,
    // Reporter configuration
    reporters: ['default', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@spheroseg/shared': path.resolve(__dirname, '../shared/src'),
      '@spheroseg/types': path.resolve(__dirname, '../types/src')
    }
  },
  // ESBuild for faster TypeScript compilation
  esbuild: {
    target: 'node18'
  }
});