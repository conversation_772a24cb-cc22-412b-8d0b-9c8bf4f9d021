#!/usr/bin/env node
/**
 * Test Database Setup Script
 * 
 * This script sets up the test database environment by:
 * 1. Generating the Prisma test client from schema.test.prisma
 * 2. Creating the SQLite test database with the schema
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const PRISMA_TEST_SCHEMA = path.join(__dirname, '../prisma/schema.test.prisma');
const PRISMA_CLIENT_OUTPUT = path.join(__dirname, '../generated/prisma-test');

console.log('🔧 Setting up test database...');

// Function to run shell commands
function runCommand(command, description) {
  return new Promise((resolve, reject) => {
    console.log(`📝 ${description}...`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ ${description} failed:`, error.message);
        if (stderr) console.error('STDERR:', stderr);
        reject(error);
        return;
      }
      
      if (stdout) console.log(stdout.trim());
      console.log(`✅ ${description} completed`);
      resolve(stdout);
    });
  });
}

async function setupTestDatabase() {
  try {
    // Check if test schema exists
    if (!fs.existsSync(PRISMA_TEST_SCHEMA)) {
      throw new Error(`Test schema not found at ${PRISMA_TEST_SCHEMA}`);
    }

    // Create test-data directory if it doesn't exist
    const testDataDir = path.join(__dirname, '../test-data');
    if (!fs.existsSync(testDataDir)) {
      fs.mkdirSync(testDataDir, { recursive: true });
      console.log('📁 Created test-data directory');
    }

    // Set environment variable for test database
    process.env.TEST_DATABASE_URL = 'file:./test-data/test.db';

    // Generate Prisma test client
    await runCommand(
      `npx prisma generate --schema=${PRISMA_TEST_SCHEMA}`,
      'Generating Prisma test client'
    );

    // Create SQLite database and apply schema
    const sqlite3Command = `npx prisma db push --schema=${PRISMA_TEST_SCHEMA} --force-reset --accept-data-loss`;
    await runCommand(sqlite3Command, 'Creating SQLite test database and applying schema');

    console.log('🎉 Test database setup completed successfully!');
    console.log(`📍 Test database location: ${path.join(__dirname, '../test-data/test.db')}`);
    console.log(`📦 Test client location: ${PRISMA_CLIENT_OUTPUT}`);
    
  } catch (error) {
    console.error('💥 Test database setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup if called directly
if (require.main === module) {
  setupTestDatabase();
}

module.exports = { setupTestDatabase };