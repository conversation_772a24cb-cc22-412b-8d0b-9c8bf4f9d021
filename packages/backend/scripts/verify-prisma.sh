#!/bin/sh
# Verify Prisma Client Installation

echo "Verifying Prisma Client installation..."

# Check if generated folder exists
if [ ! -d "/app/packages/backend/generated/prisma" ]; then
  echo "ERROR: Prisma generated folder not found"
  exit 1
fi

# Check for query engine
QUERY_ENGINE=$(find /app/packages/backend/generated/prisma -name "libquery_engine*.node" 2>/dev/null)
if [ -z "$QUERY_ENGINE" ]; then
  echo "ERROR: Query engine binary not found"
  exit 1
fi

echo "SUCCESS: Prisma client verified"
echo "Query engine: $QUERY_ENGINE"

# Test Prisma connection
node -e "
const { PrismaClient } = require('/app/packages/backend/generated/prisma');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => {
    console.log('Prisma connection successful');
    process.exit(0);
  })
  .catch(err => {
    console.error('Prisma connection failed:', err);
    process.exit(1);
  });
"