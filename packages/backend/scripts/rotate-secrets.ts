#!/usr/bin/env node

/**
 * Secret Rotation Script
 * 
 * This script handles the rotation of all sensitive secrets in the application.
 * It invalidates existing tokens, updates configuration, and notifies administrators.
 * 
 * Usage: npm run rotate-secrets [--force] [--notify]
 */

import { randomBytes } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import * as readline from 'readline';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

// Configuration
const CONFIG = {
  envFile: path.join(__dirname, '..', '.env'),
  envBackupDir: path.join(__dirname, '..', 'backups', 'env'),
  auditLogDir: path.join(__dirname, '..', 'logs', 'audit'),
  force: process.argv.includes('--force'),
  notify: process.argv.includes('--notify'),
  dryRun: process.argv.includes('--dry-run')
};

// Interfaces
interface RotationResult {
  timestamp: string;
  oldSecrets: Record<string, string>;
  newSecrets: Record<string, string>;
  invalidatedTokens: number;
  clearedSessions: number;
  notifiedAdmins: string[];
  backupFile: string;
}

interface SecretDefinition {
  name: string;
  generate: () => string;
  validate: (value: string) => boolean;
  description: string;
}

// Secret definitions
const SECRETS: SecretDefinition[] = [
  {
    name: 'JWT_SECRET',
    generate: () => randomBytes(64).toString('base64').replace(/[+/=]/g, ''),
    validate: (v) => v.length >= 64,
    description: 'JWT signing secret (min 64 chars)'
  },
  {
    name: 'SESSION_SECRET',
    generate: () => randomBytes(48).toString('base64').replace(/[+/=]/g, ''),
    validate: (v) => v.length >= 32,
    description: 'Session encryption secret (min 32 chars)'
  },
  {
    name: 'REFRESH_TOKEN_SECRET',
    generate: () => randomBytes(64).toString('base64').replace(/[+/=]/g, ''),
    validate: (v) => v.length >= 64,
    description: 'Refresh token secret (min 64 chars)'
  },
  {
    name: 'ENCRYPTION_KEY',
    generate: () => randomBytes(32).toString('hex'),
    validate: (v) => v.length === 64 && /^[0-9a-f]+$/i.test(v),
    description: 'Data encryption key (32 bytes hex)'
  }
];

class SecretRotator {
  private prisma: PrismaClient | null = null;
  private redis: Redis | null = null;
  private rl: readline.Interface;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async run(): Promise<void> {
    try {
      console.log('🔐 Secret Rotation Utility');
      console.log('═'.repeat(50));

      // Check environment
      if (process.env.NODE_ENV === 'production' && !CONFIG.force) {
        console.error('❌ Cannot run in production without --force flag');
        process.exit(1);
      }

      // Confirmation prompt
      if (!CONFIG.dryRun && !await this.confirmRotation()) {
        console.log('❌ Rotation cancelled');
        process.exit(0);
      }

      // Initialize connections
      await this.initializeConnections();

      // Perform rotation
      const result = await this.rotateSecrets();

      // Display results
      this.displayResults(result);

      // Cleanup
      await this.cleanup();

      console.log('✅ Secret rotation completed successfully');
    } catch (error) {
      console.error('❌ Error during rotation:', error);
      process.exit(1);
    }
  }

  private async confirmRotation(): Promise<boolean> {
    return new Promise((resolve) => {
      this.rl.question(
        '\n⚠️  WARNING: This will invalidate all existing tokens and sessions.\n' +
        'Are you sure you want to continue? (yes/no): ',
        (answer) => {
          resolve(answer.toLowerCase() === 'yes');
        }
      );
    });
  }

  private async initializeConnections(): Promise<void> {
    if (CONFIG.dryRun) {
      console.log('📝 Dry run mode - skipping database connections');
      return;
    }

    try {
      // Initialize Prisma
      this.prisma = new PrismaClient();
      await this.prisma.$connect();
      console.log('✅ Connected to database');

      // Initialize Redis
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      this.redis = new Redis(redisUrl);
      await this.redis.ping();
      console.log('✅ Connected to Redis');
    } catch (error) {
      console.warn('⚠️  Could not connect to database/Redis:', error);
      console.log('Continuing with file-based rotation only...');
    }
  }

  private async rotateSecrets(): Promise<RotationResult> {
    const result: RotationResult = {
      timestamp: new Date().toISOString(),
      oldSecrets: {},
      newSecrets: {},
      invalidatedTokens: 0,
      clearedSessions: 0,
      notifiedAdmins: [],
      backupFile: ''
    };

    // Step 1: Backup current .env file
    if (!CONFIG.dryRun) {
      result.backupFile = await this.backupEnvFile();
      console.log(`✅ Backed up current .env to: ${result.backupFile}`);
    }

    // Step 2: Load current secrets
    const currentEnv = await this.loadEnvFile();
    
    // Step 3: Generate new secrets
    console.log('\n🔄 Generating new secrets...');
    for (const secret of SECRETS) {
      const oldValue = currentEnv[secret.name] || '';
      const newValue = secret.generate();
      
      result.oldSecrets[secret.name] = oldValue ? '***' + oldValue.slice(-4) : 'none';
      result.newSecrets[secret.name] = newValue;
      
      if (!CONFIG.dryRun) {
        currentEnv[secret.name] = newValue;
      }
      
      console.log(`  ✅ ${secret.name}: ${secret.description}`);
      if (CONFIG.dryRun) {
        console.log(`     Preview: ${newValue.slice(0, 10)}...${newValue.slice(-4)}`);
      }
    }

    // Step 4: Update .env file
    if (!CONFIG.dryRun) {
      await this.updateEnvFile(currentEnv);
      console.log('\n✅ Updated .env file with new secrets');
    }

    // Step 5: Invalidate existing tokens and sessions
    if (!CONFIG.dryRun && this.prisma && this.redis) {
      result.invalidatedTokens = await this.invalidateTokens();
      result.clearedSessions = await this.clearSessions();
      console.log(`\n✅ Invalidated ${result.invalidatedTokens} tokens`);
      console.log(`✅ Cleared ${result.clearedSessions} sessions`);
    }

    // Step 6: Create audit log
    await this.createAuditLog(result);

    // Step 7: Notify administrators
    if (CONFIG.notify && !CONFIG.dryRun) {
      result.notifiedAdmins = await this.notifyAdministrators(result);
    }

    return result;
  }

  private async backupEnvFile(): Promise<string> {
    if (!fs.existsSync(CONFIG.envBackupDir)) {
      fs.mkdirSync(CONFIG.envBackupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(CONFIG.envBackupDir, `.env.backup.${timestamp}`);
    
    if (fs.existsSync(CONFIG.envFile)) {
      fs.copyFileSync(CONFIG.envFile, backupFile);
      // Set restrictive permissions on backup
      fs.chmodSync(backupFile, 0o600);
    }

    return backupFile;
  }

  private async loadEnvFile(): Promise<Record<string, string>> {
    const env: Record<string, string> = {};
    
    if (!fs.existsSync(CONFIG.envFile)) {
      console.warn('⚠️  .env file not found, will create new one');
      return env;
    }

    const content = fs.readFileSync(CONFIG.envFile, 'utf-8');
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key) {
          env[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
        }
      }
    }

    return env;
  }

  private async updateEnvFile(env: Record<string, string>): Promise<void> {
    const lines: string[] = [];
    
    // Add header
    lines.push('# Auto-generated by rotate-secrets.ts');
    lines.push(`# Generated at: ${new Date().toISOString()}`);
    lines.push('# WARNING: This file contains sensitive secrets. Never commit to version control!');
    lines.push('');

    // Group secrets by category
    const categories = {
      'Authentication Secrets': ['JWT_SECRET', 'REFRESH_TOKEN_SECRET'],
      'Session Security': ['SESSION_SECRET'],
      'Data Encryption': ['ENCRYPTION_KEY'],
      'Other Configuration': Object.keys(env).filter(k => 
        !['JWT_SECRET', 'REFRESH_TOKEN_SECRET', 'SESSION_SECRET', 'ENCRYPTION_KEY'].includes(k)
      )
    };

    for (const [category, keys] of Object.entries(categories)) {
      if (keys.length > 0) {
        lines.push(`# ${category}`);
        for (const key of keys) {
          if (env[key] !== undefined) {
            lines.push(`${key}=${env[key]}`);
          }
        }
        lines.push('');
      }
    }

    fs.writeFileSync(CONFIG.envFile, lines.join('\n'));
    // Set restrictive permissions
    fs.chmodSync(CONFIG.envFile, 0o600);
  }

  private async invalidateTokens(): Promise<number> {
    if (!this.prisma) return 0;

    try {
      // This would depend on your actual schema
      // Example: Clear all refresh tokens
      const result = await this.prisma.$executeRaw`
        UPDATE "User" SET "refreshTokenFamily" = '[]'::jsonb WHERE "refreshTokenFamily" IS NOT NULL
      `;
      return result;
    } catch (error) {
      console.warn('⚠️  Could not invalidate tokens:', error);
      return 0;
    }
  }

  private async clearSessions(): Promise<number> {
    if (!this.redis) return 0;

    try {
      const keys = await this.redis.keys('sess:*');
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
      return keys.length;
    } catch (error) {
      console.warn('⚠️  Could not clear Redis sessions:', error);
      return 0;
    }
  }

  private async createAuditLog(result: RotationResult): Promise<void> {
    if (!fs.existsSync(CONFIG.auditLogDir)) {
      fs.mkdirSync(CONFIG.auditLogDir, { recursive: true });
    }

    const auditFile = path.join(
      CONFIG.auditLogDir,
      `rotation-${Date.now()}.json`
    );

    const auditData = {
      ...result,
      environment: process.env.NODE_ENV,
      executedBy: process.env.USER || 'unknown',
      host: require('os').hostname(),
      dryRun: CONFIG.dryRun
    };

    fs.writeFileSync(auditFile, JSON.stringify(auditData, null, 2));
    fs.chmodSync(auditFile, 0o600);
    
    console.log(`\n📝 Audit log created: ${auditFile}`);
  }

  private async notifyAdministrators(result: RotationResult): Promise<string[]> {
    // In a real implementation, this would send emails or notifications
    console.log('\n📧 Notifying administrators...');
    
    const admins = ['<EMAIL>'];
    const message = `
Secret rotation completed at ${result.timestamp}
- Invalidated tokens: ${result.invalidatedTokens}
- Cleared sessions: ${result.clearedSessions}
- Backup file: ${result.backupFile}

All users will need to re-authenticate.
    `;

    console.log('Notification would be sent to:', admins);
    console.log('Message:', message);

    return admins;
  }

  private displayResults(result: RotationResult): void {
    console.log('\n' + '═'.repeat(50));
    console.log('📊 Rotation Summary');
    console.log('═'.repeat(50));
    console.log(`Timestamp: ${result.timestamp}`);
    console.log(`Backup: ${result.backupFile}`);
    console.log(`Invalidated Tokens: ${result.invalidatedTokens}`);
    console.log(`Cleared Sessions: ${result.clearedSessions}`);
    
    if (CONFIG.dryRun) {
      console.log('\n⚠️  DRY RUN - No actual changes were made');
    }

    console.log('\n🔑 New Secrets Generated:');
    for (const [name, value] of Object.entries(result.newSecrets)) {
      console.log(`  ${name}: ${value.slice(0, 10)}...${value.slice(-4)}`);
    }

    if (result.notifiedAdmins.length > 0) {
      console.log(`\n📧 Notified: ${result.notifiedAdmins.join(', ')}`);
    }
  }

  private async cleanup(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
    }
    if (this.redis) {
      this.redis.disconnect();
    }
    this.rl.close();
  }
}

// Run the script
if (require.main === module) {
  const rotator = new SecretRotator();
  rotator.run().catch(console.error);
}

export { SecretRotator };