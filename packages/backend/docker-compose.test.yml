version: '3.8'

services:
  # Test PostgreSQL Database
  postgres-test:
    image: postgres:15-alpine
    container_name: spheroseg-postgres-test
    environment:
      POSTGRES_DB: spheroseg_test
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD: testpass
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - spheroseg-test-network

  # Test Redis Cache
  redis-test:
    image: redis:7-alpine
    container_name: spheroseg-redis-test
    command: redis-server --appendonly yes
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - redis_test_data:/data
    networks:
      - spheroseg-test-network

networks:
  spheroseg-test-network:
    driver: bridge

volumes:
  postgres_test_data:
  redis_test_data: