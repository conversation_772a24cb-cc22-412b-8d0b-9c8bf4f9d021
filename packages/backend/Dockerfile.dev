# Development Dockerfile for Backend
# Optimized for hot reload and debugging

FROM node:20-alpine AS development

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    postgresql-client \
    git \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY packages/backend/package*.json ./packages/backend/
COPY packages/shared/package*.json ./packages/shared/
COPY packages/types/package*.json ./packages/types/
COPY packages/frontend/package*.json ./packages/frontend/

# Create ML package placeholder
RUN mkdir -p packages/ml && \
    echo '{"name":"@spheroseg/ml","version":"1.0.0","private":true}' > packages/ml/package.json

# Install all dependencies (including dev)
RUN npm install

# Copy Prisma schema for generation
COPY packages/backend/prisma ./packages/backend/prisma

# Generate Prisma Client
WORKDIR /app/packages/backend
RUN npx prisma generate

WORKDIR /app

# Copy source code (will be overridden by volume mount in dev)
COPY packages/backend ./packages/backend
COPY packages/shared ./packages/shared
COPY packages/types ./packages/types
COPY tsconfig.base.json ./

# Create necessary directories
RUN mkdir -p logs uploads

# Expose ports
EXPOSE 5001 9229 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5001/api/health || exit 1

# Development command with nodemon for hot reload
CMD ["npm", "run", "dev", "--workspace=@spheroseg/backend"]