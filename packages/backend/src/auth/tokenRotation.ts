/**
 * Token Rotation Service
 * Implements secure token rotation with family tracking for enhanced security
 */

import crypto from 'crypto';
import { createClient } from 'redis';
import logger from '../utils/logger';
import config from '../config';
import { generateAccessToken, generateRefreshToken } from '../services/tokenService';

interface TokenFamily {
  familyId: string;
  userId: string;
  createdAt: Date;
  lastRotatedAt: Date;
  rotationCount: number;
  deviceInfo?: string;
  ipAddress?: string;
}

interface RotatedTokens {
  accessToken: string;
  refreshToken: string;
  familyId: string;
  expiresIn: number;
}

class TokenRotationService {
  private redisClient: ReturnType<typeof createClient> | null = null;
  private readonly TOKEN_FAMILY_PREFIX = 'token_family:';
  private readonly REFRESH_TOKEN_PREFIX = 'refresh_token:';
  private readonly FAMILY_TTL = 30 * 24 * 60 * 60; // 30 days

  constructor() {
    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    if (!config.redis?.url) {
      logger.warn('Redis not configured, token rotation will use in-memory storage');
      return;
    }

    try {
      this.redisClient = createClient({ url: config.redis.url });
      this.redisClient.on('error', (err) => logger.error('Redis Client Error', err));
      await this.redisClient.connect();
      logger.info({ message: 'Token rotation service connected to Redis' });
    } catch (error) {
      logger.error('Failed to connect to Redis for token rotation', error);
      this.redisClient = null;
    }
  }

  /**
   * Create a new token family for a user
   */
  public async createTokenFamily(
    userId: string,
    deviceInfo?: string,
    ipAddress?: string
  ): Promise<TokenFamily> {
    const familyId = crypto.randomBytes(32).toString('hex');

    const family: TokenFamily = {
      familyId,
      userId,
      createdAt: new Date(),
      lastRotatedAt: new Date(),
      rotationCount: 0,
      deviceInfo,
      ipAddress,
    };

    // Store in Redis if available
    if (this.redisClient) {
      const key = `${this.TOKEN_FAMILY_PREFIX}${familyId}`;
      await this.redisClient.setEx(key, this.FAMILY_TTL, JSON.stringify(family));
    }

    logger.info({ message: 'Created new token family' }, { userId, familyId });
    return family;
  }

  /**
   * Rotate tokens within a family
   */
  public async rotateTokens(
    oldRefreshToken: string,
    userId: string,
    email: string,
    deviceInfo?: string
  ): Promise<RotatedTokens> {
    // Verify the old refresh token belongs to a valid family
    const familyId = await this.verifyRefreshTokenFamily(oldRefreshToken);

    if (!familyId) {
      // Possible token reuse attack - invalidate all tokens for this user
      await this.invalidateAllUserTokens(userId);
      throw new Error('Invalid refresh token - possible security breach detected');
    }

    // Get family info
    const family = await this.getTokenFamily(familyId);

    if (!family || family.userId !== userId) {
      await this.invalidateAllUserTokens(userId);
      throw new Error('Token family mismatch - security check failed');
    }

    // Generate new tokens
    const newAccessToken = generateAccessToken(userId, email, {
      tokenVersion: family.rotationCount + 1,
    });

    const newRefreshToken = await generateRefreshToken(userId, email, {
      familyId,
    });

    // Update family
    family.lastRotatedAt = new Date();
    family.rotationCount++;
    if (deviceInfo) {
      family.deviceInfo = deviceInfo;
    }

    // Store updated family and new refresh token
    if (this.redisClient) {
      const familyKey = `${this.TOKEN_FAMILY_PREFIX}${familyId}`;
      await this.redisClient.setEx(familyKey, this.FAMILY_TTL, JSON.stringify(family));

      // Store refresh token mapping
      const tokenKey = `${this.REFRESH_TOKEN_PREFIX}${newRefreshToken}`;
      await this.redisClient.setEx(tokenKey, this.FAMILY_TTL, familyId);

      // Invalidate old refresh token
      const oldTokenKey = `${this.REFRESH_TOKEN_PREFIX}${oldRefreshToken}`;
      await this.redisClient.del(oldTokenKey);
    }

    logger.info(
      { message: 'Tokens rotated successfully' },
      {
        userId,
        familyId,
        rotationCount: family.rotationCount,
      }
    );

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      familyId,
      expiresIn: 900, // 15 minutes
    };
  }

  /**
   * Verify refresh token belongs to valid family
   */
  private async verifyRefreshTokenFamily(refreshToken: string): Promise<string | null> {
    if (!this.redisClient) {
      // Fallback to basic validation without family tracking
      return 'default-family';
    }

    const tokenKey = `${this.REFRESH_TOKEN_PREFIX}${refreshToken}`;
    const familyId = await this.redisClient.get(tokenKey);

    return familyId;
  }

  /**
   * Get token family information
   */
  private async getTokenFamily(familyId: string): Promise<TokenFamily | null> {
    if (!this.redisClient) {
      return null;
    }

    const key = `${this.TOKEN_FAMILY_PREFIX}${familyId}`;
    const data = await this.redisClient.get(key);

    if (!data) {
      return null;
    }

    return JSON.parse(data) as TokenFamily;
  }

  /**
   * Invalidate all tokens for a user (security breach response)
   */
  public async invalidateAllUserTokens(userId: string): Promise<void> {
    logger.warn('Invalidating all tokens for user due to security concern', { userId });

    if (!this.redisClient) {
      return;
    }

    // Find all token families for this user
    const keys = await this.redisClient.keys(`${this.TOKEN_FAMILY_PREFIX}*`);

    for (const key of keys) {
      const data = await this.redisClient.get(key);
      if (data) {
        const family = JSON.parse(data) as TokenFamily;
        if (family.userId === userId) {
          await this.redisClient.del(key);
          logger.info({ message: 'Deleted token family' }, { familyId: family.familyId, userId });
        }
      }
    }

    // Also invalidate any cached user sessions
    const sessionKey = `user_session:${userId}`;
    await this.redisClient.del(sessionKey);
  }

  /**
   * Clean up expired token families
   */
  public async cleanupExpiredFamilies(): Promise<void> {
    if (!this.redisClient) {
      return;
    }

    const keys = await this.redisClient.keys(`${this.TOKEN_FAMILY_PREFIX}*`);
    const now = new Date();
    let cleaned = 0;

    for (const key of keys) {
      const data = await this.redisClient.get(key);
      if (data) {
        const family = JSON.parse(data) as TokenFamily;
        const lastRotated = new Date(family.lastRotatedAt);
        const daysSinceRotation = (now.getTime() - lastRotated.getTime()) / (1000 * 60 * 60 * 24);

        if (daysSinceRotation > 30) {
          await this.redisClient.del(key);
          cleaned++;
        }
      }
    }

    if (cleaned > 0) {
      logger.info(`Cleaned up ${cleaned} expired token families`);
    }
  }
}

// Export singleton instance
export const tokenRotationService = new TokenRotationService();

// Clean up expired families periodically
setInterval(
  () => {
    tokenRotationService.cleanupExpiredFamilies().catch((error) => {
      logger.error('Error cleaning up expired token families', error);
    });
  },
  24 * 60 * 60 * 1000
); // Run daily

export default tokenRotationService;
