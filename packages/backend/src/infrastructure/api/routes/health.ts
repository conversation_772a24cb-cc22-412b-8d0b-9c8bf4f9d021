/**
 * Health Check Routes
 * API endpoints for system health monitoring
 */

import { Router, Request, Response } from 'express';
import { PrismaClient } from '../../../../generated/prisma';
import * as redis from 'redis';
import { HealthCheckService, HealthStatus } from '../../health/healthCheck';
import { getMetrics, getMetricsContentType } from '../../monitoring/metrics';
import { appLogger } from '../../logging/logger';

const router = Router();

// Initialize services (these should be injected in production)
const prisma = new PrismaClient();
const redisClient = redis.createClient({
  url: process.env.REDIS_URL || 'redis://redis:6379',
});

const healthCheckService = new HealthCheckService(
  prisma,
  redisClient as any,
  process.env.RABBITMQ_URL || 'amqp://rabbitmq:5672',
  process.env.ML_SERVICE_URL || 'http://ml:5002',
  process.env.STORAGE_PATH || '/app/uploads',
  process.env.APP_VERSION || '1.0.0'
);

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Basic health check
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: ok
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/', (req: Request, res: Response) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /health/live:
 *   get:
 *     summary: Liveness probe for Kubernetes
 *     tags: [Health]
 *     description: Returns 200 if the service is alive
 *     responses:
 *       200:
 *         description: Service is alive
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: alive
 */
router.get('/live', (req: Request, res: Response) => {
  res.json({
    status: 'alive',
  });
});

/**
 * @swagger
 * /health/ready:
 *   get:
 *     summary: Readiness probe for Kubernetes
 *     tags: [Health]
 *     description: Returns 200 if the service is ready to accept traffic
 *     responses:
 *       200:
 *         description: Service is ready
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: ready
 *       503:
 *         description: Service is not ready
 */
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Quick check of critical services
    await prisma.$queryRaw`SELECT 1`;

    res.json({
      status: 'ready',
    });
  } catch (error) {
    appLogger.error('Readiness check failed', error as Error);
    res.status(503).json({
      status: 'not ready',
      error: 'Database connection failed',
    });
  }
});

/**
 * @swagger
 * /health/detailed:
 *   get:
 *     summary: Detailed health check of all system components
 *     tags: [Health]
 *     description: Returns comprehensive health information about all system components
 *     responses:
 *       200:
 *         description: Health check results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 *                 version:
 *                   type: string
 *                 checks:
 *                   type: object
 *                   properties:
 *                     database:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     redis:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     rabbitmq:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     storage:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     mlService:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     memory:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *                     cpu:
 *                       $ref: '#/components/schemas/HealthCheckResult'
 *       503:
 *         description: Service is unhealthy
 */
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const health = await healthCheckService.checkHealth();

    const statusCode =
      health.status === HealthStatus.HEALTHY
        ? 200
        : health.status === HealthStatus.DEGRADED
          ? 200
          : 503;

    res.status(statusCode).json(health);
  } catch (error) {
    appLogger.error('Detailed health check failed', error as Error);
    res.status(503).json({
      status: 'unhealthy',
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /metrics:
 *   get:
 *     summary: Prometheus metrics endpoint
 *     tags: [Health]
 *     description: Returns metrics in Prometheus format
 *     responses:
 *       200:
 *         description: Metrics in Prometheus format
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = await getMetrics();
    res.set('Content-Type', getMetricsContentType());
    res.end(metrics);
  } catch (error) {
    appLogger.error('Metrics endpoint failed', error as Error);
    res.status(500).json({
      error: 'Failed to generate metrics',
    });
  }
});

export default router;
