import { body, param } from 'express-validator';

export const createUserValidator = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('name').optional().isString().trim(),
  body('role').optional().isIn(['USER', 'ADMIN']).withMessage('Invalid role'),
];

export const updateUserValidator = [
  param('id').isUUID().withMessage('Invalid user ID'),
  body('email').optional().isEmail().normalizeEmail(),
  body('name').optional().isString().trim(),
  body('role').optional().isIn(['USER', 'ADMIN']).withMessage('Invalid role'),
];

export const getUserValidator = [param('id').isUUID().withMessage('Invalid user ID')];

// Alias for compatibility
export const userValidationRules = {
  create: createUserValidator,
  update: updateUserValidator,
  get: getUserValidator,
};
