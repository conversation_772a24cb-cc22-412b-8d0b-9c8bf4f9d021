import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '../../../../../generated/prisma';
import logger from '../../../../utils/logger';
import { ApiError } from '../../../../utils/ApiError';

const prisma = new PrismaClient();

export class GetUserController {
  constructor(private userRepository?: any) {}
  async handle(req: Request, res: Response, next: NextFunction): Promise<void> {
    return this.getById(req, res, next);
  }

  async getById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw ApiError.notFound('User not found');
      }

      res.json({ success: true, data: user });
    } catch (error) {
      if (error instanceof ApiError) {
        next(error);
      } else {
        logger.error('Error fetching user:', error);
        next(ApiError.database('Failed to fetch user', error as Error));
      }
    }
  }
}

export default new GetUserController();

export class GetAllUsersController extends GetUserController {
  async getAll(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      res.json({ success: true, data: users });
    } catch (error) {
      if (error instanceof ApiError) {
        next(error);
      } else {
        logger.error({ message: 'Error fetching all users', error });
        next(ApiError.database('Failed to fetch users', error as Error));
      }
    }
  }
}
