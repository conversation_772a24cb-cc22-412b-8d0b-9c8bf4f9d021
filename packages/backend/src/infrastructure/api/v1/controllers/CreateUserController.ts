import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '../../../../../generated/prisma';
import bcrypt from 'bcryptjs';
import logger from '../../../../utils/logger';
import { ApiError } from '../../../../utils/ApiError';

const prisma = new PrismaClient();

export class CreateUserController {
  constructor(private userRepository?: any) {}
  async handle(req: Request, res: Response, next: NextFunction): Promise<void> {
    return this.create(req, res, next);
  }

  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password, name, role = 'USER' } = req.body;

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw ApiError.conflict('User with this email already exists');
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          passwordHash: hashedPassword,
          username: name,
          role,
        },
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
        },
      });

      res.status(201).json({ success: true, data: user });
    } catch (error) {
      if (error instanceof ApiError) {
        next(error);
      } else {
        logger.error('Error creating user:', error);
        next(ApiError.database('Failed to create user', error as Error));
      }
    }
  }
}

const controller = new CreateUserController();
export default controller;
