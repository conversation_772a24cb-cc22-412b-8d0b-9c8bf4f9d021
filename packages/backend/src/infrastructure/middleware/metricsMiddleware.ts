/**
 * Metrics Middleware
 * Collects metrics for all HTTP requests
 */

import { Request, Response, NextFunction } from 'express';
import { recordHttpRequest } from '../monitoring/metrics';

export function metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();

  // Capture the original end function
  const originalEnd = res.end;

  // Override the end function to capture metrics
  res.end = function (...args: any[]): Response {
    // Calculate duration
    const duration = (Date.now() - startTime) / 1000;

    // Get route pattern (e.g., /api/v1/users/:id instead of /api/v1/users/123)
    const route = req.route?.path || req.path || 'unknown';
    const method = req.method;
    const statusCode = res.statusCode;

    // Record metrics
    recordHttpRequest(method, route, statusCode, duration);

    // Call the original end function
    return originalEnd.apply(res, args);
  };

  next();
}
