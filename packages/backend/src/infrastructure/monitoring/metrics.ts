/**
 * Prometheus Metrics Configuration
 * Application metrics for monitoring and observability
 */

import { Registry, Counter, Histogram, Gauge, collectDefaultMetrics } from 'prom-client';

// Create a custom registry
export const metricsRegistry = new Registry();

// Collect default metrics (CPU, memory, etc.)
collectDefaultMetrics({ register: metricsRegistry });

// HTTP metrics
export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.001, 0.005, 0.015, 0.05, 0.1, 0.5, 1, 2, 5],
  registers: [metricsRegistry],
});

export const httpRequestTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
  registers: [metricsRegistry],
});

// Business metrics
export const userRegistrations = new Counter({
  name: 'user_registrations_total',
  help: 'Total number of user registrations',
  registers: [metricsRegistry],
});

export const loginAttempts = new Counter({
  name: 'login_attempts_total',
  help: 'Total number of login attempts',
  labelNames: ['status'],
  registers: [metricsRegistry],
});

export const activeUsers = new Gauge({
  name: 'active_users',
  help: 'Number of active users',
  registers: [metricsRegistry],
});

export const projectsCreated = new Counter({
  name: 'projects_created_total',
  help: 'Total number of projects created',
  registers: [metricsRegistry],
});

export const imagesProcessed = new Counter({
  name: 'images_processed_total',
  help: 'Total number of images processed',
  labelNames: ['status'],
  registers: [metricsRegistry],
});

export const segmentationDuration = new Histogram({
  name: 'segmentation_duration_seconds',
  help: 'Duration of image segmentation in seconds',
  buckets: [1, 5, 10, 30, 60, 120, 300, 600],
  registers: [metricsRegistry],
});

// Database metrics
export const dbConnectionPool = new Gauge({
  name: 'db_connection_pool_size',
  help: 'Database connection pool size',
  labelNames: ['state'],
  registers: [metricsRegistry],
});

export const dbQueryDuration = new Histogram({
  name: 'db_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
  registers: [metricsRegistry],
});

// Cache metrics
export const cacheHits = new Counter({
  name: 'cache_hits_total',
  help: 'Total number of cache hits',
  labelNames: ['cache_name'],
  registers: [metricsRegistry],
});

export const cacheMisses = new Counter({
  name: 'cache_misses_total',
  help: 'Total number of cache misses',
  labelNames: ['cache_name'],
  registers: [metricsRegistry],
});

// Queue metrics
export const queueSize = new Gauge({
  name: 'queue_size',
  help: 'Current queue size',
  labelNames: ['queue_name'],
  registers: [metricsRegistry],
});

export const queueProcessingDuration = new Histogram({
  name: 'queue_processing_duration_seconds',
  help: 'Duration of queue job processing in seconds',
  labelNames: ['queue_name', 'job_type'],
  buckets: [0.1, 0.5, 1, 5, 10, 30, 60],
  registers: [metricsRegistry],
});

// Error metrics
export const errorRate = new Counter({
  name: 'errors_total',
  help: 'Total number of errors',
  labelNames: ['error_type', 'severity'],
  registers: [metricsRegistry],
});

// Performance metrics
export const memoryUsage = new Gauge({
  name: 'memory_usage_bytes',
  help: 'Memory usage in bytes',
  labelNames: ['type'],
  registers: [metricsRegistry],
});

// Helper functions
export function recordHttpRequest(
  method: string,
  route: string,
  statusCode: number,
  duration: number
): void {
  httpRequestTotal.labels(method, route, statusCode.toString()).inc();
  httpRequestDuration.labels(method, route, statusCode.toString()).observe(duration);
}

export function recordDatabaseQuery(operation: string, table: string, duration: number): void {
  dbQueryDuration.labels(operation, table).observe(duration);
}

export function recordError(errorType: string, severity: string = 'error'): void {
  errorRate.labels(errorType, severity).inc();
}

export function updateQueueMetrics(queueName: string, size: number): void {
  queueSize.labels(queueName).set(size);
}

// Export metrics as string for /metrics endpoint
export async function getMetrics(): Promise<string> {
  return await metricsRegistry.metrics();
}

// Export content type for metrics
export function getMetricsContentType(): string {
  return metricsRegistry.contentType;
}
