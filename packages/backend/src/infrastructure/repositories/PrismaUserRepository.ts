/**
 * Prisma User Repository Implementation
 * Concrete implementation of IUserRepository using Prisma ORM
 */

import { PrismaClient } from '../../../generated/prisma';
import { User, UserRole } from '../../domain/entities';
import { IUserRepository } from '../../domain/repositories';

export class PrismaUserRepository implements IUserRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!userData) return null;

    return this.mapToDomainEntity(userData);
  }

  async findByEmail(email: string): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!userData) return null;

    return this.mapToDomainEntity(userData);
  }

  async findAll(limit: number = 100, offset: number = 0): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      skip: offset,
      take: limit,
      orderBy: { createdAt: 'desc' },
    });

    return users.map((user) => this.mapToDomainEntity(user));
  }

  async create(user: User): Promise<User> {
    const userData = await this.prisma.user.create({
      data: this.mapToPersistence(user),
    });

    return this.mapToDomainEntity(userData);
  }

  async update(user: User): Promise<User> {
    const userData = await this.prisma.user.update({
      where: { id: user.id },
      data: this.mapToPersistence(user),
    });

    return this.mapToDomainEntity(userData);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }

  async exists(email: string): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { email },
    });

    return count > 0;
  }

  async countActive(): Promise<number> {
    return await this.prisma.user.count({
      where: { isActive: true },
    });
  }

  private mapToDomainEntity(userData: any): User {
    return new User({
      id: userData.id,
      email: userData.email,
      name: userData.name,
      role: userData.role as UserRole,
      isActive: userData.isActive ?? true,
      lastLogin: userData.lastLogin,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
    });
  }

  private mapToPersistence(user: User): any {
    const props = user.toJSON();
    return {
      email: props.email,
      name: props.name,
      role: props.role,
      isActive: props.isActive,
      lastLogin: props.lastLogin,
      updatedAt: props.updatedAt,
    };
  }
}
