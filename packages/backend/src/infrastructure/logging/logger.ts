/**
 * Structured Logging Configuration
 * Centralized logging for the entire application
 */

import winston from 'winston';
import { Request } from 'express';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

// Tell winston about our colors
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Define which transports to use
const transports: winston.transport[] = [];

// Console transport for development
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.printf(
          (info) => `${info.timestamp} ${info.level}: ${info.message} ${info.stack || ''}`
        )
      ),
    })
  );
}

// File transports for production
if (process.env.NODE_ENV === 'production') {
  // All logs
  transports.push(
    new winston.transports.File({
      filename: 'logs/combined.log',
      format,
      maxsize: 10485760, // 10MB
      maxFiles: 5,
    })
  );

  // Error logs
  transports.push(
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format,
      maxsize: 10485760, // 10MB
      maxFiles: 5,
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  format,
  transports,
  exitOnError: false,
});

// Logger context interface
export interface LogContext {
  requestId?: string;
  userId?: string;
  sessionId?: string;
  service?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

// Extended logger functions
export class AppLogger {
  private context: LogContext;

  constructor(context: LogContext = {}) {
    this.context = context;
  }

  private formatMessage(level: string, message: string, meta?: any): void {
    logger.log({
      level,
      message,
      ...this.context,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  }

  error(message: string, error?: Error, meta?: any): void {
    this.formatMessage('error', message, {
      ...meta,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    });
  }

  warn(message: string, meta?: any): void {
    this.formatMessage('warn', message, meta);
  }

  info(message: string, meta?: any): void {
    this.formatMessage('info', message, meta);
  }

  http(message: string, meta?: any): void {
    this.formatMessage('http', message, meta);
  }

  debug(message: string, meta?: any): void {
    this.formatMessage('debug', message, meta);
  }

  // Log database query
  logQuery(operation: string, table: string, duration: number, params?: any): void {
    this.formatMessage('debug', 'Database query executed', {
      database: {
        operation,
        table,
        duration,
        params: process.env.NODE_ENV === 'development' ? params : undefined,
      },
    });
  }

  // Log HTTP request
  logRequest(req: Request, statusCode: number, duration: number): void {
    this.formatMessage('http', 'HTTP request processed', {
      http: {
        method: req.method,
        url: req.url,
        statusCode,
        duration,
        userAgent: req.get('user-agent'),
        ip: req.ip,
        query: req.query,
        params: req.params,
      },
    });
  }

  // Log business event
  logEvent(event: string, data?: any): void {
    this.formatMessage('info', `Business event: ${event}`, {
      event: {
        name: event,
        data,
      },
    });
  }

  // Log performance metric
  logPerformance(operation: string, duration: number, metadata?: any): void {
    this.formatMessage('info', `Performance metric: ${operation}`, {
      performance: {
        operation,
        duration,
        ...metadata,
      },
    });
  }

  // Create child logger with additional context
  child(additionalContext: LogContext): AppLogger {
    return new AppLogger({
      ...this.context,
      ...additionalContext,
    });
  }
}

// Default logger instance
export const appLogger = new AppLogger({
  service: 'spheroseg-backend',
});

// Request logger factory
export function createRequestLogger(req: Request): AppLogger {
  return new AppLogger({
    service: 'spheroseg-backend',
    requestId: (req as any).id || 'unknown',
    userId: (req as any).user?.id,
    sessionId: (req as any).session?.id,
    path: req.path,
    method: req.method,
  });
}
