/**
 * Image Repository Interface
 * Defines the contract for image data persistence
 */

import { Image, ImageStatus } from '../entities';

export interface ImageFilter {
  projectId?: string;
  status?: ImageStatus;
  filename?: string;
}

export interface IImageRepository {
  findById(id: string): Promise<Image | null>;
  findByProjectId(projectId: string): Promise<Image[]>;
  findAll(filter?: ImageFilter, limit?: number, offset?: number): Promise<Image[]>;
  create(image: Image): Promise<Image>;
  update(image: Image): Promise<Image>;
  delete(id: string): Promise<void>;
  exists(id: string): Promise<boolean>;
  countByProject(projectId: string): Promise<number>;
  countByStatus(projectId: string, status: ImageStatus): Promise<number>;
  deleteByProject(projectId: string): Promise<void>;
}
