/**
 * Project Repository Interface
 * Defines the contract for project data persistence
 */

import { Project, ProjectStatus, ProjectVisibility } from '../entities';

export interface ProjectFilter {
  ownerId?: string;
  status?: ProjectStatus;
  visibility?: ProjectVisibility;
  name?: string;
}

export interface IProjectRepository {
  findById(id: string): Promise<Project | null>;
  findByOwnerId(ownerId: string): Promise<Project[]>;
  findAll(filter?: ProjectFilter, limit?: number, offset?: number): Promise<Project[]>;
  create(project: Project): Promise<Project>;
  update(project: Project): Promise<Project>;
  delete(id: string): Promise<void>;
  exists(id: string): Promise<boolean>;
  countByOwner(ownerId: string): Promise<number>;
  getSharedProjects(userId: string): Promise<Project[]>;
}
