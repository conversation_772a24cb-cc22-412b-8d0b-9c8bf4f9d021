/**
 * User Domain Entity
 * Core business logic for user management
 */

export interface UserProps {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  isActive: boolean;
  role: UserRole;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  VIEWER = 'VIEWER',
}

export class User {
  private props: UserProps;

  constructor(props: UserProps) {
    this.props = props;
    this.validate();
  }

  private validate(): void {
    if (!this.props.email || !this.isValidEmail(this.props.email)) {
      throw new Error('Invalid email address');
    }
    if (!this.props.name || this.props.name.length < 2) {
      throw new Error('Name must be at least 2 characters long');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Getters
  get id(): string {
    return this.props.id;
  }
  get email(): string {
    return this.props.email;
  }
  get name(): string {
    return this.props.name;
  }
  get role(): UserRole {
    return this.props.role;
  }
  get isActive(): boolean {
    return this.props.isActive;
  }
  get lastLogin(): Date | undefined {
    return this.props.lastLogin;
  }
  get createdAt(): Date {
    return this.props.createdAt;
  }
  get updatedAt(): Date {
    return this.props.updatedAt;
  }

  // Business logic methods
  canAccessProject(projectOwnerId: string): boolean {
    return this.props.role === UserRole.ADMIN || this.props.id === projectOwnerId;
  }

  canManageUsers(): boolean {
    return this.props.role === UserRole.ADMIN;
  }

  updateLastLogin(): void {
    this.props.lastLogin = new Date();
    this.props.updatedAt = new Date();
  }

  deactivate(): void {
    this.props.isActive = false;
    this.props.updatedAt = new Date();
  }

  activate(): void {
    this.props.isActive = true;
    this.props.updatedAt = new Date();
  }

  changeRole(newRole: UserRole): void {
    this.props.role = newRole;
    this.props.updatedAt = new Date();
  }

  toJSON(): UserProps {
    return { ...this.props };
  }
}
