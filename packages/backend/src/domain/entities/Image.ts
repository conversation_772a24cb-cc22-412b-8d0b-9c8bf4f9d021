/**
 * Image Domain Entity
 * Core business logic for image management
 */

export interface ImageProps {
  id: string;
  projectId: string;
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  createdAt: Date;
  updatedAt: Date;
  status: ImageStatus;
  segmentationData?: SegmentationData;
}

export enum ImageStatus {
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  SEGMENTED = 'SEGMENTED',
  FAILED = 'FAILED',
  DELETED = 'DELETED',
}

export interface SegmentationData {
  polygons: Polygon[];
  processedAt: Date;
  processingTime: number; // in milliseconds
  modelVersion: string;
}

export interface Polygon {
  id: string;
  points: Point[];
  confidence: number;
  label?: string;
}

export interface Point {
  x: number;
  y: number;
}

export class Image {
  private props: ImageProps;
  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/tiff',
    'image/bmp',
  ];

  constructor(props: ImageProps) {
    this.props = props;
    this.validate();
  }

  private validate(): void {
    if (!this.props.filename) {
      throw new Error('Filename is required');
    }
    if (!this.props.path) {
      throw new Error('File path is required');
    }
    if (this.props.size <= 0) {
      throw new Error('Invalid file size');
    }
    if (this.props.size > this.MAX_FILE_SIZE) {
      throw new Error(
        `File size exceeds maximum allowed size of ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
      );
    }
    if (!this.ALLOWED_MIME_TYPES.includes(this.props.mimeType)) {
      throw new Error(`Invalid file type. Allowed types: ${this.ALLOWED_MIME_TYPES.join(', ')}`);
    }
  }

  // Getters
  get id(): string {
    return this.props.id;
  }
  get projectId(): string {
    return this.props.projectId;
  }
  get filename(): string {
    return this.props.filename;
  }
  get originalName(): string {
    return this.props.originalName;
  }
  get path(): string {
    return this.props.path;
  }
  get size(): number {
    return this.props.size;
  }
  get mimeType(): string {
    return this.props.mimeType;
  }
  get width(): number | undefined {
    return this.props.width;
  }
  get height(): number | undefined {
    return this.props.height;
  }
  get status(): ImageStatus {
    return this.props.status;
  }
  get segmentationData(): SegmentationData | undefined {
    return this.props.segmentationData;
  }
  get createdAt(): Date {
    return this.props.createdAt;
  }
  get updatedAt(): Date {
    return this.props.updatedAt;
  }

  // Business logic methods
  startProcessing(): void {
    if (this.props.status !== ImageStatus.UPLOADED) {
      throw new Error('Image must be in UPLOADED status to start processing');
    }
    this.props.status = ImageStatus.PROCESSING;
    this.props.updatedAt = new Date();
  }

  markAsSegmented(segmentationData: SegmentationData): void {
    if (this.props.status !== ImageStatus.PROCESSING) {
      throw new Error('Image must be in PROCESSING status to mark as segmented');
    }
    this.props.status = ImageStatus.SEGMENTED;
    this.props.segmentationData = segmentationData;
    this.props.updatedAt = new Date();
  }

  markAsFailed(error?: string): void {
    this.props.status = ImageStatus.FAILED;
    this.props.updatedAt = new Date();
  }

  softDelete(): void {
    this.props.status = ImageStatus.DELETED;
    this.props.updatedAt = new Date();
  }

  canBeProcessed(): boolean {
    return this.props.status === ImageStatus.UPLOADED || this.props.status === ImageStatus.FAILED;
  }

  isSegmented(): boolean {
    return this.props.status === ImageStatus.SEGMENTED && this.props.segmentationData !== undefined;
  }

  getAspectRatio(): number | undefined {
    if (this.props.width && this.props.height) {
      return this.props.width / this.props.height;
    }
    return undefined;
  }

  getSegmentationStats(): { polygonCount: number; avgConfidence: number } | undefined {
    if (!this.props.segmentationData) return undefined;

    const polygons = this.props.segmentationData.polygons;
    if (polygons.length === 0) {
      return { polygonCount: 0, avgConfidence: 0 };
    }

    const avgConfidence = polygons.reduce((sum, p) => sum + p.confidence, 0) / polygons.length;
    return {
      polygonCount: polygons.length,
      avgConfidence,
    };
  }

  toJSON(): ImageProps {
    return { ...this.props };
  }
}
