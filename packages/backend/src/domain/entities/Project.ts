/**
 * Project Domain Entity
 * Core business logic for project management
 */

export interface ProjectProps {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  createdAt: Date;
  updatedAt: Date;
  status: ProjectStatus;
  visibility: ProjectVisibility;
  imageCount: number;
  segmentationCount: number;
}

export enum ProjectStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
}

export enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
  SHARED = 'SHARED',
}

export class Project {
  private props: ProjectProps;
  private readonly MAX_IMAGES = 1000;
  private readonly MAX_NAME_LENGTH = 255;

  constructor(props: ProjectProps) {
    this.props = props;
    this.validate();
  }

  private validate(): void {
    if (!this.props.name || this.props.name.trim().length === 0) {
      throw new Error('Project name is required');
    }
    if (this.props.name.length > this.MAX_NAME_LENGTH) {
      throw new Error(`Project name cannot exceed ${this.MAX_NAME_LENGTH} characters`);
    }
    if (this.props.imageCount < 0) {
      throw new Error('Image count cannot be negative');
    }
    if (this.props.segmentationCount < 0) {
      throw new Error('Segmentation count cannot be negative');
    }
  }

  // Getters
  get id(): string {
    return this.props.id;
  }
  get name(): string {
    return this.props.name;
  }
  get description(): string | undefined {
    return this.props.description;
  }
  get ownerId(): string {
    return this.props.ownerId;
  }
  get status(): ProjectStatus {
    return this.props.status;
  }
  get visibility(): ProjectVisibility {
    return this.props.visibility;
  }
  get imageCount(): number {
    return this.props.imageCount;
  }
  get segmentationCount(): number {
    return this.props.segmentationCount;
  }
  get createdAt(): Date {
    return this.props.createdAt;
  }
  get updatedAt(): Date {
    return this.props.updatedAt;
  }

  // Business logic methods
  canAddImage(): boolean {
    return this.props.status === ProjectStatus.ACTIVE && this.props.imageCount < this.MAX_IMAGES;
  }

  addImage(): void {
    if (!this.canAddImage()) {
      throw new Error('Cannot add image to this project');
    }
    this.props.imageCount++;
    this.props.updatedAt = new Date();
  }

  removeImage(): void {
    if (this.props.imageCount <= 0) {
      throw new Error('No images to remove');
    }
    this.props.imageCount--;
    this.props.updatedAt = new Date();
  }

  addSegmentation(): void {
    this.props.segmentationCount++;
    this.props.updatedAt = new Date();
  }

  archive(): void {
    if (this.props.status === ProjectStatus.DELETED) {
      throw new Error('Cannot archive deleted project');
    }
    this.props.status = ProjectStatus.ARCHIVED;
    this.props.updatedAt = new Date();
  }

  restore(): void {
    if (this.props.status === ProjectStatus.DELETED) {
      throw new Error('Cannot restore deleted project');
    }
    this.props.status = ProjectStatus.ACTIVE;
    this.props.updatedAt = new Date();
  }

  softDelete(): void {
    this.props.status = ProjectStatus.DELETED;
    this.props.updatedAt = new Date();
  }

  makePublic(): void {
    this.props.visibility = ProjectVisibility.PUBLIC;
    this.props.updatedAt = new Date();
  }

  makePrivate(): void {
    this.props.visibility = ProjectVisibility.PRIVATE;
    this.props.updatedAt = new Date();
  }

  isAccessibleBy(userId: string, userRole: string): boolean {
    // Owner always has access
    if (this.props.ownerId === userId) return true;

    // Admin always has access
    if (userRole === 'ADMIN') return true;

    // Public projects are accessible to all
    if (this.props.visibility === ProjectVisibility.PUBLIC) return true;

    // Additional logic for shared projects would go here
    return false;
  }

  getProgress(): number {
    if (this.props.imageCount === 0) return 0;
    return (this.props.segmentationCount / this.props.imageCount) * 100;
  }

  toJSON(): ProjectProps {
    return { ...this.props };
  }
}
