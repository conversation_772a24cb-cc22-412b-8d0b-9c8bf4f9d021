/**
 * Unified API Error Class for Backend
 * 
 * Extends the shared unified error system with backend-specific functionality
 * while maintaining consistency with the frontend error handling.
 */

import {
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  ErrorDetails,
  UnifiedErrorInfo,
  ErrorResponse,
  createErrorInfo,
  createErrorResponse,
  mapErrorTypeToStatus,
  ERROR_MESSAGES
} from '@spheroseg/shared/src/utils/unifiedErrorSystem';

export class UnifiedApiError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: ErrorDetails[];
  public readonly isOperational: boolean;
  public readonly timestamp: string;
  public readonly requestId?: string;
  public readonly userId?: string;
  public readonly context?: Record<string, unknown>;

  constructor(
    type: ErrorType,
    message: string,
    options: {
      code?: ErrorCode;
      statusCode?: number;
      details?: ErrorDetails[];
      isOperational?: boolean;
      requestId?: string;
      userId?: string;
      context?: Record<string, unknown>;
    } = {}
  ) {
    super(message);

    this.name = 'UnifiedApiError';
    this.type = type;
    this.severity = options.code === ErrorCode.INTERNAL_SERVER_ERROR ? ErrorSeverity.CRITICAL : 
                   type === ErrorType.VALIDATION || type === ErrorType.NOT_FOUND ? ErrorSeverity.WARNING : 
                   ErrorSeverity.ERROR;
    this.code = options.code || this._getDefaultCode(type);
    this.statusCode = options.statusCode || mapErrorTypeToStatus(type);
    this.details = options.details;
    this.isOperational = options.isOperational !== undefined ? options.isOperational : true;
    this.timestamp = new Date().toISOString();
    this.requestId = options.requestId;
    this.userId = options.userId;
    this.context = options.context;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, UnifiedApiError);
    }
  }

  private _getDefaultCode(type: ErrorType): ErrorCode {
    switch (type) {
      case ErrorType.VALIDATION: return ErrorCode.VALIDATION_ERROR;
      case ErrorType.AUTHENTICATION: return ErrorCode.AUTHENTICATION_REQUIRED;
      case ErrorType.AUTHORIZATION: return ErrorCode.INSUFFICIENT_PERMISSIONS;
      case ErrorType.NOT_FOUND: return ErrorCode.RESOURCE_NOT_FOUND;
      case ErrorType.CONFLICT: return ErrorCode.RESOURCE_CONFLICT;
      case ErrorType.RATE_LIMIT: return ErrorCode.RATE_LIMIT_EXCEEDED;
      case ErrorType.DATABASE: return ErrorCode.DATABASE_ERROR;
      case ErrorType.EXTERNAL_SERVICE: return ErrorCode.EXTERNAL_SERVICE_ERROR;
      case ErrorType.SERVICE_UNAVAILABLE: return ErrorCode.SERVICE_UNAVAILABLE_ERROR;
      case ErrorType.STORAGE: return ErrorCode.STORAGE_ERROR;
      case ErrorType.NETWORK: return ErrorCode.NETWORK_ERROR;
      case ErrorType.TIMEOUT: return ErrorCode.TIMEOUT_ERROR;
      case ErrorType.IMAGE_OPERATION: return ErrorCode.IMAGE_PROCESSING_ERROR;
      case ErrorType.PROJECT_OPERATION: return ErrorCode.PROJECT_ACCESS_ERROR;
      case ErrorType.SEGMENTATION_OPERATION: return ErrorCode.SEGMENTATION_ERROR;
      default: return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * Create a validation error
   */
  static validation(message: string = ERROR_MESSAGES.VALIDATION_FAILED, details?: ErrorDetails[]): UnifiedApiError {
    return new UnifiedApiError(ErrorType.VALIDATION, message, { details });
  }

  /**
   * Create an authentication error
   */
  static authentication(message: string = ERROR_MESSAGES.AUTHENTICATION_REQUIRED): UnifiedApiError {
    return new UnifiedApiError(ErrorType.AUTHENTICATION, message);
  }

  /**
   * Create an authorization error
   */
  static authorization(message: string = ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS): UnifiedApiError {
    return new UnifiedApiError(ErrorType.AUTHORIZATION, message);
  }

  /**
   * Create a not found error
   */
  static notFound(message: string = ERROR_MESSAGES.RESOURCE_NOT_FOUND): UnifiedApiError {
    return new UnifiedApiError(ErrorType.NOT_FOUND, message);
  }

  /**
   * Create a conflict error
   */
  static conflict(message: string = ERROR_MESSAGES.RESOURCE_CONFLICT, details?: ErrorDetails[]): UnifiedApiError {
    return new UnifiedApiError(ErrorType.CONFLICT, message, { details });
  }

  /**
   * Create a rate limit error
   */
  static rateLimit(message: string = ERROR_MESSAGES.TIMEOUT_ERROR): UnifiedApiError {
    return new UnifiedApiError(ErrorType.RATE_LIMIT, message);
  }

  /**
   * Create a database error
   */
  static database(message: string = ERROR_MESSAGES.DATABASE_ERROR, originalError?: Error): UnifiedApiError {
    const details = originalError ? [{ message: originalError.message }] : undefined;
    return new UnifiedApiError(ErrorType.DATABASE, message, { 
      details,
      context: { originalError: originalError?.name }
    });
  }

  /**
   * Create a storage error
   */
  static storage(message: string = ERROR_MESSAGES.STORAGE_ERROR): UnifiedApiError {
    return new UnifiedApiError(ErrorType.STORAGE, message);
  }

  /**
   * Create an external service error
   */
  static externalService(message: string = ERROR_MESSAGES.SERVICE_UNAVAILABLE, service?: string): UnifiedApiError {
    const details = service ? [{ field: 'service', value: service }] : undefined;
    return new UnifiedApiError(ErrorType.EXTERNAL_SERVICE, message, { details });
  }

  /**
   * Create an image processing error
   */
  static imageProcessing(message: string = ERROR_MESSAGES.IMAGE_PROCESSING_ERROR, details?: ErrorDetails[]): UnifiedApiError {
    return new UnifiedApiError(ErrorType.IMAGE_OPERATION, message, { details });
  }

  /**
   * Create a project access error
   */
  static projectAccess(message: string = ERROR_MESSAGES.PROJECT_ACCESS_ERROR): UnifiedApiError {
    return new UnifiedApiError(ErrorType.PROJECT_OPERATION, message);
  }

  /**
   * Create a segmentation error
   */
  static segmentation(message: string = ERROR_MESSAGES.SEGMENTATION_ERROR, details?: ErrorDetails[]): UnifiedApiError {
    return new UnifiedApiError(ErrorType.SEGMENTATION_OPERATION, message, { details });
  }

  /**
   * Convert to UnifiedErrorInfo
   */
  toUnifiedErrorInfo(): UnifiedErrorInfo {
    return createErrorInfo(this.type, this.message, {
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      requestId: this.requestId,
      userId: this.userId,
      context: this.context,
      stack: this.stack
    });
  }

  /**
   * Convert to JSON representation for API responses
   */
  toJSON(): ErrorResponse {
    return createErrorResponse(this.toUnifiedErrorInfo());
  }

  /**
   * Check if error is operational (expected) vs programming error
   */
  static isOperational(error: Error): boolean {
    if (error instanceof UnifiedApiError) {
      return error.isOperational;
    }
    return false;
  }

  /**
   * Create error from existing Error object
   */
  static fromError(
    error: Error, 
    type: ErrorType = ErrorType.INTERNAL_SERVER,
    options?: {
      requestId?: string;
      userId?: string;
      context?: Record<string, unknown>;
    }
  ): UnifiedApiError {
    return new UnifiedApiError(type, error.message, {
      context: { 
        originalError: error.name,
        ...options?.context 
      },
      requestId: options?.requestId,
      userId: options?.userId
    });
  }
}

// Re-export the shared error system components for convenience
export {
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  ErrorDetails,
  UnifiedErrorInfo,
  ErrorResponse,
  ERROR_MESSAGES
} from '@spheroseg/shared/src/utils/unifiedErrorSystem';

// Default export
export default UnifiedApiError;