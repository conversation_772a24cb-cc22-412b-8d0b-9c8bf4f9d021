/**
 * Login User Use Case
 * Handles user authentication business logic
 */

import { IUserRepository } from '../../../domain/repositories';
import { User } from '../../../domain/entities';
import * as bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';

export interface LoginUserRequest {
  email: string;
  password: string;
}

export interface LoginUserResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface ITokenService {
  generateAccessToken(userId: string): string;
  generateRefreshToken(userId: string): string;
}

export class LoginUserUseCase {
  constructor(
    private userRepository: IUserRepository,
    private tokenService: ITokenService
  ) {}

  async execute(request: LoginUserRequest): Promise<LoginUserResponse> {
    // Validate input
    if (!request.email || !request.password) {
      throw new Error('Email and password are required');
    }

    // Find user by email
    const user = await this.userRepository.findByEmail(request.email);
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new Error('Account is deactivated');
    }

    // Verify password (in real implementation, password hash would be stored separately)
    // This is simplified for demonstration
    const isPasswordValid = await this.verifyPassword(request.password, user.id);
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }

    // Update last login
    user.updateLastLogin();
    await this.userRepository.update(user);

    // Generate tokens
    const accessToken = this.tokenService.generateAccessToken(user.id);
    const refreshToken = this.tokenService.generateRefreshToken(user.id);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  private async verifyPassword(password: string, userId: string): Promise<boolean> {
    // In real implementation, this would fetch password hash from secure storage
    // This is a placeholder
    return true;
  }
}
