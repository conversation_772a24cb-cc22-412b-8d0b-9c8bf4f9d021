/**
 * Create Project Use Case
 * Handles project creation business logic
 */

import { IProjectRepository, IUserRepository } from '../../../domain/repositories';
import { Project, ProjectStatus, ProjectVisibility } from '../../../domain/entities';
import { v4 as uuidv4 } from 'uuid';

export interface CreateProjectRequest {
  name: string;
  description?: string;
  ownerId: string;
  visibility?: ProjectVisibility;
}

export interface CreateProjectResponse {
  project: Project;
}

export class CreateProjectUseCase {
  private readonly MAX_PROJECTS_PER_USER = 100;

  constructor(
    private projectRepository: IProjectRepository,
    private userRepository: IUserRepository
  ) {}

  async execute(request: CreateProjectRequest): Promise<CreateProjectResponse> {
    // Validate input
    if (!request.name || request.name.trim().length === 0) {
      throw new Error('Project name is required');
    }

    // Verify owner exists and is active
    const owner = await this.userRepository.findById(request.ownerId);
    if (!owner) {
      throw new Error('User not found');
    }
    if (!owner.isActive) {
      throw new Error('User account is deactivated');
    }

    // Check project limit for user
    const projectCount = await this.projectRepository.countByOwner(request.ownerId);
    if (projectCount >= this.MAX_PROJECTS_PER_USER) {
      throw new Error(
        `User has reached the maximum limit of ${this.MAX_PROJECTS_PER_USER} projects`
      );
    }

    // Create project entity
    const project = new Project({
      id: uuidv4(),
      name: request.name.trim(),
      description: request.description?.trim(),
      ownerId: request.ownerId,
      status: ProjectStatus.ACTIVE,
      visibility: request.visibility || ProjectVisibility.PRIVATE,
      imageCount: 0,
      segmentationCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Save project
    const savedProject = await this.projectRepository.create(project);

    return {
      project: savedProject,
    };
  }
}
