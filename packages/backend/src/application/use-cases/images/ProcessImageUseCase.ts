/**
 * Process Image Use Case
 * Handles image segmentation processing business logic
 */

import { IImageRepository, IProjectRepository } from '../../../domain/repositories';
import { Image, ImageStatus, SegmentationData } from '../../../domain/entities';

export interface ProcessImageRequest {
  imageId: string;
  userId: string;
}

export interface ProcessImageResponse {
  image: Image;
  jobId: string;
}

export interface ISegmentationService {
  queueSegmentation(imageId: string, imagePath: string): Promise<string>;
}

export interface INotificationService {
  notifyImageProcessing(userId: string, imageId: string): Promise<void>;
}

export class ProcessImageUseCase {
  constructor(
    private imageRepository: IImageRepository,
    private projectRepository: IProjectRepository,
    private segmentationService: ISegmentationService,
    private notificationService: INotificationService
  ) {}

  async execute(request: ProcessImageRequest): Promise<ProcessImageResponse> {
    // Find image
    const image = await this.imageRepository.findById(request.imageId);
    if (!image) {
      throw new Error('Image not found');
    }

    // Verify image can be processed
    if (!image.canBeProcessed()) {
      throw new Error(`Image cannot be processed in current status: ${image.status}`);
    }

    // Find project and verify access
    const project = await this.projectRepository.findById(image.projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    // Verify user has access to project
    if (!project.isAccessibleBy(request.userId, 'USER')) {
      throw new Error('User does not have access to this project');
    }

    // Start processing
    image.startProcessing();
    await this.imageRepository.update(image);

    // Queue segmentation job
    const jobId = await this.segmentationService.queueSegmentation(image.id, image.path);

    // Send notification
    await this.notificationService.notifyImageProcessing(request.userId, image.id);

    return {
      image,
      jobId,
    };
  }
}
