/**
 * User Repository
 * Database operations for User entity using Prisma
 */

import { prisma, User } from '../services/PrismaService';
import { UserRole } from '../../generated/prisma';
import bcrypt from 'bcryptjs';
import { createLogger } from '../utils/logger';

const logger = createLogger('UserRepository');

export interface CreateUserDto {
  email: string;
  username: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserDto {
  email?: string;
  username?: string;
  role?: UserRole;
  isActive?: boolean;
}

export class UserRepository {
  /**
   * Create new user
   */
  async create(data: CreateUserDto): Promise<User> {
    try {
      const passwordHash = await bcrypt.hash(data.password, 10);

      const user = await prisma.user.create({
        data: {
          email: data.email.toLowerCase(),
          username: data.username,
          passwordHash,
          role: data.role || UserRole.USER,
        },
      });

      logger.info({ message: 'User created:' }, { userId: user.id });
      return user;
    } catch (error: any) {
      if (error.code === 'P2002') {
        throw new Error('User with this email or username already exists');
      }
      logger.error('Failed to create user:', { error });
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id },
    });
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });
  }

  /**
   * Find user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { username },
    });
  }

  /**
   * Update user
   */
  async update(id: string, data: UpdateUserDto): Promise<User> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: {
          ...(data.email && { email: data.email.toLowerCase() }),
          ...(data.username && { username: data.username }),
          ...(data.role && { role: data.role }),
          ...(data.isActive !== undefined && { isActive: data.isActive }),
        },
      });

      logger.info({ message: 'User updated:' }, { userId: id });
      return user;
    } catch (error: any) {
      if (error.code === 'P2002') {
        throw new Error('Email or username already taken');
      }
      if (error.code === 'P2025') {
        throw new Error('User not found');
      }
      logger.error('Failed to update user:', { error });
      throw error;
    }
  }

  /**
   * Update password
   */
  async updatePassword(id: string, newPassword: string): Promise<void> {
    const passwordHash = await bcrypt.hash(newPassword, 10);

    await prisma.user.update({
      where: { id },
      data: { passwordHash },
    });

    logger.info({ message: 'Password updated for user:' }, { userId: id });
  }

  /**
   * Verify password
   */
  async verifyPassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.passwordHash);
  }

  /**
   * Delete user (soft delete by deactivating)
   */
  async delete(id: string): Promise<void> {
    await prisma.user.update({
      where: { id },
      data: { isActive: false },
    });

    logger.info({ message: 'User deactivated:' }, { userId: id });
  }

  /**
   * Hard delete user (removes from database)
   */
  async hardDelete(id: string): Promise<void> {
    await prisma.user.delete({
      where: { id },
    });

    logger.info({ message: 'User deleted:' }, { userId: id });
  }

  /**
   * List all users (admin only)
   */
  async findAll(options?: {
    skip?: number;
    take?: number;
    where?: any;
    orderBy?: any;
  }): Promise<{ users: User[]; total: number }> {
    const [users, total] = await prisma.$transaction([
      prisma.user.findMany({
        skip: options?.skip,
        take: options?.take,
        where: options?.where,
        orderBy: options?.orderBy || { createdAt: 'desc' },
      }),
      prisma.user.count({ where: options?.where }),
    ]);

    return { users, total };
  }

  /**
   * Get user statistics
   */
  async getStats(userId: string): Promise<{
    projectCount: number;
    imageCount: number;
    storageUsed: number;
  }> {
    const projectCount = await prisma.project.count({
      where: { userId },
    });

    // Calculate storage from images
    const images = await prisma.image.findMany({
      where: {
        project: {
          userId,
        },
      },
      select: {
        fileSize: true,
      },
    });

    const storageUsed = images.reduce((sum, img) => {
      return sum + Number(img.fileSize || 0);
    }, 0);

    return {
      projectCount,
      imageCount: images.length,
      storageUsed,
    };
  }
}

// Export singleton instance
export const userRepository = new UserRepository();
