/**
 * Project Repository
 * Database operations for Project entity using Prisma
 */

import {
  prisma,
  Project,
  ProjectShare,
} from '../services/PrismaService';
import { createLogger } from '../utils/logger';
import crypto from 'crypto';

const logger = createLogger('ProjectRepository');

export interface CreateProjectDto {
  userId: string;
  name: string;
  description?: string;
}

// Define enums that don't exist in Prisma schema
type ProjectStatus = 'ACTIVE' | 'ARCHIVED' | 'DELETED';
type ShareRole = 'VIEWER' | 'EDITOR' | 'ADMIN';

export interface UpdateProjectDto {
  name?: string;
  description?: string;
  status?: ProjectStatus;
}

export interface ShareProjectDto {
  projectId: string;
  userId: string;
  email?: string;
  role?: ShareRole;
}

export class ProjectRepository {
  /**
   * Create new project
   */
  async create(data: CreateProjectDto): Promise<Project> {
    const project = await prisma.project.create({
      data: {
        userId: data.userId,
        title: data.name,
        description: data.description,
      },
      include: {
        user: true,
      },
    });

    logger.info({ message: 'Project created:' }, { projectId: project.id });
    return project;
  }

  /**
   * Find project by ID
   */
  async findById(id: string, userId?: string): Promise<Project | null> {
    return prisma.project.findFirst({
      where: {
        id,
        ...(userId && { userId }),
      },
      include: {
        user: true,
        images: {
          select: {
            id: true,
            name: true,
            segmentationStatus: true,
            thumbnailPath: true,
          },
        },
        shares: true,
      },
    });
  }

  /**
   * Find all projects for user
   */
  async findByUserId(
    userId: string,
    options?: {
      skip?: number;
      take?: number;
      status?: ProjectStatus;
    }
  ): Promise<{ projects: Project[]; total: number }> {
    const where = {
      userId,
      ...(options?.status && { status: options.status }),
    };

    const [projects, total] = await prisma.$transaction([
      prisma.project.findMany({
        where,
        skip: options?.skip,
        take: options?.take,
        orderBy: { updatedAt: 'desc' },
        include: {
          _count: {
            select: {
              images: true,
            },
          },
        },
      }),
      prisma.project.count({ where }),
    ]);

    return { projects, total };
  }

  /**
   * Update project
   */
  async update(id: string, userId: string, data: UpdateProjectDto): Promise<Project> {
    try {
      const project = await prisma.project.update({
        where: {
          id,
          userId, // Ensure user owns the project
        },
        data: {
          ...(data.name && { title: data.name }),
          ...(data.description !== undefined && { description: data.description }),
        },
      });

      logger.info({ message: 'Project updated:' }, { projectId: id });
      return project;
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw new Error('Project not found or access denied');
      }
      logger.error('Failed to update project:', { error });
      throw error;
    }
  }

  /**
   * Delete project (soft delete)
   */
  async delete(id: string, userId: string): Promise<void> {
    await prisma.project.update({
      where: {
        id,
        userId,
      },
      data: {
        // Mark as deleted by setting a flag or moving to trash
        // Since there's no status field, we can't soft delete this way
      },
    });

    logger.info({ message: 'Project deleted:' }, { projectId: id });
  }

  /**
   * Hard delete project
   */
  async hardDelete(id: string, userId: string): Promise<void> {
    await prisma.project.delete({
      where: {
        id,
        userId,
      },
    });

    logger.info({ message: 'Project permanently deleted:' }, { projectId: id });
  }

  /**
   * Share project with another user
   */
  async shareProject(data: ShareProjectDto): Promise<ProjectShare> {
    const invitationToken = crypto.randomBytes(32).toString('hex');

    const share = await prisma.projectShare.create({
      data: {
        projectId: data.projectId,
        userId: data.userId,
        permission: data.role || 'view',
        invitationEmail: data.email?.toLowerCase(),
        invitationToken,
      },
    });

    logger.info({ message: 'Project shared:' }, { projectId: data.projectId, email: data.email });
    return share;
  }

  /**
   * Accept project share invitation
   */
  async acceptShare(invitationToken: string): Promise<ProjectShare> {
    const share = await prisma.projectShare.update({
      where: { invitationToken },
      data: {
        // Note: No acceptedAt or status field in current schema
        // Would need schema update to track acceptance
      },
    });

    logger.info({ message: 'Share accepted:' }, { shareId: share.id });
    return share;
  }

  /**
   * Get project shares
   */
  async getShares(projectId: string, userId: string): Promise<ProjectShare[]> {
    // Verify user owns the project
    const project = await this.findById(projectId, userId);
    if (!project) {
      throw new Error('Project not found or access denied');
    }

    return prisma.projectShare.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Revoke project share
   */
  async revokeShare(shareId: string, projectId: string, userId: string): Promise<void> {
    // Verify user owns the project
    const project = await this.findById(projectId, userId);
    if (!project) {
      throw new Error('Project not found or access denied');
    }

    await prisma.projectShare.delete({
      where: {
        id: shareId,
        projectId,
      },
    });

    logger.info({ message: 'Share revoked:' }, { shareId });
  }

  /**
   * Duplicate project
   */
  async duplicate(projectId: string, userId: string, newName?: string): Promise<Project> {
    const original = await prisma.project.findFirst({
      where: { id: projectId, userId },
      include: { images: true },
    });
    if (!original) {
      throw new Error('Project not found or access denied');
    }

    const duplicatedProject = await prisma.project.create({
      data: {
        userId,
        title: newName || `${original.title} (Copy)`,
        description: original.description,
      },
    });

    // Optionally duplicate images metadata (not files)
    if (original.images && original.images.length > 0) {
      const imagesToCreate = original.images.map((img) => ({
        projectId: duplicatedProject.id,
        name: img.name,
        userId: userId,
        segmentationStatus: img.segmentationStatus,
        thumbnailPath: img.thumbnailPath,
        storageFilename: img.storageFilename,
        storagePath: img.storagePath,
      }));

      await prisma.image.createMany({
        data: imagesToCreate,
      });

      // Note: imageCount field doesn't exist in the schema
    }

    logger.info(
      { message: 'Project duplicated:' },
      { original: projectId, new: duplicatedProject.id }
    );
    return duplicatedProject;
  }

  /**
   * Get project statistics
   */
  async getStats(
    projectId: string,
    userId: string
  ): Promise<{
    totalImages: number;
    processedImages: number;
    pendingImages: number;
    failedImages: number;
    totalSize: number;
  }> {
    // Verify ownership
    const project = await this.findById(projectId, userId);
    if (!project) {
      throw new Error('Project not found or access denied');
    }

    const stats = await prisma.image.groupBy({
      by: ['segmentationStatus'],
      where: { projectId },
      _count: true,
      _sum: {
        fileSize: true,
      },
    });

    const result = {
      totalImages: 0,
      processedImages: 0,
      pendingImages: 0,
      failedImages: 0,
      totalSize: 0,
    };

    stats.forEach((stat) => {
      result.totalImages += stat._count;
      result.totalSize += Number(stat._sum.fileSize || 0);

      switch (stat.segmentationStatus) {
        case 'COMPLETED':
          result.processedImages = stat._count;
          break;
        case 'PENDING':
          result.pendingImages = stat._count;
          break;
        case 'FAILED':
          result.failedImages = stat._count;
          break;
      }
    });

    return result;
  }
}

// Export singleton instance
export const projectRepository = new ProjectRepository();
