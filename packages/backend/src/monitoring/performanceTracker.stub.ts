/**
 * Performance Tracker Compatibility Stub
 * 
 * This file provides compatibility for existing imports of performanceTracker
 * while delegating actual functionality to the consolidated performance middleware.
 */

import { 
  performanceMonitor, 
  getPerformanceInsights, 
  getCurrentSystemMetrics 
} from '../middleware/performance';

// Performance Metric interface for monitoring sources
export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  category: string;
  timestamp: number;
  source: string;
  metadata?: Record<string, any>;
}

// Stub class to maintain compatibility
class PerformanceTrackerStub {
  getPerformanceStats() {
    const systemMetrics = getCurrentSystemMetrics();
    const insights = getPerformanceInsights();
    
    return {
      memoryUsage: systemMetrics?.memoryUsage.percentage || 0,
      cpuUsage: systemMetrics?.cpuUsage || 0,
      eventLoopLag: systemMetrics?.eventLoopLag || 0,
      insights: insights.length,
      timestamp: Date.now(),
    };
  }

  generatePerformanceReport() {
    const insights = getPerformanceInsights();
    const systemMetrics = getCurrentSystemMetrics();
    
    return {
      summary: {
        insightsGenerated: insights.length,
        criticalIssues: insights.filter(i => i.severity === 'critical').length,
        highIssues: insights.filter(i => i.severity === 'high').length,
        systemHealth: systemMetrics ? 'healthy' : 'unknown',
      },
      insights,
      systemMetrics,
      timestamp: new Date().toISOString(),
    };
  }

  // Legacy methods for backward compatibility
  startTracking(endpoint: string, type: string, metadata?: any): string {
    // No-op, handled by consolidated middleware
    return `track-${Date.now()}`;
  }

  stopTracking(trackingId: string, endpoint: string, type: string, status: string) {
    // No-op, handled by consolidated middleware
  }

  trackMemoryUsage() {
    // No-op, handled by consolidated middleware
  }

  trackCPUUsage() {
    // No-op, handled by consolidated middleware  
  }

  generateRecommendations() {
    // No-op, handled by consolidated middleware
  }
}

// Export singleton instance for backward compatibility
export const performanceTracker = new PerformanceTrackerStub();

// Export middleware for backward compatibility
export const performanceTrackingMiddleware = (req: any, res: any, next: any) => {
  // Delegate to consolidated middleware - functionality is handled there
  next();
};