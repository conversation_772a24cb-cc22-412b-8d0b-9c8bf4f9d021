# Service Layer Consolidation Plan

## 📊 Current State Analysis
- **Total service files**: 81 files
- **Test files**: ~20 files
- **Actual services**: ~60 files
- **Major duplications identified**

## 🎯 Consolidation Strategy

### 1. Email Services (✅ DONE)
- ~~emailServiceSimple.ts~~ → UnifiedEmailService ✅
- ~~emailServiceSimple25.ts~~ → UnifiedEmailService ✅
- ~~emailServiceFallback.ts~~ → UnifiedEmailService ✅
- emailServiceWrapper.ts → Keep (migration wrapper)

### 2. Image Processing Services
**To Consolidate:**
- imageProcessingService.ts
- imageProcessing.enhanced.ts
- imageDeleteService.ts

**Target:** `UnifiedImageService.ts`
- Upload handling
- Processing pipeline
- Thumbnail generation
- Deletion with cleanup
- Segmentation management

### 3. Project Services
**To Consolidate:**
- projectService.ts
- projectShareService.ts
- projectDuplicationService.ts
- projectServiceLoader.ts

**Target:** `UnifiedProjectService.ts`
- CRUD operations
- Sharing functionality
- Duplication logic
- Project statistics

### 4. Authentication Services
**Current:**
- authService.ts
- tokenService.ts

**Target:** `UnifiedAuthService.ts`
- Token management
- Session handling
- Password operations
- User verification

### 5. Queue Services
**To Consolidate:**
- segmentationQueue.ts
- segmentationQueueService.ts
- stuckImageCleanup.ts

**Target:** `UnifiedQueueService.ts`
- Job queuing
- Processing management
- Failure recovery
- Cleanup operations

### 6. Monitoring Services
**To Consolidate:**
- performanceMonitor.ts
- prometheusMetricsService.ts
- errorTracking.service.ts

**Target:** `UnifiedMonitoringService.ts`
- Performance metrics
- Error tracking
- System monitoring
- Prometheus integration

### 7. Cache Services
**Current:**
- caching.enhanced.ts
- optimizedQueryService.ts

**Target:** `UnifiedCacheService.ts`
- Query caching
- Result caching
- Cache invalidation
- Memory management

## 📁 Services to Keep As-Is
- socketService.ts (WebSocket specific)
- fileCleanupService.ts (specific utility)
- userProfileService.ts (user specific)
- recommendationAggregator.ts (ML specific)

## 🗑️ Files to Remove
- All test files in services directory (move to __tests__)
- Duplicate implementations
- Unused services
- Old backup files

## 📈 Expected Impact
- **File reduction**: 81 → ~15 files (-80%)
- **Code reduction**: ~40% less duplicate code
- **Maintainability**: Single source of truth per domain
- **Testing**: Centralized test coverage

## 🔄 Migration Strategy
1. Create unified service with interfaces
2. Implement backward compatibility
3. Migrate usage points gradually
4. Remove old services after validation
5. Update tests

## Priority Order
1. ✅ Email Services (COMPLETED)
2. Image Processing Services
3. Project Services
4. Queue Services
5. Authentication Services
6. Monitoring Services
7. Cache Services

---
Generated: 2025-08-07