import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { UnifiedImageService } from '../UnifiedImageService';
import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import sharp from 'sharp';

// Mock dependencies
vi.mock('@prisma/client');
vi.mock('fs');
vi.mock('sharp');
vi.mock('../../config/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }
}));

describe('UnifiedImageService', () => {
  let service: UnifiedImageService;
  let mockPrisma: any;

  beforeEach(() => {
    // Setup mocks
    mockPrisma = {
      image: {
        create: vi.fn(),
        findMany: vi.fn(),
        findUnique: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      project: {
        findUnique: vi.fn()
      },
      segmentationResult: {
        findMany: vi.fn(),
        create: vi.fn()
      },
      $transaction: vi.fn((callback) => callback(mockPrisma))
    };

    // Create service instance with mocked dependencies
    service = new UnifiedImageService();
    (service as any).prisma = mockPrisma;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getProjectImages', () => {
    it('should return images for a project with pagination', async () => {
      const mockImages = [
        {
          id: '1',
          filename: 'test1.jpg',
          path: '/uploads/test1.jpg',
          projectId: 'project-1',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          filename: 'test2.jpg',
          path: '/uploads/test2.jpg',
          projectId: 'project-1',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      mockPrisma.image.findMany.mockResolvedValue(mockImages);
      mockPrisma.image.count.mockResolvedValue(2);

      const result = await service.getProjectImages('project-1', { page: 1, limit: 10 });

      expect(result).toEqual({
        images: mockImages,
        total: 2,
        page: 1,
        totalPages: 1
      });

      expect(mockPrisma.image.findMany).toHaveBeenCalledWith({
        where: { projectId: 'project-1' },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' }
      });
    });

    it('should handle empty results', async () => {
      mockPrisma.image.findMany.mockResolvedValue([]);
      mockPrisma.image.count.mockResolvedValue(0);

      const result = await service.getProjectImages('project-1', { page: 1, limit: 10 });

      expect(result).toEqual({
        images: [],
        total: 0,
        page: 1,
        totalPages: 0
      });
    });

    it('should handle pagination correctly', async () => {
      mockPrisma.image.findMany.mockResolvedValue([]);
      mockPrisma.image.count.mockResolvedValue(25);

      await service.getProjectImages('project-1', { page: 2, limit: 10 });

      expect(mockPrisma.image.findMany).toHaveBeenCalledWith({
        where: { projectId: 'project-1' },
        skip: 10,
        take: 10,
        orderBy: { createdAt: 'desc' }
      });
    });
  });

  describe('generateThumbnail', () => {
    it('should generate a thumbnail for an image', async () => {
      const mockImage = {
        id: 'image-1',
        path: '/uploads/test.jpg',
        filename: 'test.jpg'
      };

      const mockSharpInstance = {
        resize: vi.fn().mockReturnThis(),
        toFile: vi.fn().mockResolvedValue(undefined)
      };

      (sharp as unknown as Mock).mockReturnValue(mockSharpInstance);
      (fs.existsSync as unknown as Mock).mockReturnValue(false);
      (fs.mkdirSync as unknown as Mock).mockImplementation(() => {});

      mockPrisma.image.findUnique.mockResolvedValue(mockImage);
      mockPrisma.image.update.mockResolvedValue({
        ...mockImage,
        thumbnailPath: '/uploads/thumbnails/test_thumb.jpg'
      });

      const result = await service.generateThumbnail('image-1');

      expect(result).toBeTruthy();
      expect(result?.thumbnailPath).toContain('thumb');
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(200, 200, {
        fit: 'cover',
        position: 'center'
      });
    });

    it('should handle missing images', async () => {
      mockPrisma.image.findUnique.mockResolvedValue(null);

      await expect(service.generateThumbnail('non-existent')).rejects.toThrow('Image not found');
    });
  });

  describe('deleteImage', () => {
    it('should delete an image and its files', async () => {
      const mockImage = {
        id: 'image-1',
        path: '/uploads/test.jpg',
        thumbnailPath: '/uploads/thumbnails/test_thumb.jpg'
      };

      mockPrisma.image.findUnique.mockResolvedValue(mockImage);
      mockPrisma.image.delete.mockResolvedValue(mockImage);
      mockPrisma.segmentationResult.findMany.mockResolvedValue([]);
      
      (fs.existsSync as unknown as Mock).mockReturnValue(true);
      (fs.unlinkSync as unknown as Mock).mockImplementation(() => {});

      const result = await service.deleteImage('image-1');

      expect(result).toBe(true);
      expect(fs.unlinkSync).toHaveBeenCalledTimes(2);
      expect(mockPrisma.image.delete).toHaveBeenCalledWith({
        where: { id: 'image-1' }
      });
    });

    it('should handle images with segmentation results', async () => {
      const mockImage = {
        id: 'image-1',
        path: '/uploads/test.jpg'
      };

      const mockSegmentations = [
        { id: 'seg-1', maskPath: '/masks/mask1.png' }
      ];

      mockPrisma.image.findUnique.mockResolvedValue(mockImage);
      mockPrisma.segmentationResult.findMany.mockResolvedValue(mockSegmentations);
      mockPrisma.image.delete.mockResolvedValue(mockImage);
      
      (fs.existsSync as unknown as Mock).mockReturnValue(true);
      (fs.unlinkSync as unknown as Mock).mockImplementation(() => {});

      await service.deleteImage('image-1');

      expect(fs.unlinkSync).toHaveBeenCalledWith('/masks/mask1.png');
    });
  });

  describe('validateImageFile', () => {
    it('should validate supported image formats', () => {
      const validFiles = [
        { mimetype: 'image/jpeg', size: 1000000 },
        { mimetype: 'image/png', size: 2000000 },
        { mimetype: 'image/webp', size: 3000000 }
      ];

      validFiles.forEach(file => {
        expect(() => service.validateImageFile(file as any)).not.toThrow();
      });
    });

    it('should reject unsupported formats', () => {
      const invalidFile = { mimetype: 'image/svg+xml', size: 1000000 };
      
      expect(() => service.validateImageFile(invalidFile as any)).toThrow('Unsupported image format');
    });

    it('should reject files that are too large', () => {
      const largeFile = { mimetype: 'image/jpeg', size: 15 * 1024 * 1024 }; // 15MB
      
      expect(() => service.validateImageFile(largeFile as any)).toThrow('File size exceeds maximum');
    });
  });

  describe('getBatchImageStatus', () => {
    it('should return status for multiple images', async () => {
      const mockImages = [
        { id: '1', status: 'processing' },
        { id: '2', status: 'completed' },
        { id: '3', status: 'failed' }
      ];

      mockPrisma.image.findMany.mockResolvedValue(mockImages);

      const result = await service.getBatchImageStatus(['1', '2', '3']);

      expect(result).toEqual(mockImages);
      expect(mockPrisma.image.findMany).toHaveBeenCalledWith({
        where: { id: { in: ['1', '2', '3'] } },
        select: expect.objectContaining({
          id: true,
          status: true
        })
      });
    });
  });

  describe('processImageMetadata', () => {
    it('should extract and store image metadata', async () => {
      const mockMetadata = {
        width: 1920,
        height: 1080,
        format: 'jpeg',
        size: 500000
      };

      const mockSharpInstance = {
        metadata: vi.fn().mockResolvedValue(mockMetadata)
      };

      (sharp as unknown as Mock).mockReturnValue(mockSharpInstance);

      mockPrisma.image.update.mockResolvedValue({
        id: 'image-1',
        metadata: mockMetadata
      });

      const result = await service.processImageMetadata('image-1', '/path/to/image.jpg');

      expect(result).toEqual(mockMetadata);
      expect(mockPrisma.image.update).toHaveBeenCalledWith({
        where: { id: 'image-1' },
        data: { metadata: mockMetadata }
      });
    });
  });
});