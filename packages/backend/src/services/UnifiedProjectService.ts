/**
 * Unified Project Service
 * Consolidates all project functionality from:
 * - projectService.ts (728 lines)
 * - projectShareService.ts (436 lines)
 * - projectDuplicationService.ts (269 lines)
 * - projectServiceLoader.ts (7 lines)
 */

import { prisma } from './PrismaService';
import * as crypto from 'crypto';
import { createLogger } from '../utils/logger';

const logger = createLogger('UnifiedProjectService');

// Interfaces
export interface Project {
  id: string;
  userId: string;
  name: string;
  description?: string;
  status: string;
  imageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectShare {
  id: string;
  projectId: string;
  email: string;
  role: string;
  status: string;
  invitationToken?: string;
  createdAt: Date;
  acceptedAt?: Date;
}

export interface ProjectStats {
  totalImages: number;
  processedImages: number;
  pendingImages: number;
  failedImages: number;
  totalSize: number;
  averageProcessingTime?: number;
}

export interface DuplicationOptions {
  includeImages?: boolean;
  includeShares?: boolean;
  includeSettings?: boolean;
}

export class UnifiedProjectService {
  constructor() {}

  /**
   * Create new project
   */
  async createProject(userId: string, name: string, description?: string): Promise<Project> {
    try {
      const project = await prisma.project.create({
        data: {
          userId,
          title: name,
          description: description || null,
        },
      });

      logger.info({ message: 'Project created:' }, { id: project.id, userId, name });
      return this.mapPrismaToProject(project);
    } catch (error) {
      logger.error('Failed to create project:', error);
      throw error;
    }
  }

  /**
   * Get project by ID
   */
  async getProject(projectId: string, userId: string): Promise<Project | null> {
    try {
      const project = await prisma.project.findFirst({
        where: {
          id: projectId,
          userId: userId,
        },
      });

      if (!project) {
        return null;
      }

      return this.mapPrismaToProject(project);
    } catch (error) {
      logger.error('Failed to get project:', error);
      throw error;
    }
  }

  /**
   * Get all projects for user
   */
  async getUserProjects(userId: string): Promise<Project[]> {
    try {
      const projects = await prisma.project.findMany({
        where: {
          userId: userId,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      return projects.map((project) => this.mapPrismaToProject(project));
    } catch (error) {
      logger.error('Failed to get user projects:', error);
      throw error;
    }
  }

  /**
   * Update project
   */
  async updateProject(
    projectId: string,
    userId: string,
    updates: Partial<Pick<Project, 'name' | 'description' | 'status'>>
  ): Promise<Project | null> {
    try {
      // Build update data object
      const updateData: any = {};
      if (updates.name !== undefined) {
        updateData.title = updates.name;
      }
      if (updates.description !== undefined) {
        updateData.description = updates.description;
      }

      if (Object.keys(updateData).length === 0) {
        return await this.getProject(projectId, userId);
      }

      const project = await prisma.project.updateMany({
        where: {
          id: projectId,
          userId: userId,
        },
        data: updateData,
      });

      if (project.count === 0) {
        return null;
      }

      logger.info({ message: 'Project updated:', projectId });
      return await this.getProject(projectId, userId);
    } catch (error) {
      logger.error('Failed to update project:', error);
      throw error;
    }
  }

  /**
   * Delete project (hard delete)
   */
  async deleteProject(projectId: string, userId: string): Promise<boolean> {
    try {
      const result = await prisma.project.deleteMany({
        where: {
          id: projectId,
          userId: userId,
        },
      });

      if (result.count === 0) {
        return false;
      }

      logger.info({ message: 'Project deleted:', projectId });
      return true;
    } catch (error) {
      logger.error('Failed to delete project:', error);
      throw error;
    }
  }

  /**
   * Share project with another user
   */
  async shareProject(
    projectId: string,
    ownerId: string,
    email: string,
    role: string = 'view'
  ): Promise<ProjectShare> {
    try {
      // Verify project ownership
      const project = await this.getProject(projectId, ownerId);
      if (!project) {
        throw new Error('Project not found or access denied');
      }

      // Check if already shared - using userId lookup
      const user = await prisma.user.findUnique({
        where: { email: email },
      });

      if (!user) {
        throw new Error('User with this email not found');
      }

      const existing = await prisma.projectShare.findFirst({
        where: {
          projectId: projectId,
          userId: user.id,
        },
      });

      if (existing) {
        throw new Error('Project already shared with this user');
      }

      // Create share
      const invitationToken = crypto.randomBytes(32).toString('hex');

      const share = await prisma.projectShare.create({
        data: {
          projectId,
          userId: user.id,
          permission: role,
          invitationToken,
          invitationEmail: email,
        },
      });

      logger.info({ message: 'Project shared:' }, { projectId, email, role });
      return this.mapPrismaToProjectShare(share);
    } catch (error) {
      logger.error('Failed to share project:', error);
      throw error;
    }
  }

  /**
   * Get project shares
   */
  async getProjectShares(projectId: string, ownerId: string): Promise<ProjectShare[]> {
    try {
      // Verify ownership
      const project = await this.getProject(projectId, ownerId);
      if (!project) {
        throw new Error('Project not found or access denied');
      }

      const shares = await prisma.projectShare.findMany({
        where: {
          projectId: projectId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return shares.map((share) => this.mapPrismaToProjectShare(share));
    } catch (error) {
      logger.error('Failed to get project shares:', error);
      throw error;
    }
  }

  /**
   * Accept project share invitation
   */
  async acceptShare(invitationToken: string): Promise<ProjectShare | null> {
    try {
      const result = await prisma.projectShare.updateMany({
        where: {
          invitationToken: invitationToken,
        },
        data: {
          // Accept is handled by removing invitation fields
          invitationToken: null,
        },
      });

      if (result.count === 0) {
        return null;
      }

      // Get the updated share
      const share = await prisma.projectShare.findFirst({
        where: {
          invitationToken: invitationToken,
        },
      });

      if (!share) {
        return null;
      }

      logger.info({ message: 'Share accepted:', invitationToken });
      return this.mapPrismaToProjectShare(share);
    } catch (error) {
      logger.error('Failed to accept share:', error);
      throw error;
    }
  }

  /**
   * Revoke project share
   */
  async revokeShare(shareId: string, projectId: string, ownerId: string): Promise<boolean> {
    try {
      // Verify ownership
      const project = await this.getProject(projectId, ownerId);
      if (!project) {
        throw new Error('Project not found or access denied');
      }

      const result = await prisma.projectShare.deleteMany({
        where: {
          id: shareId,
          projectId: projectId,
        },
      });

      if (result.count === 0) {
        return false;
      }

      logger.info({ message: 'Share revoked:', shareId });
      return true;
    } catch (error) {
      logger.error('Failed to revoke share:', error);
      throw error;
    }
  }

  /**
   * Duplicate project
   */
  async duplicateProject(
    projectId: string,
    userId: string,
    newName?: string,
    options: DuplicationOptions = {}
  ): Promise<Project> {
    const { includeImages = false, includeShares = false, includeSettings = true } = options;

    try {
      // Get original project
      const original = await this.getProject(projectId, userId);
      if (!original) {
        throw new Error('Project not found or access denied');
      }

      // Create new project
      const duplicatedName = newName || `${original.name} (Copy)`;
      const newProject = await this.createProject(userId, duplicatedName, original.description);

      // Duplicate settings if requested
      if (includeSettings) {
        // Copy project settings (if you have a settings table)
        logger.info({ message: 'Duplicating project settings...' });
      }

      // Use transaction for complex operations
      await prisma.$transaction(async (tx) => {
        // Duplicate images if requested
        if (includeImages) {
          const images = await tx.image.findMany({
            where: {
              projectId: projectId,
            },
          });

          for (const image of images) {
            await tx.image.create({
              data: {
                projectId: newProject.id,
                userId: newProject.userId,
                storageFilename: image.storageFilename,
                originalFilename: image.originalFilename,
                storagePath: image.storagePath,
                fileSize: image.fileSize,
                width: image.width,
                height: image.height,
                format: image.format,
              },
            });
          }

          // Image count is not tracked in this schema
        }

        // Duplicate shares if requested
        if (includeShares) {
          const shares = await tx.projectShare.findMany({
            where: {
              projectId: projectId,
            },
          });

          for (const share of shares) {
            await this.shareProject(
              newProject.id,
              userId,
              share.invitationEmail || '',
              share.permission
            );
          }
        }
      });

      logger.info({ message: 'Project duplicated:' }, { original: projectId, new: newProject.id });
      return newProject;
    } catch (error) {
      logger.error('Failed to duplicate project:', error);
      throw error;
    }
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectId: string, userId: string): Promise<ProjectStats> {
    try {
      // Verify ownership
      const project = await this.getProject(projectId, userId);
      if (!project) {
        throw new Error('Project not found or access denied');
      }

      const images = await prisma.image.findMany({
        where: {
          projectId: projectId,
        },
        select: {
          segmentationStatus: true,
          fileSize: true,
        },
      });

      const stats = images.reduce(
        (acc, image) => {
          acc.totalImages++;
          if (image.segmentationStatus === 'completed') acc.processedImages++;
          if (image.segmentationStatus === 'pending') acc.pendingImages++;
          if (image.segmentationStatus === 'failed') acc.failedImages++;
          if (image.fileSize) acc.totalSize += Number(image.fileSize);
          return acc;
        },
        {
          totalImages: 0,
          processedImages: 0,
          pendingImages: 0,
          failedImages: 0,
          totalSize: 0,
        }
      );

      return stats;
    } catch (error) {
      logger.error('Failed to get project stats:', error);
      throw error;
    }
  }

  /**
   * Helper: Map Prisma model to Project interface
   */
  private mapPrismaToProject(project: any): Project {
    return {
      id: project.id,
      userId: project.userId,
      name: project.title,
      description: project.description,
      status: 'active', // Default status since not in schema
      imageCount: 0, // Not tracked in this schema
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    };
  }

  /**
   * Helper: Map Prisma model to ProjectShare interface
   */
  private mapPrismaToProjectShare(share: any): ProjectShare {
    return {
      id: share.id,
      projectId: share.projectId,
      email: share.invitationEmail,
      role: share.permission,
      status: share.invitationToken ? 'pending' : 'accepted',
      invitationToken: share.invitationToken,
      createdAt: share.createdAt,
      acceptedAt: share.invitationToken ? null : share.createdAt,
    };
  }
}

// Singleton instance
let instance: UnifiedProjectService | null = null;

export function getProjectService(): UnifiedProjectService {
  if (!instance) {
    instance = new UnifiedProjectService();
  }
  return instance;
}
