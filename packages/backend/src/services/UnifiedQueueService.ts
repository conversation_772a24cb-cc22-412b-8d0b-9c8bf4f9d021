/**
 * Unified Queue Service
 * Consolidates all queue functionality from:
 * - segmentationQueue.ts (134 lines)
 * - segmentationQueueService.ts (903 lines)
 * - stuckImageCleanup.ts (229 lines)
 * - taskQueueService.ts (627 lines)
 */

import { prisma } from './PrismaService';
// Using type definitions for now - Bull/BullMQ should be installed separately
interface Bull {
  Queue: any;
}
const Bull: Bull = { Queue: class {} } as any;
import Redis from 'ioredis';
import { createLogger } from '../utils/logger';

const logger = createLogger('UnifiedQueueService');

// Configuration
const REDIS_URL = process.env.REDIS_URL || (process.env.REDIS_HOST ? `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT || '6379'}` : 'redis://redis:6379');
const MAX_RETRIES = 3;
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
const STUCK_THRESHOLD = 30 * 60 * 1000; // 30 minutes
const CONCURRENCY = parseInt(process.env.QUEUE_CONCURRENCY || '5');

// Interfaces
export interface QueueJob {
  id: string;
  type: string;
  data: any;
  priority?: number;
  delay?: number;
  attempts?: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface SegmentationJobData {
  imageId: string;
  projectId: string;
  userId: string;
  imagePath: string;
  modelName?: string;
  parameters?: Record<string, any>;
  priority?: number;
  delay?: number;
}

export interface ProcessingResult {
  success: boolean;
  imageId: string;
  resultPath?: string;
  error?: string;
  processingTime?: number;
}

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

export class UnifiedQueueService {
  private redis: Redis;
  private queues: Map<string, any> = new Map();
  private cleanupInterval?: NodeJS.Timeout;
  private initialized: boolean = false;

  constructor() {
    this.redis = new Redis(REDIS_URL);
    this.initialize();
  }

  /**
   * Initialize queue service
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Create queues
      this.createQueue('segmentation');
      this.createQueue('processing');
      this.createQueue('cleanup');
      this.createQueue('general');

      // Setup processors
      this.setupProcessors();

      // Start cleanup service
      this.startCleanupService();

      this.initialized = true;
      logger.info({ message: 'UnifiedQueueService initialized successfully' });
    } catch (error) {
      logger.error('Failed to initialize UnifiedQueueService:', error);
      throw error;
    }
  }

  /**
   * Create a new queue
   */
  private createQueue(name: string): any {
    const queue = new (Bull as any).Queue(name, {
      redis: {
        host: this.redis.options.host,
        port: this.redis.options.port as number,
        password: this.redis.options.password,
      },
      defaultJobOptions: {
        attempts: MAX_RETRIES,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    });

    this.queues.set(name, queue);

    // Setup event listeners
    queue.on('completed', (job, result) => {
      logger.info(`Job completed: ${job.id}`, { queue: name, result });
    });

    queue.on('failed', (job, err) => {
      logger.error(`Job failed: ${job.id}`, { queue: name, error: err.message });
    });

    queue.on('stalled', (job) => {
      logger.warn(`Job stalled: ${job.id}`, { queue: name });
    });

    return queue;
  }

  /**
   * Setup job processors
   */
  private setupProcessors(): void {
    // Segmentation processor
    const segmentationQueue = this.queues.get('segmentation');
    if (segmentationQueue) {
      segmentationQueue.process(CONCURRENCY, async (job) => {
        return await this.processSegmentationJob(job.data);
      });
    }

    // Processing processor
    const processingQueue = this.queues.get('processing');
    if (processingQueue) {
      processingQueue.process(CONCURRENCY, async (job) => {
        return await this.processImageJob(job.data);
      });
    }

    // Cleanup processor
    const cleanupQueue = this.queues.get('cleanup');
    if (cleanupQueue) {
      cleanupQueue.process(1, async (job) => {
        return await this.processCleanupJob(job.data);
      });
    }

    // General processor
    const generalQueue = this.queues.get('general');
    if (generalQueue) {
      generalQueue.process(CONCURRENCY, async (job) => {
        return await this.processGeneralJob(job.data);
      });
    }
  }

  /**
   * Add segmentation job to queue
   */
  async addSegmentationJob(data: SegmentationJobData): Promise<string> {
    try {
      const queue = this.queues.get('segmentation');
      if (!queue) throw new Error('Segmentation queue not initialized');

      const job = await queue.add(data, {
        priority: data.priority || 0,
        delay: data.delay || 0,
      });

      // Store in database using SegmentationQueue
      await prisma.segmentationQueue.create({
        data: {
          id: job.id,
          imageId: data.imageId,
          userId: data.userId,
          projectId: data.projectId,
          status: 'pending',
          priority: data.priority || 0,
        },
      });

      logger.info({ message: 'Segmentation job added:', jobId: job.id });
      return job.id as string;
    } catch (error) {
      logger.error('Failed to add segmentation job:', error);
      throw error;
    }
  }

  /**
   * Process segmentation job
   */
  private async processSegmentationJob(data: SegmentationJobData): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      // Update status in database
      await prisma.image.update({
        where: { id: data.imageId },
        data: {
          segmentationStatus: 'processing',
          processingStartedAt: new Date(),
        },
      });

      // Update queue status
      await prisma.segmentationQueue.updateMany({
        where: {
          imageId: data.imageId,
          status: 'pending',
        },
        data: {
          status: 'processing',
          startedAt: new Date(),
        },
      });

      // Simulate segmentation processing (replace with actual ML service call)
      logger.info({ message: 'Processing segmentation:', imageId: data.imageId });

      // TODO: Call actual ML service
      // const result = await mlService.segment(data.imagePath, data.modelName, data.parameters);

      // For now, simulate success
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Update success status
      const processingTime = Date.now() - startTime;
      await prisma.image.update({
        where: { id: data.imageId },
        data: {
          segmentationStatus: 'completed',
          processingCompletedAt: new Date(),
          processingTime: processingTime,
        },
      });

      // Update queue status
      await prisma.segmentationQueue.updateMany({
        where: {
          imageId: data.imageId,
          status: 'processing',
        },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });

      return {
        success: true,
        imageId: data.imageId,
        processingTime: processingTime,
      };
    } catch (error) {
      // Update failure status
      await prisma.image.update({
        where: { id: data.imageId },
        data: {
          segmentationStatus: 'failed',
          error: (error as Error).message,
        },
      });

      // Update queue status
      await prisma.segmentationQueue.updateMany({
        where: {
          imageId: data.imageId,
          status: 'processing',
        },
        data: {
          status: 'failed',
          errorMessage: (error as Error).message,
        },
      });

      logger.error('Segmentation job failed:', error);

      return {
        success: false,
        imageId: data.imageId,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Process image job
   */
  private async processImageJob(data: any): Promise<ProcessingResult> {
    try {
      logger.info({ message: 'Processing image job:' }, data);

      // Image processing logic here
      // This would handle thumbnail generation, optimization, etc.

      return {
        success: true,
        imageId: data.imageId,
      };
    } catch (error) {
      logger.error('Image processing failed:', error);
      return {
        success: false,
        imageId: data.imageId,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Process cleanup job
   */
  private async processCleanupJob(data: any): Promise<void> {
    try {
      logger.info({ message: 'Processing cleanup job:' }, data);

      // Cleanup stuck images
      await this.cleanupStuckImages();

      // Clean old completed jobs
      await this.cleanOldJobs();

      // Clean temporary files
      await this.cleanTempFiles();
    } catch (error) {
      logger.error('Cleanup job failed:', error);
      throw error;
    }
  }

  /**
   * Process general job
   */
  private async processGeneralJob(data: any): Promise<any> {
    try {
      logger.info({ message: 'Processing general job:' }, data);

      // General job processing logic
      // This is for any custom job types

      return { success: true };
    } catch (error) {
      logger.error('General job failed:', error);
      throw error;
    }
  }

  /**
   * Start cleanup service
   */
  private startCleanupService(): void {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupStuckImages();
        await this.cleanOldJobs();
      } catch (error) {
        logger.error('Cleanup service error:', error);
      }
    }, CLEANUP_INTERVAL);

    logger.info({ message: 'Cleanup service started' });
  }

  /**
   * Cleanup stuck images
   */
  async cleanupStuckImages(): Promise<number> {
    try {
      const stuckThresholdDate = new Date(Date.now() - STUCK_THRESHOLD);

      const result = await prisma.image.updateMany({
        where: {
          segmentationStatus: 'processing',
          processingStartedAt: {
            lt: stuckThresholdDate,
          },
        },
        data: {
          segmentationStatus: 'failed',
          error: 'Processing timeout',
        },
      });

      // Also update the queue
      await prisma.segmentationQueue.updateMany({
        where: {
          status: 'processing',
          startedAt: {
            lt: stuckThresholdDate,
          },
        },
        data: {
          status: 'failed',
          errorMessage: 'Processing timeout',
        },
      });

      const count = result.count || 0;
      if (count > 0) {
        logger.info(`Cleaned up ${count} stuck images`);
      }

      return count;
    } catch (error) {
      logger.error('Failed to cleanup stuck images:', error);
      return 0;
    }
  }

  /**
   * Clean old completed jobs
   */
  async cleanOldJobs(): Promise<number> {
    try {
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      const result = await prisma.segmentationQueue.deleteMany({
        where: {
          status: {
            in: ['completed', 'failed'],
          },
          completedAt: {
            lt: sevenDaysAgo,
          },
        },
      });

      const count = result.count || 0;
      if (count > 0) {
        logger.info(`Cleaned up ${count} old jobs`);
      }

      return count;
    } catch (error) {
      logger.error('Failed to clean old jobs:', error);
      return 0;
    }
  }

  /**
   * Clean temporary files
   */
  async cleanTempFiles(): Promise<void> {
    try {
      // This would be implemented by the image service
      logger.info({ message: 'Cleaning temporary files...' });
    } catch (error) {
      logger.error('Failed to clean temp files:', error);
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(queueName?: string): Promise<QueueStats | Map<string, QueueStats>> {
    try {
      if (queueName) {
        const queue = this.queues.get(queueName);
        if (!queue) throw new Error(`Queue ${queueName} not found`);

        return await this.getStatsForQueue(queue);
      }

      // Get stats for all queues
      const stats = new Map<string, QueueStats>();
      for (const [name, queue] of this.queues) {
        stats.set(name, await this.getStatsForQueue(queue));
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      throw error;
    }
  }

  /**
   * Get stats for specific queue
   */
  private async getStatsForQueue(queue: any): Promise<QueueStats> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaitingCount(),
      queue.getActiveCount(),
      queue.getCompletedCount(),
      queue.getFailedCount(),
      queue.getDelayedCount(),
    ]);

    const isPaused = await queue.isPaused();

    return {
      waiting,
      active,
      completed,
      failed,
      delayed,
      paused: isPaused,
    };
  }

  /**
   * Pause queue
   */
  async pauseQueue(queueName?: string): Promise<void> {
    try {
      if (queueName) {
        const queue = this.queues.get(queueName);
        if (!queue) throw new Error(`Queue ${queueName} not found`);
        await queue.pause();
        logger.info(`Queue ${queueName} paused`);
      } else {
        // Pause all queues
        for (const [name, queue] of this.queues) {
          await queue.pause();
          logger.info(`Queue ${name} paused`);
        }
      }
    } catch (error) {
      logger.error('Failed to pause queue:', error);
      throw error;
    }
  }

  /**
   * Resume queue
   */
  async resumeQueue(queueName?: string): Promise<void> {
    try {
      if (queueName) {
        const queue = this.queues.get(queueName);
        if (!queue) throw new Error(`Queue ${queueName} not found`);
        await queue.resume();
        logger.info(`Queue ${queueName} resumed`);
      } else {
        // Resume all queues
        for (const [name, queue] of this.queues) {
          await queue.resume();
          logger.info(`Queue ${name} resumed`);
        }
      }
    } catch (error) {
      logger.error('Failed to resume queue:', error);
      throw error;
    }
  }

  /**
   * Clear queue
   */
  async clearQueue(queueName: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) throw new Error(`Queue ${queueName} not found`);

      await queue.empty();
      logger.info(`Queue ${queueName} cleared`);
    } catch (error) {
      logger.error('Failed to clear queue:', error);
      throw error;
    }
  }

  /**
   * Get job by ID
   */
  async getJob(queueName: string, jobId: string): Promise<any | null> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) throw new Error(`Queue ${queueName} not found`);

      return await queue.getJob(jobId);
    } catch (error) {
      logger.error('Failed to get job:', error);
      return null;
    }
  }

  /**
   * Retry failed job
   */
  async retryJob(queueName: string, jobId: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) throw new Error(`Queue ${queueName} not found`);

      const job = await queue.getJob(jobId);
      if (!job) throw new Error(`Job ${jobId} not found`);

      await job.retry();
      logger.info(`Job ${jobId} retried`);
    } catch (error) {
      logger.error('Failed to retry job:', error);
      throw error;
    }
  }

  /**
   * Remove job
   */
  async removeJob(queueName: string, jobId: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) throw new Error(`Queue ${queueName} not found`);

      const job = await queue.getJob(jobId);
      if (!job) throw new Error(`Job ${jobId} not found`);

      await job.remove();
      logger.info(`Job ${jobId} removed`);
    } catch (error) {
      logger.error('Failed to remove job:', error);
      throw error;
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    try {
      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Close all queues
      for (const [name, queue] of this.queues) {
        await queue.close();
        logger.info(`Queue ${name} closed`);
      }

      // Close Redis connection
      await this.redis.quit();

      logger.info({ message: 'UnifiedQueueService shut down successfully' });
    } catch (error) {
      logger.error('Failed to shutdown queue service:', error);
      throw error;
    }
  }
}

// Singleton instance
let instance: UnifiedQueueService | null = null;

export function getQueueService(): UnifiedQueueService {
  if (!instance) {
    instance = new UnifiedQueueService();
  }
  return instance;
}
