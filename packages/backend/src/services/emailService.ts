/**
 * Email Service Wrapper
 * 
 * This file provides a bridge between the legacy emailService interface
 * and the new CentralizedEmailService implementation.
 * It ensures backward compatibility with existing tests and code.
 */

import {
  emailService as centralizedEmailService,
  sendPasswordResetSimple,
  sendProjectShareInvite,
  sendNewPasswordEmail,
  sendVerificationEmail as sendVerificationEmailLegacy,
  sendAccessRequest,
  sendAccessRequestNotification,
  sendInvitationAcceptedNotification,
  EmailOptions,
  EmailVerificationData,
  PasswordResetData,
  AccessRequestData,
  ProjectShareData
} from './email/CentralizedEmailService';
import logger from '../utils/logger';

// Main email service instance
export const emailService = centralizedEmailService;

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(data: PasswordResetData): Promise<void> {
  try {
    await emailService.sendPasswordResetEmail(data);
  } catch (error) {
    logger.error('Failed to send password reset email:', error);
    throw error;
  }
}

/**
 * Send verification email
 */
export async function sendVerificationEmail(data: EmailVerificationData): Promise<void> {
  try {
    await emailService.sendVerificationEmail(data);
  } catch (error) {
    logger.error('Failed to send verification email:', error);
    throw error;
  }
}

/**
 * Send access request email to admins
 */
export async function sendAccessRequestEmail(data: AccessRequestData): Promise<void> {
  try {
    await emailService.sendAccessRequestEmail(data);
  } catch (error) {
    logger.error('Failed to send access request email:', error);
    throw error;
  }
}

/**
 * Send project share invitation email
 */
export async function sendProjectShareEmail(data: ProjectShareData): Promise<void> {
  try {
    await emailService.sendProjectShareEmail(data);
  } catch (error) {
    logger.error('Failed to send project share email:', error);
    throw error;
  }
}

/**
 * Initialize email service
 */
export async function initializeEmailService(): Promise<void> {
  try {
    await emailService.initialize();
    logger.info('Email service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize email service:', error);
    // Don't throw in development - allow the app to run without email
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(): Promise<boolean> {
  try {
    return await emailService.testEmailConfiguration();
  } catch (error) {
    logger.error('Email configuration test failed:', error);
    return false;
  }
}

/**
 * Verify email token
 */
export async function verifyEmailToken(token: string): Promise<{ userId: string } | null> {
  try {
    return await emailService.verifyEmailToken(token);
  } catch (error) {
    logger.error('Failed to verify email token:', error);
    return null;
  }
}

// Legacy function exports for backward compatibility
export {
  sendPasswordResetSimple,
  sendProjectShareInvite,
  sendNewPasswordEmail,
  sendAccessRequest,
  sendAccessRequestNotification,
  sendInvitationAcceptedNotification
};

// Type exports
export type {
  EmailOptions,
  EmailVerificationData,
  PasswordResetData,
  AccessRequestData,
  ProjectShareData
};

// Default export for modules expecting it
export default {
  emailService,
  sendPasswordResetEmail,
  sendVerificationEmail,
  sendAccessRequestEmail,
  sendProjectShareEmail,
  initializeEmailService,
  testEmailConfiguration,
  verifyEmailToken,
  sendPasswordResetSimple,
  sendProjectShareInvite,
  sendNewPasswordEmail,
  sendAccessRequest,
  sendAccessRequestNotification,
  sendInvitationAcceptedNotification
};