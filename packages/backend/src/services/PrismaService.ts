/**
 * Prisma Database Service with Optimized Connection Pooling
 * Type-safe database operations with performance monitoring
 */

import { PrismaClient } from '../../generated/prisma';
import { createLogger } from '../utils/logger';
import { getDatabaseConfig, buildPrismaUrl, databaseMonitor } from '../config/database.config';
import * as fs from 'fs';
import * as path from 'path';

const logger = createLogger('PrismaService');

// Singleton Prisma client with optimized configuration
class PrismaService {
  private static instance: PrismaService;
  public prisma: PrismaClient;
  public queryCount = 0;
  public totalQueryTime = 0;

  private constructor() {
    // Check for query engine before initializing
    const queryEnginePath = path.join(__dirname, '../../generated/prisma/libquery_engine-linux-musl.so.node');
    const engineExists = fs.existsSync(queryEnginePath);
    
    logger.info({
      message: 'Prisma initialization',
      platform: process.platform,
      arch: process.arch,
      queryEngineExists: engineExists,
      queryEnginePath: engineExists ? queryEnginePath : 'NOT FOUND'
    });
    
    if (!engineExists) {
      const generatedDir = path.join(__dirname, '../../generated/prisma');
      if (fs.existsSync(generatedDir)) {
        logger.error('CRITICAL: Prisma query engine not found at expected path');
        logger.error({ message: 'Available files:', files: fs.readdirSync(generatedDir) });
      } else {
        logger.error('CRITICAL: Prisma generated directory does not exist');
      }
    }

    // Get optimized database configuration
    const dbConfig = getDatabaseConfig();
    const datasourceUrl = buildPrismaUrl(dbConfig);
    
    logger.info('Database configuration:', {
      poolSize: dbConfig.connectionPoolSize,
      connectionTimeout: dbConfig.connectionTimeout,
      queryTimeout: dbConfig.queryTimeout,
      ssl: dbConfig.enableSsl,
    });
    
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: datasourceUrl,
        },
      },
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'event' },
        { level: 'warn', emit: 'event' },
        { level: 'info', emit: 'event' },
      ],
    });

    // Monitor query performance
    this.prisma.$on('query' as any, (e: any) => {
      const duration = e.duration || 0;
      this.queryCount++;
      this.totalQueryTime += duration;
      
      // Record in monitor
      databaseMonitor.recordQuery(duration);
      
      // Log in development
      if (process.env.NODE_ENV !== 'production') {
        logger.debug({ message: 'Query:', query: e.query });
        logger.debug({ message: 'Duration:', duration: `${duration} ms` });
      }
      
      // Warn about slow queries
      if (duration > 1000) {
        logger.warn(`Slow query detected (${duration}ms):`, e.query);
      }
    });

    // Log errors
    this.prisma.$on('error' as any, (e: any) => {
      logger.error('Database error:', e.message);
    });

    // Log warnings
    this.prisma.$on('warn' as any, (e: any) => {
      logger.warn('Database warning:', e.message);
    });
  }

  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  /**
   * Get Prisma client
   */
  public getClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Connect to database
   */
  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info({ message: 'Prisma connected to database' });
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info({ message: 'Prisma disconnected from database' });
    } catch (error) {
      logger.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Transaction helper with timeout
   */
  public async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    return this.prisma.$transaction(fn, {
      maxWait: 5000, // Max time to wait for a transaction slot
      timeout: 30000, // Max time for the transaction to complete
    });
  }

  /**
   * Get database performance metrics
   */
  public getPerformanceMetrics() {
    const avgQueryTime = this.queryCount > 0 ? this.totalQueryTime / this.queryCount : 0;
    
    return {
      totalQueries: this.queryCount,
      totalQueryTime: this.totalQueryTime,
      averageQueryTime: avgQueryTime,
      metrics: databaseMonitor.getMetrics(),
    };
  }

  /**
   * Reset performance metrics
   */
  public resetMetrics() {
    this.queryCount = 0;
    this.totalQueryTime = 0;
    databaseMonitor.reset();
  }

  /**
   * Execute raw SQL with performance tracking
   */
  public async executeRaw<T>(query: string, params?: any[]): Promise<T> {
    const start = Date.now();
    try {
      const result = await this.prisma.$queryRawUnsafe<T>(query, ...(params || []));
      const duration = Date.now() - start;
      databaseMonitor.recordQuery(duration);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      logger.error(`Query failed after ${duration}ms:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const prismaService = PrismaService.getInstance();
export const prisma = prismaService.getClient();

// Re-export Prisma types and enums
export type {
  User,
  UserProfile,
  Project,
  Image,
  Segmentation,
  SegmentationResult,
  ProjectShare,
  SegmentationQueue,
  ProjectDuplicationTask,
  RefreshToken,
  EmailVerification,
  PasswordResetToken,
  AccessRequest,
  Session,
  Migration,
} from '../../generated/prisma';

// Export Prisma for direct usage
export { PrismaClient } from '../../generated/prisma';
export default prismaService;
