/**
 * User Routes Module
 * User profile, settings, and statistics endpoints
 */

import { Router, Request, Response } from 'express';
import { createLogger } from '../../utils/logger';
import { prisma } from '../../services/PrismaService';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import { validateCsrfToken } from '../../middleware/csrfProtection';

const logger = createLogger('UserRoutes');
const router = Router();

interface AuthenticatedRequest extends Request {
  userId?: string;
}

export function createUserRoutes(): Router {
  // Get user profile
  router.get('/profile', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      logger.info({ message: 'GET /api/users/profile - User:', userId });

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
          createdAt: true,
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found',
        });
      }

      // Convert to snake_case for API response consistency
      res.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.username,
          avatar: null,
          created_at: user.createdAt,
          last_login: null,
          subscription_tier: null,
          subscription_status: null,
        },
      });
    } catch (error) {
      logger.error('Get user profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Update user profile
  router.put(
    '/profile',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { name, avatar } = req.body;

        logger.info({ message: 'PUT /api/users/profile' }, { userId, name });

        // Build update data dynamically
        const updateData: any = {};
        if (name !== undefined) updateData.username = name;
        // avatar field doesn't exist in schema

        if (Object.keys(updateData).length === 0) {
          return res.status(400).json({
            success: false,
            error: 'No fields to update',
          });
        }

        const updatedUser = await prisma.user.update({
          where: { id: userId },
          data: updateData,
          select: {
            id: true,
            email: true,
            username: true,
          },
        });

        logger.info({ message: 'User profile updated successfully:', userId });

        res.json({
          success: true,
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            name: updatedUser.username,
            avatar: null,
            subscription_tier: null,
            subscription_status: null,
          },
        });
      } catch (error) {
        logger.error('Update user profile error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get user statistics
  router.get('/stats', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      logger.info({ message: 'GET /api/users/stats - User:', userId });

      // Get project count
      const projectsCount = await prisma.project.count({
        where: { userId: userId },
      });

      // Get total images count
      const imagesCount = await prisma.image.count({
        where: {
          project: { userId: userId },
        },
      });

      // Get segmented images count
      const segmentedCount = await prisma.image.count({
        where: {
          project: { userId: userId },
          segmentationStatus: 'completed',
        },
      });

      // Get storage used using aggregation
      const storageAggregate = await prisma.image.aggregate({
        where: {
          project: { userId: userId },
        },
        _sum: {
          fileSize: true,
        },
      });

      res.json({
        success: true,
        stats: {
          projectsCount,
          imagesCount,
          segmentedCount,
          storageUsed: storageAggregate._sum.fileSize || 0,
          storageLimit: 5368709120, // 5GB default
          processingLimit: 1000, // default monthly limit
        },
      });
    } catch (error) {
      logger.error('Get user stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Get user settings
  router.get('/settings', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      logger.info({ message: 'GET /api/users/settings - User:', userId });

      // In a real app, this would come from a user_settings table
      const defaultSettings = {
        theme: 'light',
        language: 'en',
        notifications: {
          email: true,
          segmentationComplete: true,
          projectShared: true,
        },
        privacy: {
          profileVisible: true,
          statisticsVisible: false,
        },
      };

      res.json({
        success: true,
        settings: defaultSettings,
      });
    } catch (error) {
      logger.error('Get user settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Update user settings
  router.put(
    '/settings',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { theme, language, notifications, privacy } = req.body;

        logger.info({ message: 'PUT /api/users/settings' }, { userId, theme, language });

        // In a real app, this would update a user_settings table
        // For now, just return the updated settings
        res.json({
          success: true,
          settings: {
            theme: theme || 'light',
            language: language || 'en',
            notifications: notifications || {},
            privacy: privacy || {},
          },
        });
      } catch (error) {
        logger.error('Update user settings error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Delete user account
  router.delete(
    '/account',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { confirmPassword } = req.body;

        logger.info({ message: 'DELETE /api/users/account - User requesting deletion:', userId });

        if (!confirmPassword) {
          return res.status(400).json({
            success: false,
            error: 'Password confirmation required',
          });
        }

        // Verify password
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { passwordHash: true },
        });

        if (!user) {
          return res.status(404).json({
            success: false,
            error: 'User not found',
          });
        }

        // In production, verify password with bcrypt
        // const isValid = await bcrypt.compare(confirmPassword, user.passwordHash);

        // Use Prisma transaction to delete user and cascade delete related data
        await prisma.$transaction(async (tx) => {
          // Delete images first (they have foreign key to projects)
          await tx.image.deleteMany({
            where: {
              project: { userId: userId },
            },
          });

          // Delete projects
          await tx.project.deleteMany({
            where: { userId: userId },
          });

          // Delete user
          await tx.user.delete({
            where: { id: userId },
          });
        });

        logger.info({ message: 'User account deleted successfully:', userId });

        res.json({
          success: true,
          message: 'Account deleted successfully',
        });
      } catch (error) {
        logger.error('Delete user account error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  return router;
}

export default createUserRoutes;
