import { Router, Request, Response } from 'express';
import { authenticate } from '../../middleware/authenticate';
import { asyncHandler } from '../../utils/asyncHandler';
import axios from 'axios';
import { logger } from '../../utils/logger';
import { prisma } from '../../prisma';
import { z } from 'zod';

const router = Router();

// ML Service URL
const ML_SERVICE_URL = process.env.ML_SERVICE_URL || 'http://ml:5002';
const ML_SERVICE_API_KEY = process.env.ML_SERVICE_API_KEY || '';

// Validation schemas
const segmentationParamsSchema = z.object({
  modelId: z.string().optional(),
  confidenceThreshold: z.number().min(0).max(1).optional(),
  minArea: z.number().positive().optional(),
  maxArea: z.number().positive().optional(),
  preprocessingOptions: z.object({
    denoise: z.boolean().optional(),
    normalize: z.boolean().optional(),
    resize: z.boolean().optional()
  }).optional()
});

/**
 * Get list of available ML models
 * GET /api/models
 */
router.get('/', authenticate, asyncHandler(async (req: Request, res: Response) => {
  try {
    // Fetch models from ML service
    const response = await axios.get(`${ML_SERVICE_URL}/models`, {
      headers: {
        'X-API-Key': ML_SERVICE_API_KEY
      },
      timeout: 5000
    });

    // Get user's model preferences
    const userId = (req as any).user?.id;
    const userPreferences = await prisma.userPreference.findUnique({
      where: { userId },
      select: {
        defaultModelId: true,
        modelConfigurations: true
      }
    });

    // Merge with user preferences
    const models = response.data.models.map((model: any) => ({
      ...model,
      isUserDefault: userPreferences?.defaultModelId === model.id,
      userConfig: userPreferences?.modelConfigurations?.[model.id] || null
    }));

    res.json({
      models,
      defaultModel: response.data.default,
      userDefaultModel: userPreferences?.defaultModelId || response.data.default,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to fetch ML models:', error);
    
    // Return cached or default models if ML service is unavailable
    res.json({
      models: [
        {
          id: 'resunet_v1',
          name: 'ResUNet Original',
          type: 'resunet',
          version: '1.0',
          description: 'Original ResUNet model',
          default: true,
          performance: {
            accuracy: 0.92,
            inference_time_ms: 150,
            memory_mb: 512
          }
        }
      ],
      defaultModel: 'resunet_v1',
      userDefaultModel: 'resunet_v1',
      timestamp: new Date().toISOString(),
      cached: true
    });
  }
}));

/**
 * Get details of a specific model
 * GET /api/models/:modelId
 */
router.get('/:modelId', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { modelId } = req.params;

  try {
    const response = await axios.get(`${ML_SERVICE_URL}/models/${modelId}`, {
      headers: {
        'X-API-Key': ML_SERVICE_API_KEY
      },
      timeout: 5000
    });

    // Get user's configuration for this model
    const userId = (req as any).user?.id;
    const userPreferences = await prisma.userPreference.findUnique({
      where: { userId },
      select: {
        modelConfigurations: true
      }
    });

    const modelData = {
      ...response.data,
      userConfig: userPreferences?.modelConfigurations?.[modelId] || null
    };

    res.json(modelData);
  } catch (error: any) {
    if (error.response?.status === 404) {
      return res.status(404).json({ error: 'Model not found' });
    }
    
    logger.error(`Failed to fetch model ${modelId}:`, error);
    res.status(500).json({ error: 'Failed to fetch model details' });
  }
}));

/**
 * Set user's default model
 * PUT /api/models/default
 */
router.put('/default', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id;
  const { modelId } = req.body;

  if (!modelId) {
    return res.status(400).json({ error: 'Model ID is required' });
  }

  // Verify model exists
  try {
    await axios.get(`${ML_SERVICE_URL}/models/${modelId}`, {
      headers: {
        'X-API-Key': ML_SERVICE_API_KEY
      },
      timeout: 5000
    });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return res.status(404).json({ error: 'Model not found' });
    }
    logger.error(`Failed to verify model ${modelId}:`, error);
  }

  // Update user preference
  const preference = await prisma.userPreference.upsert({
    where: { userId },
    update: {
      defaultModelId: modelId,
      updatedAt: new Date()
    },
    create: {
      userId,
      defaultModelId: modelId,
      modelConfigurations: {}
    }
  });

  res.json({
    success: true,
    defaultModelId: preference.defaultModelId,
    message: 'Default model updated successfully'
  });
}));

/**
 * Update user's model configuration
 * PUT /api/models/:modelId/config
 */
router.put('/:modelId/config', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id;
  const { modelId } = req.params;
  
  // Validate configuration
  const result = segmentationParamsSchema.safeParse(req.body);
  if (!result.success) {
    return res.status(400).json({ 
      error: 'Invalid configuration',
      details: result.error.errors 
    });
  }

  // Get current preferences
  const currentPrefs = await prisma.userPreference.findUnique({
    where: { userId }
  });

  const modelConfigurations = currentPrefs?.modelConfigurations || {};
  modelConfigurations[modelId] = result.data;

  // Update user preference
  const preference = await prisma.userPreference.upsert({
    where: { userId },
    update: {
      modelConfigurations,
      updatedAt: new Date()
    },
    create: {
      userId,
      modelConfigurations
    }
  });

  res.json({
    success: true,
    modelId,
    configuration: modelConfigurations[modelId],
    message: 'Model configuration updated successfully'
  });
}));

/**
 * Preload a model into ML service memory
 * POST /api/models/:modelId/preload
 */
router.post('/:modelId/preload', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { modelId } = req.params;
  const { device = 'cuda' } = req.body;

  // Check user permissions (admin only)
  const user = (req as any).user;
  if (user?.role !== 'ADMIN') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  try {
    const response = await axios.post(
      `${ML_SERVICE_URL}/models/${modelId}/load`,
      { device },
      {
        headers: {
          'X-API-Key': ML_SERVICE_API_KEY
        },
        timeout: 30000 // 30 seconds for model loading
      }
    );

    res.json(response.data);
  } catch (error: any) {
    logger.error(`Failed to preload model ${modelId}:`, error);
    res.status(error.response?.status || 500).json({ 
      error: 'Failed to preload model',
      details: error.response?.data?.error || error.message
    });
  }
}));

/**
 * Get model performance statistics
 * GET /api/models/:modelId/stats
 */
router.get('/:modelId/stats', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { modelId } = req.params;
  const { startDate, endDate } = req.query;

  // Build date filter
  const dateFilter: any = {};
  if (startDate) {
    dateFilter.gte = new Date(startDate as string);
  }
  if (endDate) {
    dateFilter.lte = new Date(endDate as string);
  }

  // Get segmentation statistics for this model
  const stats = await prisma.segmentationTask.aggregate({
    where: {
      modelId,
      status: 'COMPLETED',
      ...(startDate || endDate ? { completedAt: dateFilter } : {})
    },
    _count: {
      id: true
    },
    _avg: {
      processingTime: true
    },
    _min: {
      processingTime: true
    },
    _max: {
      processingTime: true
    }
  });

  // Get error rate
  const errors = await prisma.segmentationTask.count({
    where: {
      modelId,
      status: 'FAILED',
      ...(startDate || endDate ? { updatedAt: dateFilter } : {})
    }
  });

  const totalTasks = stats._count.id + errors;
  const errorRate = totalTasks > 0 ? (errors / totalTasks) * 100 : 0;

  // Get usage by day
  const dailyUsage = await prisma.$queryRaw`
    SELECT 
      DATE(completed_at) as date,
      COUNT(*) as count,
      AVG(processing_time) as avg_time
    FROM segmentation_tasks
    WHERE model_id = ${modelId}
      AND status = 'COMPLETED'
      ${startDate ? `AND completed_at >= ${new Date(startDate as string)}` : ''}
      ${endDate ? `AND completed_at <= ${new Date(endDate as string)}` : ''}
    GROUP BY DATE(completed_at)
    ORDER BY date DESC
    LIMIT 30
  `;

  res.json({
    modelId,
    statistics: {
      totalProcessed: stats._count.id,
      totalErrors: errors,
      errorRate: errorRate.toFixed(2),
      averageProcessingTime: stats._avg.processingTime || 0,
      minProcessingTime: stats._min.processingTime || 0,
      maxProcessingTime: stats._max.processingTime || 0
    },
    dailyUsage,
    period: {
      startDate: startDate || 'all-time',
      endDate: endDate || 'current'
    }
  });
}));

/**
 * Compare multiple models
 * POST /api/models/compare
 */
router.post('/compare', authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { modelIds, imageId } = req.body;

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length < 2) {
    return res.status(400).json({ error: 'At least 2 model IDs required for comparison' });
  }

  if (modelIds.length > 4) {
    return res.status(400).json({ error: 'Maximum 4 models can be compared at once' });
  }

  // Get performance metrics for each model
  const comparisons = await Promise.all(
    modelIds.map(async (modelId: string) => {
      // Get model info
      let modelInfo;
      try {
        const response = await axios.get(`${ML_SERVICE_URL}/models/${modelId}`, {
          headers: { 'X-API-Key': ML_SERVICE_API_KEY },
          timeout: 5000
        });
        modelInfo = response.data;
      } catch (error) {
        modelInfo = { id: modelId, name: modelId, error: 'Model info unavailable' };
      }

      // Get performance stats
      const stats = await prisma.segmentationTask.aggregate({
        where: {
          modelId,
          status: 'COMPLETED'
        },
        _count: { id: true },
        _avg: { processingTime: true }
      });

      // Get results for specific image if provided
      let imageResults = null;
      if (imageId) {
        const segmentation = await prisma.segmentationTask.findFirst({
          where: {
            imageId,
            modelId,
            status: 'COMPLETED'
          },
          orderBy: { completedAt: 'desc' },
          select: {
            id: true,
            processingTime: true,
            polygonCount: true,
            completedAt: true
          }
        });
        imageResults = segmentation;
      }

      return {
        modelId,
        modelInfo,
        performance: {
          totalProcessed: stats._count.id,
          averageTime: stats._avg.processingTime || 0
        },
        imageResults
      };
    })
  );

  res.json({
    comparisons,
    imageId,
    timestamp: new Date().toISOString()
  });
}));

export default router;