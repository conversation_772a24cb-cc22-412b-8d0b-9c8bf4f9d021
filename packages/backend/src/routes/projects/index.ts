/**
 * Project Routes Module
 * Handles all project-related endpoints
 */

import { Router, Request, Response } from 'express';
import * as crypto from 'crypto';
import { createLogger } from '../../utils/logger';
import { prisma } from '../../services/PrismaService';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import { validateCsrfToken } from '../../middleware/csrfProtection';

const logger = createLogger('ProjectRoutes');
const router = Router();

interface AuthenticatedRequest extends Request {
  userId?: string;
}

export function createProjectRoutes(): Router {
  // Get all projects for user
  router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      logger.info({ message: 'GET /api/projects - User:', userId });

      const projects = await prisma.project.findMany({
        where: { userId: userId },
        select: {
          id: true,
          title: true,
          description: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { updatedAt: 'desc' },
      });

      // Convert to snake_case for API response consistency
      const formattedProjects = projects.map((project) => ({
        id: project.id,
        name: project.title,
        description: project.description,
        status: 'active', // Default status since not in schema
        image_count: 0, // Will need to count separately if needed
        created_at: project.createdAt,
        updated_at: project.updatedAt,
      }));

      res.json({
        success: true,
        projects: formattedProjects,
      });
    } catch (error) {
      logger.error('Get projects error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Create new project
  router.post(
    '/',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { name, description } = req.body;

        logger.info({ message: 'POST /api/projects - Creating project:' }, { name, userId });

        if (!name) {
          return res.status(400).json({
            success: false,
            error: 'Project name is required',
          });
        }

        const project = await prisma.project.create({
          data: {
            id: crypto.randomUUID(),
            userId: userId!,
            title: name,
            description: description || '',
          },
          select: {
            id: true,
            title: true,
            description: true,
            createdAt: true,
            updatedAt: true,
          },
        });

        logger.info({ message: 'Project created successfully:', projectId: project.id });

        res.status(201).json({
          success: true,
          project: {
            id: project.id,
            name: project.title,
            description: project.description,
            status: 'active',
            image_count: 0,
            created_at: project.createdAt,
            updated_at: project.updatedAt,
          },
        });
      } catch (error) {
        logger.error('Create project error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get single project
  router.get('/:projectId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      const { projectId } = req.params;

      logger.info({ message: 'GET /api/projects/:projectId' }, { projectId, userId });

      const project = await prisma.project.findFirst({
        where: {
          id: projectId,
          userId: userId,
        },
        select: {
          id: true,
          title: true,
          description: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!project) {
        return res.status(404).json({
          success: false,
          error: 'Project not found',
        });
      }

      res.json({
        success: true,
        project: {
          id: project.id,
          name: project.title,
          description: project.description,
          status: 'active',
          image_count: 0,
          created_at: project.createdAt,
          updated_at: project.updatedAt,
        },
      });
    } catch (error) {
      logger.error('Get project error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Update project
  router.put(
    '/:projectId',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { projectId } = req.params;
        const { name, description, status } = req.body;

        logger.info({ message: 'PUT /api/projects/:projectId' }, { projectId, userId });

        // Build update data dynamically
        const updateData: any = {};
        if (name !== undefined) updateData.title = name;
        if (description !== undefined) updateData.description = description;

        if (Object.keys(updateData).length === 0) {
          return res.status(400).json({
            success: false,
            error: 'No fields to update',
          });
        }

        try {
          // First verify ownership, then update
          const existingProject = await prisma.project.findFirst({
            where: {
              id: projectId,
              userId: userId!,
            },
          });

          if (!existingProject) {
            return res.status(404).json({
              success: false,
              error: 'Project not found',
            });
          }

          const updatedProject = await prisma.project.update({
            where: { id: projectId },
            data: updateData,
            select: {
              id: true,
              title: true,
              description: true,
              createdAt: true,
              updatedAt: true,
            },
          });

          logger.info({ message: 'Project updated successfully:', projectId });

          res.json({
            success: true,
            project: {
              id: updatedProject.id,
              name: updatedProject.title,
              description: updatedProject.description,
              status: 'active',
              image_count: 0,
              created_at: updatedProject.createdAt,
              updated_at: updatedProject.updatedAt,
            },
          });
        } catch (error: any) {
          if (error.code === 'P2025') {
            return res.status(404).json({
              success: false,
              error: 'Project not found',
            });
          }
          throw error;
        }
      } catch (error) {
        logger.error('Update project error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Delete project
  router.delete(
    '/:projectId',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { projectId } = req.params;

        logger.info({ message: 'DELETE /api/projects/:projectId' }, { projectId, userId });

        // Use Prisma transaction to delete project and related images
        try {
          await prisma.$transaction(async (tx) => {
            // Check if project exists and belongs to user
            const project = await tx.project.findFirst({
              where: {
                id: projectId,
                userId: userId,
              },
            });

            if (!project) {
              throw new Error('Project not found');
            }

            // Delete associated images first (they have foreign key to project)
            await tx.image.deleteMany({
              where: { projectId: projectId },
            });

            // Delete project
            await tx.project.delete({
              where: { id: projectId },
            });
          });
        } catch (error: any) {
          if (error.message === 'Project not found') {
            return res.status(404).json({
              success: false,
              error: 'Project not found',
            });
          }
          throw error;
        }

        logger.info({ message: 'Project deleted successfully:', projectId });

        res.json({
          success: true,
          message: 'Project deleted successfully',
        });
      } catch (error) {
        logger.error('Delete project error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get project images
  router.get(
    '/:projectId/images',
    authenticateToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { projectId } = req.params;

        logger.info({ message: 'GET /api/projects/:projectId/images' }, { projectId, userId });

        // Get images with project ownership verification
        const images = await prisma.image.findMany({
          where: {
            projectId: projectId,
            project: {
              userId: userId,
            },
          },
          select: {
            id: true,
            storageFilename: true,
            originalFilename: true,
            fileSize: true,
            width: true,
            height: true,
            uploadDate: true,
            segmentationStatus: true,
          },
          orderBy: { uploadDate: 'desc' },
        });

        // If no images found, verify project exists
        if (images.length === 0) {
          const project = await prisma.project.findFirst({
            where: {
              id: projectId,
              userId: userId,
            },
          });

          if (!project) {
            return res.status(404).json({
              success: false,
              error: 'Project not found',
            });
          }
        }

        // Convert to snake_case for API response consistency
        const formattedImages = images.map((image) => ({
          id: image.id,
          filename: image.storageFilename,
          original_filename: image.originalFilename,
          file_size: image.fileSize,
          width: image.width,
          height: image.height,
          upload_date: image.uploadDate,
          segmentation_status: image.segmentationStatus,
        }));

        res.json({
          success: true,
          images: formattedImages,
        });
      } catch (error) {
        logger.error('Get project images error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Project statistics
  router.get(
    '/:projectId/stats',
    authenticateToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { projectId } = req.params;

        logger.info({ message: 'GET /api/projects/:projectId/stats' }, { projectId, userId });

        // Verify project ownership and get statistics
        const project = await prisma.project.findFirst({
          where: {
            id: projectId,
            userId: userId,
          },
        });

        if (!project) {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
          });
        }

        // Get image statistics using aggregation
        const [totalCount, completedCount, processingCount, pendingCount, sizeAggregate] =
          await Promise.all([
            prisma.image.count({
              where: { projectId: projectId },
            }),
            prisma.image.count({
              where: {
                projectId: projectId,
                segmentationStatus: 'completed',
              },
            }),
            prisma.image.count({
              where: {
                projectId: projectId,
                segmentationStatus: 'processing',
              },
            }),
            prisma.image.count({
              where: {
                projectId: projectId,
                segmentationStatus: 'pending',
              },
            }),
            prisma.image.aggregate({
              where: { projectId: projectId },
              _sum: { fileSize: true },
            }),
          ]);

        res.json({
          success: true,
          stats: {
            totalImages: totalCount,
            completedImages: completedCount,
            processingImages: processingCount,
            pendingImages: pendingCount,
            totalSize: sizeAggregate._sum.fileSize || 0,
          },
        });
      } catch (error) {
        logger.error('Get project stats error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  return router;
}

export default createProjectRoutes;
