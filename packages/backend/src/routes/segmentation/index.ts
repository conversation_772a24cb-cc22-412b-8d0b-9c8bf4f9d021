/**
 * Segmentation Routes Module
 * Handles segmentation import and processing operations
 */

import { Router, Request, Response } from 'express';
import multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import sharp from 'sharp';
import { createLogger } from '../../utils/logger';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import { validateCsrfToken } from '../../middleware/csrfProtection';

const logger = createLogger('SegmentationRoutes');
const router = Router();

interface AuthenticatedRequest extends Request {
  userId?: string;
}

interface Point {
  x: number;
  y: number;
}

interface Polygon {
  id: string;
  points: Point[];
  type: 'external' | 'internal';
  class?: string;
  color?: string;
}

// Configure multer for mask uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../../uploads/temp');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `mask-${Date.now()}-${crypto.randomBytes(8).toString('hex')}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|bmp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

export function createSegmentationRoutes(): Router {
  /**
   * Import polygons from binary mask image
   */
  router.post(
    '/import/mask',
    authenticateToken,
    upload.single('file'),
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const file = req.file;
        const { imageWidth, imageHeight } = req.body;

        if (!file) {
          return res.status(400).json({
            success: false,
            errors: ['No mask file provided'],
            warnings: []
          });
        }

        if (!imageWidth || !imageHeight) {
          return res.status(400).json({
            success: false,
            errors: ['Image dimensions (imageWidth, imageHeight) are required'],
            warnings: []
          });
        }

        logger.info({ message: 'Binary mask import:', userId, filename: file.filename });

        const errors: string[] = [];
        const warnings: string[] = [];
        const polygons: Polygon[] = [];

        try {
          // Load and process the mask image using Sharp
          const image = sharp(file.path);
          const metadata = await image.metadata();
          
          if (!metadata.width || !metadata.height) {
            errors.push('Unable to read image dimensions');
          } else {
            // Convert image to grayscale and get raw pixel data
            const { data, info } = await image
              .grayscale()
              .raw()
              .toBuffer({ resolveWithObject: true });

            // Convert binary mask to polygons using marching squares algorithm
            const extractedPolygons = await extractPolygonsFromMask(
              data,
              info.width,
              info.height,
              parseInt(imageWidth),
              parseInt(imageHeight)
            );

            polygons.push(...extractedPolygons);

            if (polygons.length === 0) {
              warnings.push('No polygons found in mask image');
            } else {
              logger.info(`Extracted ${polygons.length} polygons from mask`);
            }
          }
        } catch (processingError) {
          logger.error('Mask processing error:', processingError);
          errors.push(`Failed to process mask: ${processingError instanceof Error ? processingError.message : String(processingError)}`);
        }

        // Clean up temporary file
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          logger.warn('Failed to clean up temporary file:', cleanupError);
        }

        res.json({
          success: errors.length === 0,
          polygons,
          errors,
          warnings
        });
      } catch (error) {
        logger.error('Mask import error:', error);
        
        // Clean up file on error
        if (req.file) {
          try {
            fs.unlinkSync(req.file.path);
          } catch (cleanupError) {
            logger.warn('Failed to clean up file after error:', cleanupError);
          }
        }

        res.status(500).json({
          success: false,
          polygons: [],
          errors: ['Internal server error'],
          warnings: []
        });
      }
    }
  );

  return router;
}

/**
 * Extract polygons from binary mask using simplified contour detection
 * This is a basic implementation - for production, consider using OpenCV or similar
 */
async function extractPolygonsFromMask(
  pixelData: Buffer,
  maskWidth: number,
  maskHeight: number,
  targetWidth: number,
  targetHeight: number
): Promise<Polygon[]> {
  const polygons: Polygon[] = [];
  
  try {
    // Create a binary array from pixel data (assuming grayscale)
    const binaryMask: boolean[][] = [];
    for (let y = 0; y < maskHeight; y++) {
      binaryMask[y] = [];
      for (let x = 0; x < maskWidth; x++) {
        const pixelIndex = y * maskWidth + x;
        // Consider pixels with value > 127 as foreground
        binaryMask[y][x] = pixelData[pixelIndex] > 127;
      }
    }

    // Simple flood fill to find connected components
    const visited: boolean[][] = Array.from({ length: maskHeight }, () => Array(maskWidth).fill(false));
    let polygonId = 0;

    for (let y = 0; y < maskHeight; y++) {
      for (let x = 0; x < maskWidth; x++) {
        if (binaryMask[y][x] && !visited[y][x]) {
          const boundary = extractBoundary(binaryMask, visited, x, y, maskWidth, maskHeight);
          
          if (boundary.length >= 3) {
            // Scale coordinates to target dimensions
            const scaledPoints: Point[] = boundary.map(point => ({
              x: (point.x / maskWidth) * targetWidth,
              y: (point.y / maskHeight) * targetHeight
            }));

            // Simplify polygon to reduce number of points
            const simplifiedPoints = simplifyPolygon(scaledPoints, 2.0); // 2 pixel tolerance

            if (simplifiedPoints.length >= 3) {
              polygons.push({
                id: `mask_${polygonId++}`,
                points: simplifiedPoints,
                type: 'external',
                class: 'imported'
              });
            }
          }
        }
      }
    }

    return polygons;
  } catch (error) {
    throw new Error(`Failed to extract polygons: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Extract boundary points of a connected component using flood fill
 */
function extractBoundary(
  binaryMask: boolean[][],
  visited: boolean[][],
  startX: number,
  startY: number,
  width: number,
  height: number
): Point[] {
  const boundary: Point[] = [];
  const stack: Point[] = [{ x: startX, y: startY }];
  const component: Point[] = [];

  // Flood fill to find all points in the component
  while (stack.length > 0) {
    const point = stack.pop()!;
    const { x, y } = point;

    if (x < 0 || x >= width || y < 0 || y >= height || visited[y][x] || !binaryMask[y][x]) {
      continue;
    }

    visited[y][x] = true;
    component.push(point);

    // Add neighbors to stack
    stack.push(
      { x: x + 1, y },
      { x: x - 1, y },
      { x, y: y + 1 },
      { x, y: y - 1 }
    );
  }

  // Find boundary points (points that have at least one background neighbor)
  const directions = [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]];
  
  for (const point of component) {
    const { x, y } = point;
    let isBoundary = false;

    for (const [dx, dy] of directions) {
      const nx = x + dx;
      const ny = y + dy;

      if (nx < 0 || nx >= width || ny < 0 || ny >= height || !binaryMask[ny][nx]) {
        isBoundary = true;
        break;
      }
    }

    if (isBoundary) {
      boundary.push(point);
    }
  }

  // Sort boundary points to form a polygon (simple approach)
  if (boundary.length > 0) {
    return sortBoundaryPoints(boundary);
  }

  return boundary;
}

/**
 * Sort boundary points to form a coherent polygon outline
 * This is a simplified implementation - in production, use proper boundary tracing
 */
function sortBoundaryPoints(points: Point[]): Point[] {
  if (points.length <= 3) {
    return points;
  }

  // Find centroid
  const centroidX = points.reduce((sum, p) => sum + p.x, 0) / points.length;
  const centroidY = points.reduce((sum, p) => sum + p.y, 0) / points.length;

  // Sort points by angle from centroid
  return points.sort((a, b) => {
    const angleA = Math.atan2(a.y - centroidY, a.x - centroidX);
    const angleB = Math.atan2(b.y - centroidY, b.x - centroidX);
    return angleA - angleB;
  });
}

/**
 * Simplify polygon using Douglas-Peucker algorithm
 */
function simplifyPolygon(points: Point[], tolerance: number): Point[] {
  if (points.length <= 2) {
    return points;
  }

  // Find the point with maximum distance from line between first and last points
  let maxDistance = 0;
  let maxIndex = 0;
  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i < points.length - 1; i++) {
    const distance = pointToLineDistance(points[i], firstPoint, lastPoint);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  // If max distance is greater than tolerance, recursively simplify
  if (maxDistance > tolerance) {
    const leftPoints = simplifyPolygon(points.slice(0, maxIndex + 1), tolerance);
    const rightPoints = simplifyPolygon(points.slice(maxIndex), tolerance);
    
    // Combine results, avoiding duplicate middle point
    return [...leftPoints.slice(0, -1), ...rightPoints];
  } else {
    // All points are within tolerance, return only endpoints
    return [firstPoint, lastPoint];
  }
}

/**
 * Calculate perpendicular distance from point to line
 */
function pointToLineDistance(point: Point, lineStart: Point, lineEnd: Point): number {
  const A = lineEnd.x - lineStart.x;
  const B = lineEnd.y - lineStart.y;
  const C = point.x - lineStart.x;
  const D = point.y - lineStart.y;

  const dot = A * C + B * D;
  const lenSq = A * A + B * B;
  
  if (lenSq === 0) {
    // Line start and end are the same point
    return Math.sqrt(C * C + D * D);
  }

  const param = dot / lenSq;
  let xx: number, yy: number;

  if (param < 0) {
    xx = lineStart.x;
    yy = lineStart.y;
  } else if (param > 1) {
    xx = lineEnd.x;
    yy = lineEnd.y;
  } else {
    xx = lineStart.x + param * A;
    yy = lineStart.y + param * B;
  }

  const dx = point.x - xx;
  const dy = point.y - yy;
  
  return Math.sqrt(dx * dx + dy * dy);
}