/**
 * Comprehensive Authentication Routes Test Suite
 *
 * Tests all authentication endpoints with proper mocking and error handling
 */

// Mock dependencies before imports
jest.mock('../../services/authService');
jest.mock('../../utils/logger');
jest.mock('../../utils/responseHelpers', () => ({
  sendSuccess: jest.fn((res, data) => res.json(data)),
  sendCreated: jest.fn((res, data) => res.status(201).json(data)),
  sendUnauthorized: jest.fn((res, message) => res.status(401).json({ message })),
  asyncHandler: jest.fn((fn) => fn),
}));

import request from 'supertest';
import express from 'express';
import authRouter from '../auth';
import authService from '../../services/authService';
import logger from '../../utils/logger';
import { ApiError } from '../../utils/errors';
import { ErrorCode } from '../../utils/ApiError';

// Type the mocked modules
const mockedAuthService = authService as jest.Mocked<typeof authService>;
const mockedLogger = logger as jest.Mocked<typeof logger>;

// Create test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRouter);

// Add error handler
app.use((err: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  const statusCode = err.statusCode || 500;
  res.status(statusCode).json({
    message: err.message,
    code: err.code,
  });
});

describe('Authentication Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Setup default logger behavior
    mockedLogger.info = jest.fn();
    mockedLogger.error = jest.fn();
    mockedLogger.warn = jest.fn();
  });

  describe('POST /api/auth/register', () => {
    const validRegistrationData = {
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      name: 'Test User',
    };

    it('should register a new user successfully', async () => {
      const mockResponse = {
        user: {
          id: 'user-123',
          email: validRegistrationData.email,
          name: validRegistrationData.name,
          created_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      mockedAuthService.registerUser.mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/register').send(validRegistrationData);

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        user: expect.objectContaining({
          id: mockResponse.user.id,
          email: mockResponse.user.email,
          name: mockResponse.user.name,
        }),
        accessToken: mockResponse.accessToken,
        refreshToken: mockResponse.refreshToken,
        tokenType: mockResponse.tokenType,
      });
      expect(mockedAuthService.registerUser).toHaveBeenCalledWith(
        validRegistrationData.email,
        validRegistrationData.password,
        validRegistrationData.name,
        undefined // preferred_language
      );
    });

    it('should handle registration with preferred language', async () => {
      const dataWithLanguage = {
        ...validRegistrationData,
        preferred_language: 'cs',
      };

      const mockResponse = {
        user: {
          id: 'user-123',
          email: dataWithLanguage.email,
          name: dataWithLanguage.name,
          created_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      mockedAuthService.registerUser.mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/register').send(dataWithLanguage);

      expect(response.status).toBe(201);
      expect(mockedAuthService.registerUser).toHaveBeenCalledWith(
        dataWithLanguage.email,
        dataWithLanguage.password,
        dataWithLanguage.name,
        dataWithLanguage.preferred_language
      );
    });

    it('should return 409 if email already exists', async () => {
      mockedAuthService.registerUser.mockRejectedValue(
        new ApiError('Email already in use', 409, ErrorCode.RESOURCE_CONFLICT)
      );

      const response = await request(app).post('/api/auth/register').send(validRegistrationData);

      expect(response.status).toBe(409);
      expect(response.body.message).toBe('Email already in use');
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...validRegistrationData,
          email: 'invalid-email',
        });

      expect(response.status).toBe(400);
      expect(mockedAuthService.registerUser).not.toHaveBeenCalled();
    });

    it('should return 400 for weak password', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...validRegistrationData,
          password: '123', // Too weak
        });

      expect(response.status).toBe(400);
      expect(mockedAuthService.registerUser).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should login user successfully', async () => {
      const mockResponse = {
        user: {
          id: 'user-123',
          email: validLoginData.email,
          name: 'Test User',
          created_at: new Date(),
          updated_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      mockedAuthService.loginUser.mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/login').send(validLoginData);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        user: expect.objectContaining({
          id: mockResponse.user.id,
          email: mockResponse.user.email,
        }),
        accessToken: mockResponse.accessToken,
        refreshToken: mockResponse.refreshToken,
        tokenType: mockResponse.tokenType,
      });
      expect(mockedAuthService.loginUser).toHaveBeenCalledWith(
        validLoginData.email,
        validLoginData.password,
        undefined // fingerprint
      );
    });

    it('should return 401 for invalid credentials', async () => {
      mockedAuthService.loginUser.mockRejectedValue(
        new ApiError('Invalid email or password', 401, ErrorCode.INVALID_CREDENTIALS)
      );

      const response = await request(app).post('/api/auth/login').send(validLoginData);

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should handle login with fingerprint', async () => {
      const dataWithFingerprint = {
        ...validLoginData,
        fingerprint: 'device-fingerprint-123',
      };

      const mockResponse = {
        user: {
          id: 'user-123',
          email: validLoginData.email,
          name: 'Test User',
          created_at: new Date(),
          updated_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      mockedAuthService.loginUser.mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/login').send(dataWithFingerprint);

      expect(response.status).toBe(200);
      expect(mockedAuthService.loginUser).toHaveBeenCalledWith(
        dataWithFingerprint.email,
        dataWithFingerprint.password,
        dataWithFingerprint.fingerprint
      );
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh access token successfully', async () => {
      const mockResponse = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        tokenType: 'Bearer',
      };

      mockedAuthService.refreshAccessToken.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'old-refresh-token' });

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockResponse);
      expect(mockedAuthService.refreshAccessToken).toHaveBeenCalledWith('old-refresh-token');
    });

    it('should return 401 for invalid refresh token', async () => {
      mockedAuthService.refreshAccessToken.mockRejectedValue(
        new ApiError('Invalid refresh token', 401, ErrorCode.INVALID_TOKEN)
      );

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' });

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('Invalid refresh token');
    });

    it('should return 400 if refresh token is missing', async () => {
      const response = await request(app).post('/api/auth/refresh').send({});

      expect(response.status).toBe(400);
      expect(mockedAuthService.refreshAccessToken).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    it('should send password reset email successfully', async () => {
      mockedAuthService.forgotPassword.mockResolvedValue();

      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe(
        'If an account exists with that email, a password reset link has been sent'
      );
      expect(mockedAuthService.forgotPassword).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should return success even for non-existent email', async () => {
      mockedAuthService.forgotPassword.mockResolvedValue();

      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe(
        'If an account exists with that email, a password reset link has been sent'
      );
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: 'invalid-email' });

      expect(response.status).toBe(400);
      expect(mockedAuthService.forgotPassword).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/reset-password', () => {
    it('should reset password successfully', async () => {
      mockedAuthService.resetPassword.mockResolvedValue();

      const response = await request(app).post('/api/auth/reset-password').send({
        token: 'reset-token-123',
        newPassword: 'NewSecurePassword123!',
      });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Password has been reset successfully');
      expect(mockedAuthService.resetPassword).toHaveBeenCalledWith(
        'reset-token-123',
        'NewSecurePassword123!'
      );
    });

    it('should return 400 for invalid token', async () => {
      mockedAuthService.resetPassword.mockRejectedValue(
        new ApiError('Invalid or expired reset token', 400, ErrorCode.INVALID_TOKEN)
      );

      const response = await request(app).post('/api/auth/reset-password').send({
        token: 'invalid-token',
        newPassword: 'NewSecurePassword123!',
      });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid or expired reset token');
    });

    it('should return 400 for weak password', async () => {
      const response = await request(app).post('/api/auth/reset-password').send({
        token: 'reset-token-123',
        newPassword: '123', // Too weak
      });

      expect(response.status).toBe(400);
      expect(mockedAuthService.resetPassword).not.toHaveBeenCalled();
    });
  });

  describe('GET /api/auth/check-email', () => {
    it('should return exists=true for existing email', async () => {
      mockedAuthService.checkEmailExists.mockResolvedValue({
        exists: true,
        hasAccessRequest: false,
      });

      const response = await request(app)
        .get('/api/auth/check-email')
        .query({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        exists: true,
        hasAccessRequest: false,
      });
    });

    it('should return exists=false for non-existent email', async () => {
      mockedAuthService.checkEmailExists.mockResolvedValue({
        exists: false,
        hasAccessRequest: false,
      });

      const response = await request(app)
        .get('/api/auth/check-email')
        .query({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        exists: false,
        hasAccessRequest: false,
      });
    });

    it('should return 400 if email is missing', async () => {
      const response = await request(app).get('/api/auth/check-email');

      expect(response.status).toBe(400);
      expect(mockedAuthService.checkEmailExists).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/verify-email/:token', () => {
    it('should verify email successfully', async () => {
      mockedAuthService.verifyEmail.mockResolvedValue();

      const response = await request(app).post('/api/auth/verify-email/verification-token-123');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Email verified successfully');
      expect(mockedAuthService.verifyEmail).toHaveBeenCalledWith('verification-token-123');
    });

    it('should return 400 for invalid verification token', async () => {
      mockedAuthService.verifyEmail.mockRejectedValue(
        new ApiError('Invalid verification token', 400, ErrorCode.INVALID_TOKEN)
      );

      const response = await request(app).post('/api/auth/verify-email/invalid-token');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid verification token');
    });
  });
});
