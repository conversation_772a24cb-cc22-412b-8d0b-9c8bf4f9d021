/**
 * Routes Index Tests
 * Tests for the main router configuration and legacy route consolidation
 */

import request from 'supertest';
import express from 'express';
import apiRouter from '../index';
import { createTestApp } from '../../test-utils';

describe('API Router Configuration', () => {
  let app: express.Application;

  beforeAll(() => {
    app = createTestApp();
    app.use('/api', apiRouter);
  });

  describe('Core API Routes', () => {
    it('should serve root API info', async () => {
      const response = await request(app).get('/api/');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', 'SpheroSeg API');
      expect(response.body).toHaveProperty('status', 'operational');
      expect(response.body).toHaveProperty('version');
    });

    it('should mount health routes at /api/health', async () => {
      const response = await request(app).get('/api/health');

      expect([200, 503]).toContain(response.status);
      expect(response.body).toHaveProperty('overall');
    });

    it('should mount v1 routes at /api/v1', async () => {
      const response = await request(app).get('/api/v1/version');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('version', 'v1');
      expect(response.body).toHaveProperty('apiVersion', '1.0.0');
    });
  });

  describe('Legacy Route Consolidation', () => {
    it('should not have duplicate auth route mountings', async () => {
      // Test that auth routes are properly consolidated
      const authTestResponse = await request(app).get('/api/auth/test');
      expect(authTestResponse.status).toBe(200);

      // Should not have conflicting routes
      const headers = authTestResponse.headers;
      expect(headers).not.toHaveProperty('x-duplicate-route');
    });

    it('should handle project route priority correctly', async () => {
      // Test that imageRoutes mounted before projectRoutes works correctly
      // This ensures /:projectId/images matches before /:id
      const _response = await request(app)
        .get('/api/projects/test-project/images')
        .expect((res) => {
          // Should not match as generic project ID
          expect([200, 401, 404]).toContain(res.status); // Valid responses, not 500
        });
    });

    it('should handle segmentation route aliases', async () => {
      // Test both /segmentation and /segmentations work
      const segmentationResponse = await request(app).get('/api/segmentation');
      const segmentationsResponse = await request(app).get('/api/segmentations');

      // Both should respond similarly (auth required)
      expect([200, 401, 404]).toContain(segmentationResponse.status);
      expect([200, 401, 404]).toContain(segmentationsResponse.status);
    });

    it('should handle metrics route consolidation', async () => {
      // Test that both Prometheus and custom metrics work
      const prometheusResponse = await request(app).get('/api/metrics');
      expect([200, 401]).toContain(prometheusResponse.status);

      const performanceResponse = await request(app).get('/api/metrics/performance');
      expect([200, 401]).toContain(performanceResponse.status);
    });
  });

  describe('Debug Routes (Development Only)', () => {
    it('should exclude debug routes in production', () => {
      const originalEnv = process.env.NODE_ENV;

      try {
        process.env.NODE_ENV = 'production';

        // Re-require the router to test production behavior
        jest.resetModules();
        const prodRouter = require('../index').default;
        const prodApp = express();
        prodApp.use('/api', prodRouter);

        return request(prodApp).get('/api/debug').expect(404);
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });

    it('should include debug routes in development', async () => {
      const originalEnv = process.env.NODE_ENV;

      try {
        process.env.NODE_ENV = 'development';

        const response = await request(app).get('/api/debug');
        // Should be available in development (may require auth)
        expect([200, 401, 404]).toContain(response.status);
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
  });

  describe('Route Security Headers', () => {
    it('should set API version headers for v1 routes', async () => {
      const response = await request(app).get('/api/v1/version');

      expect(response.headers).toHaveProperty('api-version', 'v1');
    });

    it('should not expose internal route information', async () => {
      const response = await request(app).get('/api/');

      // Should not expose sensitive internal information
      expect(response.body).not.toHaveProperty('routes');
      expect(response.body).not.toHaveProperty('config');
      expect(response.body).not.toHaveProperty('environment');
    });
  });

  describe('Route Error Handling', () => {
    it('should handle invalid routes gracefully', async () => {
      const response = await request(app).get('/api/nonexistent-route');

      expect(response.status).toBe(404);
    });

    it('should handle malformed requests gracefully', async () => {
      const response = await request(app).post('/api/auth/login').send('invalid-json');

      expect([400, 422]).toContain(response.status);
    });
  });
});
