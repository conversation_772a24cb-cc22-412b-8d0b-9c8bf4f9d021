/**
 * Authentication Integration Tests
 * Tests the integration between different auth routes and frontend-backend communication
 */

import request from 'supertest';
import express from 'express';
import authRouter from '../auth';
import authSessionRouter from '../auth.session';
import { createTestApp } from '../../test-utils';

describe('Authentication Integration Tests', () => {
  let app: express.Application;

  beforeAll(() => {
    app = createTestApp();
    // Mount both auth routers as they are in the main app
    app.use('/api/auth', authRouter);
    app.use('/api/auth', authSessionRouter);
  });

  describe('Token Format Consistency', () => {
    it('should return accessToken in login response (not token)', async () => {
      // Mock a successful login
      jest.mock('../../services/authService', () => ({
        default: {
          loginUser: jest.fn().mockResolvedValue({
            user: {
              id: 'test-user-id',
              email: '<EMAIL>',
              name: 'Test User',
            },
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            tokenType: 'Bearer',
          }),
        },
      }));

      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      // Should have accessToken field for frontend compatibility
      if (response.status === 200) {
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).not.toHaveProperty('token'); // Avoid legacy token field
      }
    });

    it('should return consistent token format in refresh endpoint', async () => {
      const response = await request(app).post('/api/auth/refresh-token').send({
        refreshToken: 'mock-refresh-token',
      });

      if (response.status === 200) {
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body).toHaveProperty('refreshToken');
        expect(response.body).not.toHaveProperty('token');
      }
    });
  });

  describe('Route Conflict Resolution', () => {
    it('should handle auth.ts and auth.session.ts route conflicts', async () => {
      // Test that both routers can coexist without conflicts
      const regularLoginResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      // Both should handle the same endpoint without 500 errors
      expect(regularLoginResponse.status).toBeOneOf([200, 401, 422, 400]);
      expect(regularLoginResponse.status).not.toBe(500);
    });

    it('should not have duplicate CSRF token endpoints', async () => {
      const csrfResponse = await request(app).get('/api/auth/csrf-token');

      expect(csrfResponse.status).toBeOneOf([200, 404]);
      // Should not return duplicate headers or responses
      expect(csrfResponse.headers['content-length']).toBeDefined();
    });
  });

  describe('Session and JWT Compatibility', () => {
    it('should support both session and JWT authentication', async () => {
      // Test that session-based auth works
      const sessionResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password123',
        remember_me: true,
      });

      if (sessionResponse.status === 200) {
        // Should set session cookie
        const setCookieHeader = sessionResponse.headers['set-cookie'];
        expect(setCookieHeader).toBeDefined();

        // Should also return JWT tokens for backward compatibility
        expect(sessionResponse.body).toHaveProperty('accessToken');
      }
    });
  });

  describe('Frontend API Contract', () => {
    it('should match frontend expected response format for login', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'validpassword',
      });

      if (response.status === 200) {
        // Frontend expects this exact structure
        expect(response.body).toMatchObject({
          success: expect.any(Boolean),
          message: expect.any(String),
          user: expect.objectContaining({
            id: expect.any(String),
            email: expect.any(String),
          }),
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        });
      }
    });

    it('should match frontend expected error format', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      if (response.status === 401) {
        expect(response.body).toMatchObject({
          success: false,
          message: expect.any(String),
          error: expect.any(String),
        });
      }
    });

    it('should handle CORS preflight requests correctly', async () => {
      const response = await request(app)
        .options('/api/auth/login')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST');

      expect(response.status).toBeOneOf([200, 204]);
      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
  });

  describe('Route Performance', () => {
    it('should respond to auth endpoints within reasonable time', async () => {
      const start = Date.now();

      const response = await request(app).get('/api/auth/test');

      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should respond within 1 second
      expect(response.status).toBe(200);
    });
  });

  describe('Error Handling Integration', () => {
    it('should propagate errors through middleware correctly', async () => {
      // Test malformed JSON
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"malformed": json}');

      expect(response.status).toBeOneOf([400, 422]);
      expect(response.body).toHaveProperty('success', false);
    });

    it('should handle database connection errors gracefully', async () => {
      // This would need actual database mocking in a real test
      // For now, test that 500 errors have proper format
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      if (response.status === 500) {
        expect(response.body).toHaveProperty('success', false);
        expect(response.body).toHaveProperty('error');
        expect(response.body).not.toHaveProperty('stack'); // No stack traces in response
      }
    });
  });
});
