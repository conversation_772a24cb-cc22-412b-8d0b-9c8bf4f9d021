/**
 * Users Route Integration Tests
 * Tests for user-related routes including authentication endpoints
 */

import request from 'supertest';
import express from 'express';
import userRoutes from '../users';
import { createTestApp } from '../../test-utils';
import pool from '../../db';

// Mock dependencies
jest.mock('../../db');
jest.mock('../../services/tokenService', () => ({
  default: {
    createTokenResponse: jest.fn().mockResolvedValue({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      expiresIn: 3600,
    }),
  },
}));

describe('Users Routes Integration Tests', () => {
  let app: express.Application;
  const mockPoolQuery = pool.query as jest.Mock;

  beforeAll(() => {
    app = createTestApp();
    app.use('/api/users', userRoutes);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/users/me', () => {
    it('should require authentication', async () => {
      const response = await request(app).get('/api/users/me');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
    });

    it('should return current user profile when authenticated', async () => {
      // Mock authenticated user
      const authenticatedApp = createTestApp();
      authenticatedApp.use((req, res, next) => {
        req.user = { id: 'test-user-id', email: '<EMAIL>' };
        next();
      });
      authenticatedApp.use('/api/users', userRoutes);

      mockPoolQuery.mockResolvedValueOnce({
        rows: [
          {
            id: 'test-user-id',
            email: '<EMAIL>',
            created_at: '2024-01-01T00:00:00Z',
            username: 'testuser',
            full_name: 'Test User',
            bio: 'Test bio',
          },
        ],
      });

      const response = await request(authenticatedApp).get('/api/users/me');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 'test-user-id');
      expect(response.body).toHaveProperty('email', '<EMAIL>');
      expect(response.body).toHaveProperty('profile');
    });
  });

  describe('POST /api/users/login', () => {
    it('should return accessToken field for frontend compatibility', async () => {
      mockPoolQuery
        .mockResolvedValueOnce({ rows: [{ exists: true }] }) // Table exists
        .mockResolvedValueOnce({
          // User found
          rows: [
            {
              id: 'user-123',
              email: '<EMAIL>',
              name: 'Test User',
            },
          ],
        });

      const response = await request(app).post('/api/users/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Login successful');
      expect(response.body).toHaveProperty('accessToken', 'mock-access-token');
      expect(response.body).not.toHaveProperty('token'); // Should NOT have old 'token' field
      expect(response.body).toHaveProperty('user');
    });

    it('should handle development user creation', async () => {
      mockPoolQuery
        .mockResolvedValueOnce({ rows: [{ exists: false }] }) // Table doesn't exist
        .mockResolvedValueOnce({ rows: [] }) // CREATE TABLE
        .mockResolvedValueOnce({ rows: [] }); // INSERT user

      const response = await request(app).post('/api/users/login').send({
        email: '<EMAIL>',
        password: 'devpassword',
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken'); // Must have accessToken
      expect(response.body).not.toHaveProperty('token'); // Must NOT have token
    });

    it('should handle database errors gracefully', async () => {
      mockPoolQuery.mockRejectedValueOnce(new Error('Database connection failed'));

      const response = await request(app).post('/api/users/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('message', 'Internal server error during login');
    });
  });

  describe('GET /api/users', () => {
    it('should require admin authentication', async () => {
      const response = await request(app).get('/api/users');

      expect(response.status).toBe(401);
    });

    it('should return users list for admin', async () => {
      // Mock admin user
      const adminApp = createTestApp();
      adminApp.use((req, res, next) => {
        req.user = { id: 'admin-id', email: '<EMAIL>', role: 'admin' };
        next();
      });
      adminApp.use('/api/users', userRoutes);

      mockPoolQuery.mockResolvedValueOnce({
        rows: [
          { id: 'user-1', email: '<EMAIL>', created_at: '2024-01-01' },
          { id: 'user-2', email: '<EMAIL>', created_at: '2024-01-02' },
        ],
      });

      const response = await request(adminApp).get('/api/users');

      expect(response.status).toBe(200);
      expect(response.body).toBeInstanceOf(Array);
      expect(response.body).toHaveLength(2);
    });
  });

  describe('PUT /api/users/:id', () => {
    it('should update user information', async () => {
      const authenticatedApp = createTestApp();
      authenticatedApp.use((req, res, next) => {
        req.user = { id: 'user-123', email: '<EMAIL>' };
        next();
      });
      authenticatedApp.use('/api/users', userRoutes);

      mockPoolQuery.mockResolvedValueOnce({
        rows: [
          {
            id: 'user-123',
            email: '<EMAIL>',
            name: 'Updated Name',
          },
        ],
      });

      const response = await request(authenticatedApp).put('/api/users/user-123').send({
        email: '<EMAIL>',
        name: 'Updated Name',
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'User updated successfully');
      expect(response.body.user).toHaveProperty('email', '<EMAIL>');
    });

    it('should not allow users to update other users', async () => {
      const authenticatedApp = createTestApp();
      authenticatedApp.use((req, res, next) => {
        req.user = { id: 'user-123', email: '<EMAIL>' };
        next();
      });
      authenticatedApp.use('/api/users', userRoutes);

      const response = await request(authenticatedApp).put('/api/users/other-user-id').send({
        email: '<EMAIL>',
      });

      expect(response.status).toBe(403);
    });
  });

  describe('Token Format Consistency', () => {
    it('should always return accessToken in auth responses', async () => {
      // Test all endpoints that return tokens
      const endpoints = [
        { method: 'post', path: '/api/users/login' },
        // Add other auth endpoints if they exist in users.ts
      ];

      for (const endpoint of endpoints) {
        mockPoolQuery.mockResolvedValueOnce({ rows: [{ exists: true }] }).mockResolvedValueOnce({
          rows: [
            {
              id: 'user-123',
              email: '<EMAIL>',
              name: 'Test User',
            },
          ],
        });

        const response = await request(app)[endpoint.method](endpoint.path).send({
          email: '<EMAIL>',
          password: 'password123',
        });

        if (response.status === 200) {
          expect(response.body).toHaveProperty('accessToken');
          expect(response.body).not.toHaveProperty('token');
        }
      }
    });
  });

  describe('Error Response Format', () => {
    it('should return consistent error format', async () => {
      mockPoolQuery.mockRejectedValueOnce(new Error('Database error'));

      const response = await request(app).post('/api/users/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
      expect(response.body).not.toHaveProperty('stack'); // No stack traces in production
    });
  });
});
