/**
 * Simplified Authentication Routes Test
 *
 * Basic tests for core auth endpoints without complex mocking
 */

// Mock the middleware first
jest.mock('../../middleware/validationMiddleware', () => ({
  validate: () => (req: any, res: any, next: any) => next(),
}));

jest.mock('../../security/middleware/auth', () => ({
  authenticate: (req: any, res: any, next: any) => {
    req.user = { id: 'user-123', email: '<EMAIL>' };
    next();
  },
  optionalAuthenticate: (req: any, res: any, next: any) => next(),
}));

jest.mock('../../services/authService', () => ({
  __esModule: true,
  default: {
    registerUser: jest.fn(),
    loginUser: jest.fn(),
    refreshAccessToken: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
    checkEmailExists: jest.fn(),
    verifyEmail: jest.fn(),
  },
}));

jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

import request from 'supertest';
import express from 'express';
import authRouter from '../auth';
import authService from '../../services/authService';

const app = express();
app.use(express.json());
app.use('/api/auth', authRouter);

// Simple error handler
app.use((err: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  res.status(err.statusCode || 500).json({ message: err.message });
});

describe('Auth Routes - Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const mockResponse = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          created_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      (authService.registerUser as jest.Mock).mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'Test User',
      });

      expect(response.status).toBe(201);
      // Response is wrapped by sendCreated helper
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Registration successful');
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(authService.registerUser).toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login user successfully', async () => {
      const mockResponse = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          created_at: new Date(),
          updated_at: new Date(),
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
      };

      (authService.loginUser as jest.Mock).mockResolvedValue(mockResponse);

      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(response.status).toBe(200);
      // Response is wrapped by sendSuccess helper
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(authService.loginUser).toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh access token', async () => {
      const mockResponse = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        tokenType: 'Bearer',
      };

      (authService.refreshAccessToken as jest.Mock).mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'old-refresh-token' });

      expect(response.status).toBe(200);
      // Response is wrapped by sendSuccess helper
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Token refreshed successfully');
      expect(response.body.data.accessToken).toBe('new-access-token');
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    it('should send password reset email', async () => {
      (authService.forgotPassword as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('A new password has been sent to your email');
    });
  });

  describe('GET /api/auth/check-email', () => {
    it('should check if email exists', async () => {
      (authService.checkEmailExists as jest.Mock).mockResolvedValue({
        exists: true,
        hasAccessRequest: false,
      });

      const response = await request(app)
        .get('/api/auth/check-email')
        .query({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body.exists).toBe(true);
    });
  });
});
