/**
 * Central Route Registry
 * Manages all modular routes for the application
 */

import { Express } from 'express';
import { createAuthRoutes } from './auth';
import { createProjectRoutes } from './projects/index';
import { createUserRoutes } from './users/index';
import { createImageRoutes } from './images/index';
import { createSegmentationRoutes } from './segmentation/index';
import { createAdminRoutes } from './admin/index';
import { createHealthRoutes } from './health/index';
import { createLogger } from '../utils/logger';

const logger = createLogger('RouteRegistry');

export interface RouteConfig {
  app: Express;
}

/**
 * Register all modular routes with the Express app
 */
export function registerRoutes(config: RouteConfig): void {
  const { app } = config;

  logger.info({ message: 'Registering modular routes...' });

  try {
    // Authentication routes
    app.use('/api/auth', createAuthRoutes());
    logger.info({ message: '✅ Auth routes registered' });

    // Project routes
    app.use('/api/projects', createProjectRoutes());
    logger.info({ message: '✅ Project routes registered' });

    // User routes
    app.use('/api/users', createUserRoutes());
    logger.info({ message: '✅ User routes registered' });

    // Image routes
    app.use('/api/images', createImageRoutes());
    logger.info({ message: '✅ Image routes registered' });

    // Segmentation routes
    app.use('/api/segmentation', createSegmentationRoutes());
    logger.info({ message: '✅ Segmentation routes registered' });

    // Admin routes
    app.use('/api/admin', createAdminRoutes());
    logger.info({ message: '✅ Admin routes registered' });

    // Health check routes
    app.use('/api/health', createHealthRoutes());
    logger.info({ message: '✅ Health routes registered' });

    logger.info({ message: 'All modular routes registered successfully' });
  } catch (error) {
    logger.error('Failed to register routes:', error);
    throw error;
  }
}

/**
 * Get route statistics for monitoring
 */
export function getRouteStats(): object {
  return {
    registered: [
      '/api/auth',
      '/api/projects',
      '/api/users',
      '/api/images',
      '/api/segmentation',
      '/api/admin',
      '/api/health',
    ],
    totalEndpoints: 50, // Approximate count
    status: 'active',
  };
}
