/**
 * Authentication Routes Module
 * Extracted from monolithic server for better organization
 */

import { Router, Request, Response } from 'express';
import * as bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import { createLogger } from '../../utils/logger';
import { prisma } from '../../services/PrismaService';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import { validateCsrfToken } from '../../middleware/csrfProtection';
import { validateBody } from '../../utils/validation';
import { dynamicRateLimit } from '../../middleware/rateLimiter';
import {
  LoginSchema,
  RegisterSchema,
  RefreshTokenSchema,
  ForgotPasswordSchema,
  ChangePasswordSchema,
  sanitizeInput,
  isValidEmailDomain,
  checkPasswordStrength,
} from '../../schemas/auth.schemas';
import * as fs from 'fs';
import * as path from 'path';

const logger = createLogger('AuthRoutes');
const router = Router();

// JWT keys
let privateKey: string;
let publicKey: string;

// Try multiple paths for JWT keys
const keyPaths = [
  { private: path.join(__dirname, '../../../keys/private.key'), public: path.join(__dirname, '../../../keys/public.key') },
  { private: path.join(__dirname, '../../keys/private.key'), public: path.join(__dirname, '../../keys/public.key') },
  { private: '/app/keys/private.key', public: '/app/keys/public.key' },
  // Add path for test environment
  { private: path.join(process.cwd(), 'keys/private.key'), public: path.join(process.cwd(), 'keys/public.key') }
];

let keysLoaded = false;
for (const keyPath of keyPaths) {
  try {
    privateKey = fs.readFileSync(keyPath.private, 'utf8');
    publicKey = fs.readFileSync(keyPath.public, 'utf8');
    logger.info(`JWT keys loaded successfully from ${keyPath.private}`);
    keysLoaded = true;
    break;
  } catch (error) {
    // Continue to next path
  }
}

if (!keysLoaded) {
  // For test environment, generate keys on the fly if not found
  if (process.env.NODE_ENV === 'test') {
    logger.warn('JWT keys not found, using test keys');
    privateKey = 'test-private-key';
    publicKey = 'test-public-key';
  } else {
    logger.error('JWT keys not found in any of the expected locations');
    throw new Error('JWT keys not found');
  }
}

export function createAuthRoutes(): Router {
  // Test endpoint
  router.get('/test', async (req: Request, res: Response) => {
    try {
      const userCount = await prisma.user.count();
      res.json({ success: true, userCount, message: 'Auth route working' });
    } catch (error) {
      console.error('Test endpoint error:', error);
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Check email endpoint - checks if email exists in database
  router.get('/check-email', async (req: Request, res: Response) => {
    try {
      const { email } = req.query;
      
      if (!email || typeof email !== 'string') {
        return res.status(400).json({
          success: false,
          error: 'Email parameter is required',
        });
      }

      // Sanitize email
      const sanitizedEmail = sanitizeInput(email);
      
      // Check if user exists with this email
      const user = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
        select: { id: true },
      });

      res.json({
        success: true,
        exists: !!user,
        hasAccessRequest: false, // We don't have access requests in the current schema
        message: user ? 'Email already registered' : 'Email available',
      });
    } catch (error) {
      logger.error('Check email error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });
  
  // Login endpoint
  router.post('/login', 
    dynamicRateLimit,
    validateBody(LoginSchema),
    async (req: Request, res: Response) => {
    try {
      console.log('Login endpoint hit');
      const { email, password } = req.body;
      console.log('Login body:', { email, passwordLength: password?.length });
      logger.info({ message: 'Login attempt for email:' }, { email });

      // Additional email domain validation
      if (!isValidEmailDomain(email)) {
        logger.warn('Login failed: Invalid email domain:', { email });
        return res.status(400).json({
          success: false,
          error: 'Invalid email format',
        });
      }

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          passwordHash: true,
          username: true,
        },
      });

      if (!user) {
        logger.warn('Login failed: User not found for email:', { email });
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password',
        });
      }

      // Verify password
      console.log('Verifying password...');
      const isValid = await bcrypt.compare(password, user.passwordHash);
      console.log('Password valid:', isValid);
      if (!isValid) {
        logger.warn('Login failed: Invalid password for email:', { email });
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password',
        });
      }

      // Generate tokens
      console.log('Generating tokens for user:', user.id);
      const payload = {
        userId: user.id,
        email: user.email,
        username: user.username,
      };
      console.log('Token payload:', payload);

      const accessToken = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
        expiresIn: '1h',
      });
      console.log('Access token generated');

      const refreshToken = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
        expiresIn: '30d',
      });
      console.log('Refresh token generated');

      // Generate CSRF token
      const csrfToken = crypto.randomBytes(32).toString('hex');

      // Note: lastLogin field not in schema - could track with session

      logger.info({ message: 'Login successful for user:' }, { userId: user.id });

      res.json({
        success: true,
        accessToken,
        refreshToken,
        csrfToken,
        access_token: accessToken,  // Also provide snake_case for compatibility
        refresh_token: refreshToken,
        expires_in: 3600,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          avatar: null,
          subscription_tier: null,
          subscription_status: null,
        },
      });
    } catch (error) {
      logger.error('Login error:', { error });
      console.error('Login error details:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Register endpoint
  router.post('/register', 
    dynamicRateLimit,
    validateBody(RegisterSchema),
    async (req: Request, res: Response) => {
    try {
      console.log('Registration endpoint hit');
      const { email, password, name, username } = req.body;
      
      // Sanitize inputs
      const sanitizedEmail = sanitizeInput(email);
      const sanitizedName = name ? sanitizeInput(name) : undefined;
      const sanitizedUsername = username ? sanitizeInput(username) : undefined;
      
      // Use name as username if username not provided, or fallback to email prefix
      const finalUsername = sanitizedUsername || 
        (sanitizedName ? sanitizedName.toLowerCase().replace(/\s+/g, '') : sanitizedEmail.split('@')[0]);
      
      console.log('Request body:', { email: sanitizedEmail, finalUsername, passwordLength: password?.length });
      logger.info({ message: 'Registration attempt for email:' }, { email: sanitizedEmail });

      // Additional email domain validation
      if (!isValidEmailDomain(sanitizedEmail)) {
        logger.warn('Registration failed: Invalid email domain:', { email: sanitizedEmail });
        return res.status(400).json({
          success: false,
          error: 'Invalid email format',
        });
      }

      // Check password strength
      const passwordStrength = checkPasswordStrength(password);
      if (passwordStrength.score < 5) {
        return res.status(400).json({
          success: false,
          error: 'Password is not strong enough',
          details: passwordStrength.feedback,
        });
      }

      // Check if user already exists
      console.log('Checking for existing user...');
      const existingUser = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
        select: { id: true },
      });
      console.log('Existing user check result:', existingUser);

      if (existingUser) {
        logger.warn('Registration failed: Email already exists:', { email: sanitizedEmail });
        return res.status(409).json({
          success: false,
          error: 'Email already registered',
        });
      }

      // Check if username already exists
      if (finalUsername) {
        const existingUsername = await prisma.user.findUnique({
          where: { username: finalUsername },
          select: { id: true },
        });
        if (existingUsername) {
          logger.warn('Registration failed: Username already exists:', { username: finalUsername });
          return res.status(409).json({
            success: false,
            error: 'Username already taken',
          });
        }
      }

      // Hash password
      console.log('Hashing password...');
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);
      console.log('Password hashed successfully');

      // Create user
      console.log('Creating user in database...');
      const user = await prisma.user.create({
        data: {
          email: sanitizedEmail,
          passwordHash,
          username: finalUsername,
        },
        select: {
          id: true,
          email: true,
          username: true,
        },
      });
      console.log('User created:', user);

      // Generate tokens
      const payload = {
        userId: user.id,
        email: user.email,
        username: user.username,
      };

      const accessToken = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
        expiresIn: '1h',
      });

      const refreshToken = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
        expiresIn: '30d',
      });

      // Generate CSRF token
      const csrfToken = crypto.randomBytes(32).toString('hex');

      logger.info({ message: 'Registration successful for user:' }, { userId: user.id });

      res.status(201).json({
        success: true,
        accessToken,
        refreshToken,
        csrfToken,
        access_token: accessToken,  // Also provide snake_case for compatibility
        refresh_token: refreshToken,
        expires_in: 3600,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          avatar: null,
          subscription_tier: null,
          subscription_status: null,
        },
      });
    } catch (error) {
      logger.error('Registration error:', error);
      console.error('Registration error details:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Refresh token endpoint
  router.post('/refresh-token', 
    dynamicRateLimit,
    validateBody(RefreshTokenSchema),
    async (req: Request, res: Response) => {
    try {
      const { refreshToken } = req.body;

      // Verify refresh token
      let decoded: any;
      try {
        decoded = jwt.verify(refreshToken, publicKey, {
          algorithms: ['RS256'],
        });
      } catch (err) {
        logger.warn('Invalid refresh token');
        return res.status(401).json({
          success: false,
          error: 'Invalid refresh token',
        });
      }

      // Generate new access token
      const payload = {
        userId: decoded.userId,
        email: decoded.email,
        username: decoded.username,
      };

      const accessToken = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
        expiresIn: '1h',
      });

      logger.info({ message: 'Token refreshed for user:' }, { userId: decoded.userId });

      res.json({
        success: true,
        accessToken,
      });
    } catch (error) {
      logger.error('Refresh token error:', { error });
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Logout endpoint
  router.post('/logout', async (req: Request, res: Response) => {
    logger.info({ message: 'Logout request received' });

    // Since we're using JWT, we can't invalidate tokens on the server side
    // The client should remove the token from storage
    res.json({
      success: true,
      message: 'Logged out successfully',
    });
  });

  // Forgot password endpoint
  router.post('/forgot-password', 
    dynamicRateLimit,
    validateBody(ForgotPasswordSchema),
    async (req: Request, res: Response) => {
    try {
      logger.info({ message: 'Password reset request received' });
      const { email } = req.body;
      const sanitizedEmail = sanitizeInput(email);
      logger.info({ message: 'Processing password reset for email:' }, { email: sanitizedEmail });

      // Additional email domain validation
      if (!isValidEmailDomain(sanitizedEmail)) {
        logger.warn('Password reset failed: Invalid email domain:', { email: sanitizedEmail });
        return res.status(400).json({
          success: false,
          error: 'Invalid email format',
        });
      }

      // Check if user exists
      logger.info({ message: 'Checking if user exists...' });
      const user = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
        select: { id: true, email: true, username: true },
      });

      if (!user) {
        // Don't reveal if user exists or not for security
        logger.info({ message: 'User not found, returning generic message' });
        return res.json({
          success: true,
          message: 'If an account exists with this email, a new password has been sent.',
        });
      }
      logger.info({ message: 'User found, generating new password...' });

      // Generate a new random password
      const newPassword = crypto.randomBytes(8).toString('hex');
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(newPassword, saltRounds);

      // Log for debugging (remove in production!)
      logger.warn(`TEMPORARY DEBUG: Generated password for ${user.email}: ${newPassword}`);

      // Update user's password
      logger.info({ message: 'Updating user password in database...' });
      await prisma.user.update({
        where: { id: user.id },
        data: { passwordHash },
      });

      // Send email using UnifiedEmailService
      logger.info({ message: 'Sending password reset email...' });

      try {
        const { sendPasswordResetSimple } = await import('../../services/email/CentralizedEmailService');
        await sendPasswordResetSimple(user.email, user.username || 'User', newPassword);
        logger.info({ message: 'Password reset email sent successfully' });
      } catch (emailError) {
        logger.error('Email setup error (non-blocking):', { error: emailError });
      }

      // Return success response immediately (don't wait for email)
      res.json({
        success: true,
        message: 'If an account exists with this email, a new password has been sent.',
      });
    } catch (error) {
      logger.error('Forgot password error:', { error });
      logger.error('Error stack:', (error as any).stack);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Change password endpoint
  router.post(
    '/change-password',
    dynamicRateLimit,
    authenticateToken,
    validateCsrfToken,
    validateBody(ChangePasswordSchema),
    async (req: Request & { userId?: string }, res: Response) => {
      try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.userId;

        logger.info({ message: 'Password change request for user:', userId });

        // Check new password strength
        const passwordStrength = checkPasswordStrength(newPassword);
        if (passwordStrength.score < 5) {
          return res.status(400).json({
            success: false,
            error: 'New password is not strong enough',
            details: passwordStrength.feedback,
          });
        }

        // Ensure new password is different from current
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { passwordHash: true },
        });

        if (!user) {
          return res.status(404).json({
            success: false,
            error: 'User not found',
          });
        }

        // Verify current password
        const isValidCurrent = await bcrypt.compare(currentPassword, user.passwordHash);
        if (!isValidCurrent) {
          return res.status(401).json({
            success: false,
            error: 'Current password is incorrect',
          });
        }

        // Check if new password is same as current
        const isSamePassword = await bcrypt.compare(newPassword, user.passwordHash);
        if (isSamePassword) {
          return res.status(400).json({
            success: false,
            error: 'New password must be different from current password',
          });
        }

        // Hash new password
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await prisma.user.update({
          where: { id: userId! },
          data: { passwordHash: newPasswordHash },
        });

        logger.info({ message: 'Password changed successfully for user:', userId });

        res.json({
          success: true,
          message: 'Password changed successfully',
        });
      } catch (error) {
        logger.error('Change password error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Verify token endpoint
  router.get(
    '/verify',
    authenticateToken,
    async (req: Request & { userId?: string }, res: Response) => {
      try {
        const userId = req.userId;

        // Get user details
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            email: true,
            username: true,
          },
        });

        if (!user) {
          return res.status(404).json({
            success: false,
            error: 'User not found',
          });
        }

        res.json({
          success: true,
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            avatar: null,
            subscription_tier: null,
            subscription_status: null,
          },
        });
      } catch (error) {
        logger.error('Verify token error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  return router;
}

export default createAuthRoutes;
