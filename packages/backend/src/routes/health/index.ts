/**
 * Health Check Routes Module
 * System health and monitoring endpoints
 */

import { Router, Request, Response } from 'express';
import { healthCheckService } from './healthcheck';
import { createLogger } from '../../utils/logger';
import { prisma, prismaService } from '../../services/PrismaService';
import { getRedis } from '../../config/redis';

const logger = createLogger('HealthRoutes');

// Fix BigInt serialization
(BigInt.prototype as any).toJSON = function() {
  return this.toString();
};

export function createHealthRoutes(): Router {
  const router = Router();

  // Basic health check - fast response for load balancers
  router.get('/', healthCheckService.basicHealthCheck.bind(healthCheckService));
  
  // Kubernetes-style health checks
  router.get('/live', healthCheckService.livenessCheck.bind(healthCheckService));
  router.get('/ready', healthCheckService.readinessCheck.bind(healthCheckService));

  // Detailed health check
  router.get('/detailed', healthCheckService.detailedHealthCheck.bind(healthCheckService));

  // Database-specific health check
  router.get('/db', async (req: Request, res: Response) => {
    try {
      const start = Date.now();
      const result =
        (await prisma.$queryRaw`SELECT version()::text as version, current_database()::text as current_database`) as any[];
      const latency = Date.now() - start;

      // Note: Prisma doesn't expose pool stats directly like pg.Pool
      const connectionStats = {
        status: 'active',
        note: 'Prisma manages connections internally',
      };

      res.json({
        status: 'healthy',
        service: 'PostgreSQL',
        latency: `${latency}ms`,
        version: result[0]?.version,
        database: result[0]?.current_database,
        connections: connectionStats,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Database health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        service: 'PostgreSQL',
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Redis health check
  router.get('/redis', async (req: Request, res: Response) => {
    try {
      const redis = getRedis();
      
      if (!redis) {
        return res.status(503).json({
          status: 'unhealthy',
          service: 'Redis',
          error: 'Redis client not initialized',
          timestamp: new Date().toISOString(),
        });
      }

      const start = Date.now();
      const pong = await redis.ping();
      const latency = Date.now() - start;
      
      // Get Redis info
      const info = await redis.info('server');
      const versionMatch = info.match(/redis_version:([^\r\n]+)/);
      const uptimeMatch = info.match(/uptime_in_seconds:([^\r\n]+)/);

      res.json({
        status: pong === 'PONG' ? 'healthy' : 'unhealthy',
        service: 'Redis',
        response: pong,
        latency: `${latency}ms`,
        version: versionMatch?.[1],
        uptime: uptimeMatch?.[1] ? parseInt(uptimeMatch[1]) : undefined,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Redis health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        service: 'Redis',
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      });
    }
  });

  // ML Service health check
  router.get('/ml', async (req: Request, res: Response) => {
    try {
      const mlServiceUrl = process.env.ML_SERVICE_URL || 'http://ml:5002';
      const mlHealthUrl = `${mlServiceUrl}/health`;
      
      logger.debug(`Checking ML service health at: ${mlHealthUrl}`);
      
      const start = Date.now();
      
      try {
        const response = await fetch(`${mlServiceUrl}/health`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: AbortSignal.timeout(5000), // 5 second timeout
        });
        
        const latency = Date.now() - start;
        
        if (!response.ok) {
          throw new Error(`ML service returned status ${response.status}`);
        }
        
        const data = await response.json();
        
        res.json({
          status: 'healthy',
          service: 'ML Service',
          latency: `${latency}ms`,
          mlServiceData: data,
          timestamp: new Date().toISOString(),
        });
      } catch (fetchError) {
        throw new Error(`Failed to connect to ML service: ${(fetchError as Error).message}`);
      }
    } catch (error) {
      logger.error('ML service health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        service: 'ML Service',
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Database metrics endpoint
  router.get('/db/metrics', async (req: Request, res: Response) => {
    try {
      const { getOptimizationRecommendations } = await import('../../config/database.config');
      const metrics = prismaService.getPerformanceMetrics();
      const recommendations = getOptimizationRecommendations(metrics.metrics);
      
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        performance: {
          totalQueries: metrics.totalQueries,
          averageQueryTime: `${metrics.averageQueryTime.toFixed(2)}ms`,
          totalTime: `${metrics.totalQueryTime}ms`,
        },
        connectionPool: metrics.metrics,
        recommendations,
      });
    } catch (error) {
      logger.error('Failed to get database metrics:', error);
      res.status(500).json({
        status: 'error',
        error: (error as Error).message,
      });
    }
  });

  // RabbitMQ health check
  router.get('/rabbitmq', async (req: Request, res: Response) => {
    try {
      const rabbitmqUrl = process.env.RABBITMQ_URL || 'http://rabbitmq:15672';
      const rabbitmqApiUrl = `${rabbitmqUrl}/api/health/checks/alarms`;
      
      logger.debug(`Checking RabbitMQ health at: ${rabbitmqApiUrl}`);
      
      const start = Date.now();
      
      const response = await fetch(rabbitmqApiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${Buffer.from('guest:guest').toString('base64')}`,
        },
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });
      
      const latency = Date.now() - start;
      
      if (!response.ok) {
        throw new Error(`RabbitMQ returned status ${response.status}`);
      }
      
      res.json({
        status: 'healthy',
        service: 'RabbitMQ',
        latency: `${latency}ms`,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('RabbitMQ health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        service: 'RabbitMQ',
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      });
    }
  });

  return router;
}

export default createHealthRoutes;