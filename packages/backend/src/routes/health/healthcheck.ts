/**
 * Enhanced Health Check Service
 * Provides comprehensive health monitoring for all system components
 */

import { Request, Response } from 'express';
import { prisma } from '../../services/PrismaService';
import { getRedis } from '../../config/redis';
import { createLogger } from '../../utils/logger';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

const logger = createLogger('HealthCheck');

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version?: string;
  components: {
    database: ComponentHealth;
    redis: ComponentHealth;
    rabbitmq?: ComponentHealth;
    mlService?: ComponentHealth;
    filesystem: ComponentHealth;
    memory: ComponentHealth;
  };
  metrics?: {
    cpu: {
      usage: number;
      cores: number;
    };
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    disk?: {
      used: number;
      total: number;
      percentage: number;
    };
  };
}

interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  latency?: number;
  details?: any;
}

export class HealthCheckService {
  private readonly ML_SERVICE_URL = process.env.ML_SERVICE_URL || 'http://ml:5002';
  private readonly RABBITMQ_URL = process.env.RABBITMQ_URL || 'http://rabbitmq:15672';

  /**
   * Basic health check - fast response for load balancers
   */
  async basicHealthCheck(req: Request, res: Response): Promise<void> {
    try {
      // Quick database ping
      const start = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      const dbLatency = Date.now() - start;

      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: dbLatency < 100 ? 'healthy' : 'slow',
        uptime: process.uptime(),
      });
    } catch (error) {
      logger.error('Basic health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Service unavailable',
      });
    }
  }

  /**
   * Comprehensive health check - detailed status of all components
   */
  async detailedHealthCheck(req: Request, res: Response): Promise<void> {
    const health: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0',
      components: {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        filesystem: await this.checkFilesystem(),
        memory: this.checkMemory(),
      },
      metrics: this.getSystemMetrics(),
    };

    // Check optional components
    if (process.env.ENABLE_ML_SERVICE !== 'false') {
      health.components.mlService = await this.checkMLService();
    }

    if (process.env.ENABLE_RABBITMQ !== 'false') {
      health.components.rabbitmq = await this.checkRabbitMQ();
    }

    // Determine overall health status
    const componentStatuses = Object.values(health.components)
      .filter(c => c)
      .map(c => c!.status);

    if (componentStatuses.some(s => s === 'unhealthy')) {
      health.status = 'unhealthy';
      res.status(503);
    } else if (componentStatuses.some(s => s === 'degraded')) {
      health.status = 'degraded';
      res.status(200);
    } else {
      res.status(200);
    }

    res.json(health);
  }

  /**
   * Check database health
   */
  private async checkDatabase(): Promise<ComponentHealth> {
    try {
      const start = Date.now();
      const result = await prisma.$queryRaw<any[]>`SELECT NOW() as time, version() as version`;
      const latency = Date.now() - start;

      // Check connection count
      const connectionCount = await prisma.$queryRaw<any[]>`
        SELECT count(*) as connections 
        FROM pg_stat_activity 
        WHERE datname = current_database()
      `;

      return {
        status: latency < 100 ? 'healthy' : latency < 500 ? 'degraded' : 'unhealthy',
        message: 'Database operational',
        latency,
        details: {
          version: result[0]?.version,
          connections: parseInt(connectionCount[0]?.connections || '0'),
          time: result[0]?.time,
        },
      };
    } catch (error) {
      logger.error('Database health check failed:', error);
      return {
        status: 'unhealthy',
        message: 'Database connection failed',
        details: { error: (error as Error).message },
      };
    }
  }

  /**
   * Check Redis health
   */
  private async checkRedis(): Promise<ComponentHealth> {
    try {
      const redis = getRedis();
      if (!redis) {
        return {
          status: 'unhealthy',
          message: 'Redis not configured',
        };
      }

      const start = Date.now();
      const pong = await redis.ping();
      const latency = Date.now() - start;

      // Get Redis info
      const info = await redis.info('memory');
      const memoryUsed = info.match(/used_memory_human:([^\r\n]+)/)?.[1];

      return {
        status: pong === 'PONG' && latency < 50 ? 'healthy' : 'degraded',
        message: 'Redis operational',
        latency,
        details: {
          memoryUsed,
        },
      };
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return {
        status: 'unhealthy',
        message: 'Redis connection failed',
        details: { error: (error as Error).message },
      };
    }
  }

  /**
   * Check ML Service health
   */
  private async checkMLService(): Promise<ComponentHealth> {
    try {
      const start = Date.now();
      const response = await fetch(`${this.ML_SERVICE_URL}/health`, {
        signal: AbortSignal.timeout(5000),
      });
      const latency = Date.now() - start;

      if (response.ok) {
        const data = await response.json();
        return {
          status: 'healthy',
          message: 'ML service operational',
          latency,
          details: data,
        };
      } else {
        return {
          status: 'degraded',
          message: `ML service returned ${response.status}`,
          latency,
        };
      }
    } catch (error) {
      logger.error('ML service health check failed:', error);
      return {
        status: 'unhealthy',
        message: 'ML service unreachable',
        details: { error: (error as Error).message },
      };
    }
  }

  /**
   * Check RabbitMQ health
   */
  private async checkRabbitMQ(): Promise<ComponentHealth> {
    try {
      const start = Date.now();
      const response = await fetch(`${this.RABBITMQ_URL}/api/health/checks/alarms`, {
        headers: {
          Authorization: `Basic ${Buffer.from('guest:guest').toString('base64')}`,
        },
        signal: AbortSignal.timeout(5000),
      });
      const latency = Date.now() - start;

      if (response.ok) {
        return {
          status: 'healthy',
          message: 'RabbitMQ operational',
          latency,
        };
      } else {
        return {
          status: 'degraded',
          message: `RabbitMQ returned ${response.status}`,
          latency,
        };
      }
    } catch (error) {
      logger.debug('RabbitMQ health check failed (this may be normal):', error);
      return {
        status: 'degraded',
        message: 'RabbitMQ health check unavailable',
      };
    }
  }

  /**
   * Check filesystem health
   */
  private async checkFilesystem(): Promise<ComponentHealth> {
    try {
      const uploadDir = process.env.UPLOAD_DIR || '/app/uploads';
      
      // Check if upload directory exists and is writable
      if (fs.existsSync(uploadDir)) {
        const testFile = path.join(uploadDir, '.health-check');
        
        // Try to write a test file
        fs.writeFileSync(testFile, 'health-check');
        fs.unlinkSync(testFile);

        return {
          status: 'healthy',
          message: 'Filesystem operational',
          details: {
            uploadDir,
            writable: true,
          },
        };
      } else {
        return {
          status: 'degraded',
          message: 'Upload directory does not exist',
          details: { uploadDir },
        };
      }
    } catch (error) {
      logger.error('Filesystem health check failed:', error);
      return {
        status: 'unhealthy',
        message: 'Filesystem not writable',
        details: { error: (error as Error).message },
      };
    }
  }

  /**
   * Check memory health
   */
  private checkMemory(): ComponentHealth {
    const memUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
    const percentage = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);

    return {
      status: percentage < 80 ? 'healthy' : percentage < 90 ? 'degraded' : 'unhealthy',
      message: `Memory usage: ${percentage}%`,
      details: {
        heapUsedMB,
        heapTotalMB,
        percentage,
        rss: Math.round(memUsage.rss / 1024 / 1024),
      },
    };
  }

  /**
   * Get system metrics
   */
  private getSystemMetrics() {
    const cpus = os.cpus();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    // Calculate CPU usage
    const cpuUsage = cpus.reduce((acc, cpu) => {
      const total = Object.values(cpu.times).reduce((a, b) => a + b);
      const idle = cpu.times.idle;
      return acc + ((total - idle) / total);
    }, 0) / cpus.length;

    return {
      cpu: {
        usage: Math.round(cpuUsage * 100),
        cores: cpus.length,
      },
      memory: {
        used: Math.round(usedMemory / 1024 / 1024),
        total: Math.round(totalMemory / 1024 / 1024),
        percentage: Math.round((usedMemory / totalMemory) * 100),
      },
    };
  }

  /**
   * Readiness check - is the service ready to accept traffic?
   */
  async readinessCheck(req: Request, res: Response): Promise<void> {
    try {
      // Check if database is ready
      await prisma.$queryRaw`SELECT 1`;
      
      // Check if Redis is ready (if configured)
      const redis = getRedis();
      if (redis) {
        await redis.ping();
      }

      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Readiness check failed:', error);
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Liveness check - is the service alive?
   */
  async livenessCheck(req: Request, res: Response): Promise<void> {
    // Simple check - if we can respond, we're alive
    res.status(200).json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  }
}

export const healthCheckService = new HealthCheckService();