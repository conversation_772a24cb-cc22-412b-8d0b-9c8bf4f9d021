/**
 * Admin Routes Module
 * Administrative endpoints for system management
 */

import { Router, Request, Response } from 'express';
import { createLogger } from '../../utils/logger';
import { prisma } from '../../services/PrismaService';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

const logger = createLogger('AdminRoutes');

interface AuthenticatedRequest extends Request {
  userId?: string;
}

// Simple admin check middleware (in production, check user role)
function requireAdmin(req: AuthenticatedRequest, res: Response, next: Function) {
  // For now, just check if user is authenticated
  // In production, check if user.role === 'admin'
  if (!req.userId) {
    return res.status(403).json({
      success: false,
      error: 'Admin access required',
    });
  }
  next();
}

export function createAdminRoutes(): Router {
  const router = Router();

  // Get system statistics
  router.get(
    '/stats',
    authenticateToken,
    requireAdmin,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        logger.info({ message: 'GET /api/admin/stats' });

        // Get counts using Prisma
        const [userCount, projectCount, imageCount, segmentationCounts, storageUsage] =
          await Promise.all([
            prisma.user.count(),
            prisma.project.count(),
            prisma.image.count(),
            prisma.image.groupBy({
              by: ['segmentationStatus'],
              _count: true,
            }),
            prisma.image.aggregate({
              _sum: { fileSize: true },
            }),
          ]);

        // Process segmentation stats
        const segmentationStats = {
          completed:
            segmentationCounts.find((s) => s.segmentationStatus === 'completed')?._count || 0,
          processing:
            segmentationCounts.find((s) => s.segmentationStatus === 'processing')?._count || 0,
          pending: segmentationCounts.find((s) => s.segmentationStatus === 'pending')?._count || 0,
          failed: segmentationCounts.find((s) => s.segmentationStatus === 'failed')?._count || 0,
        };

        // System metrics
        const systemMetrics = {
          cpu: os.cpus(),
          memory: {
            total: os.totalmem(),
            free: os.freemem(),
            used: os.totalmem() - os.freemem(),
          },
          uptime: os.uptime(),
          loadAverage: os.loadavg(),
        };

        res.json({
          success: true,
          stats: {
            users: userCount,
            projects: projectCount,
            images: imageCount,
            segmentation: segmentationStats,
            storage: {
              used: storageUsage._sum.fileSize || 0,
              limit: 100 * 1024 * 1024 * 1024, // 100GB
            },
            system: systemMetrics,
          },
        });
      } catch (error) {
        logger.error('Get admin stats error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get all users (paginated)
  router.get(
    '/users',
    authenticateToken,
    requireAdmin,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 20;
        const offset = (page - 1) * limit;

        logger.info({ message: 'GET /api/admin/users' }, { page, limit });

        const [totalCount, users] = await Promise.all([
          prisma.user.count(),
          prisma.user.findMany({
            select: {
              id: true,
              email: true,
              username: true,
              createdAt: true,
              // lastLogin field not in schema
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
            skip: offset,
          }),
        ]);

        // Convert to snake_case for API response consistency
        const formattedUsers = users.map((user) => ({
          id: user.id,
          email: user.email,
          name: user.username,
          created_at: user.createdAt,
          last_login: null, // lastLogin field not in schema
          subscription_tier: null,
          subscription_status: null,
        }));

        res.json({
          success: true,
          users: formattedUsers,
          pagination: {
            total: totalCount,
            page,
            limit,
            pages: Math.ceil(totalCount / limit),
          },
        });
      } catch (error) {
        logger.error('Get admin users error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get system logs
  router.get(
    '/logs',
    authenticateToken,
    requireAdmin,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const lines = parseInt(req.query.lines as string) || 100;
        const level = (req.query.level as string) || 'all';

        logger.info({ message: 'GET /api/admin/logs' }, { lines, level });

        // In production, this would read from actual log files
        // For now, return mock data
        const logs = [
          { timestamp: new Date(), level: 'info', message: 'System started' },
          { timestamp: new Date(), level: 'warn', message: 'High memory usage detected' },
          { timestamp: new Date(), level: 'error', message: 'Failed to process image' },
        ];

        res.json({
          success: true,
          logs,
        });
      } catch (error) {
        logger.error('Get admin logs error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Clean up old data
  router.post(
    '/cleanup',
    authenticateToken,
    requireAdmin,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const { olderThanDays = 30 } = req.body;

        logger.info({ message: 'POST /api/admin/cleanup' }, { olderThanDays });

        // Delete old failed segmentations
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

        const { count: deletedImagesCount } = await prisma.image.deleteMany({
          where: {
            segmentationStatus: 'failed',
            updatedAt: { lt: cutoffDate },
          },
        });

        // Clean up orphaned files
        const uploadsDir = path.join(__dirname, '../../../uploads');
        let orphanedFiles = 0;

        if (fs.existsSync(uploadsDir)) {
          const files = fs.readdirSync(uploadsDir);
          for (const file of files) {
            const filePath = path.join(uploadsDir, file);
            const stats = fs.statSync(filePath);
            const daysSinceModified = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);

            if (daysSinceModified > olderThanDays) {
              // Check if file is still referenced in database
              const fileCheck = await prisma.image.findFirst({
                where: { storageFilename: file },
                select: { id: true },
              });

              if (!fileCheck) {
                fs.unlinkSync(filePath);
                orphanedFiles++;
              }
            }
          }
        }

        res.json({
          success: true,
          cleaned: {
            deletedImages: deletedImagesCount,
            orphanedFiles,
          },
        });
      } catch (error) {
        logger.error('Admin cleanup error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  return router;
}
