import { Router, Request, Response } from 'express';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';

export function createStatusRoutes(): Router {
  const router = Router();

  // Health check
  router.get('/health', async (req: Request, res: Response) => {
    try {
      // Check database connection
      await prisma.$queryRaw`SELECT 1`;

      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: 'connected',
        },
      });
    } catch (error) {
      logger.error('Health check failed:', { error });
      res.status(503).json({
        status: 'unhealthy',
        error: 'Service unavailable',
      });
    }
  });

  // Readiness check
  router.get('/ready', async (req: Request, res: Response) => {
    try {
      await prisma.$queryRaw`SELECT 1`;
      res.json({ ready: true });
    } catch (error) {
      logger.error('Readiness check failed:', { error });
      res.status(503).json({ ready: false });
    }
  });

  return router;
}

export default createStatusRoutes;
