/**
 * Prisma Helpers for Migration from Raw SQL
 * Provides utility functions to simplify migration from raw SQL to Prisma
 */

import { prisma } from '../services/PrismaService';
import { Prisma } from '../../generated/prisma';
import { createLogger } from '../utils/logger';

const logger = createLogger('PrismaHelpers');

/**
 * Helper to convert SQL result to consistent format
 */
export function formatResult<T>(data: T | T[] | null): { rows: T[] } {
  if (!data) {
    return { rows: [] };
  }
  return { rows: Array.isArray(data) ? data : [data] };
}

/**
 * Helper for transaction handling
 */
export async function withPrismaTransaction<T>(
  fn: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(fn);
}

/**
 * Helper to handle common query patterns
 */
export const queryHelpers = {
  /**
   * Find user by email or ID
   */
  async findUser(identifier: string) {
    return prisma.user.findFirst({
      where: {
        OR: [{ id: identifier }, { email: identifier }],
      },
    });
  },

  /**
   * Find project with relations
   */
  async findProjectWithRelations(projectId: string, userId?: string) {
    return prisma.project.findFirst({
      where: {
        id: projectId,
        ...(userId && { userId }),
      },
      include: {
        user: true,
        images: true,
        shares: true,
      },
    });
  },

  /**
   * Count entities with optional filter
   */
  async count(model: keyof typeof prisma, where?: any) {
    const modelInstance = prisma[model] as any;
    return modelInstance.count({ where });
  },

  /**
   * Upsert helper
   */
  async upsert<T>(model: keyof typeof prisma, where: any, create: any, update: any): Promise<T> {
    const modelInstance = prisma[model] as any;
    return modelInstance.upsert({
      where,
      create,
      update,
    });
  },

  /**
   * Batch delete helper
   */
  async deleteMany(model: keyof typeof prisma, where: any) {
    const modelInstance = prisma[model] as any;
    return modelInstance.deleteMany({ where });
  },

  /**
   * Raw query helper for complex queries that can't be easily migrated
   * @deprecated Use Prisma queries instead
   */
  async rawQuery<T = any>(sql: string, params?: any[]): Promise<T[]> {
    logger.warn('Using raw SQL query - consider migrating to Prisma', { sql });
    return prisma.$queryRawUnsafe<T[]>(sql, ...(params || []));
  },
};

/**
 * Export Prisma client for direct use
 */
export { prisma } from '../services/PrismaService';
export type { Prisma } from '../../generated/prisma';
