/**
 * Prisma Database Module
 * Fully migrated to Prisma ORM
 */

import { prisma, prismaService } from '../services/PrismaService';
import logger from '../utils/logger';

// =====================
// Legacy Pool Support (Deprecated)
// =====================

/**
 * @deprecated Pool is no longer available. Use prisma from '../services/PrismaService' instead.
 */
export function getPool() {
  return {
    connect: () => Promise.resolve({
      query: (...args: any[]) => Promise.reject(new Error("Pool is deprecated. Use Prisma instead.")),
      release: (...args: any[]) => { /* noop */ },
      // Add minimal PoolClient compatibility
      connect: () => Promise.reject(new Error("Pool is deprecated")),
      copyFrom: () => Promise.reject(new Error("Pool is deprecated")),
      copyTo: () => Promise.reject(new Error("Pool is deprecated")),
      pauseDrain: () => Promise.reject(new Error("Pool is deprecated")),
      resumeDrain: () => Promise.reject(new Error("Pool is deprecated")),
      escapeIdentifier: () => "",
      escapeLiteral: () => "",
    } as any),
    query: (...args: any[]) => Promise.reject(new Error("Pool is deprecated. Use Prisma instead.")),
    end: () => Promise.reject(new Error("Pool is deprecated. Use Prisma instead.")),
    on: () => { /* noop */ },
  };
}

/**
 * @deprecated Use prisma.$queryRaw or prisma.$executeRaw instead
 */
export async function query<T = any>(text: string, params?: any[]): Promise<{ rows: T[] }> {
  throw new Error(
    "query() is no longer available. Use prisma.$queryRaw or prisma.$executeRaw from '../services/PrismaService'."
  );
}

/**
 * @deprecated Use prisma.$transaction instead
 */
export async function withTransaction<T>(fn: (client: any) => Promise<T>): Promise<never> {
  throw new Error(
    "withTransaction() is no longer available. Use prisma.$transaction from '../services/PrismaService'."
  );
}

// =====================
// Prisma Exports
// =====================

// Export Prisma instance and service
export { prisma, prismaService };

// Export Prisma types
export type {
  User,
  UserProfile,
  Project,
  Image,
  Segmentation,
  SegmentationResult,
  ProjectShare,
  SegmentationQueue,
  ProjectDuplicationTask,
  RefreshToken,
  EmailVerification,
  PasswordResetToken,
  AccessRequest,
  Session,
  Migration,
} from '../services/PrismaService';

// =====================
// Migration Helpers
// =====================

/**
 * Initialize database connection
 */
export async function initializeDatabase(): Promise<void> {
  try {
    // Connect Prisma
    await prismaService.connect();
    logger.info({ message: 'Database initialized with Prisma' });

    // Test the connection
    const isHealthy = await prismaService.healthCheck();
    if (!isHealthy) {
      throw new Error('Database health check failed');
    }
  } catch (error) {
    logger.error('Failed to initialize database', { error });
    throw error;
  }
}

/**
 * Close database connections
 */
export async function closeDatabase(): Promise<void> {
  try {
    // Disconnect Prisma
    await prismaService.disconnect();

    logger.info({ message: 'Database connections closed' });
  } catch (error) {
    logger.error('Failed to close database connections', { error });
    throw error;
  }
}

// Default export for backward compatibility
export default class DatabaseModule {
  static pool = getPool();
  static getPool = getPool;
  static query = query;
  static withTransaction = withTransaction;
  static prisma = prisma;
  static get prismaService() { return prismaService; }
  static initializeDatabase = initializeDatabase;
  static closeDatabase = closeDatabase;
}
