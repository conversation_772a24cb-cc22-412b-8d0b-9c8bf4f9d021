<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Project Invitation - SpheroSeg</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .container {
      max-width: 600px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
    }
    .content {
      padding: 30px;
    }
    .project-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    .project-title {
      font-size: 22px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 10px 0;
    }
    .role-badge {
      display: inline-block;
      padding: 6px 16px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .role-badge.viewer {
      background: #e7f5ff;
      color: #1971c2;
      border: 1px solid #74c0fc;
    }
    .role-badge.editor {
      background: #fff4e6;
      color: #e8590c;
      border: 1px solid #ffc078;
    }
    .role-badge.admin {
      background: #f3f0ff;
      color: #6741d9;
      border: 1px solid #9775fa;
    }
    .button {
      display: inline-block;
      padding: 14px 32px;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      margin: 20px 0;
      box-shadow: 0 4px 6px rgba(79, 172, 254, 0.3);
    }
    .button:hover {
      opacity: 0.9;
    }
    .sender-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 20px 0;
    }
    .sender-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 20px;
    }
    .message-section {
      background: #f0f9ff;
      padding: 15px;
      border-left: 4px solid #4facfe;
      margin: 20px 0;
      border-radius: 4px;
      font-style: italic;
    }
    .permissions-list {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      margin: 20px 0;
    }
    .permissions-list h4 {
      margin: 0 0 10px 0;
      color: #495057;
    }
    .permissions-list ul {
      margin: 0;
      padding-left: 20px;
      color: #6c757d;
    }
    .permissions-list li {
      margin: 5px 0;
    }
    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
    .link-text {
      word-break: break-all;
      color: #4facfe;
      font-size: 14px;
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>You're Invited to Collaborate!</h1>
    </div>
    <div class="content">
      <p>Hello,</p>
      
      <div class="sender-info">
        <div class="sender-avatar">{{uppercase (substring senderName 0 1)}}</div>
        <div>
          <strong>{{senderName}}</strong> has invited you to collaborate on a SpheroSeg project
        </div>
      </div>
      
      <div class="project-card">
        <h2 class="project-title">{{projectTitle}}</h2>
        <p>Your role: <span class="role-badge {{role}}">{{role}}</span></p>
      </div>
      
      {{#if message}}
      <div class="message-section">
        <strong>Message from {{senderName}}:</strong><br>
        "{{message}}"
      </div>
      {{/if}}
      
      <div class="permissions-list">
        <h4>As a {{role}}, you will be able to:</h4>
        <ul>
          {{#eq role 'viewer'}}
            <li>View project images and segmentation results</li>
            <li>Download exported data</li>
            <li>View project statistics and analytics</li>
          {{/eq}}
          {{#eq role 'editor'}}
            <li>View and edit segmentation results</li>
            <li>Upload new images</li>
            <li>Run segmentation analysis</li>
            <li>Export and download data</li>
            <li>Add comments and annotations</li>
          {{/eq}}
          {{#eq role 'admin'}}
            <li>Full project management capabilities</li>
            <li>Invite and manage other collaborators</li>
            <li>Edit project settings</li>
            <li>Delete project resources</li>
            <li>Access all project features</li>
          {{/eq}}
        </ul>
      </div>
      
      <div style="text-align: center;">
        <a href="{{inviteUrl}}" class="button">Accept Invitation</a>
      </div>
      
      <p>Or copy and paste this link into your browser:</p>
      <div class="link-text">{{inviteUrl}}</div>
      
      <p><small>This invitation link will expire in 7 days for security reasons. If you need a new invitation, please contact {{senderName}}.</small></p>
      
      <div class="footer">
        <p>© 2025 SpheroSeg - Advanced Cell Segmentation Platform</p>
        <p>This invitation was sent to {{recipientEmail}}</p>
      </div>
    </div>
  </div>
</body>
</html>