<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Access Request - SpheroSeg</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .container {
      max-width: 600px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .header {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .content {
      padding: 30px;
    }
    .info-box {
      background: #f8f9fa;
      padding: 20px;
      border-left: 4px solid #43e97b;
      margin: 20px 0;
      border-radius: 4px;
    }
    .info-box p {
      margin: 8px 0;
    }
    .info-box strong {
      color: #2c3e50;
      display: inline-block;
      min-width: 120px;
    }
    .priority-badge {
      display: inline-block;
      padding: 4px 12px;
      background: #ff6b6b;
      color: white;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      margin-left: 10px;
    }
    .message-box {
      background: #fff8e1;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
      border: 1px solid #ffeb3b;
    }
    .action-section {
      background: #e3f2fd;
      padding: 20px;
      border-radius: 4px;
      margin: 20px 0;
      text-align: center;
    }
    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
    .timestamp {
      color: #999;
      font-size: 12px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Access Request</h1>
    </div>
    <div class="content">
      <p>Dear Administrator,</p>
      <p>A new access request has been submitted for the SpheroSeg platform. Please review the details below:</p>
      
      <div class="info-box">
        <p><strong>Requester:</strong> {{requesterName}} <span class="priority-badge">New Request</span></p>
        <p><strong>Email:</strong> {{requesterEmail}}</p>
        <p><strong>Project:</strong> {{projectTitle}}</p>
        <p><strong>Project ID:</strong> <code>{{projectId}}</code></p>
        <p><strong>Request ID:</strong> <code>{{requesterId}}</code></p>
      </div>
      
      {{#if message}}
      <div class="message-box">
        <h3 style="margin-top: 0;">Message from Requester:</h3>
        <p>"{{message}}"</p>
      </div>
      {{/if}}
      
      <div class="action-section">
        <h3>Action Required</h3>
        <p>Please log in to the SpheroSeg admin panel to review and process this access request.</p>
        <p><a href="{{../appUrl}}/admin/access-requests" style="color: #43e97b; font-weight: 600;">Go to Admin Panel →</a></p>
      </div>
      
      <p class="timestamp">Request submitted at: {{formatDate ../createdAt}}</p>
      
      <div class="footer">
        <p>© 2025 SpheroSeg - Advanced Cell Segmentation Platform</p>
        <p>This is an automated administrative notification.</p>
      </div>
    </div>
  </div>
</body>
</html>