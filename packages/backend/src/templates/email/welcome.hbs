<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to SpheroSeg</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .container {
      max-width: 600px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 32px;
      font-weight: 600;
    }
    .header p {
      margin: 10px 0 0 0;
      font-size: 18px;
      opacity: 0.95;
    }
    .content {
      padding: 30px;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 15px;
      margin: 30px 0;
    }
    .feature-item {
      display: flex;
      align-items: start;
      gap: 15px;
    }
    .feature-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      flex-shrink: 0;
    }
    .feature-content h3 {
      margin: 0 0 5px 0;
      font-size: 16px;
      color: #2c3e50;
    }
    .feature-content p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
    .button {
      display: inline-block;
      padding: 14px 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      margin: 20px 0;
      box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
    }
    .button:hover {
      opacity: 0.9;
    }
    .button-secondary {
      display: inline-block;
      padding: 12px 24px;
      background: white;
      color: #667eea;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      margin: 10px;
      border: 2px solid #667eea;
    }
    .getting-started {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin: 30px 0;
    }
    .getting-started h2 {
      margin: 0 0 15px 0;
      color: #2c3e50;
      font-size: 20px;
    }
    .steps {
      counter-reset: step-counter;
      padding-left: 0;
      list-style: none;
    }
    .steps li {
      counter-increment: step-counter;
      margin: 15px 0;
      padding-left: 40px;
      position: relative;
    }
    .steps li::before {
      content: counter(step-counter);
      position: absolute;
      left: 0;
      top: 0;
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
    }
    .resource-links {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
      margin: 30px 0;
    }
    .resource-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
    }
    .resource-link:hover {
      text-decoration: underline;
    }
    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Welcome to SpheroSeg!</h1>
      <p>Your Advanced Cell Segmentation Platform</p>
    </div>
    <div class="content">
      <p>Hi {{name}},</p>
      <p>Congratulations! Your SpheroSeg account has been successfully created. You now have access to powerful cell segmentation tools that will help you analyze microscopy images with precision and efficiency.</p>
      
      <div class="feature-grid">
        <div class="feature-item">
          <div class="feature-icon">📸</div>
          <div class="feature-content">
            <h3>Upload & Organize</h3>
            <p>Upload microscopy images in various formats and organize them into projects</p>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🔬</div>
          <div class="feature-content">
            <h3>AI-Powered Segmentation</h3>
            <p>Leverage our ResUNet model for automatic cell boundary detection</p>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">✏️</div>
          <div class="feature-content">
            <h3>Edit & Refine</h3>
            <p>Manually adjust segmentation results with intuitive editing tools</p>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">📊</div>
          <div class="feature-content">
            <h3>Analyze & Export</h3>
            <p>Generate statistics and export results in multiple formats</p>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">👥</div>
          <div class="feature-content">
            <h3>Collaborate</h3>
            <p>Share projects with colleagues and work together in real-time</p>
          </div>
        </div>
      </div>
      
      <div class="getting-started">
        <h2>Getting Started</h2>
        <ol class="steps">
          <li><strong>Create your first project</strong> - Click "New Project" in your dashboard</li>
          <li><strong>Upload images</strong> - Drag and drop or browse to add microscopy images</li>
          <li><strong>Run segmentation</strong> - Click "Segment" to process your images automatically</li>
          <li><strong>Review results</strong> - Examine and refine the detected cell boundaries</li>
          <li><strong>Export data</strong> - Download results in your preferred format</li>
        </ol>
      </div>
      
      <div style="text-align: center;">
        <a href="{{appUrl}}/dashboard" class="button">Go to Dashboard</a>
      </div>
      
      <div class="resource-links">
        <a href="{{appUrl}}/docs" class="resource-link">Documentation</a>
        <a href="{{appUrl}}/tutorials" class="resource-link">Video Tutorials</a>
        <a href="{{appUrl}}/support" class="resource-link">Get Support</a>
        <a href="{{appUrl}}/community" class="resource-link">Community Forum</a>
      </div>
      
      <p style="text-align: center; color: #6c757d; font-size: 14px;">
        Need help? Our support team is here to assist you.<br>
        Email us at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
      </p>
      
      <div class="footer">
        <p>© 2025 SpheroSeg - Advanced Cell Segmentation Platform</p>
        <p>You're receiving this email because you signed up for SpheroSeg.</p>
      </div>
    </div>
  </div>
</body>
</html>