#!/usr/bin/env node
/**
 * Migration script to generate thumbnails for existing images
 * Run with: npx ts-node src/scripts/generate-missing-thumbnails.ts
 */

import * as path from 'path';
import * as fs from 'fs';
import { prisma } from '../services/PrismaService';
import { UnifiedImageService } from '../services/UnifiedImageService';
import { createLogger } from '../utils/logger';
import * as crypto from 'crypto';

const logger = createLogger('ThumbnailMigration');
const imageService = new UnifiedImageService();

async function generateMissingThumbnails() {
  try {
    logger.info('Starting thumbnail generation for existing images...');

    // Get all images without thumbnails
    const images = await prisma.image.findMany({
      where: {
        OR: [
          { thumbnailPath: null },
          { thumbnailPath: '' },
        ],
      },
      select: {
        id: true,
        storageFilename: true,
        storagePath: true,
        originalFilename: true,
      },
    });

    logger.info(`Found ${images.length} images without thumbnails`);

    let successCount = 0;
    let failureCount = 0;

    for (const image of images) {
      try {
        // Determine the source path
        let sourcePath: string;
        if (image.storagePath && fs.existsSync(image.storagePath)) {
          sourcePath = image.storagePath;
        } else {
          sourcePath = path.join(__dirname, '../../../uploads', image.storageFilename);
        }

        // Check if source image exists
        if (!fs.existsSync(sourcePath)) {
          logger.warn(`Source image not found for ${image.id}: ${sourcePath}`);
          failureCount++;
          continue;
        }

        // Generate thumbnail
        const thumbnailName = `thumb-${Date.now()}-${crypto.randomBytes(8).toString('hex')}.png`;
        const thumbnailPath = path.join(__dirname, '../../../uploads/thumbnails', thumbnailName);
        
        // Ensure thumbnails directory exists
        const thumbnailDir = path.dirname(thumbnailPath);
        if (!fs.existsSync(thumbnailDir)) {
          fs.mkdirSync(thumbnailDir, { recursive: true });
        }

        await imageService.generateThumbnail(sourcePath, thumbnailPath);

        // Update database
        const thumbnailRelativePath = path.join('thumbnails', thumbnailName);
        await prisma.image.update({
          where: { id: image.id },
          data: { thumbnailPath: thumbnailRelativePath },
        });

        successCount++;
        logger.info(`Generated thumbnail for image ${image.id} (${successCount}/${images.length})`);

        // Add a small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        logger.error(`Failed to generate thumbnail for image ${image.id}:`, error);
        failureCount++;
      }
    }

    logger.info(`Thumbnail generation complete. Success: ${successCount}, Failures: ${failureCount}`);
    
    // Cleanup
    await prisma.$disconnect();
    process.exit(0);
  } catch (error) {
    logger.error('Fatal error during thumbnail generation:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

// Run the migration
generateMissingThumbnails().catch((error) => {
  logger.error('Unhandled error:', error);
  process.exit(1);
});