/**
 * Response compression middleware
 */

import compression from 'compression';
import { Request, Response } from 'express';

/**
 * Custom compression filter
 */
const shouldCompress = (req: Request, res: Response): boolean => {
  // Don't compress for HEAD requests
  if (req.headers['x-no-compression']) {
    return false;
  }

  // Compress everything else
  return (compression as any).filter(req, res);
};

/**
 * Compression middleware with optimized settings
 */
export const compressionMiddleware = compression({
  // Compression level (0-9, higher = better compression but slower)
  level: 6, // Default level, good balance

  // Threshold - minimum size to compress (in bytes)
  threshold: 1024, // Only compress responses larger than 1KB

  // Memory level (1-9, higher = more memory but faster)
  memLevel: 8,

  // Strategy
  strategy: 0, // Default strategy

  // Custom filter
  filter: shouldCompress as any,

  // Brotli options (if supported)
  brotli: {
    zlib: {
      params: {
        [require('zlib').constants.BROTLI_PARAM_QUALITY]: 4,
      },
    },
  } as any,
});

/**
 * JSON compression for large API responses
 */
export const jsonCompression = (minSize: number = 1024) => {
  return (req: Request, res: Response, next: Function) => {
    const originalJson = res.json.bind(res);

    res.json = function (body: any) {
      const jsonString = JSON.stringify(body);

      // Only compress large responses
      if (jsonString.length > minSize) {
        // Remove unnecessary whitespace
        const compressed = JSON.stringify(JSON.parse(jsonString));

        // Set compression header
        res.setHeader('X-JSON-Compressed', 'true');
        res.setHeader('X-Original-Size', jsonString.length.toString());
        res.setHeader('X-Compressed-Size', compressed.length.toString());

        return originalJson(JSON.parse(compressed));
      }

      return originalJson(body);
    };

    next();
  };
};

/**
 * Image compression headers
 */
export const imageCompressionHeaders = (req: Request, res: Response, next: Function) => {
  const path = req.path.toLowerCase();

  if (path.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
    // Set cache headers for images
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
    res.setHeader('Vary', 'Accept-Encoding');

    // Enable compression for SVG
    if (path.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml');
      res.setHeader('Content-Encoding', 'gzip');
    }
  }

  next();
};

/**
 * Streaming compression for large downloads
 */
export const streamCompression = () => {
  const zlib = require('zlib');

  return (req: Request, res: Response, next: Function) => {
    // Check if client accepts gzip
    const acceptEncoding = req.headers['accept-encoding'] || '';

    if (acceptEncoding.includes('gzip')) {
      // Store original pipe method
      const originalPipe = res.pipe;

      // Override pipe to add compression
      res.pipe = function (destination: any, options?: any) {
        // Create gzip stream
        const gzip = zlib.createGzip({
          level: 6,
          memLevel: 8,
        });

        // Set headers
        res.setHeader('Content-Encoding', 'gzip');
        res.removeHeader('Content-Length'); // Length will change

        // Pipe through gzip
        originalPipe.call(this, gzip);
        return gzip.pipe(destination, options);
      };
    }

    next();
  };
};

/**
 * Combined compression middleware
 */
export const setupCompression = () => {
  return [compressionMiddleware, jsonCompression(), imageCompressionHeaders, streamCompression()];
};

export default compressionMiddleware;
