/**
 * CSRF Protection Middleware
 */

import { Request, Response, NextFunction } from 'express';
import { createLogger } from '../utils/logger';

const logger = createLogger('CSRFMiddleware');

export function validateCsrfToken(req: Request, res: Response, next: NextFunction): void {
  const csrfToken = req.headers['x-csrf-token'] || req.body.csrfToken;

  if (!csrfToken) {
    logger.warn('CSRF token missing');
    res.status(403).json({ error: 'CSRF token required' });
    return;
  }

  // In production, you would validate the token against a stored value
  // For now, we'll just check that it exists
  // TODO: Implement proper CSRF token validation

  next();
}
