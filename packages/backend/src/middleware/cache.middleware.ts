/**
 * Redis caching middleware for API responses
 */

import { Request, Response, NextFunction } from 'express';
import { Redis } from 'ioredis';
import crypto from 'crypto';

const redis = new Redis({
  host: process.env.REDIS_HOST || 'redis',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  lazyConnect: true,
});

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key?: (req: Request) => string; // Custom key generator
  condition?: (req: Request) => boolean; // Conditional caching
  invalidatePattern?: string; // Pattern for cache invalidation
}

/**
 * Generate cache key from request
 */
const generateCacheKey = (req: Request): string => {
  const { method, originalUrl, query, params } = req;
  const userId = (req as any).user?.id || 'anonymous';

  const keyData = {
    method,
    url: originalUrl,
    query,
    params,
    userId,
  };

  const hash = crypto.createHash('sha256').update(JSON.stringify(keyData)).digest('hex');

  return `api:${method}:${hash}`;
};

/**
 * Cache middleware factory
 */
export const cache = (options: CacheOptions = {}) => {
  const {
    ttl = 300, // Default 5 minutes
    key = generateCacheKey,
    condition = () => true,
    invalidatePattern,
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests by default
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      return next();
    }

    // Check condition
    if (!condition(req)) {
      return next();
    }

    const cacheKey = key(req);

    try {
      // Try to get from cache
      const cached = await redis.get(cacheKey);

      if (cached) {
        const data = JSON.parse(cached);

        // Set cache headers
        res.setHeader('X-Cache', 'HIT');
        res.setHeader('X-Cache-Key', cacheKey);
        res.setHeader('Cache-Control', `private, max-age=${ttl}`);

        // Send cached response
        return res.status(data.status || 200).json(data.body);
      }
    } catch (error) {
      console.error('Cache get error:', error);
      // Continue without cache on error
    }

    // Store original send method
    const originalSend = res.json.bind(res);

    // Override json method to cache the response
    res.json = function (body: any) {
      // Only cache successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const cacheData = {
          status: res.statusCode,
          body,
          timestamp: Date.now(),
        };

        // Cache in background (don't wait)
        redis
          .setex(cacheKey, ttl, JSON.stringify(cacheData))
          .catch((err) => console.error('Cache set error:', err));

        // Set cache headers
        res.setHeader('X-Cache', 'MISS');
        res.setHeader('X-Cache-Key', cacheKey);
        res.setHeader('Cache-Control', `private, max-age=${ttl}`);
      }

      // Call original send
      return originalSend(body);
    };

    next();
  };
};

/**
 * Cache invalidation helper
 */
export const invalidateCache = async (pattern: string): Promise<number> => {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      const deleted = await redis.del(...keys);
      console.log(`Invalidated ${deleted} cache entries matching pattern: ${pattern}`);
      return deleted;
    }
    return 0;
  } catch (error) {
    console.error('Cache invalidation error:', error);
    return 0;
  }
};

/**
 * Clear all cache
 */
export const clearCache = async (): Promise<void> => {
  try {
    await redis.flushdb();
    console.log('Cache cleared');
  } catch (error) {
    console.error('Clear cache error:', error);
  }
};

/**
 * Specific cache strategies for different routes
 */
export const cacheStrategies = {
  // Short TTL for frequently changing data
  short: cache({ ttl: 60 }), // 1 minute

  // Medium TTL for semi-static data
  medium: cache({ ttl: 300 }), // 5 minutes

  // Long TTL for static data
  long: cache({ ttl: 3600 }), // 1 hour

  // User-specific cache
  user: cache({
    ttl: 300,
    key: (req) => {
      const userId = (req as any).user?.id || 'anonymous';
      return `user:${userId}:${req.originalUrl}`;
    },
  }),

  // Project-specific cache
  project: cache({
    ttl: 300,
    key: (req) => {
      const projectId = req.params.projectId || req.params.id;
      return `project:${projectId}:${req.originalUrl}`;
    },
  }),

  // No cache for sensitive data
  none: (req: Request, res: Response, next: NextFunction) => {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    next();
  },
};

/**
 * Cache warming for critical routes
 */
export const warmCache = async (routes: string[]): Promise<void> => {
  // Implementation for pre-warming cache with critical data
  console.log('Cache warming not implemented yet');
};

export default cache;
