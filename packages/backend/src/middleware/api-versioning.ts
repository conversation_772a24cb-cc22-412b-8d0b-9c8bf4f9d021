/**
 * API Versioning Middleware
 * Provides backward compatibility with v1 API
 */

import { Request, Response, NextFunction } from 'express';
import { createLogger } from '../utils/logger';

const logger = createLogger('APIVersioning');

export interface VersionedRequest extends Request {
  apiVersion: string;
  isLegacyApi: boolean;
}

/**
 * API version detection and routing
 */
export function apiVersionMiddleware(
  req: VersionedRequest,
  res: Response,
  next: NextFunction
): void {
  // Detect API version from header or URL
  const headerVersion = req.headers['x-api-version'] as string;
  const urlVersion = req.path.match(/^\/api\/v(\d+)/)?.[1];

  // Determine version (default to v2)
  const version = headerVersion || (urlVersion ? `v${urlVersion}` : 'v2');

  // Set version on request
  req.apiVersion = version;
  req.isLegacyApi = version === 'v1';

  // Log API version usage
  logger.debug(`API request version: ${version}`, {
    path: req.path,
    method: req.method,
    userAgent: req.headers['user-agent'],
  });

  // Add deprecation warning for v1
  if (req.isLegacyApi) {
    res.setHeader('X-API-Deprecation', 'API v1 is deprecated and will be removed in v3.0.0');
    res.setHeader('X-API-Deprecation-Date', '2025-02-07');
    logger.warn('Legacy API v1 request', { path: req.path });
  }

  next();
}

/**
 * Route transformer for v1 compatibility
 */
export function v1CompatibilityMiddleware(
  req: VersionedRequest,
  res: Response,
  next: NextFunction
): void {
  if (!req.isLegacyApi) {
    return next();
  }

  // Transform v1 routes to v2
  const v1ToV2Map: Record<string, string> = {
    '/api/v1/auth/login': '/api/auth/login',
    '/api/v1/auth/register': '/api/auth/register',
    '/api/v1/auth/logout': '/api/auth/logout',
    '/api/v1/projects': '/api/projects',
    '/api/v1/users/me': '/api/auth/me',
    '/api/v1/images': '/api/images',
    '/api/v1/segmentation': '/api/segmentation',
  };

  // Check if route needs transformation
  const v2Route = v1ToV2Map[req.path];
  if (v2Route) {
    req.url = v2Route;
    // req.path is read-only, use Object.defineProperty to override
    Object.defineProperty(req, 'path', {
      value: v2Route,
      writable: true,
      configurable: true,
    });
    logger.debug(`Transformed v1 route to v2: ${req.path} -> ${v2Route}`);
  }

  // Transform request body for v1 compatibility
  if (req.method === 'POST' && req.body) {
    req.body = transformV1RequestBody(req.path, req.body);
  }

  // Intercept response to transform for v1 format
  const originalJson = res.json.bind(res);
  res.json = function (data: any) {
    const transformedData = transformV2ResponseToV1(req.path, data);
    return originalJson(transformedData);
  };

  next();
}

/**
 * Transform v1 request body to v2 format
 */
function transformV1RequestBody(path: string, body: any): any {
  switch (path) {
    case '/api/auth/register':
      // v2 requires 'name' field
      if (!body.name && body.email) {
        body.name = body.email.split('@')[0];
      }
      break;

    case '/api/projects':
      // v2 has additional settings
      if (!body.settings) {
        body.settings = {
          autoSegmentation: false,
          confidenceThreshold: 0.5,
        };
      }
      break;

    case '/api/segmentation/process':
      // v2 requires modelType and parameters
      if (!body.modelType) {
        body.modelType = 'resunet';
      }
      if (!body.parameters) {
        body.parameters = {
          threshold: 0.5,
          minArea: 10,
        };
      }
      break;
  }

  return body;
}

/**
 * Transform v2 response to v1 format
 */
function transformV2ResponseToV1(path: string, data: any): any {
  // Handle error responses
  if (data?.error && typeof data.error === 'object') {
    // v1 expects simple string error
    return {
      error: data.error.message || 'An error occurred',
    };
  }

  // Handle paginated responses
  if (data?.pagination) {
    // v1 doesn't have pagination wrapper
    return data.data;
  }

  // Path-specific transformations
  switch (path) {
    case '/api/auth/login':
      // v1 doesn't return refresh token
      if (data?.refreshToken) {
        delete data.refreshToken;
      }
      break;

    case '/api/projects':
      // v1 doesn't have settings in project response
      if (Array.isArray(data)) {
        data = data.map((project) => {
          const { settings, ...v1Project } = project;
          return v1Project;
        });
      }
      break;

    case '/api/segmentation/results':
      // v1 has simpler response structure
      if (data?.metadata) {
        delete data.metadata;
      }
      break;
  }

  return data;
}

/**
 * API version router
 */
export function setupAPIVersioning(app: any): void {
  // Apply versioning middleware globally
  app.use(apiVersionMiddleware);

  // Apply v1 compatibility for legacy routes
  app.use('/api/v1/*', v1CompatibilityMiddleware);

  // Redirect root v1 to v2
  app.get('/api/v1', (req: Request, res: Response) => {
    res.redirect('/api');
  });

  // Version info endpoint
  app.get('/api/version', (req: Request, res: Response) => {
    res.json({
      current: 'v2',
      supported: ['v1', 'v2'],
      deprecated: ['v1'],
      deprecationDate: '2025-02-07',
      documentation: '/api/docs',
    });
  });

  logger.info({ message: 'API versioning configured with v1 backward compatibility' });
}
