/**
 * Middleware Setup for Production Server
 * Configures all middleware in the correct order
 */

import { Express } from 'express';
import compression from 'compression';
import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import RedisStore from 'connect-redis';
import { createClient } from 'redis';
import morgan from 'morgan';
import express from 'express';
import path from 'path';
import { ProductionConfig } from '../config/production.config';
import { createLogger } from '../utils/logger';

const logger = createLogger('MiddlewareSetup');

export async function setupMiddleware(app: Express, config: ProductionConfig): Promise<void> {
  // Trust proxy if configured
  if (config.enableTrust) {
    app.set('trust proxy', 1);
    logger.info({ message: 'Trust proxy enabled' });
  }

  // Security headers
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          imgSrc: ["'self'", 'data:', 'blob:', 'https:'],
          connectSrc: ["'self'", 'ws:', 'wss:'],
        },
      },
      crossOriginEmbedderPolicy: false,
    })
  );

  // CORS configuration
  app.use(
    cors({
      origin: config.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'X-CSRF-Token',
        'If-None-Match',
        'Cache-Control',
        'Pragma',
      ],
      exposedHeaders: ['X-Total-Count', 'X-Page', 'X-Per-Page'],
      maxAge: 86400, // 24 hours
    })
  );

  // Compression
  app.use(
    compression({
      level: config.compressionLevel,
      threshold: 1024, // Only compress responses > 1KB
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      },
    })
  );

  // Rate limiting
  const limiter = rateLimit({
    windowMs: config.rateLimitWindow,
    max: config.rateLimitRequests,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/api/health' || req.path === '/api/health/ready';
    },
  });
  app.use('/api/', limiter);

  // Body parsing
  app.use(
    express.json({
      limit: '50mb',
      verify: (req: any, res, buf, encoding) => {
        // Store raw body for webhook signature verification
        if (req.headers['x-webhook-signature']) {
          req.rawBody = buf.toString((encoding || 'utf8') as BufferEncoding);
        }
      },
    })
  );
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // Cookie parser
  app.use(cookieParser(config.sessionSecret));

  // Session management with Redis
  if (config.redisUrl) {
    try {
      const redisClient = createClient({
        url: config.redisUrl,
      });

      await redisClient.connect();
      logger.info({ message: 'Redis connected for session management' });

      app.use(
        session({
          store: new RedisStore({ client: redisClient as any }),
          secret: config.sessionSecret,
          resave: false,
          saveUninitialized: false,
          rolling: true,
          cookie: {
            secure: config.nodeEnv === 'production',
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            sameSite: config.nodeEnv === 'production' ? 'strict' : 'lax',
          },
        })
      );
      logger.info({ message: 'Session management configured with Redis' });
    } catch (error) {
      logger.error('Failed to connect to Redis, using memory sessions:', error);

      // Fallback to memory sessions
      app.use(
        session({
          secret: config.sessionSecret,
          resave: false,
          saveUninitialized: false,
          rolling: true,
          cookie: {
            secure: config.nodeEnv === 'production',
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: config.nodeEnv === 'production' ? 'strict' : 'lax',
          },
        })
      );
    }
  }

  // Request logging
  if (config.nodeEnv === 'production') {
    // Production: JSON logs
    app.use(
      morgan('combined', {
        stream: {
          write: (message) => logger.info(message.trim()),
        },
        skip: (req) => req.path === '/api/health',
      })
    );
  } else {
    // Development: Colored console logs
    app.use(morgan('dev'));
  }

  // Static files with caching
  const uploadsDir = path.join(__dirname, '../../uploads');
  app.use(
    '/uploads',
    express.static(uploadsDir, {
      maxAge: config.staticCacheDuration,
      etag: true,
      lastModified: true,
      setHeaders: (res, filepath) => {
        // Set cache headers based on file type
        if (filepath.endsWith('.jpg') || filepath.endsWith('.png') || filepath.endsWith('.webp')) {
          res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        } else if (filepath.endsWith('.json')) {
          res.setHeader('Cache-Control', 'no-cache');
        }
      },
    })
  );

  // Request ID middleware
  app.use((req: any, res, next) => {
    req.id =
      req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    res.setHeader('X-Request-ID', req.id);
    next();
  });

  logger.info({ message: 'All middleware configured successfully' });
}
