/**
 * Database Setup for Production Server
 * Handles database connection and initialization
 */

import { Pool } from 'pg';
import { ProductionConfig } from '../config/production.config';
import { createLogger } from '../utils/logger';
import fs from 'fs';
import path from 'path';

const logger = createLogger('DatabaseSetup');

export async function setupDatabase(config: ProductionConfig): Promise<Pool> {
  // Parse database URL
  const dbUrl = new URL(config.databaseUrl);

  // Create connection pool
  const pool = new Pool({
    host: dbUrl.hostname,
    port: parseInt(dbUrl.port || '5432'),
    database: dbUrl.pathname.slice(1),
    user: dbUrl.username,
    password: dbUrl.password,
    max: config.maxConnections,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    // SSL configuration for production
    ssl:
      config.nodeEnv === 'production'
        ? {
            rejectUnauthorized: false, // Set to true if you have proper certificates
          }
        : undefined,
  });

  // Test connection
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    logger.info({ message: 'Database connection successful:' }, result.rows[0].now);
    client.release();
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }

  // Initialize database schema if needed
  await initializeSchema(pool);

  // Setup connection pool monitoring
  pool.on('error', (err) => {
    logger.error({ message: 'Unexpected database error:', error: err });
  });

  pool.on('connect', () => {
    logger.debug('New database connection established');
  });

  pool.on('remove', () => {
    logger.debug('Database connection removed from pool');
  });

  return pool;
}

/**
 * Initialize database schema
 */
async function initializeSchema(pool: Pool): Promise<void> {
  try {
    // Check if tables exist
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);

    const tables = tablesResult.rows.map((row) => row.table_name);

    if (tables.length === 0) {
      logger.warn('No tables found in database, initializing schema...');
      await createTables(pool);
    } else {
      logger.info(`Database has ${tables.length} tables`);

      // Verify required tables exist
      const requiredTables = ['users', 'projects', 'images', 'sessions'];
      const missingTables = requiredTables.filter((t) => !tables.includes(t));

      if (missingTables.length > 0) {
        logger.warn({ message: 'Missing required tables:', missingTables });
        // In production, we should not auto-create tables
        if (process.env.NODE_ENV === 'production') {
          throw new Error(`Missing required tables: ${missingTables.join(', ')}`);
        } else {
          await createTables(pool);
        }
      }
    }

    // Run any pending migrations
    await runMigrations(pool);
  } catch (error) {
    logger.error('Failed to initialize database schema:', error);
    throw error;
  }
}

/**
 * Create database tables
 */
async function createTables(pool: Pool): Promise<void> {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Users table
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Projects table
    await client.query(`
      CREATE TABLE IF NOT EXISTS projects (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status VARCHAR(50) DEFAULT 'active',
        image_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Images table
    await client.query(`
      CREATE TABLE IF NOT EXISTS images (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        filename VARCHAR(500) NOT NULL,
        original_filename VARCHAR(500),
        file_size BIGINT,
        width INTEGER,
        height INTEGER,
        thumbnail_path VARCHAR(500),
        preview_path VARCHAR(500),
        segmentation_status VARCHAR(50) DEFAULT 'pending',
        segmentation_result JSONB,
        processing_started_at TIMESTAMP,
        processing_completed_at TIMESTAMP,
        processing_time INTEGER,
        error_message TEXT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Sessions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS sessions (
        sid VARCHAR(255) PRIMARY KEY,
        sess JSONB NOT NULL,
        expire TIMESTAMP NOT NULL
      )
    `);

    // Queue jobs table
    await client.query(`
      CREATE TABLE IF NOT EXISTS queue_jobs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        type VARCHAR(50) NOT NULL,
        data JSONB,
        status VARCHAR(50) DEFAULT 'pending',
        attempts INTEGER DEFAULT 0,
        error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP
      )
    `);

    // Project shares table
    await client.query(`
      CREATE TABLE IF NOT EXISTS project_shares (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        email VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'viewer',
        status VARCHAR(50) DEFAULT 'pending',
        invitation_token VARCHAR(255) UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        accepted_at TIMESTAMP
      )
    `);

    // Create indexes
    await client.query('CREATE INDEX IF NOT EXISTS idx_images_project_id ON images(project_id)');
    await client.query(
      'CREATE INDEX IF NOT EXISTS idx_images_segmentation_status ON images(segmentation_status)'
    );
    await client.query('CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_sessions_expire ON sessions(expire)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status)');

    await client.query('COMMIT');
    logger.info({ message: 'Database tables created successfully' });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Run database migrations
 */
async function runMigrations(pool: Pool): Promise<void> {
  try {
    // Create migrations table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get list of executed migrations
    const executedResult = await pool.query('SELECT filename FROM migrations');
    const executed = new Set(executedResult.rows.map((r) => r.filename));

    // Get migration files
    const migrationsDir = path.join(__dirname, '../migrations');

    // Check if migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      logger.info({ message: 'No migrations directory found, skipping migrations' });
      return;
    }

    const files = fs
      .readdirSync(migrationsDir)
      .filter((f) => f.endsWith('.sql'))
      .sort();

    // Execute pending migrations
    for (const file of files) {
      if (!executed.has(file)) {
        logger.info(`Running migration: ${file}`);

        const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');

        const client = await pool.connect();
        try {
          await client.query('BEGIN');
          await client.query(sql);
          await client.query('INSERT INTO migrations (filename) VALUES ($1)', [file]);
          await client.query('COMMIT');
          logger.info(`Migration ${file} completed successfully`);
        } catch (error) {
          await client.query('ROLLBACK');
          logger.error(`Migration ${file} failed:`, error);
          throw error;
        } finally {
          client.release();
        }
      }
    }

    logger.info({ message: 'All migrations completed' });
  } catch (error) {
    logger.error('Failed to run migrations:', error);
    // In production, fail if migrations fail
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
}
