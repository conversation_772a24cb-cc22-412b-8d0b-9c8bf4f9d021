/**
 * Graceful Shutdown Setup for Production Server
 * Handles clean shutdown of all services
 */

import { Server } from 'http';
import { createLogger } from '../utils/logger';
import { closeDatabase } from '../db/prisma';

const logger = createLogger('Shutdown');

let isShuttingDown = false;

export function setupGracefulShutdown(server: Server, pool: any = null): void {
  const shutdown = async (signal: string) => {
    if (isShuttingDown) {
      logger.info({ message: 'Shutdown already in progress...' });
      return;
    }

    isShuttingDown = true;
    logger.info(`Received ${signal}, starting graceful shutdown...`);

    // Stop accepting new connections
    server.close(() => {
      logger.info({ message: 'HTTP server closed' });
    });

    // Give existing connections time to complete
    const shutdownTimeout = setTimeout(() => {
      logger.error('Forced shutdown due to timeout');
      process.exit(1);
    }, 30000); // 30 seconds timeout

    try {
      // Close database connections
      if (pool) {
        await pool.end();
        logger.info({ message: 'Legacy Pool connections closed' });
      } else {
        // Use Prisma disconnect
        await closeDatabase();
        logger.info({ message: 'Database connections closed' });
      }

      // Clear timeout if we finished in time
      clearTimeout(shutdownTimeout);

      logger.info({ message: 'Graceful shutdown completed' });
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      clearTimeout(shutdownTimeout);
      process.exit(1);
    }
  };

  // Listen for termination signals
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  // Health check endpoint for orchestrators
  const _healthCheck = (req: any, res: any) => {
    if (isShuttingDown) {
      res.status(503).json({ status: 'shutting_down' });
    } else {
      res.status(200).json({ status: 'healthy' });
    }
  };

  logger.info({ message: 'Graceful shutdown handlers configured' });
}
