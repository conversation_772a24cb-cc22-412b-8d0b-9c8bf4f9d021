/**
 * Error Handling Setup for Production Server
 * Centralized error handling and logging
 */

import { Express, Request, Response, NextFunction } from 'express';
import { createLogger } from '../utils/logger';

const logger = createLogger('ErrorHandling');

// Custom error class
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
    Error.captureStackTrace(this, this.constructor);
  }
}

// Async error wrapper
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => void | Promise<void>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

export function setupErrorHandling(app: Express): void {
  // 404 Error - This should be before the error handler
  app.use((req: Request, res: Response, next: NextFunction) => {
    const error = new AppError(`Cannot ${req.method} ${req.originalUrl}`, 404, 'NOT_FOUND');
    next(error);
  });

  // Global error handler
  app.use((error: any, req: Request, res: Response, _next: NextFunction) => {
    // Set default values
    let statusCode = error.statusCode || 500;
    let message = error.message || 'Internal Server Error';
    let code = error.code || 'INTERNAL_ERROR';

    // Log error
    const errorInfo = {
      message: error.message,
      statusCode,
      code,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userId: (req as any).userId,
      requestId: (req as any).id,
      stack: error.stack,
      details: error.details,
    };

    if (statusCode >= 500) {
      logger.error('Server error:', errorInfo);
    } else if (statusCode >= 400) {
      logger.warn('Client error:', errorInfo);
    }

    // Handle specific error types
    if (error.name === 'ValidationError') {
      statusCode = 400;
      message = 'Validation Error';
      code = 'VALIDATION_ERROR';
    } else if (error.name === 'UnauthorizedError') {
      statusCode = 401;
      message = 'Unauthorized';
      code = 'UNAUTHORIZED';
    } else if (error.name === 'JsonWebTokenError') {
      statusCode = 401;
      message = 'Invalid token';
      code = 'INVALID_TOKEN';
    } else if (error.name === 'TokenExpiredError') {
      statusCode = 401;
      message = 'Token expired';
      code = 'TOKEN_EXPIRED';
    } else if (error.code === 'ECONNREFUSED') {
      statusCode = 503;
      message = 'Service temporarily unavailable';
      code = 'SERVICE_UNAVAILABLE';
    } else if (error.code === '23505') {
      // PostgreSQL unique violation
      statusCode = 409;
      message = 'Resource already exists';
      code = 'DUPLICATE_RESOURCE';
    }

    // Prepare error response
    const errorResponse: any = {
      error: {
        message,
        code,
        statusCode,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        requestId: (req as any).id,
      },
    };

    // Add stack trace in development
    if (process.env.NODE_ENV !== 'production') {
      errorResponse.error.stack = error.stack;
      errorResponse.error.details = error.details;
    }

    // Send error response
    res.status(statusCode).json(errorResponse);
  });

  logger.info({ message: 'Error handling configured' });
}
