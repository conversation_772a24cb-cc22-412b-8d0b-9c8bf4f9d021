/**
 * Authentication Input Validation Schemas
 * Using Zod for comprehensive input validation and sanitization
 */

import { z } from 'zod';

// Email validation schema with proper format check
export const EmailSchema = z
  .string()
  .trim()
  .toLowerCase()
  .email('Please provide a valid email address')
  .max(254, 'Email address is too long') // RFC 5321 limit
  .refine((email) => {
    // Additional email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, 'Please provide a valid email address');

// Password strength validation
export const PasswordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password is too long')
  .refine((password) => {
    // Check for at least one lowercase letter
    return /[a-z]/.test(password);
  }, 'Password must contain at least one lowercase letter')
  .refine((password) => {
    // Check for at least one uppercase letter
    return /[A-Z]/.test(password);
  }, 'Password must contain at least one uppercase letter')
  .refine((password) => {
    // Check for at least one number
    return /\d/.test(password);
  }, 'Password must contain at least one number')
  .refine((password) => {
    // Check for at least one special character
    return /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);
  }, 'Password must contain at least one special character')
  .refine((password) => {
    // Check that password doesn't contain common patterns (full words only)
    const commonPatterns = [
      'password',
      '123456',
      'qwerty',
      'admin',
      'letmein',
      'welcome',
    ];
    const lowerPassword = password.toLowerCase();
    return !commonPatterns.some((pattern) =>
      lowerPassword === pattern || lowerPassword.includes(pattern.toLowerCase())
    );
  }, 'Password contains common patterns and is not secure enough');

// Username validation and sanitization
export const UsernameSchema = z
  .string()
  .trim()
  .min(2, 'Username must be at least 2 characters long')
  .max(30, 'Username must be at most 30 characters long')
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    'Username can only contain letters, numbers, underscores, and hyphens'
  )
  .refine((username) => {
    // Prevent usernames that start or end with special characters
    return /^[a-zA-Z0-9]/.test(username) && /[a-zA-Z0-9]$/.test(username);
  }, 'Username must start and end with a letter or number')
  .refine((username) => {
    // Prevent reserved usernames
    const reserved = [
      'admin',
      'administrator',
      'root',
      'system',
      'user',
      'guest',
      'public',
      'private',
      'api',
      'www',
      'mail',
      'ftp',
      'support',
      'help',
      'info',
      'contact',
      'service',
      'test',
      'demo',
      'sample',
      'null',
      'undefined',
      'anonymous',
    ];
    return !reserved.includes(username.toLowerCase());
  }, 'This username is reserved and cannot be used');

// Name validation for registration
export const NameSchema = z
  .string()
  .trim()
  .min(1, 'Name is required')
  .max(100, 'Name must be at most 100 characters long')
  .regex(
    /^[a-zA-Z\s'-]+$/,
    'Name can only contain letters, spaces, apostrophes, and hyphens'
  )
  .refine((name) => {
    // Prevent names that are only whitespace or special characters
    return /[a-zA-Z]/.test(name);
  }, 'Name must contain at least one letter');

// Login request validation
export const LoginSchema = z.object({
  email: EmailSchema,
  password: z.string().min(1, 'Password is required'),
});

// Registration request validation
export const RegisterSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
  username: UsernameSchema.optional(),
  name: NameSchema.optional(),
});

// Refresh token request validation
export const RefreshTokenSchema = z.object({
  refreshToken: z
    .string()
    .trim()
    .min(1, 'Refresh token is required')
    .max(1000, 'Invalid refresh token format'),
});

// Forgot password request validation
export const ForgotPasswordSchema = z.object({
  email: EmailSchema,
});

// Change password request validation
export const ChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: PasswordSchema,
});

// Type exports for TypeScript
export type LoginRequest = z.infer<typeof LoginSchema>;
export type RegisterRequest = z.infer<typeof RegisterSchema>;
export type RefreshTokenRequest = z.infer<typeof RefreshTokenSchema>;
export type ForgotPasswordRequest = z.infer<typeof ForgotPasswordSchema>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordSchema>;

// Additional security validation helpers
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>"']/g, '') // Remove potentially dangerous characters
    .substring(0, 1000); // Limit length to prevent attacks
};

export const isValidEmailDomain = (email: string): boolean => {
  const domain = email.split('@')[1];
  if (!domain) return false;
  
  // Basic domain validation
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain);
};

export const checkPasswordStrength = (password: string): {
  score: number;
  feedback: string[];
} => {
  let score = 0;
  const feedback: string[] = [];

  if (password.length >= 12) score += 2;
  else if (password.length >= 8) score += 1;
  else feedback.push('Use at least 8 characters');

  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('Add lowercase letters');

  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('Add uppercase letters');

  if (/\d/.test(password)) score += 1;
  else feedback.push('Add numbers');

  if (/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) score += 2;
  else feedback.push('Add special characters');

  if (!/(.)\1{2,}/.test(password)) score += 1; // No repeated characters
  else feedback.push('Avoid repeated characters');

  return { score: Math.min(score, 8), feedback };
};