/**
 * Database Configuration with Optimized Connection Pooling
 * 
 * Best practices for PostgreSQL connection pooling:
 * - Connection pool size should be based on available resources
 * - Too many connections can hurt performance
 * - Too few connections can cause bottlenecks
 */

import { createLogger } from '../utils/logger';

const logger = createLogger('DatabaseConfig');

export interface DatabaseConfig {
  connectionUrl: string;
  connectionPoolSize: number;
  connectionTimeout: number;
  queryTimeout: number;
  idleTimeout: number;
  maxLifetime: number;
  statementCacheSize: number;
  enableSsl: boolean;
}

/**
 * Calculate optimal connection pool size based on environment
 * Formula: connections = (core_count * 2) + effective_spindle_count
 * For most cloud environments with SSDs, effective_spindle_count = 1
 */
function calculateOptimalPoolSize(): number {
  const cpuCount = parseInt(process.env.CPU_COUNT || '0') || require('os').cpus().length;
  const baseConnections = cpuCount * 2 + 1;
  
  // Apply limits based on environment
  const minConnections = parseInt(process.env.DB_MIN_CONNECTIONS || '5');
  const maxConnections = parseInt(process.env.DB_MAX_CONNECTIONS || '20');
  
  const optimal = Math.max(minConnections, Math.min(baseConnections, maxConnections));
  
  logger.info(`Calculated optimal pool size: ${optimal} (CPUs: ${cpuCount}, Min: ${minConnections}, Max: ${maxConnections})`);
  
  return optimal;
}

/**
 * Get database configuration with optimized settings
 */
export function getDatabaseConfig(): DatabaseConfig {
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Base configuration
  const config: DatabaseConfig = {
    connectionUrl: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/spheroseg?schema=public',
    connectionPoolSize: calculateOptimalPoolSize(),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '20000'), // 20 seconds
    queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'), // 30 seconds
    idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'), // 30 seconds
    maxLifetime: parseInt(process.env.DB_MAX_LIFETIME || '1800000'), // 30 minutes
    statementCacheSize: parseInt(process.env.DB_STATEMENT_CACHE_SIZE || '100'),
    enableSsl: process.env.DB_SSL === 'true' || isProduction,
  };

  // Production optimizations
  if (isProduction) {
    // Increase pool size for production
    config.connectionPoolSize = Math.min(config.connectionPoolSize * 1.5, 30);
    
    // Longer timeouts for production stability
    config.connectionTimeout = 30000; // 30 seconds
    config.queryTimeout = 60000; // 60 seconds
    
    // Keep connections alive longer in production
    config.idleTimeout = 60000; // 1 minute
    config.maxLifetime = 3600000; // 1 hour
  }

  return config;
}

/**
 * Build Prisma datasource URL with connection pool parameters
 */
export function buildPrismaUrl(config: DatabaseConfig): string {
  const url = new URL(config.connectionUrl);
  
  // Add connection pool parameters to URL
  url.searchParams.set('connection_limit', config.connectionPoolSize.toString());
  url.searchParams.set('connect_timeout', (config.connectionTimeout / 1000).toString());
  url.searchParams.set('pool_timeout', (config.connectionTimeout / 1000).toString());
  url.searchParams.set('statement_cache_size', config.statementCacheSize.toString());
  
  // Add SSL mode if enabled
  if (config.enableSsl) {
    url.searchParams.set('sslmode', 'require');
  }
  
  // Add schema if not present
  if (!url.searchParams.has('schema')) {
    url.searchParams.set('schema', 'public');
  }
  
  // Connection pooling mode
  url.searchParams.set('pgbouncer', 'false'); // Direct connection, not using PgBouncer
  
  return url.toString();
}

/**
 * Database health metrics
 */
export interface DatabaseMetrics {
  poolSize: number;
  activeConnections: number;
  idleConnections: number;
  waitingRequests: number;
  totalQueries: number;
  averageQueryTime: number;
  slowQueries: number;
}

/**
 * Connection pool monitoring
 */
export class DatabaseMonitor {
  private metrics: DatabaseMetrics = {
    poolSize: 0,
    activeConnections: 0,
    idleConnections: 0,
    waitingRequests: 0,
    totalQueries: 0,
    averageQueryTime: 0,
    slowQueries: 0,
  };

  private queryTimes: number[] = [];
  private readonly SLOW_QUERY_THRESHOLD = 1000; // 1 second

  updateMetrics(poolSize: number, active: number, idle: number, waiting: number) {
    this.metrics.poolSize = poolSize;
    this.metrics.activeConnections = active;
    this.metrics.idleConnections = idle;
    this.metrics.waitingRequests = waiting;
  }

  recordQuery(duration: number) {
    this.metrics.totalQueries++;
    this.queryTimes.push(duration);
    
    // Keep only last 1000 query times
    if (this.queryTimes.length > 1000) {
      this.queryTimes.shift();
    }
    
    // Update average
    this.metrics.averageQueryTime = 
      this.queryTimes.reduce((a, b) => a + b, 0) / this.queryTimes.length;
    
    // Count slow queries
    if (duration > this.SLOW_QUERY_THRESHOLD) {
      this.metrics.slowQueries++;
    }
  }

  getMetrics(): DatabaseMetrics {
    return { ...this.metrics };
  }

  reset() {
    this.metrics.totalQueries = 0;
    this.metrics.averageQueryTime = 0;
    this.metrics.slowQueries = 0;
    this.queryTimes = [];
  }
}

export const databaseMonitor = new DatabaseMonitor();

/**
 * Database optimization recommendations
 */
export function getOptimizationRecommendations(metrics: DatabaseMetrics): string[] {
  const recommendations: string[] = [];

  // Check connection pool utilization
  const utilizationRate = metrics.activeConnections / metrics.poolSize;
  if (utilizationRate > 0.8) {
    recommendations.push('Consider increasing connection pool size - high utilization detected');
  } else if (utilizationRate < 0.2 && metrics.poolSize > 10) {
    recommendations.push('Consider decreasing connection pool size - low utilization detected');
  }

  // Check for waiting requests
  if (metrics.waitingRequests > 0) {
    recommendations.push(`${metrics.waitingRequests} requests waiting for connections - increase pool size`);
  }

  // Check query performance
  if (metrics.averageQueryTime > 500) {
    recommendations.push('High average query time detected - review query optimization');
  }

  if (metrics.slowQueries > metrics.totalQueries * 0.1) {
    recommendations.push('More than 10% of queries are slow - investigate query performance');
  }

  // Check idle connections
  const idleRate = metrics.idleConnections / metrics.poolSize;
  if (idleRate > 0.5) {
    recommendations.push('Many idle connections - consider reducing pool size or idle timeout');
  }

  return recommendations;
}