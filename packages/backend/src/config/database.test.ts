/**
 * Test Database Configuration
 * Uses SQLite for fast, dependency-free testing
 */

import { PrismaClient } from '../../generated/prisma-test';
import path from 'path';
import fs from 'fs';

export interface TestDatabaseConfig {
  databaseUrl: string;
  databasePath: string;
  logging: boolean;
}

/**
 * Get test database configuration
 */
export function getTestDatabaseConfig(): TestDatabaseConfig {
  const testDir = path.join(__dirname, '../../test-data');
  const databasePath = path.join(testDir, 'test.db');
  
  // Ensure test directory exists
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  return {
    databaseUrl: `file:${databasePath}`,
    databasePath,
    logging: process.env.TEST_DEBUG === 'true',
  };
}

/**
 * Create test Prisma client instance
 */
export function createTestPrismaClient(): PrismaClient {
  const config = getTestDatabaseConfig();
  
  const prisma = new PrismaClient({
    log: config.logging ? ['query', 'info', 'warn', 'error'] : ['error'],
    datasources: {
      db: {
        url: config.databaseUrl,
      },
    },
  });

  return prisma;
}

/**
 * Global test Prisma instance
 */
let testPrismaInstance: PrismaClient | null = null;

export function getTestPrismaClient(): PrismaClient {
  if (!testPrismaInstance) {
    testPrismaInstance = createTestPrismaClient();
  }
  return testPrismaInstance;
}

/**
 * Clean up test database and connections
 */
export async function cleanupTestDatabase(): Promise<void> {
  if (testPrismaInstance) {
    await testPrismaInstance.$disconnect();
    testPrismaInstance = null;
  }

  const config = getTestDatabaseConfig();
  
  // Remove test database file if it exists
  if (fs.existsSync(config.databasePath)) {
    try {
      fs.unlinkSync(config.databasePath);
    } catch (error) {
      // Ignore errors during cleanup
      console.warn('Failed to remove test database file:', error);
    }
  }
}

/**
 * Reset test database (recreate schema)
 */
export async function resetTestDatabase(): Promise<void> {
  const config = getTestDatabaseConfig();
  
  // Remove existing database if it exists
  try {
    if (fs.existsSync(config.databasePath)) {
      fs.unlinkSync(config.databasePath);
    }
  } catch (error) {
    console.warn('Failed to remove existing test database:', error);
  }
  
  // Create new instance and run migrations
  const prisma = createTestPrismaClient();
  
  try {
    // Execute schema creation directly - just test the connection
    await prisma.$executeRaw`PRAGMA foreign_keys = ON;`;
    
    // Close connection
    await prisma.$disconnect();
  } catch (error) {
    console.error('Failed to reset test database:', error);
    throw error;
  }
}

/**
 * Seed test database with basic data
 */
export async function seedTestDatabase(): Promise<{
  testUser: any;
  testProject: any;
}> {
  const prisma = getTestPrismaClient();
  
  // Create test user
  const testUser = await prisma.user.create({
    data: {
      id: 'test-user-id',
      email: '<EMAIL>',
      username: 'testuser',
      passwordHash: '$2b$10$test.hash.for.testing.purposes',
      role: 'USER',
      isActive: true,
    },
  });

  // Create test project
  const testProject = await prisma.project.create({
    data: {
      id: 'test-project-id',
      userId: testUser.id,
      title: 'Test Project',
      description: 'A test project for unit tests',
      public: false,
    },
  });

  return { testUser, testProject };
}

/**
 * Utility functions for test data cleanup
 */
export const testDbUtils = {
  /**
   * Clear all data from test database
   */
  async clearAll(): Promise<void> {
    const prisma = getTestPrismaClient();
    
    // Delete in order to respect foreign key constraints
    await prisma.segmentationResult.deleteMany();
    await prisma.segmentation.deleteMany();
    await prisma.segmentationQueue.deleteMany();
    await prisma.projectDuplicationTask.deleteMany();
    await prisma.image.deleteMany();
    await prisma.projectShare.deleteMany();
    await prisma.project.deleteMany();
    await prisma.refreshToken.deleteMany();
    await prisma.emailVerification.deleteMany();
    await prisma.passwordResetToken.deleteMany();
    await prisma.accessRequest.deleteMany();
    await prisma.userProfile.deleteMany();
    await prisma.user.deleteMany();
    await prisma.session.deleteMany();
    await prisma.migration.deleteMany();
  },

  /**
   * Clear specific tables
   */
  async clearTables(...tableNames: string[]): Promise<void> {
    const prisma = getTestPrismaClient();
    
    for (const tableName of tableNames) {
      switch (tableName) {
        case 'users':
          await prisma.user.deleteMany();
          break;
        case 'projects':
          await prisma.project.deleteMany();
          break;
        case 'images':
          await prisma.image.deleteMany();
          break;
        case 'segmentations':
          await prisma.segmentation.deleteMany();
          break;
        // Add more cases as needed
        default:
          console.warn(`Unknown table: ${tableName}`);
      }
    }
  },

  /**
   * Create test user with optional data
   */
  async createTestUser(userData: Partial<any> = {}): Promise<any> {
    const prisma = getTestPrismaClient();
    
    return prisma.user.create({
      data: {
        id: userData.id || `test-user-${Date.now()}`,
        email: userData.email || `test${Date.now()}@example.com`,
        username: userData.username || `testuser${Date.now()}`,
        passwordHash: userData.passwordHash || '$2b$10$test.hash.for.testing.purposes',
        role: userData.role || 'USER',
        isActive: userData.isActive ?? true,
        ...userData,
      },
    });
  },

  /**
   * Create test project with optional data
   */
  async createTestProject(userId: string, projectData: Partial<any> = {}): Promise<any> {
    const prisma = getTestPrismaClient();
    
    return prisma.project.create({
      data: {
        id: projectData.id || `test-project-${Date.now()}`,
        userId,
        title: projectData.title || `Test Project ${Date.now()}`,
        description: projectData.description || 'Test project description',
        public: projectData.public ?? false,
        tags: projectData.tags || '',
        ...projectData,
      },
    });
  },

  /**
   * Create test image with optional data
   */
  async createTestImage(projectId: string, userId: string, imageData: Partial<any> = {}): Promise<any> {
    const prisma = getTestPrismaClient();
    
    return prisma.image.create({
      data: {
        id: imageData.id || `test-image-${Date.now()}`,
        projectId,
        userId,
        name: imageData.name || `test-image-${Date.now()}.jpg`,
        storageFilename: imageData.storageFilename || `storage-${Date.now()}.jpg`,
        originalFilename: imageData.originalFilename || `original-${Date.now()}.jpg`,
        storagePath: imageData.storagePath || `/test/storage/${Date.now()}.jpg`,
        fileSize: imageData.fileSize || 12345, // Int in SQLite, not BigInt
        width: imageData.width || 800,
        height: imageData.height || 600,
        format: imageData.format || 'jpeg',
        status: imageData.status || 'pending',
        segmentationStatus: imageData.segmentationStatus || 'pending',
        metadata: imageData.metadata ? JSON.stringify(imageData.metadata) : null, // JSON as string
        ...imageData,
      },
    });
  },
};

export default {
  getTestDatabaseConfig,
  createTestPrismaClient,
  getTestPrismaClient,
  cleanupTestDatabase,
  resetTestDatabase,
  seedTestDatabase,
  testDbUtils,
};