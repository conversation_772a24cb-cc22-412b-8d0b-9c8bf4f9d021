/**
 * Database Configuration
 * Optimized connection pool settings for PostgreSQL
 */

import { PrismaClient } from '../../generated/prisma';

export interface DatabaseConfig {
  connectionUrl: string;
  pool: {
    min: number;
    max: number;
    idleTimeoutMillis: number;
    connectionTimeoutMillis: number;
    maxUses?: number;
    allowExitOnIdle?: boolean;
  };
  logging: {
    queries: boolean;
    errors: boolean;
    warnings: boolean;
    info: boolean;
  };
  retry: {
    maxAttempts: number;
    initialDelay: number;
    maxDelay: number;
    factor: number;
  };
}

/**
 * Get database configuration from environment variables
 */
export function getDatabaseConfig(): DatabaseConfig {
  const env = process.env;
  const isDevelopment = env.NODE_ENV === 'development';
  const isProduction = env.NODE_ENV === 'production';

  // Construct database URL if not provided
  const databaseUrl = env.DATABASE_URL || 
    `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}?schema=public`;

  return {
    connectionUrl: databaseUrl,
    pool: {
      // Minimum number of connections in the pool
      min: parseInt(env.DB_POOL_MIN || '2', 10),
      
      // Maximum number of connections in the pool
      // Production should have more connections available
      max: parseInt(env.DB_POOL_MAX || (isProduction ? '25' : '10'), 10),
      
      // How long a connection can be idle before being closed
      idleTimeoutMillis: parseInt(env.DB_POOL_IDLE_TIMEOUT || '10000', 10),
      
      // Maximum time to wait for a connection
      connectionTimeoutMillis: parseInt(env.DB_CONNECTION_TIMEOUT || '5000', 10),
      
      // Maximum number of times a connection can be reused
      maxUses: isProduction ? 7500 : undefined,
      
      // Allow the pool to exit if idle
      allowExitOnIdle: !isProduction,
    },
    logging: {
      queries: env.DEBUG_SQL === 'true' || (isDevelopment && env.LOG_LEVEL === 'debug'),
      errors: true,
      warnings: true,
      info: isDevelopment,
    },
    retry: {
      maxAttempts: parseInt(env.DB_RETRY_ATTEMPTS || '3', 10),
      initialDelay: parseInt(env.DB_RETRY_INITIAL_DELAY || '100', 10),
      maxDelay: parseInt(env.DB_RETRY_MAX_DELAY || '5000', 10),
      factor: parseFloat(env.DB_RETRY_FACTOR || '2'),
    },
  };
}

/**
 * Create optimized Prisma client instance
 */
export function createPrismaClient(config?: Partial<DatabaseConfig>): PrismaClient {
  const dbConfig = { ...getDatabaseConfig(), ...config };
  
  // Configure Prisma logging
  const logLevels: Array<'query' | 'info' | 'warn' | 'error'> = [];
  if (dbConfig.logging.queries) logLevels.push('query');
  if (dbConfig.logging.info) logLevels.push('info');
  if (dbConfig.logging.warnings) logLevels.push('warn');
  if (dbConfig.logging.errors) logLevels.push('error');

  const prisma = new PrismaClient({
    log: logLevels,
    datasources: {
      db: {
        url: dbConfig.connectionUrl,
      },
    },
  });

  // Add connection pool monitoring in development
  if (process.env.NODE_ENV === 'development') {
    prisma.$on('query' as never, (e: any) => {
      console.log(`Query: ${e.query}`);
      console.log(`Duration: ${e.duration}ms`);
    });
  }

  return prisma;
}

/**
 * Global Prisma instance with connection pooling
 */
let prismaInstance: PrismaClient | null = null;

export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = createPrismaClient();
  }
  return prismaInstance;
}

/**
 * Gracefully close database connections
 */
export async function closeDatabaseConnection(): Promise<void> {
  if (prismaInstance) {
    await prismaInstance.$disconnect();
    prismaInstance = null;
  }
}

/**
 * Health check for database connection
 */
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const prisma = getPrismaClient();
    await prisma.$queryRaw`SELECT 1`;
    
    return {
      healthy: true,
      latency: Date.now() - startTime,
    };
  } catch (error) {
    return {
      healthy: false,
      latency: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Database connection retry logic
 */
export async function connectWithRetry(
  config?: Partial<DatabaseConfig>
): Promise<PrismaClient> {
  const dbConfig = { ...getDatabaseConfig(), ...config };
  const { maxAttempts, initialDelay, maxDelay, factor } = dbConfig.retry;
  
  let lastError: Error | null = null;
  let delay = initialDelay;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`Attempting database connection (attempt ${attempt}/${maxAttempts})...`);
      
      const prisma = createPrismaClient(config);
      await prisma.$connect();
      
      console.log('Database connected successfully');
      return prisma;
      
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.error(`Database connection failed (attempt ${attempt}):`, lastError.message);
      
      if (attempt < maxAttempts) {
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * factor, maxDelay);
      }
    }
  }

  throw new Error(`Failed to connect to database after ${maxAttempts} attempts: ${lastError?.message}`);
}

/**
 * Connection pool statistics
 */
export interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
}

/**
 * Get connection pool statistics (for monitoring)
 */
export async function getPoolStats(): Promise<PoolStats> {
  // Prisma doesn't expose pool stats directly, but we can query pg_stat_activity
  const prisma = getPrismaClient();
  
  try {
    const stats = await prisma.$queryRaw<Array<{
      total: bigint;
      idle: bigint;
      active: bigint;
    }>>`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE state = 'idle') as idle,
        COUNT(*) FILTER (WHERE state = 'active') as active
      FROM pg_stat_activity
      WHERE datname = current_database()
        AND pid != pg_backend_pid()
    `;

    const result = stats[0];
    return {
      totalConnections: Number(result.total),
      idleConnections: Number(result.idle),
      waitingClients: 0, // Prisma handles this internally
    };
  } catch (error) {
    console.error('Failed to get pool stats:', error);
    return {
      totalConnections: 0,
      idleConnections: 0,
      waitingClients: 0,
    };
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database connections...');
  await closeDatabaseConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database connections...');
  await closeDatabaseConnection();
  process.exit(0);
});

export default {
  getDatabaseConfig,
  createPrismaClient,
  getPrismaClient,
  closeDatabaseConnection,
  checkDatabaseHealth,
  connectWithRetry,
  getPoolStats,
};