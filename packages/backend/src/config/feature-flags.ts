/**
 * Feature Flags Configuration
 * Controls gradual rollout of new features
 */

export interface FeatureFlags {
  usePrismaORM: boolean;
  useNewAuthFlow: boolean;
  useModularRoutes: boolean;
  enableAPIVersioning: boolean;
  enableRateLimiting: boolean;
  enableMonitoring: boolean;
  enableE2ETests: boolean;
}

/**
 * Get feature flags from environment or defaults
 */
export function getFeatureFlags(): FeatureFlags {
  const env = process.env.NODE_ENV;
  const flags = process.env.FEATURE_FLAGS ? JSON.parse(process.env.FEATURE_FLAGS) : {};

  // Default configuration based on environment
  const defaults: FeatureFlags = {
    usePrismaORM: env === 'production' ? (flags.usePrismaORM ?? false) : true,
    useNewAuthFlow: env === 'production' ? (flags.useNewAuthFlow ?? false) : true,
    useModularRoutes: env === 'production' ? (flags.useModularRoutes ?? false) : true,
    enableAPIVersioning: true, // Always enabled for backward compatibility
    enableRateLimiting: env === 'production',
    enableMonitoring: env === 'production',
    enableE2ETests: env === 'test' || env === 'development',
  };

  return { ...defaults, ...flags };
}

/**
 * Check if a specific feature is enabled
 */
export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  const flags = getFeatureFlags();
  return flags[feature];
}

/**
 * Feature flag middleware
 */
export function featureFlagMiddleware(feature: keyof FeatureFlags) {
  return (req: any, res: any, next: any) => {
    if (isFeatureEnabled(feature)) {
      next();
    } else {
      res.status(501).json({
        error: {
          code: 'FEATURE_DISABLED',
          message: `Feature '${feature}' is not enabled`,
          feature,
        },
      });
    }
  };
}

/**
 * Gradual rollout helper
 */
export function gradualRollout(percentage: number, userId?: string): boolean {
  if (percentage >= 100) return true;
  if (percentage <= 0) return false;

  // Use userId for consistent rollout per user
  if (userId) {
    const hash = userId.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0);
    return hash % 100 < percentage;
  }

  // Random rollout if no userId
  return Math.random() * 100 < percentage;
}

/**
 * A/B testing helper
 */
export function getVariant(
  experimentName: string,
  userId?: string,
  variants: string[] = ['control', 'treatment']
): string {
  const flags = getFeatureFlags();

  // Check if experiment is enabled
  const experimentFlag = `experiment_${experimentName}`;
  if (experimentFlag in flags && !flags[experimentFlag as keyof FeatureFlags]) {
    return variants[0]; // Return control if experiment is disabled
  }

  // Consistent variant assignment based on userId
  if (userId) {
    const hash = (experimentName + userId).split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0);
    return variants[hash % variants.length];
  }

  // Random variant if no userId
  return variants[Math.floor(Math.random() * variants.length)];
}
