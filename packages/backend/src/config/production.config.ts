/**
 * Production Configuration
 * Centralized configuration for production server
 * SECURITY: All sensitive values MUST come from environment variables
 */

import crypto from 'crypto';
import { createLogger } from '../utils/logger';

const logger = createLogger('ProductionConfig');

// Validate required environment variables
function validateEnvironment(): void {
  const required = ['DATABASE_URL', 'SESSION_SECRET', 'JWT_SECRET', 'REDIS_URL'];

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    logger.error('Missing required environment variables:', { missing });

    // Provide detailed setup instructions
    console.error('\n========================================');
    console.error('⚠️  ENVIRONMENT CONFIGURATION REQUIRED');
    console.error('========================================\n');
    console.error('Missing environment variables:', missing.join(', '));
    console.error('\nPlease create a .env file with the following variables:');
    console.error('\nDATABASE_URL=postgresql://user:password@localhost:5432/spheroseg');
    console.error('SESSION_SECRET=your-session-secret-here');
    console.error('JWT_SECRET=your-jwt-secret-here');
    console.error('REDIS_URL=redis://localhost:6379');
    console.error('\nOr set them in your environment.');
    console.error('\nFor development, you can copy .env.example:');
    console.error('cp .env.example .env');
    console.error('========================================\n');

    // Graceful fallback for development
    if (process.env.NODE_ENV === 'development') {
      logger.warn('Using fallback configuration for development');
      return; // Continue with defaults in development
    }

    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

// SECURITY: Never use hardcoded values in production
export interface ProductionConfig {
  // Server
  port: number;
  nodeEnv: string;
  enableTrust: boolean;

  // Security
  sessionSecret: string;
  jwtSecret: string;
  corsOrigin: string | string[];

  // Database
  databaseUrl: string;
  maxConnections: number;

  // Redis
  redisUrl: string;

  // Rate Limiting
  rateLimitWindow: number;
  rateLimitRequests: number;

  // Performance
  compressionLevel: number;
  staticCacheDuration: number;

  // Services
  rabbitmqUrl?: string;
  rabbitmqQueue: string;
  mlServiceApiKey?: string;
  mlApiUrl: string;

  // Email
  smtpHost?: string;
  smtpPort: number;
  smtpUser?: string;
  smtpPassword?: string;
}

export function getProductionConfig(): ProductionConfig {
  // Validate environment in production
  if (process.env.NODE_ENV === 'production') {
    validateEnvironment();
  }

  // Parse CORS origins
  let corsOrigin: string | string[];
  if (process.env.CORS_ORIGIN) {
    // Support comma-separated list
    corsOrigin = process.env.CORS_ORIGIN.includes(',')
      ? process.env.CORS_ORIGIN.split(',').map((s) => s.trim())
      : process.env.CORS_ORIGIN;
  } else {
    // Default based on environment - require explicit configuration in production
    if (process.env.NODE_ENV === 'production') {
      throw new Error('CORS_ORIGIN must be configured in production');
    }
    corsOrigin = ['http://localhost:3000', 'http://localhost:5173'];
  }

  const config: ProductionConfig = {
    // Server
    port: parseInt(process.env.PORT || '5001'),
    nodeEnv: process.env.NODE_ENV || 'production',
    enableTrust: process.env.TRUST_PROXY === 'true',

    // Security - MUST come from environment
    sessionSecret:
      process.env.SESSION_SECRET ||
      (process.env.NODE_ENV === 'production'
        ? (() => {
            throw new Error('SESSION_SECRET is required in production');
          })()
        : crypto.randomBytes(32).toString('hex')),
    jwtSecret:
      process.env.JWT_SECRET ||
      (process.env.NODE_ENV === 'production'
        ? (() => {
            throw new Error('JWT_SECRET is required in production');
          })()
        : crypto.randomBytes(32).toString('hex')),
    corsOrigin,

    // Database
    databaseUrl:
      process.env.DATABASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? (() => {
            throw new Error('DATABASE_URL is required in production');
          })()
        : '**************************************/spheroseg'),
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),

    // Redis - Use environment variables or Docker defaults
    redisUrl: process.env.REDIS_URL || (process.env.REDIS_HOST ? `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT || '6379'}` : 'redis://redis:6379'),

    // Rate Limiting
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'),
    rateLimitRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),

    // Performance
    compressionLevel: parseInt(process.env.COMPRESSION_LEVEL || '6'),
    staticCacheDuration: parseInt(process.env.STATIC_CACHE_DURATION || '86400000'),

    // Services - Optional
    rabbitmqUrl: process.env.RABBITMQ_URL,
    rabbitmqQueue: process.env.RABBITMQ_QUEUE || 'segmentation_tasks',
    mlServiceApiKey: process.env.ML_SERVICE_API_KEY,
    mlApiUrl: process.env.ML_API_URL || 'http://ml:5002',

    // Email - Optional
    smtpHost: process.env.SMTP_HOST,
    smtpPort: parseInt(process.env.SMTP_PORT || '25'),
    smtpUser: process.env.SMTP_USER,
    smtpPassword: process.env.SMTP_PASSWORD,
  };

  // Log configuration (without sensitive values)
  logger.info(
    { message: 'Production configuration loaded' },
    {
      port: config.port,
      nodeEnv: config.nodeEnv,
      corsOrigin: Array.isArray(config.corsOrigin)
        ? config.corsOrigin.join(', ')
        : config.corsOrigin,
      enableTrust: config.enableTrust,
      rateLimitWindow: config.rateLimitWindow,
      rateLimitRequests: config.rateLimitRequests,
      // Never log sensitive values!
      hasSessionSecret: !!config.sessionSecret,
      hasJwtSecret: !!config.jwtSecret,
      hasDatabaseUrl: !!config.databaseUrl,
      hasMLApiKey: !!config.mlServiceApiKey,
    }
  );

  return config;
}

// Export singleton instance
export const productionConfig = getProductionConfig();
