# Email Service Migration Notes

## Migration Status

### Completed
- ✅ Created UnifiedEmailService with template system
- ✅ Created email wrapper for backward compatibility
- ✅ Updated project share invite to use new service (line 3075)

### In Progress
- 🔄 Password reset email migration (lines 3238-3320)

### TODO
- ⏳ Remove old email service files after testing
- ⏳ Initialize email service on server startup
- ⏳ Test all email functionality

## Manual Migration Steps

### Step 1: Password Reset Email (server-production-simple.ts)

**Current Implementation (lines 3238-3320):**
- Inline nodemailer implementation
- Direct SMTP configuration
- HTML template hardcoded

**Replace with:**
```typescript
try {
  const { sendPasswordResetSimple } = await import('./services/emailServiceWrapper');
  await sendPasswordResetSimple(
    user.email,
    user.name || 'User',
    newPassword
  );
  logger.info('Password reset email sent successfully');
} catch (emailError) {
  logger.error('Email setup error (non-blocking):', emailError);
}
```

### Step 2: Server Initialization

Add to server startup (around line 3650):
```typescript
import { initializeEmailService } from './services/emailServiceWrapper';

// Initialize email service
initializeEmailService().catch(err => {
  logger.error('Failed to initialize email service:', err);
});
```

### Step 3: Clean Up

After testing, remove:
- `/packages/backend/src/services/emailServiceSimple.ts`
- `/packages/backend/src/services/emailServiceSimple25.ts`
- `/packages/backend/src/services/emailServiceFallback.ts`
- `/packages/backend/src/services/emailQueue.ts`
- Backup files

## Testing Checklist

- [ ] Password reset email sends correctly
- [ ] Project invite email sends correctly
- [ ] Email templates render properly
- [ ] Error handling works as expected
- [ ] Timeout handling functions correctly

## Notes

The monolithic server file (3,693 lines) makes direct editing challenging. The migration approach uses:
1. UnifiedEmailService - Core service with templates
2. emailServiceWrapper - Backward compatibility layer
3. Progressive migration - Replace one function at a time
4. Testing between each change

This ensures minimal disruption to the production system.