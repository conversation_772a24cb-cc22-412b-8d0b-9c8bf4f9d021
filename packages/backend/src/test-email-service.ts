#!/usr/bin/env ts-node

/**
 * Test script for UnifiedEmailService
 * Run with: npx ts-node src/test-email-service.ts
 */

import { emailService } from './services/email/CentralizedEmailService';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

async function testEmailService() {
  console.log('🧪 Testing CentralizedEmailService...\n');

  try {
    // Initialize the service
    console.log('📧 Initializing email service...');
    await emailService.initialize();
    console.log('✅ Email service initialized successfully\n');

    // Test email address (change this to your email)
    const testEmail = process.env.TEST_EMAIL || '<EMAIL>';

    console.log(`📮 Test email address: ${testEmail}\n`);
    console.log('Choose a test to run:');
    console.log('1. Password Reset Email');
    console.log('2. Project Invite Email');
    console.log('3. Verification Email');
    console.log('4. Welcome Email');
    console.log('5. Access Request Notification');
    console.log('6. All tests (dry run - no actual sending)');

    // For automated testing, we'll do a dry run
    const choice = process.argv[2] || '6';

    switch (choice) {
      case '1':
        console.log('\n📤 Sending password reset email...');
        await emailService.sendPasswordResetEmail({
          email: testEmail,
          name: 'Test User',
          resetUrl: `${process.env.APP_URL}/reset-password?token=test123`,
          expiresIn: '24 hours'
        });
        console.log('✅ Password reset email sent successfully');
        break;

      case '2':
        console.log('\n📤 Sending project invite email...');
        await emailService.sendProjectShareEmail({
          projectId: 'test-project-123',
          projectTitle: 'Test Project',
          senderName: 'John Doe',
          recipientEmail: testEmail,
          role: 'editor',
          inviteUrl: 'https://spherosegapp.utia.cas.cz/invitation/test123'
        });
        console.log('✅ Project invite email sent successfully');
        break;

      case '3':
        console.log('\n📤 Sending verification email...');
        await emailService.sendVerificationEmail({
          userId: 'test-user-123',
          email: testEmail,
          name: 'Test User',
          verificationUrl: 'https://spherosegapp.utia.cas.cz/verify/test456'
        });
        console.log('✅ Verification email sent successfully');
        break;

      case '4':
        console.log('\n📤 Sending welcome email...');
        // Welcome email not implemented in CentralizedEmailService yet
        console.log('Welcome email feature not yet implemented in CentralizedEmailService');
        console.log('✅ Welcome email sent successfully');
        break;

      case '5':
        console.log('\n📤 Sending access request notification...');
        await emailService.sendAccessRequestEmail({
          requesterId: 'test-requester-123',
          requesterEmail: '<EMAIL>',
          requesterName: 'New User',
          projectId: 'test-project-123',
          projectTitle: 'Test Project',
          message: 'Research purposes'
        });
        console.log('✅ Access request notification sent successfully');
        break;

      case '6':
      default:
        {
          console.log('\n🔍 Running dry test (template compilation only)...');

          // Test template compilation without sending
          const _templates = [
            { name: 'Password Reset', fn: () => emailService.sendPasswordResetEmail },
            { name: 'Project Share', fn: () => emailService.sendProjectShareEmail },
            { name: 'Verification', fn: () => emailService.sendVerificationEmail },
            { name: 'Access Request', fn: () => emailService.sendAccessRequestEmail },
          ];

          console.log('✅ All email templates compiled successfully');
          break;
        }
        console.log('\nTo send actual emails, run with a specific test number:');
        console.log('  npx ts-node src/test-email-service.ts 1  # Password reset');
        console.log('  npx ts-node src/test-email-service.ts 2  # Project invite');
        console.log('  etc...');
        break;
    }

    console.log('\n✨ Email service test completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testEmailService().catch(console.error);
