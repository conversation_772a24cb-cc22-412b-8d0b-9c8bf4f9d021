/**
 * Optimized RabbitMQ queue with connection pooling and batching
 */

import amqp from 'amqplib';
import { EventEmitter } from 'events';
import pLimit from 'p-limit';

interface QueueOptions {
  url?: string;
  prefetch?: number;
  heartbeat?: number;
  maxRetries?: number;
  retryDelay?: number;
  batchSize?: number;
  batchTimeout?: number;
}

interface Message {
  id: string;
  type: string;
  data: any;
  priority?: number;
  timestamp?: number;
  retries?: number;
}

export class OptimizedQueue extends EventEmitter {
  private connection: amqp.Connection | null = null;
  private channel: amqp.Channel | null = null;
  private options: Required<QueueOptions>;
  private reconnecting = false;
  private messageBuffer: Message[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private concurrencyLimit: any;
  private stats = {
    processed: 0,
    failed: 0,
    retried: 0,
    avgProcessingTime: 0,
  };

  constructor(options: QueueOptions = {}) {
    super();

    this.options = {
      url: options.url || process.env.RABBITMQ_URL || 'amqp://rabbitmq:5672',
      prefetch: options.prefetch || 10,
      heartbeat: options.heartbeat || 60,
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 5000,
      batchSize: options.batchSize || 10,
      batchTimeout: options.batchTimeout || 1000,
    };

    // Set concurrency limit for processing
    this.concurrencyLimit = pLimit(this.options.prefetch);
  }

  /**
   * Connect to RabbitMQ with automatic reconnection
   */
  async connect(): Promise<void> {
    try {
      this.connection = (await amqp.connect(this.options.url, {
        heartbeat: this.options.heartbeat,
      })) as unknown as amqp.Connection;

      this.connection.on('error', (err) => {
        console.error('RabbitMQ connection error:', err);
        this.reconnect();
      });

      this.connection.on('close', () => {
        console.log('RabbitMQ connection closed');
        this.reconnect();
      });

      this.channel = (await (this.connection as any).createChannel()) as amqp.Channel;
      await this.channel.prefetch(this.options.prefetch);

      console.log('Connected to RabbitMQ');
      this.emit('connected');
    } catch (error) {
      console.error('Failed to connect to RabbitMQ:', error);
      this.reconnect();
      throw error;
    }
  }

  /**
   * Reconnect with exponential backoff
   */
  private async reconnect(): Promise<void> {
    if (this.reconnecting) return;
    this.reconnecting = true;

    let retries = 0;
    const maxRetries = 10;
    const baseDelay = 1000;

    while (retries < maxRetries) {
      try {
        const delay = Math.min(baseDelay * Math.pow(2, retries), 30000);
        console.log(`Reconnecting to RabbitMQ in ${delay}ms...`);

        await new Promise((resolve) => setTimeout(resolve, delay));
        await this.connect();

        this.reconnecting = false;
        return;
      } catch (error) {
        retries++;
        console.error(`Reconnection attempt ${retries} failed:`, error);
      }
    }

    this.reconnecting = false;
    this.emit('error', new Error('Failed to reconnect to RabbitMQ'));
  }

  /**
   * Setup queue with optimizations
   */
  async setupQueue(queueName: string): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    // Assert queue with optimizations
    await this.channel.assertQueue(queueName, {
      durable: true,
      arguments: {
        'x-max-priority': 10, // Enable priority queue
        'x-message-ttl': 3600000, // 1 hour TTL
        'x-max-length': 10000, // Max queue length
        'x-overflow': 'drop-head', // Drop oldest messages when full
      },
    });

    // Create dead letter queue for failed messages
    await this.channel.assertQueue(`${queueName}.dlq`, {
      durable: true,
      arguments: {
        'x-message-ttl': 86400000, // 24 hours
      },
    });

    // Bind dead letter exchange
    await this.channel.assertExchange(`${queueName}.dlx`, 'direct', { durable: true });
    await this.channel.bindQueue(`${queueName}.dlq`, `${queueName}.dlx`, queueName);
  }

  /**
   * Send message with batching
   */
  async sendMessage(queueName: string, message: Message): Promise<void> {
    // Add to buffer
    this.messageBuffer.push({
      ...message,
      timestamp: message.timestamp || Date.now(),
    });

    // Check if we should send batch
    if (this.messageBuffer.length >= this.options.batchSize) {
      await this.flushMessageBuffer(queueName);
    } else {
      // Set timer for batch timeout
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.flushMessageBuffer(queueName);
        }, this.options.batchTimeout);
      }
    }
  }

  /**
   * Flush message buffer
   */
  private async flushMessageBuffer(queueName: string): Promise<void> {
    if (!this.channel || this.messageBuffer.length === 0) return;

    const messages = [...this.messageBuffer];
    this.messageBuffer = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    try {
      // Send messages in batch
      for (const message of messages) {
        const buffer = Buffer.from(JSON.stringify(message));
        const priority = message.priority || 0;

        await this.channel.sendToQueue(queueName, buffer, {
          persistent: true,
          priority,
          timestamp: message.timestamp,
          messageId: message.id,
          headers: {
            'x-retry-count': message.retries || 0,
          },
        });
      }

      console.log(`Sent batch of ${messages.length} messages to ${queueName}`);
    } catch (error) {
      console.error('Failed to send message batch:', error);
      // Re-add messages to buffer for retry
      this.messageBuffer.unshift(...messages);
      throw error;
    }
  }

  /**
   * Consume messages with concurrency control
   */
  async consumeMessages(
    queueName: string,
    handler: (message: Message) => Promise<void>
  ): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    await this.channel.consume(queueName, async (msg) => {
      if (!msg) return;

      const startTime = Date.now();

      try {
        // Parse message
        const message: Message = JSON.parse(msg.content.toString());

        // Process with concurrency limit
        await this.concurrencyLimit(async () => {
          await handler(message);
        });

        // Acknowledge message
        this.channel!.ack(msg);

        // Update stats
        const processingTime = Date.now() - startTime;
        this.updateStats('processed', processingTime);

        console.log(`Processed message ${message.id} in ${processingTime}ms`);
      } catch (error) {
        console.error('Failed to process message:', error);

        // Handle retry logic
        const retryCount = msg.properties.headers?.['x-retry-count'] || 0;

        if (retryCount < this.options.maxRetries) {
          // Requeue with delay
          setTimeout(
            () => {
              this.channel!.nack(msg, false, true);
              this.updateStats('retried');
            },
            this.options.retryDelay * (retryCount + 1)
          );
        } else {
          // Send to dead letter queue
          this.channel!.nack(msg, false, false);
          this.updateStats('failed');
        }
      }
    });
  }

  /**
   * Update statistics
   */
  private updateStats(type: 'processed' | 'failed' | 'retried', processingTime?: number): void {
    this.stats[type]++;

    if (processingTime && type === 'processed') {
      const total = this.stats.processed;
      this.stats.avgProcessingTime =
        (this.stats.avgProcessingTime * (total - 1) + processingTime) / total;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(queueName: string): Promise<any> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    const queueInfo = await this.channel.checkQueue(queueName);

    return {
      queue: queueName,
      messageCount: queueInfo.messageCount,
      consumerCount: queueInfo.consumerCount,
      ...this.stats,
    };
  }

  /**
   * Graceful shutdown
   */
  async close(): Promise<void> {
    // Flush any pending messages
    if (this.messageBuffer.length > 0) {
      await this.flushMessageBuffer('default');
    }

    if (this.channel) {
      await this.channel.close();
    }

    if (this.connection) {
      await (this.connection as any).close();
    }

    console.log('RabbitMQ connection closed');
  }
}

// Create singleton instance
let queueInstance: OptimizedQueue | null = null;

export const getQueue = (): OptimizedQueue => {
  if (!queueInstance) {
    queueInstance = new OptimizedQueue();
  }
  return queueInstance;
};

// Export default instance
export default getQueue();
