/**
 * Test App Creation Utilities
 * 
 * Provides utilities for creating Express apps for testing with
 * configurable middleware and routes.
 */

import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import { json, urlencoded } from 'body-parser';
import { createImageRoutes } from '../routes/images';
import { createProjectRoutes } from '../routes/projects';
import authRoutes from '../routes/auth';
import { errorHandler } from '../middleware/errorHandler';
import { requestLogger } from '../middleware/requestLogger';

// Types
export interface TestAppOptions {
  useAuth?: boolean;
  useDatabase?: boolean;
  useMiddleware?: boolean;
  useRoutes?: boolean;
  useCors?: boolean;
  useCompression?: boolean;
  useErrorHandler?: boolean;
  customMiddleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
  customRoutes?: Array<{ path: string; router: any }>;
}

/**
 * Create a full test app with all middleware and routes
 */
export function createTestApp(options: TestAppOptions = {}): Express {
  const {
    useAuth = true,
    useDatabase = false,
    useMiddleware = true,
    useRoutes = true,
    useCors = true,
    useCompression = false,
    useErrorHandler = true,
    customMiddleware = [],
    customRoutes = [],
  } = options;

  const app = express();

  // Basic middleware
  if (useMiddleware) {
    app.use(json({ limit: '50mb' }));
    app.use(urlencoded({ extended: true, limit: '50mb' }));
    app.use(cookieParser());
  }

  // Optional middleware
  if (useCors) {
    app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'x-csrf-token'],
    }));
  }

  if (useCompression) {
    app.use(compression());
  }

  // Request logging (minimal for tests)
  if (process.env.DEBUG) {
    app.use(requestLogger);
  }

  // Custom middleware
  customMiddleware.forEach(middleware => {
    app.use(middleware);
  });

  // Health check route
  app.get('/health', (req: Request, res: Response) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // API routes
  if (useRoutes) {
    // Auth routes
    if (useAuth) {
      app.use('/api/auth', authRoutes);
    }

    // Main API routes
    app.use('/api/projects', createProjectRoutes());
    app.use('/api/images', createImageRoutes());

    // Custom routes
    customRoutes.forEach(({ path, router }) => {
      app.use(path, router);
    });
  }

  // 404 handler
  app.use((req: Request, res: Response) => {
    res.status(404).json({
      success: false,
      error: 'Not Found',
      path: req.path,
    });
  });

  // Error handler
  if (useErrorHandler) {
    app.use(errorHandler);
  }

  return app;
}

/**
 * Create a minimal test app with only basic middleware
 */
export function createMinimalApp(): Express {
  const app = express();
  
  app.use(json());
  app.use(urlencoded({ extended: true }));
  
  // Simple test route
  app.get('/test', (req: Request, res: Response) => {
    res.json({ message: 'Test route working' });
  });
  
  return app;
}

/**
 * Create an app with specific test routes
 */
export function createAppWithTestRoutes(routes: any[]): Express {
  const app = createMinimalApp();
  
  routes.forEach(({ method, path, handler }) => {
    (app as any)[method.toLowerCase()](path, handler);
  });
  
  return app;
}

/**
 * Create an app with mock authentication
 */
export function createAuthenticatedApp(userId: string = 'test-user'): Express {
  const app = createMinimalApp();
  
  // Mock auth middleware
  app.use((req: any, res: Response, next: NextFunction) => {
    req.userId = userId;
    req.user = {
      id: userId,
      email: '<EMAIL>',
      name: 'Test User',
    };
    next();
  });
  
  // Protected route
  app.get('/api/protected', (req: any, res: Response) => {
    res.json({
      message: 'Protected route',
      userId: req.userId,
      user: req.user,
    });
  });
  
  return app;
}

/**
 * Create an app with error simulation
 */
export function createAppWithErrors(): Express {
  const app = createMinimalApp();
  
  // Route that throws sync error
  app.get('/error/sync', (req: Request, res: Response) => {
    throw new Error('Sync error');
  });
  
  // Route that throws async error
  app.get('/error/async', async (req: Request, res: Response) => {
    await Promise.reject(new Error('Async error'));
  });
  
  // Route that returns error response
  app.get('/error/response', (req: Request, res: Response) => {
    res.status(500).json({ error: 'Response error' });
  });
  
  // Error handler
  app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
    res.status(500).json({
      error: err.message,
      stack: process.env.NODE_ENV === 'test' ? err.stack : undefined,
    });
  });
  
  return app;
}

/**
 * Add test data endpoints to an app
 */
export function addTestDataEndpoints(app: Express): Express {
  // Get test data
  app.get('/api/test-data/:type', (req: Request, res: Response) => {
    const { type } = req.params;
    
    const testData: any = {
      users: [
        { id: '1', name: 'User 1', email: '<EMAIL>' },
        { id: '2', name: 'User 2', email: '<EMAIL>' },
      ],
      projects: [
        { id: '1', name: 'Project 1', userId: '1' },
        { id: '2', name: 'Project 2', userId: '2' },
      ],
      images: [
        { id: '1', projectId: '1', filename: 'image1.jpg' },
        { id: '2', projectId: '1', filename: 'image2.jpg' },
      ],
    };
    
    res.json(testData[type] || []);
  });
  
  // Create test data
  app.post('/api/test-data/:type', (req: Request, res: Response) => {
    res.status(201).json({
      id: Date.now().toString(),
      ...req.body,
    });
  });
  
  return app;
}

/**
 * Create an app for WebSocket testing
 */
export function createWebSocketApp(): Express {
  const app = createMinimalApp();
  
  // WebSocket upgrade endpoint
  app.get('/ws', (req: Request, res: Response) => {
    res.status(426).json({
      error: 'WebSocket upgrade required',
      headers: { 'Upgrade': 'websocket' },
    });
  });
  
  return app;
}

/**
 * Create an app with rate limiting simulation
 */
export function createRateLimitedApp(limit: number = 10): Express {
  const app = createMinimalApp();
  const requests = new Map<string, number>();
  
  // Rate limiting middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    const count = requests.get(ip) || 0;
    
    if (count >= limit) {
      return res.status(429).json({
        error: 'Too Many Requests',
        retryAfter: 60,
      });
    }
    
    requests.set(ip, count + 1);
    
    // Reset after 1 minute
    setTimeout(() => {
      requests.set(ip, (requests.get(ip) || 1) - 1);
    }, 60000);
    
    next();
  });
  
  app.get('/api/limited', (req: Request, res: Response) => {
    res.json({ message: 'Success' });
  });
  
  return app;
}

// Export default app utilities
export default {
  createTestApp,
  createMinimalApp,
  createAppWithTestRoutes,
  createAuthenticatedApp,
  createAppWithErrors,
  addTestDataEndpoints,
  createWebSocketApp,
  createRateLimitedApp,
};