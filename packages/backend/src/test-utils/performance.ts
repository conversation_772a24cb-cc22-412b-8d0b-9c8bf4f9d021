/**
 * Performance Testing Utilities
 * 
 * Provides utilities for performance testing, benchmarking,
 * and load testing of the backend services.
 */

import { performance } from 'perf_hooks';
import { EventEmitter } from 'events';

// Types
export interface PerformanceMetrics {
  duration: number;
  startTime: number;
  endTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
}

export interface BenchmarkResult {
  name: string;
  iterations: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  standardDeviation: number;
  opsPerSecond: number;
  memoryDelta: number;
}

export interface LoadTestOptions {
  concurrency: number;
  iterations: number;
  warmup?: boolean;
  timeout?: number;
}

/**
 * Performance timer for measuring execution time
 */
export class PerformanceTimer {
  private startTime: number = 0;
  private startMemory: NodeJS.MemoryUsage | null = null;
  private startCpu: NodeJS.CpuUsage | null = null;
  
  /**
   * Start the timer
   */
  start(): void {
    this.startTime = performance.now();
    this.startMemory = process.memoryUsage();
    this.startCpu = process.cpuUsage();
  }
  
  /**
   * Stop the timer and return metrics
   */
  stop(): PerformanceMetrics {
    const endTime = performance.now();
    const endMemory = process.memoryUsage();
    const endCpu = process.cpuUsage(this.startCpu!);
    
    return {
      duration: endTime - this.startTime,
      startTime: this.startTime,
      endTime,
      memoryUsage: {
        rss: endMemory.rss - (this.startMemory?.rss || 0),
        heapTotal: endMemory.heapTotal - (this.startMemory?.heapTotal || 0),
        heapUsed: endMemory.heapUsed - (this.startMemory?.heapUsed || 0),
        external: endMemory.external - (this.startMemory?.external || 0),
        arrayBuffers: endMemory.arrayBuffers - (this.startMemory?.arrayBuffers || 0),
      },
      cpuUsage: endCpu,
    };
  }
  
  /**
   * Measure async function execution
   */
  static async measure<T>(
    fn: () => Promise<T>
  ): Promise<{ result: T; metrics: PerformanceMetrics }> {
    const timer = new PerformanceTimer();
    timer.start();
    
    try {
      const result = await fn();
      const metrics = timer.stop();
      return { result, metrics };
    } catch (error) {
      const metrics = timer.stop();
      throw { error, metrics };
    }
  }
}

/**
 * Benchmark runner for performance testing
 */
export class BenchmarkRunner {
  private results: BenchmarkResult[] = [];
  
  /**
   * Run a benchmark
   */
  async run(
    name: string,
    fn: () => Promise<void> | void,
    iterations: number = 100
  ): Promise<BenchmarkResult> {
    const times: number[] = [];
    const memoryDeltas: number[] = [];
    
    // Warmup
    for (let i = 0; i < Math.min(10, iterations / 10); i++) {
      await fn();
    }
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    // Run benchmark
    for (let i = 0; i < iterations; i++) {
      const startMemory = process.memoryUsage().heapUsed;
      const startTime = performance.now();
      
      await fn();
      
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;
      
      times.push(endTime - startTime);
      memoryDeltas.push(endMemory - startMemory);
    }
    
    // Calculate statistics
    const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    const variance = times.reduce((sum, time) => {
      return sum + Math.pow(time - averageTime, 2);
    }, 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    
    const averageMemoryDelta = 
      memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length;
    
    const result: BenchmarkResult = {
      name,
      iterations,
      averageTime,
      minTime,
      maxTime,
      standardDeviation,
      opsPerSecond: 1000 / averageTime,
      memoryDelta: averageMemoryDelta,
    };
    
    this.results.push(result);
    return result;
  }
  
  /**
   * Compare two functions
   */
  async compare(
    name1: string,
    fn1: () => Promise<void> | void,
    name2: string,
    fn2: () => Promise<void> | void,
    iterations: number = 100
  ): Promise<{ result1: BenchmarkResult; result2: BenchmarkResult; faster: string; ratio: number }> {
    const result1 = await this.run(name1, fn1, iterations);
    const result2 = await this.run(name2, fn2, iterations);
    
    const faster = result1.averageTime < result2.averageTime ? name1 : name2;
    const ratio = Math.max(result1.averageTime, result2.averageTime) / 
                  Math.min(result1.averageTime, result2.averageTime);
    
    return { result1, result2, faster, ratio };
  }
  
  /**
   * Get all results
   */
  getResults(): BenchmarkResult[] {
    return this.results;
  }
  
  /**
   * Print results to console
   */
  printResults(): void {
    console.table(this.results.map(r => ({
      Name: r.name,
      'Avg Time (ms)': r.averageTime.toFixed(3),
      'Min Time (ms)': r.minTime.toFixed(3),
      'Max Time (ms)': r.maxTime.toFixed(3),
      'Std Dev': r.standardDeviation.toFixed(3),
      'Ops/sec': Math.round(r.opsPerSecond),
      'Memory Δ (KB)': (r.memoryDelta / 1024).toFixed(2),
    })));
  }
  
  /**
   * Clear results
   */
  clear(): void {
    this.results = [];
  }
}

/**
 * Load tester for concurrent request testing
 */
export class LoadTester extends EventEmitter {
  private results: any[] = [];
  private errors: any[] = [];
  
  /**
   * Run load test
   */
  async run(
    fn: () => Promise<any>,
    options: LoadTestOptions
  ): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
    errors: any[];
  }> {
    const { concurrency, iterations, warmup = true, timeout = 30000 } = options;
    
    // Warmup
    if (warmup) {
      await fn();
    }
    
    const startTime = performance.now();
    const promises: Promise<void>[] = [];
    
    // Create workers
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.worker(fn, Math.ceil(iterations / concurrency), timeout));
    }
    
    // Wait for all workers
    await Promise.all(promises);
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    // Calculate statistics
    const successfulRequests = this.results.length;
    const failedRequests = this.errors.length;
    const totalRequests = successfulRequests + failedRequests;
    
    const averageResponseTime = this.results.length > 0
      ? this.results.reduce((sum, r) => sum + r.duration, 0) / this.results.length
      : 0;
    
    const requestsPerSecond = (totalRequests / totalTime) * 1000;
    
    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      requestsPerSecond,
      errors: this.errors,
    };
  }
  
  /**
   * Worker function for load testing
   */
  private async worker(
    fn: () => Promise<any>,
    iterations: number,
    timeout: number
  ): Promise<void> {
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      
      try {
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), timeout);
        });
        
        const result = await Promise.race([fn(), timeoutPromise]);
        
        const endTime = performance.now();
        
        this.results.push({
          result,
          duration: endTime - startTime,
          timestamp: startTime,
        });
        
        this.emit('success', { result, duration: endTime - startTime });
      } catch (error) {
        const endTime = performance.now();
        
        this.errors.push({
          error,
          duration: endTime - startTime,
          timestamp: startTime,
        });
        
        this.emit('error', { error, duration: endTime - startTime });
      }
    }
  }
  
  /**
   * Clear results
   */
  clear(): void {
    this.results = [];
    this.errors = [];
  }
}

/**
 * Memory leak detector
 */
export class MemoryLeakDetector {
  private snapshots: NodeJS.MemoryUsage[] = [];
  private interval: NodeJS.Timeout | null = null;
  
  /**
   * Start monitoring memory
   */
  start(intervalMs: number = 1000): void {
    this.snapshots = [];
    this.interval = setInterval(() => {
      this.snapshots.push(process.memoryUsage());
    }, intervalMs);
  }
  
  /**
   * Stop monitoring and analyze
   */
  stop(): {
    leakDetected: boolean;
    averageGrowth: number;
    maxHeap: number;
    minHeap: number;
  } {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    
    if (this.snapshots.length < 2) {
      return {
        leakDetected: false,
        averageGrowth: 0,
        maxHeap: 0,
        minHeap: 0,
      };
    }
    
    // Calculate heap growth
    const heapSizes = this.snapshots.map(s => s.heapUsed);
    const growths: number[] = [];
    
    for (let i = 1; i < heapSizes.length; i++) {
      growths.push(heapSizes[i] - heapSizes[i - 1]);
    }
    
    const averageGrowth = growths.reduce((a, b) => a + b, 0) / growths.length;
    const maxHeap = Math.max(...heapSizes);
    const minHeap = Math.min(...heapSizes);
    
    // Simple leak detection: consistent growth over time
    const consistentGrowth = growths.filter(g => g > 0).length > growths.length * 0.7;
    const significantGrowth = averageGrowth > 1024 * 1024; // 1MB average growth
    
    return {
      leakDetected: consistentGrowth && significantGrowth,
      averageGrowth,
      maxHeap,
      minHeap,
    };
  }
  
  /**
   * Get snapshots
   */
  getSnapshots(): NodeJS.MemoryUsage[] {
    return this.snapshots;
  }
}

/**
 * Helper function to measure database query performance
 */
export async function measureQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<{ result: T; metrics: PerformanceMetrics & { queryName: string } }> {
  const { result, metrics } = await PerformanceTimer.measure(queryFn);
  
  return {
    result,
    metrics: {
      ...metrics,
      queryName,
    },
  };
}

/**
 * Helper function to profile a test suite
 */
export async function profileTestSuite(
  suiteName: string,
  tests: Array<{ name: string; fn: () => Promise<void> }>
): Promise<void> {
  console.log(`\nProfiling suite: ${suiteName}`);
  console.log('='.repeat(50));
  
  const runner = new BenchmarkRunner();
  
  for (const test of tests) {
    await runner.run(test.name, test.fn, 10);
  }
  
  runner.printResults();
}

// Export default performance utilities
export default {
  PerformanceTimer,
  BenchmarkRunner,
  LoadTester,
  MemoryLeakDetector,
  measureQuery,
  profileTestSuite,
};