/**
 * Unified Test Utilities
 * 
 * Single source of truth for all test utilities in the backend.
 * This module consolidates functionality from multiple legacy test helpers.
 */

// Re-export all test utilities
export * from './auth';
export * from './database';
export * from './app';
export * from './mocks';
export * from './performance';
export * from './integration';

// Convenience imports for common use cases
import { createTestApp, createMinimalApp } from './app';
import { createMockRequest, createMockResponse, createMockNext } from './mocks';
import { setupTestDatabase, cleanupTestDatabase, createTestUser } from './database';
import { createAuthToken, mockAuthMiddleware } from './auth';

export const TestUtils = {
  // App utilities
  createTestApp,
  createMinimalApp,
  
  // Mock utilities
  createMockRequest,
  createMockResponse,
  createMockNext,
  
  // Database utilities
  setupTestDatabase,
  cleanupTestDatabase,
  createTestUser,
  
  // Auth utilities
  createAuthToken,
  mockAuthMiddleware,
};

export default TestUtils;