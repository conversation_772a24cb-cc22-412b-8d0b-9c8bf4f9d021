/**
 * Mock Factory Utilities
 * 
 * Provides utilities for creating mock objects for testing,
 * including Express req/res/next, data factories, and more.
 */

import { Request, Response, NextFunction } from 'express';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';

// Types
export interface MockRequestOptions {
  method?: string;
  url?: string;
  path?: string;
  query?: any;
  params?: any;
  body?: any;
  headers?: any;
  cookies?: any;
  session?: any;
  userId?: string;
  user?: any;
  file?: any;
  files?: any[];
}

export interface MockResponseOptions {
  locals?: any;
}

// Mock cache for performance
const mockCache = new Map<string, any>();

/**
 * Create a mock Express request
 */
export function createMockRequest(options: MockRequestOptions = {}): Partial<Request> {
  const cacheKey = JSON.stringify(options);
  
  if (mockCache.has(cacheKey)) {
    return { ...mockCache.get(cacheKey) };
  }
  
  const mockReq: any = {
    method: options.method || 'GET',
    url: options.url || '/',
    path: options.path || '/',
    originalUrl: options.url || '/',
    baseUrl: '',
    query: options.query || {},
    params: options.params || {},
    body: options.body || {},
    headers: {
      'content-type': 'application/json',
      ...options.headers,
    },
    cookies: options.cookies || {},
    session: options.session || {},
    get: jest.fn((header: string) => mockReq.headers[header.toLowerCase()]),
    header: jest.fn((header: string) => mockReq.headers[header.toLowerCase()]),
    accepts: jest.fn(() => true),
    acceptsCharsets: jest.fn(() => true),
    acceptsEncodings: jest.fn(() => true),
    acceptsLanguages: jest.fn(() => true),
    is: jest.fn(() => true),
    ip: '127.0.0.1',
    ips: ['127.0.0.1'],
    hostname: 'localhost',
    protocol: 'http',
    secure: false,
    fresh: false,
    stale: true,
    xhr: false,
    app: {},
    route: {},
  };
  
  // Add authentication properties if provided
  if (options.userId) {
    mockReq.userId = options.userId;
  }
  if (options.user) {
    mockReq.user = options.user;
  }
  
  // Add file upload properties if provided
  if (options.file) {
    mockReq.file = options.file;
  }
  if (options.files) {
    mockReq.files = options.files;
  }
  
  // Cache for reuse
  mockCache.set(cacheKey, mockReq);
  
  return mockReq;
}

/**
 * Create a mock Express response
 */
export function createMockResponse(options: MockResponseOptions = {}): Partial<Response> {
  const mockRes: any = {
    statusCode: 200,
    locals: options.locals || {},
    headers: {},
    
    // Methods
    status: jest.fn(function(this: any, code: number) {
      this.statusCode = code;
      return this;
    }),
    
    json: jest.fn(function(this: any, data: any) {
      this.jsonData = data;
      return this;
    }),
    
    send: jest.fn(function(this: any, data: any) {
      this.sentData = data;
      return this;
    }),
    
    sendStatus: jest.fn(function(this: any, code: number) {
      this.statusCode = code;
      return this;
    }),
    
    sendFile: jest.fn(function(this: any, path: string) {
      this.sentFile = path;
      return this;
    }),
    
    redirect: jest.fn(function(this: any, url: string) {
      this.redirectUrl = url;
      return this;
    }),
    
    set: jest.fn(function(this: any, field: string, value: string) {
      this.headers[field] = value;
      return this;
    }),
    
    setHeader: jest.fn(function(this: any, field: string, value: string) {
      this.headers[field] = value;
      return this;
    }),
    
    get: jest.fn(function(this: any, field: string) {
      return this.headers[field];
    }),
    
    cookie: jest.fn(function(this: any, name: string, value: string, options?: any) {
      this.cookies = this.cookies || {};
      this.cookies[name] = { value, options };
      return this;
    }),
    
    clearCookie: jest.fn(function(this: any, name: string) {
      if (this.cookies) {
        delete this.cookies[name];
      }
      return this;
    }),
    
    type: jest.fn(function(this: any, type: string) {
      this.headers['Content-Type'] = type;
      return this;
    }),
    
    format: jest.fn(),
    render: jest.fn(),
    end: jest.fn(),
    
    // Additional properties
    headersSent: false,
    app: {},
    req: {},
  };
  
  // Bind methods to maintain context
  Object.keys(mockRes).forEach(key => {
    if (typeof mockRes[key] === 'function') {
      mockRes[key] = mockRes[key].bind(mockRes);
    }
  });
  
  return mockRes;
}

/**
 * Create a mock Next function
 */
export function createMockNext(): NextFunction {
  return jest.fn() as NextFunction;
}

/**
 * Create a mock WebSocket
 */
export function createMockWebSocket(): any {
  const ws = new EventEmitter();
  
  return {
    ...ws,
    send: jest.fn(),
    close: jest.fn(),
    ping: jest.fn(),
    pong: jest.fn(),
    terminate: jest.fn(),
    readyState: 1, // OPEN
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3,
  };
}

/**
 * Create mock file upload
 */
export function createMockFile(options: any = {}): Express.Multer.File {
  return {
    fieldname: options.fieldname || 'file',
    originalname: options.originalname || 'test.jpg',
    encoding: options.encoding || '7bit',
    mimetype: options.mimetype || 'image/jpeg',
    destination: options.destination || '/tmp',
    filename: options.filename || `${Date.now()}-test.jpg`,
    path: options.path || `/tmp/${Date.now()}-test.jpg`,
    size: options.size || 1024,
    stream: options.stream || null,
    buffer: options.buffer || Buffer.from('test'),
  };
}

/**
 * Data factory for creating test data
 */
export class TestDataFactory {
  private cache = new Map<string, any[]>();
  
  /**
   * Create a test user
   */
  createUser(overrides: any = {}): any {
    return {
      id: uuidv4(),
      email: `user-${Date.now()}@example.com`,
      name: 'Test User',
      passwordHash: 'hashed_password',
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
  
  /**
   * Create a test project
   */
  createProject(userId: string, overrides: any = {}): any {
    return {
      id: uuidv4(),
      userId,
      name: `Project ${Date.now()}`,
      description: 'Test project description',
      createdAt: new Date(),
      updatedAt: new Date(),
      imageCount: 0,
      ...overrides,
    };
  }
  
  /**
   * Create a test image
   */
  createImage(projectId: string, overrides: any = {}): any {
    return {
      id: uuidv4(),
      projectId,
      storageFilename: `${Date.now()}-test.jpg`,
      originalFilename: 'test.jpg',
      storagePath: `/uploads/${Date.now()}-test.jpg`,
      fileSize: 1024 * 100, // 100KB
      width: 1920,
      height: 1080,
      uploadDate: new Date(),
      segmentationStatus: 'pending',
      ...overrides,
    };
  }
  
  /**
   * Create a segmentation result
   */
  createSegmentationResult(imageId: string, overrides: any = {}): any {
    return {
      id: uuidv4(),
      imageId,
      polygons: [
        { points: [[0, 0], [100, 0], [100, 100], [0, 100]] },
      ],
      cellCount: 1,
      confidence: 0.95,
      processingTime: 1500,
      createdAt: new Date(),
      ...overrides,
    };
  }
  
  /**
   * Create multiple entities
   */
  createMany<T>(
    factory: (...args: any[]) => T,
    count: number,
    ...args: any[]
  ): T[] {
    const results: T[] = [];
    for (let i = 0; i < count; i++) {
      results.push(factory(...args));
    }
    return results;
  }
  
  /**
   * Get or create cached entities
   */
  getCached<T>(
    key: string,
    factory: () => T[],
    count: number = 10
  ): T[] {
    if (!this.cache.has(key)) {
      const items = [];
      for (let i = 0; i < count; i++) {
        items.push(factory());
      }
      this.cache.set(key, items);
    }
    return this.cache.get(key)!;
  }
  
  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

/**
 * Create mock Prisma transaction
 */
export function createMockPrismaTransaction(): any {
  return {
    $transaction: jest.fn(async (fn: any) => {
      const tx = {
        user: {
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          findUnique: jest.fn(),
          findMany: jest.fn(),
        },
        project: {
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          findUnique: jest.fn(),
          findMany: jest.fn(),
        },
        image: {
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          findUnique: jest.fn(),
          findMany: jest.fn(),
        },
      };
      return fn(tx);
    }),
  };
}

/**
 * Create mock Redis client
 */
export function createMockRedis(): any {
  const store = new Map<string, any>();
  
  return {
    get: jest.fn((key: string) => Promise.resolve(store.get(key) || null)),
    set: jest.fn((key: string, value: any) => {
      store.set(key, value);
      return Promise.resolve('OK');
    }),
    setex: jest.fn((key: string, ttl: number, value: any) => {
      store.set(key, value);
      return Promise.resolve('OK');
    }),
    del: jest.fn((key: string) => {
      const existed = store.has(key);
      store.delete(key);
      return Promise.resolve(existed ? 1 : 0);
    }),
    exists: jest.fn((key: string) => Promise.resolve(store.has(key) ? 1 : 0)),
    keys: jest.fn((pattern: string) => {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return Promise.resolve(
        Array.from(store.keys()).filter(k => regex.test(k))
      );
    }),
    flushdb: jest.fn(() => {
      store.clear();
      return Promise.resolve('OK');
    }),
    ttl: jest.fn(() => Promise.resolve(3600)),
    expire: jest.fn(() => Promise.resolve(1)),
    incr: jest.fn((key: string) => {
      const val = parseInt(store.get(key) || '0') + 1;
      store.set(key, val.toString());
      return Promise.resolve(val);
    }),
  };
}

// Singleton test data factory
let factoryInstance: TestDataFactory | null = null;

/**
 * Get or create test data factory
 */
export function getTestDataFactory(): TestDataFactory {
  if (!factoryInstance) {
    factoryInstance = new TestDataFactory();
  }
  return factoryInstance;
}

/**
 * Reset test data factory
 */
export function resetTestDataFactory(): void {
  if (factoryInstance) {
    factoryInstance.clearCache();
  }
  factoryInstance = null;
}

// Export default mock utilities
export default {
  createMockRequest,
  createMockResponse,
  createMockNext,
  createMockWebSocket,
  createMockFile,
  TestDataFactory,
  getTestDataFactory,
  resetTestDataFactory,
  createMockPrismaTransaction,
  createMockRedis,
};