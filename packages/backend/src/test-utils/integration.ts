/**
 * Integration Testing Utilities
 * 
 * Provides utilities for integration testing, including
 * API testing, database seeding, and end-to-end test helpers.
 */

import supertest from 'supertest';
import { Express } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import * as jwt from 'jsonwebtoken';

// Types
export interface IntegrationTestContext {
  app: Express;
  request: supertest.SuperTest<supertest.Test>;
  prisma: PrismaClient;
  authToken?: string;
  userId?: string;
  cleanup: () => Promise<void>;
}

export interface TestScenario {
  name: string;
  setup: () => Promise<void>;
  test: () => Promise<void>;
  teardown: () => Promise<void>;
}

export interface ApiTestOptions {
  auth?: boolean;
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * Setup integration test environment
 */
export async function setupIntegrationTest(
  app: Express,
  prisma?: PrismaClient
): Promise<IntegrationTestContext> {
  const request = supertest(app) as any;
  
  const context: IntegrationTestContext = {
    app,
    request,
    prisma: prisma || new PrismaClient(),
    cleanup: async () => {
      if (prisma) {
        await prisma.$disconnect();
      }
    },
  };
  
  return context;
}

/**
 * API test helper class
 */
export class ApiTestHelper {
  private request: supertest.SuperTest<supertest.Test>;
  private authToken?: string;
  private defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  constructor(app: Express) {
    this.request = supertest(app) as any;
  }
  
  /**
   * Set authentication token
   */
  setAuth(token: string): void {
    this.authToken = token;
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }
  
  /**
   * Clear authentication
   */
  clearAuth(): void {
    this.authToken = undefined;
    delete this.defaultHeaders['Authorization'];
  }
  
  /**
   * Make GET request
   */
  async get(path: string, options: ApiTestOptions = {}): Promise<supertest.Response> {
    const req = this.request.get(path);
    
    this.applyOptions(req, options);
    
    return req;
  }
  
  /**
   * Make POST request
   */
  async post(path: string, body?: any, options: ApiTestOptions = {}): Promise<supertest.Response> {
    const req = this.request.post(path);
    
    if (body) {
      req.send(body);
    }
    
    this.applyOptions(req, options);
    
    return req;
  }
  
  /**
   * Make PUT request
   */
  async put(path: string, body?: any, options: ApiTestOptions = {}): Promise<supertest.Response> {
    const req = this.request.put(path);
    
    if (body) {
      req.send(body);
    }
    
    this.applyOptions(req, options);
    
    return req;
  }
  
  /**
   * Make DELETE request
   */
  async delete(path: string, options: ApiTestOptions = {}): Promise<supertest.Response> {
    const req = this.request.delete(path);
    
    this.applyOptions(req, options);
    
    return req;
  }
  
  /**
   * Upload file
   */
  async upload(
    path: string,
    fieldName: string,
    filePath: string,
    options: ApiTestOptions = {}
  ): Promise<supertest.Response> {
    const req = this.request.post(path);
    
    req.attach(fieldName, filePath);
    
    // Don't set Content-Type for multipart
    const { 'Content-Type': _, ...headers } = this.defaultHeaders;
    
    this.applyOptions(req, { ...options, headers: { ...headers, ...options.headers } });
    
    return req;
  }
  
  /**
   * Apply options to request
   */
  private applyOptions(req: supertest.Test, options: ApiTestOptions): void {
    // Apply headers
    const headers = { ...this.defaultHeaders, ...options.headers };
    
    if (options.auth && this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    } else if (options.auth === false) {
      delete headers['Authorization'];
    }
    
    Object.entries(headers).forEach(([key, value]) => {
      req.set(key, value);
    });
    
    // Apply timeout
    if (options.timeout) {
      req.timeout(options.timeout);
    }
  }
  
  /**
   * Login helper
   */
  async login(email: string, password: string): Promise<string> {
    const response = await this.post('/api/auth/login', { email, password });
    
    if (response.status !== 200) {
      throw new Error(`Login failed: ${response.status}`);
    }
    
    const token = response.body.accessToken || response.body.token;
    this.setAuth(token);
    
    return token;
  }
  
  /**
   * Assert API response
   */
  assertSuccess(response: supertest.Response, expectedStatus: number = 200): void {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toBeDefined();
    
    if (response.body.success !== undefined) {
      expect(response.body.success).toBe(true);
    }
  }
  
  /**
   * Assert API error
   */
  assertError(response: supertest.Response, expectedStatus: number, errorMessage?: string): void {
    expect(response.status).toBe(expectedStatus);
    
    if (response.body.success !== undefined) {
      expect(response.body.success).toBe(false);
    }
    
    if (errorMessage) {
      expect(response.body.error).toContain(errorMessage);
    }
  }
}

/**
 * Database seeder for integration tests
 */
export class TestDataSeeder {
  constructor(private prisma: PrismaClient) {}
  
  /**
   * Seed complete test scenario
   */
  async seedScenario(scenario: 'basic' | 'complex' | 'performance'): Promise<{
    users: any[];
    projects: any[];
    images: any[];
  }> {
    switch (scenario) {
      case 'basic':
        return this.seedBasicScenario();
      case 'complex':
        return this.seedComplexScenario();
      case 'performance':
        return this.seedPerformanceScenario();
      default:
        throw new Error(`Unknown scenario: ${scenario}`);
    }
  }
  
  /**
   * Seed basic test scenario
   */
  private async seedBasicScenario() {
    const user = await this.createUser();
    const project = await this.createProject(user.id);
    const image = await this.createImage(project.id);
    
    return {
      users: [user],
      projects: [project],
      images: [image],
    };
  }
  
  /**
   * Seed complex test scenario
   */
  private async seedComplexScenario() {
    const users = await Promise.all([
      this.createUser({ email: '<EMAIL>' }),
      this.createUser({ email: '<EMAIL>' }),
      this.createUser({ email: '<EMAIL>' }),
    ]);
    
    const projects = await Promise.all([
      this.createProject(users[0].id, { name: 'Project A' }),
      this.createProject(users[0].id, { name: 'Project B' }),
      this.createProject(users[1].id, { name: 'Project C' }),
    ]);
    
    const images = [];
    for (const project of projects) {
      for (let i = 0; i < 5; i++) {
        images.push(await this.createImage(project.id));
      }
    }
    
    // Add project shares
    await this.prisma.projectShare.create({
      data: {
        id: uuidv4(),
        projectId: projects[0].id,
        sharedWithUserId: users[1].id,
        permission: 'view',
      },
    });
    
    return { users, projects, images };
  }
  
  /**
   * Seed performance test scenario
   */
  private async seedPerformanceScenario() {
    const users = [];
    const projects = [];
    const images = [];
    
    // Create 10 users
    for (let i = 0; i < 10; i++) {
      users.push(await this.createUser({ email: `perf${i}@test.com` }));
    }
    
    // Create 50 projects
    for (const user of users) {
      for (let i = 0; i < 5; i++) {
        projects.push(await this.createProject(user.id, { name: `Perf Project ${i}` }));
      }
    }
    
    // Create 500 images
    for (const project of projects) {
      for (let i = 0; i < 10; i++) {
        images.push(await this.createImage(project.id));
      }
    }
    
    return { users, projects, images };
  }
  
  /**
   * Create a test user
   */
  async createUser(data: any = {}): Promise<any> {
    return this.prisma.user.create({
      data: {
        id: uuidv4(),
        email: data.email || `test-${Date.now()}@example.com`,
        passwordHash: data.passwordHash || '$2a$10$test',
        name: data.name || 'Test User',
        emailVerified: data.emailVerified !== false,
      },
    });
  }
  
  /**
   * Create a test project
   */
  async createProject(userId: string, data: any = {}): Promise<any> {
    return this.prisma.project.create({
      data: {
        id: uuidv4(),
        userId,
        name: data.name || `Project ${Date.now()}`,
        description: data.description || 'Test project',
      },
    });
  }
  
  /**
   * Create a test image
   */
  async createImage(projectId: string, data: any = {}): Promise<any> {
    return this.prisma.image.create({
      data: {
        id: uuidv4(),
        projectId,
        storageFilename: data.storageFilename || `${Date.now()}.jpg`,
        originalFilename: data.originalFilename || 'test.jpg',
        fileSize: data.fileSize || 1024000,
        width: data.width || 1920,
        height: data.height || 1080,
        segmentationStatus: data.segmentationStatus || 'pending',
      },
    });
  }
  
  /**
   * Clean all test data
   */
  async cleanup(): Promise<void> {
    await this.prisma.segmentationResult.deleteMany();
    await this.prisma.image.deleteMany();
    await this.prisma.projectShare.deleteMany();
    await this.prisma.project.deleteMany();
    await this.prisma.session.deleteMany();
    await this.prisma.user.deleteMany();
  }
}

/**
 * Test scenario runner
 */
export class ScenarioRunner {
  private scenarios: TestScenario[] = [];
  
  /**
   * Add a test scenario
   */
  add(scenario: TestScenario): void {
    this.scenarios.push(scenario);
  }
  
  /**
   * Run all scenarios
   */
  async runAll(): Promise<void> {
    for (const scenario of this.scenarios) {
      await this.run(scenario);
    }
  }
  
  /**
   * Run a single scenario
   */
  async run(scenario: TestScenario): Promise<void> {
    console.log(`Running scenario: ${scenario.name}`);
    
    try {
      await scenario.setup();
      await scenario.test();
      console.log(`✓ ${scenario.name}`);
    } catch (error) {
      console.error(`✗ ${scenario.name}:`, error);
      throw error;
    } finally {
      await scenario.teardown();
    }
  }
}

/**
 * WebSocket test helper
 */
export class WebSocketTestHelper {
  private ws: any;
  private messages: any[] = [];
  private connected: boolean = false;
  
  /**
   * Connect to WebSocket
   */
  async connect(url: string, options: any = {}): Promise<void> {
    // This would use a WebSocket library in real implementation
    // For now, just mock it
    this.connected = true;
  }
  
  /**
   * Send message
   */
  send(data: any): void {
    if (!this.connected) {
      throw new Error('WebSocket not connected');
    }
    // Send implementation
  }
  
  /**
   * Wait for message
   */
  async waitForMessage(
    predicate?: (msg: any) => boolean,
    timeout: number = 5000
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Timeout waiting for message'));
      }, timeout);
      
      // Check existing messages
      const existing = this.messages.find(predicate || (() => true));
      if (existing) {
        clearTimeout(timer);
        resolve(existing);
      }
      
      // Wait for new message
      // Implementation would listen for WebSocket messages
    });
  }
  
  /**
   * Disconnect
   */
  disconnect(): void {
    this.connected = false;
    this.messages = [];
  }
}

/**
 * Generate test JWT token
 */
export function generateTestToken(
  userId: string,
  options: { secret?: string; expiresIn?: string } = {}
): string {
  return (jwt as any).sign(
    { userId, type: 'access' },
    options.secret || 'test-secret',
    { expiresIn: options.expiresIn || '1h' }
  );
}

/**
 * Wait for condition with timeout
 */
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error('Timeout waiting for condition');
}

/**
 * Retry helper for flaky tests
 */
export async function retry<T>(
  fn: () => Promise<T>,
  attempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let i = 0; i < attempts; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < attempts - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// Export default integration utilities
export default {
  setupIntegrationTest,
  ApiTestHelper,
  TestDataSeeder,
  ScenarioRunner,
  WebSocketTestHelper,
  generateTestToken,
  waitFor,
  retry,
};