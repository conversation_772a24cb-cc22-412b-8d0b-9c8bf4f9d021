/**
 * Database Test Utilities
 * 
 * Provides unified database testing utilities supporting multiple strategies:
 * - In-memory mocking
 * - SQLite for integration tests
 * - PostgreSQL with transactions for E2E tests
 */

import { PrismaClient } from '@prisma/client';
// Simple mock types without external dependency
type DeepMockProxy<T> = T;
import { v4 as uuidv4 } from 'uuid';

// Types
export interface TestDatabaseOptions {
  strategy?: 'mock' | 'sqlite' | 'postgres';
  connectionUrl?: string;
  useTransactions?: boolean;
  seed?: boolean;
}

export interface TestDatabaseContext {
  prisma: PrismaClient | DeepMockProxy<PrismaClient>;
  cleanup: () => Promise<void>;
  reset: () => Promise<void>;
  seed: () => Promise<void>;
}

// Database instances
let mockPrisma: DeepMockProxy<PrismaClient> | null = null;
let testPrisma: PrismaClient | null = null;
let transactionClient: any = null;

/**
 * Setup test database based on strategy
 */
export async function setupTestDatabase(
  options: TestDatabaseOptions = {}
): Promise<TestDatabaseContext> {
  const { 
    strategy = 'mock',
    connectionUrl,
    useTransactions = false,
    seed = false,
  } = options;

  switch (strategy) {
    case 'mock':
      return setupMockDatabase();
    
    case 'sqlite':
      return setupSQLiteDatabase(connectionUrl);
    
    case 'postgres':
      return setupPostgresDatabase(connectionUrl, useTransactions);
    
    default:
      throw new Error(`Unknown database strategy: ${strategy}`);
  }
}

/**
 * Setup mock database with jest-mock-extended
 */
function setupMockDatabase(): TestDatabaseContext {
  mockPrisma = {} as any;
  
  // Setup default mock behaviors
  setupDefaultMocks(mockPrisma);
  
  return {
    prisma: mockPrisma,
    cleanup: async () => {},
    reset: async () => {},
    seed: async () => seedMockData(mockPrisma),
  };
}

/**
 * Setup SQLite database for integration tests
 */
async function setupSQLiteDatabase(connectionUrl?: string): Promise<TestDatabaseContext> {
  const url = connectionUrl || 'file:./test.db';
  
  testPrisma = new PrismaClient({
    datasources: {
      db: { url },
    },
    log: process.env.DEBUG ? ['query', 'error', 'warn'] : [],
  });
  
  // Run migrations
  await testPrisma.$executeRawUnsafe('PRAGMA foreign_keys = ON');
  
  return {
    prisma: testPrisma,
    cleanup: async () => {
      await cleanupDatabase(testPrisma);
      await testPrisma.$disconnect();
    },
    reset: async () => {
      await cleanupDatabase(testPrisma);
    },
    seed: async () => seedTestData(testPrisma),
  };
}

/**
 * Setup PostgreSQL database with transaction support
 */
async function setupPostgresDatabase(
  connectionUrl?: string,
  useTransactions: boolean = false
): Promise<TestDatabaseContext> {
  const url = connectionUrl || process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test';
  
  testPrisma = new PrismaClient({
    datasources: {
      db: { url },
    },
    log: process.env.DEBUG ? ['query', 'error', 'warn'] : [],
  });
  
  if (useTransactions) {
    // Start a transaction for test isolation
    transactionClient = await testPrisma.$transaction(async (tx) => tx);
    
    return {
      prisma: transactionClient,
      cleanup: async () => {
        // Rollback transaction
        await transactionClient.$executeRaw`ROLLBACK`;
        await testPrisma.$disconnect();
      },
      reset: async () => {
        await transactionClient.$executeRaw`ROLLBACK`;
        transactionClient = await testPrisma.$transaction(async (tx) => tx);
      },
      seed: async () => seedTestData(transactionClient),
    };
  }
  
  return {
    prisma: testPrisma,
    cleanup: async () => {
      await cleanupDatabase(testPrisma);
      await testPrisma.$disconnect();
    },
    reset: async () => {
      await cleanupDatabase(testPrisma);
    },
    seed: async () => seedTestData(testPrisma),
  };
}

/**
 * Setup default mocks for mock database
 */
function setupDefaultMocks(mockPrisma: DeepMockProxy<PrismaClient>): void {
  // User mocks
  mockPrisma.user.findUnique.mockResolvedValue(null);
  mockPrisma.user.findFirst.mockResolvedValue(null);
  mockPrisma.user.findMany.mockResolvedValue([]);
  mockPrisma.user.create.mockImplementation((args: any) => 
    Promise.resolve({ id: uuidv4(), ...args.data })
  );
  
  // Project mocks
  mockPrisma.project.findUnique.mockResolvedValue(null);
  mockPrisma.project.findMany.mockResolvedValue([]);
  mockPrisma.project.create.mockImplementation((args: any) =>
    Promise.resolve({ id: uuidv4(), ...args.data })
  );
  
  // Image mocks
  mockPrisma.image.findUnique.mockResolvedValue(null);
  mockPrisma.image.findMany.mockResolvedValue([]);
  mockPrisma.image.create.mockImplementation((args: any) =>
    Promise.resolve({ id: uuidv4(), ...args.data })
  );
}

/**
 * Cleanup database by deleting all data
 */
async function cleanupDatabase(prisma: PrismaClient): Promise<void> {
  // Delete in reverse order of dependencies
  const tables = [
    'segmentationResult',
    'image',
    'projectShare',
    'project',
    'session',
    'user',
  ];
  
  for (const table of tables) {
    try {
      await (prisma as any)[table].deleteMany();
    } catch (error) {
      console.warn(`Failed to clean table ${table}:`, error);
    }
  }
}

/**
 * Cleanup test database
 */
export async function cleanupTestDatabase(): Promise<void> {
  if (mockPrisma) {
    // Mock reset;
    mockPrisma = null;
  }
  
  if (transactionClient) {
    await transactionClient.$executeRaw`ROLLBACK`;
    transactionClient = null;
  }
  
  if (testPrisma) {
    await testPrisma.$disconnect();
    testPrisma = null;
  }
}

/**
 * Seed mock data for testing
 */
async function seedMockData(mockPrisma: DeepMockProxy<PrismaClient>): Promise<void> {
  const users = [
    { id: 'user1', email: '<EMAIL>', name: 'User One' },
    { id: 'user2', email: '<EMAIL>', name: 'User Two' },
  ];
  
  const projects = [
    { id: 'project1', userId: 'user1', name: 'Project One' },
    { id: 'project2', userId: 'user1', name: 'Project Two' },
  ];
  
  mockPrisma.user.findMany.mockResolvedValue(users as any);
  mockPrisma.project.findMany.mockResolvedValue(projects as any);
}

/**
 * Seed test data for real databases
 */
async function seedTestData(prisma: PrismaClient | any): Promise<void> {
  // Create test users
  const user1 = await prisma.user.create({
    data: {
      id: uuidv4(),
      email: '<EMAIL>',
      passwordHash: 'hashed_password',
      name: 'Test User 1',
      emailVerified: true,
    },
  });
  
  const user2 = await prisma.user.create({
    data: {
      id: uuidv4(),
      email: '<EMAIL>',
      passwordHash: 'hashed_password',
      name: 'Test User 2',
      emailVerified: true,
    },
  });
  
  // Create test projects
  await prisma.project.create({
    data: {
      id: uuidv4(),
      userId: user1.id,
      name: 'Test Project 1',
      description: 'Test project description',
    },
  });
  
  await prisma.project.create({
    data: {
      id: uuidv4(),
      userId: user2.id,
      name: 'Test Project 2',
      description: 'Another test project',
    },
  });
}

/**
 * Create a test user in the database
 */
export async function createTestUser(
  prisma: PrismaClient | any,
  userData: any = {}
): Promise<any> {
  return prisma.user.create({
    data: {
      id: uuidv4(),
      email: userData.email || `test-${Date.now()}@example.com`,
      passwordHash: userData.passwordHash || 'hashed_password',
      name: userData.name || 'Test User',
      emailVerified: userData.emailVerified !== false,
      ...userData,
    },
  });
}

/**
 * Create a test project in the database
 */
export async function createTestProject(
  prisma: PrismaClient | any,
  userId: string,
  projectData: any = {}
): Promise<any> {
  return prisma.project.create({
    data: {
      id: uuidv4(),
      userId,
      name: projectData.name || 'Test Project',
      description: projectData.description || 'Test project description',
      ...projectData,
    },
  });
}

/**
 * Create a test image in the database
 */
export async function createTestImage(
  prisma: PrismaClient | any,
  projectId: string,
  imageData: any = {}
): Promise<any> {
  return prisma.image.create({
    data: {
      id: uuidv4(),
      projectId,
      storageFilename: imageData.storageFilename || 'test-image.jpg',
      originalFilename: imageData.originalFilename || 'test.jpg',
      fileSize: imageData.fileSize || 1024,
      width: imageData.width || 800,
      height: imageData.height || 600,
      segmentationStatus: imageData.segmentationStatus || 'pending',
      ...imageData,
    },
  });
}

/**
 * Get or create mock Prisma client
 */
export function getMockPrisma(): DeepMockProxy<PrismaClient> {
  if (!mockPrisma) {
    mockPrisma = {} as any;
    setupDefaultMocks(mockPrisma);
  }
  return mockPrisma;
}

/**
 * Reset mock Prisma client
 */
export function resetMockPrisma(): void {
  if (mockPrisma) {
    // Mock reset;
    setupDefaultMocks(mockPrisma);
  }
}

// Export default database utilities
export default {
  setupTestDatabase,
  cleanupTestDatabase,
  createTestUser,
  createTestProject,
  createTestImage,
  getMockPrisma,
  resetMockPrisma,
};