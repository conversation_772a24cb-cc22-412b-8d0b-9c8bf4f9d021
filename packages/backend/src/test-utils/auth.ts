/**
 * Authentication Test Utilities
 * 
 * Consolidates authentication mocking and testing utilities.
 * Combines the best of MockAuth class and database-backed auth helpers.
 */

import * as jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

// Types
export interface TestUser {
  id: string;
  email: string;
  password: string;
  hashedPassword?: string;
  name?: string;
  role?: string;
  emailVerified?: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface MockAuthOptions {
  usePrisma?: boolean;
  defaultRole?: string;
  jwtSecret?: string;
  tokenExpiry?: string;
}

// Default configuration
const DEFAULT_JWT_SECRET = 'test-secret-key';
const DEFAULT_ACCESS_TOKEN_EXPIRY = '15m';
const DEFAULT_REFRESH_TOKEN_EXPIRY = '7d';

/**
 * Mock Authentication Service
 * Provides both in-memory and database-backed authentication for tests
 */
export class MockAuth {
  private users: Map<string, TestUser> = new Map();
  private sessions: Map<string, any> = new Map();
  private options: MockAuthOptions;

  constructor(options: MockAuthOptions = {}) {
    this.options = {
      jwtSecret: DEFAULT_JWT_SECRET,
      tokenExpiry: DEFAULT_ACCESS_TOKEN_EXPIRY,
      defaultRole: 'user',
      ...options,
    };
  }

  /**
   * Create a test user
   */
  async createUser(userData: Partial<TestUser> = {}): Promise<TestUser> {
    const user: TestUser = {
      id: userData.id || uuidv4(),
      email: userData.email || `test-${Date.now()}@example.com`,
      password: userData.password || 'password123',
      name: userData.name || 'Test User',
      role: userData.role || this.options.defaultRole,
      emailVerified: userData.emailVerified !== false,
    };

    // Hash password
    user.hashedPassword = await bcrypt.hash(user.password, 10);

    // Store user
    this.users.set(user.id, user);
    this.users.set(user.email, user);

    return user;
  }

  /**
   * Generate JWT tokens for a user
   */
  generateTokens(userId: string): AuthTokens {
    const accessToken = (jwt as any).sign(
      { userId, type: 'access' },
      this.options.jwtSecret!,
      { expiresIn: this.options.tokenExpiry }
    );

    const refreshToken = (jwt as any).sign(
      { userId, type: 'refresh' },
      this.options.jwtSecret!,
      { expiresIn: DEFAULT_REFRESH_TOKEN_EXPIRY }
    );

    return { accessToken, refreshToken };
  }

  /**
   * Verify a token
   */
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.options.jwtSecret!);
    } catch (error) {
      return null;
    }
  }

  /**
   * Mock authentication middleware
   */
  mockAuthMiddleware() {
    return (req: any, res: Response, next: NextFunction) => {
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        return res.status(401).json({ error: 'No token provided' });
      }

      const token = authHeader.replace('Bearer ', '');
      const decoded = this.verifyToken(token);

      if (!decoded) {
        return res.status(401).json({ error: 'Invalid token' });
      }

      req.userId = decoded.userId;
      req.user = this.users.get(decoded.userId);
      next();
    };
  }

  /**
   * Login a user
   */
  async login(email: string, password: string): Promise<AuthTokens | null> {
    const user = this.users.get(email);
    
    if (!user) {
      return null;
    }

    const isValid = await bcrypt.compare(password, user.hashedPassword!);
    
    if (!isValid) {
      return null;
    }

    return this.generateTokens(user.id);
  }

  /**
   * Clear all test data
   */
  reset(): void {
    this.users.clear();
    this.sessions.clear();
  }

  /**
   * Get a user by ID or email
   */
  getUser(identifier: string): TestUser | undefined {
    return this.users.get(identifier);
  }

  /**
   * Get all users
   */
  getAllUsers(): TestUser[] {
    const uniqueUsers = new Map<string, TestUser>();
    this.users.forEach((user) => {
      uniqueUsers.set(user.id, user);
    });
    return Array.from(uniqueUsers.values());
  }
}

// Singleton instance for tests
let mockAuthInstance: MockAuth | null = null;

/**
 * Get or create the mock auth instance
 */
export function getMockAuth(options?: MockAuthOptions): MockAuth {
  if (!mockAuthInstance) {
    mockAuthInstance = new MockAuth(options);
  }
  return mockAuthInstance;
}

/**
 * Reset the mock auth instance
 */
export function resetMockAuth(): void {
  if (mockAuthInstance) {
    mockAuthInstance.reset();
  }
  mockAuthInstance = null;
}

/**
 * Create an authenticated request
 */
export function createAuthenticatedRequest(
  user: TestUser,
  options: any = {}
): any {
  const mockAuth = getMockAuth();
  const tokens = mockAuth.generateTokens(user.id);
  
  return {
    headers: {
      authorization: `Bearer ${tokens.accessToken}`,
      ...options.headers,
    },
    userId: user.id,
    user,
    ...options,
  };
}

/**
 * Create a test JWT token
 */
export function createAuthToken(
  userId: string,
  options: { secret?: string; expiresIn?: string } = {}
): string {
  return (jwt as any).sign(
    { userId, type: 'access' },
    options.secret || DEFAULT_JWT_SECRET,
    { expiresIn: options.expiresIn || DEFAULT_ACCESS_TOKEN_EXPIRY }
  );
}

/**
 * Mock authentication middleware for tests
 */
export function mockAuthMiddleware(userId?: string) {
  return (req: any, res: Response, next: NextFunction) => {
    req.userId = userId || 'test-user-id';
    req.user = {
      id: req.userId,
      email: '<EMAIL>',
      name: 'Test User',
    };
    next();
  };
}

/**
 * Create test session data
 */
export function createTestSession(userId: string, sessionData: any = {}) {
  return {
    id: uuidv4(),
    userId,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    ...sessionData,
  };
}

// Export default auth utilities
export default {
  MockAuth,
  getMockAuth,
  resetMockAuth,
  createAuthenticatedRequest,
  createAuthToken,
  mockAuthMiddleware,
  createTestSession,
};