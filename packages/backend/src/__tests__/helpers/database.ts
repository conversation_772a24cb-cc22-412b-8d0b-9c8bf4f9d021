import { PrismaClient } from '@prisma/client';
// Simple mock types without external dependency
type DeepMockProxy<T> = T;

// Create a mock Prisma client for testing
export const prismaMock = {} as DeepMockProxy<PrismaClient>;

// Reset the mock before each test
beforeEach(() => {
  // Mock reset
});

// Helper to create a test database instance
export async function createTestDatabase(): Promise<PrismaClient> {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/spheroseg_test'
      }
    }
  });

  // Connect to the database
  await prisma.$connect();

  return prisma;
}

// Helper to clean up test database
export async function cleanupTestDatabase(prisma: PrismaClient): Promise<void> {
  // Delete all data in reverse order of dependencies
  await prisma.$transaction([
    prisma.segmentation.deleteMany(),
    prisma.image.deleteMany(),
    prisma.projectShare.deleteMany(),
    prisma.project.deleteMany(),
    prisma.session.deleteMany(),
    prisma.emailVerification.deleteMany(),
    prisma.passwordReset.deleteMany(),
    prisma.userProfile.deleteMany(),
    prisma.user.deleteMany(),
  ]);

  await prisma.$disconnect();
}

// Helper to seed test data
export async function seedTestData(prisma: PrismaClient) {
  // Create test user
  const user = await prisma.user.create({
    data: {
      id: 'test-user-id',
      email: '<EMAIL>',
      username: 'testuser',
      passwordHash: '$2b$10$K7L1OJ0TfPIZ3jU6w3Z3XeVQGxScMz.BZGrZgLgLqn/vO0xJZ.Jxq', // password: Test123!
      isActive: true,
      role: 'USER',
      profile: {
        create: {
          id: 'test-profile-id',
          title: 'Test User',
          fullName: 'Test User',
          organization: 'Test Org',
          bio: 'Test bio',
          location: 'Test Location',
          avatarUrl: null,
          preferredLanguage: 'en',
          themePreference: 'light',
          notificationPreferences: {}
        }
      }
    }
  });

  // Create test project
  const project = await prisma.project.create({
    data: {
      id: 'test-project-id',
      userId: user.id,
      title: 'Test Project',
      description: 'Test project description',
      tags: ['test', 'sample'],
      public: false
    }
  });

  // Create test image
  const image = await prisma.image.create({
    data: {
      id: 'test-image-id',
      projectId: project.id,
      userId: user.id,
      name: 'test-image.jpg',
      storageFilename: 'test-image-storage.jpg',
      originalFilename: 'test-image.jpg',
      storagePath: '/uploads/test-project-id/test-image-storage.jpg',
      thumbnailPath: '/uploads/test-project-id/thumb-test-image-storage.jpg',
      fileSize: 1024000,
      mimeType: 'image/jpeg',
      width: 1920,
      height: 1080,
      metadata: {},
      segmentationStatus: 'pending',
      processingTime: 0
    }
  });

  return { user, project, image };
}

// Mock authentication helper
export function mockAuthUser(userId: string = 'test-user-id') {
  return {
    userId,
    email: '<EMAIL>',
    role: 'USER'
  };
}

// Helper to create authenticated request
export function createAuthenticatedRequest(userId?: string) {
  const user = mockAuthUser(userId);
  return {
    user,
    headers: {
      authorization: 'Bearer mock-jwt-token'
    }
  };
}