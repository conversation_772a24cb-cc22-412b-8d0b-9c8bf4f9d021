/**
 * Test Database Helper
 * 
 * Provides helper functions to replace deprecated pool.query() calls in tests
 * with proper Prisma API methods using SQLite test database.
 */

import { getTestPrismaClient } from '../../config/database.test';
import { PrismaClient } from '../../../generated/prisma-test';

/**
 * Execute a raw SQL query and return results in pool.query format
 * Uses Prisma's safe parameterized queries to prevent SQL injection
 * 
 * WARNING: This function is deprecated for new code. Use Prisma's type-safe
 * client methods instead (e.g., prisma.user.findMany()).
 */
export async function query<T = any>(
  sql: string, 
  params: any[] = []
): Promise<{ rows: T[] }> {
  const prisma = getTestPrismaClient();
  
  try {
    let result: any;
    if (params.length === 0) {
      // No parameters - use template literal for safety
      result = await prisma.$queryRawUnsafe<T[]>(sql);
    } else {
      // With parameters - SQLite uses different parameter syntax
      const sqliteQuery = sql.replace(/\$(\d+)/g, '?');
      result = await prisma.$queryRawUnsafe<T[]>(sqliteQuery, ...params);
    }
    return { rows: Array.isArray(result) ? result : [result] };
  } catch (error) {
    console.error('Test database query error:', error);
    console.error('SQL:', sql);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Execute raw SQL without expecting results (for INSERT, UPDATE, DELETE)
 * 
 * WARNING: This function is deprecated for new code. Use Prisma's type-safe
 * client methods instead (e.g., prisma.user.create()).
 */
export async function execute(sql: string, params: any[] = []): Promise<void> {
  const prisma = getTestPrismaClient();
  
  try {
    if (params.length === 0) {
      await prisma.$executeRawUnsafe(sql);
    } else {
      // Convert PostgreSQL parameter markers to SQLite
      const sqliteQuery = sql.replace(/\$(\d+)/g, '?');
      await prisma.$executeRawUnsafe(sqliteQuery, ...params);
    }
  } catch (error) {
    console.error('Test database execute error:', error);
    console.error('SQL:', sql);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Helper for cleanup operations in tests
 */
export const testDb = {
  query,
  execute,
  
  // Common cleanup operations
  async cleanupUsers(emailPattern?: string) {
    const prisma = getTestPrismaClient();
    if (emailPattern) {
      await prisma.user.deleteMany({
        where: {
          email: {
            contains: emailPattern
          }
        }
      });
    } else {
      await prisma.user.deleteMany();
    }
  },

  async cleanupProjects(userId?: string) {
    const prisma = getTestPrismaClient();
    if (userId) {
      await prisma.project.deleteMany({
        where: {
          userId: userId
        }
      });
    } else {
      await prisma.project.deleteMany();
    }
  },

  async cleanupImages(projectId?: string) {
    const prisma = getTestPrismaClient();
    if (projectId) {
      await prisma.image.deleteMany({
        where: {
          projectId: projectId
        }
      });
    } else {
      await prisma.image.deleteMany();
    }
  },

  // Test data creation helpers
  async createTestUser(email: string, passwordHash: string, userData: any = {}) {
    const prisma = getTestPrismaClient();
    const result = await prisma.user.create({
      data: {
        email,
        username: userData.username || email.split('@')[0],
        passwordHash,
        role: userData.role || 'USER',
        isActive: userData.isActive ?? true,
        ...userData
      }
    });
    return result;
  },

  async createTestProject(userId: string, projectData: any = {}) {
    const prisma = getTestPrismaClient();
    const result = await prisma.project.create({
      data: {
        title: projectData.title || projectData.name || 'Test Project',
        description: projectData.description,
        userId,
        public: projectData.public ?? false,
        tags: projectData.tags || '',
        ...projectData
      }
    });
    return result;
  }
};

export default testDb;