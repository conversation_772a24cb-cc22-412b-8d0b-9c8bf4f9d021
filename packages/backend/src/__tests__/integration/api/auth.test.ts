import request from 'supertest';
import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { createApp } from '../../../app/createApp';
import { PrismaService } from '../../../services/PrismaService';
import bcrypt from 'bcrypt';
import { Application } from 'express';

describe('Auth API Integration Tests', () => {
  let app: Application;
  let prisma: PrismaService;

  beforeAll(async () => {
    // Initialize app and services
    app = await createApp();
    prisma = PrismaService.getInstance();
    await prisma.connect();
  });

  afterAll(async () => {
    await prisma.disconnect();
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.getClient().$transaction([
      prisma.getClient().session.deleteMany(),
      prisma.getClient().user.deleteMany()
    ]);
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          name: 'New User'
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body.user.email).toBe('<EMAIL>');
    });

    it('should reject duplicate email', async () => {
      // Create first user
      await prisma.getClient().user.create({
        data: {
          email: '<EMAIL>',
          username: 'existing',
          passwordHash: await bcrypt.hash('Test123!', 10),
          role: 'USER'
        }
      });

      // Try to register with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          name: 'Another User'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('already exists');
    });

    it('should validate password strength', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123',
          name: 'Weak Password'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('password');
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          password: 'SecurePass123!',
          name: 'Invalid Email'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('email');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user
      await prisma.getClient().user.create({
        data: {
          id: 'test-user-id',
          email: '<EMAIL>',
          username: 'testuser',
          passwordHash: await bcrypt.hash('Test123!', 10),
          isActive: true,
          role: 'USER'
        }
      });
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body.user.email).toBe('<EMAIL>');
    });

    it('should reject invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid');
    });

    it('should reject non-existent user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid');
    });

    it('should reject inactive user', async () => {
      await prisma.getClient().user.update({
        where: { email: '<EMAIL>' },
        data: { isActive: false }
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!'
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('not active');
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh tokens with valid refresh token', async () => {
      // First register to get tokens
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'Test123!',
          name: 'Refresh User'
        });

      const { refreshToken } = registerResponse.body;

      // Use refresh token
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body.refreshToken).not.toBe(refreshToken);
    });

    it('should reject invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' });

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout authenticated user', async () => {
      // Login first
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!'
        });

      const { accessToken } = loginResponse.body;

      // Logout
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('success');
    });

    it('should reject unauthenticated logout', async () => {
      const response = await request(app)
        .post('/api/auth/logout');

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    beforeEach(async () => {
      await prisma.getClient().user.create({
        data: {
          id: 'forgot-user-id',
          email: '<EMAIL>',
          username: 'forgotuser',
          passwordHash: await bcrypt.hash('OldPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });
    });

    it('should request password reset', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('sent');

      // Verify reset token created
      const resetToken = await prisma.getClient().passwordReset.findFirst({
        where: { userId: 'forgot-user-id' }
      });

      expect(resetToken).toBeTruthy();
    });

    it('should not reveal non-existent email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' });

      // Should still return success to not reveal email existence
      expect(response.status).toBe(200);
      expect(response.body.message).toContain('sent');
    });
  });

  describe('POST /api/auth/reset-password', () => {
    let resetToken: string;

    beforeEach(async () => {
      const user = await prisma.getClient().user.create({
        data: {
          id: 'reset-user-id',
          email: '<EMAIL>',
          username: 'resetuser',
          passwordHash: await bcrypt.hash('OldPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });

      // Create reset token
      resetToken = 'test-reset-token-123';
      await prisma.getClient().passwordReset.create({
        data: {
          userId: user.id,
          tokenHash: await bcrypt.hash(resetToken, 10),
          expiresAt: new Date(Date.now() + 3600000), // 1 hour
          used: false
        }
      });
    });

    it('should reset password with valid token', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('success');

      // Verify can login with new password
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'NewSecurePass123!'
        });

      expect(loginResponse.status).toBe(200);
    });

    it('should reject expired token', async () => {
      // Update token to be expired
      await prisma.getClient().passwordReset.updateMany({
        where: { userId: 'reset-user-id' },
        data: { expiresAt: new Date(Date.now() - 3600000) } // Expired
      });

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('expired');
    });

    it('should reject used token', async () => {
      // Mark token as used
      await prisma.getClient().passwordReset.updateMany({
        where: { userId: 'reset-user-id' },
        data: { used: true }
      });

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Invalid');
    });
  });

  describe('POST /api/auth/change-password', () => {
    let accessToken: string;

    beforeEach(async () => {
      // Create and login user
      await prisma.getClient().user.create({
        data: {
          id: 'change-user-id',
          email: '<EMAIL>',
          username: 'changeuser',
          passwordHash: await bcrypt.hash('CurrentPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'CurrentPass123!'
        });

      accessToken = loginResponse.body.accessToken;
    });

    it('should change password with correct current password', async () => {
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'CurrentPass123!',
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('success');

      // Verify can login with new password
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'NewSecurePass123!'
        });

      expect(loginResponse.status).toBe(200);
    });

    it('should reject incorrect current password', async () => {
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'WrongPass123!',
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('incorrect');
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/auth/change-password')
        .send({
          currentPassword: 'CurrentPass123!',
          newPassword: 'NewSecurePass123!'
        });

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/auth/me', () => {
    let accessToken: string;

    beforeEach(async () => {
      // Create and login user with profile
      const user = await prisma.getClient().user.create({
        data: {
          id: 'me-user-id',
          email: '<EMAIL>',
          username: 'meuser',
          passwordHash: await bcrypt.hash('Test123!', 10),
          isActive: true,
          role: 'USER',
          profile: {
            create: {
              fullName: 'Test User',
              organization: 'Test Org',
              bio: 'Test bio'
            }
          }
        },
        include: { profile: true }
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!'
        });

      accessToken = loginResponse.body.accessToken;
    });

    it('should return current user info', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.profile).toBeTruthy();
      expect(response.body.profile.fullName).toBe('Test User');
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/auth/me');

      expect(response.status).toBe(401);
    });
  });
});