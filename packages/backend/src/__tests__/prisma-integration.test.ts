import { PrismaClient } from '../../generated/prisma';
import { prismaService } from '../services/PrismaService';

describe('Prisma Integration', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  test('should connect to database', async () => {
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(result).toBeDefined();
    expect(result[0].test).toBe(1);
  });

  test('should perform CRUD operations', async () => {
    // Create
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        passwordHash: 'hash'
      }
    });
    expect(user.id).toBeDefined();

    // Read
    const found = await prisma.user.findUnique({
      where: { id: user.id }
    });
    expect(found?.email).toBe('<EMAIL>');

    // Update
    const updated = await prisma.user.update({
      where: { id: user.id },
      data: { username: 'updated' }
    });
    expect(updated.username).toBe('updated');

    // Delete
    await prisma.user.delete({
      where: { id: user.id }
    });
  });

  test('should handle transactions', async () => {
    const result = await prisma.$transaction(async (tx) => {
      const user = await tx.user.create({
        data: {
          email: '<EMAIL>',
          username: 'txuser',
          passwordHash: 'hash'
        }
      });
      return user;
    });
    expect(result.id).toBeDefined();

    // Cleanup
    await prisma.user.delete({ where: { id: result.id } });
  });

  test('should verify query engine exists', async () => {
    // This test verifies that the query engine is properly loaded
    const testQuery = async () => {
      const result = await prisma.$queryRaw`SELECT version()`;
      return result;
    };
    
    await expect(testQuery()).resolves.toBeDefined();
  });

  test('should handle connection pool', async () => {
    // Test multiple concurrent connections
    const promises = Array(10).fill(null).map(async () => {
      const result = await prisma.$queryRaw`SELECT NOW()`;
      return result;
    });
    
    const results = await Promise.all(promises);
    expect(results).toHaveLength(10);
    results.forEach(result => {
      expect(result).toBeDefined();
    });
  });

  test('should handle error cases gracefully', async () => {
    // Test invalid query
    await expect(
      prisma.user.findUnique({ where: { id: 'invalid-uuid' } })
    ).rejects.toThrow();
    
    // Test duplicate unique constraint
    const email = '<EMAIL>';
    const user1 = await prisma.user.create({
      data: {
        email,
        username: 'user1',
        passwordHash: 'hash'
      }
    });
    
    await expect(
      prisma.user.create({
        data: {
          email, // Same email - should fail
          username: 'user2',
          passwordHash: 'hash'
        }
      })
    ).rejects.toThrow();
    
    // Cleanup
    await prisma.user.delete({ where: { id: user1.id } });
  });
});