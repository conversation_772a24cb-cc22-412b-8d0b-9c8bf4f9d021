import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { AuthService } from '../../../services/authService';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { createTestDatabase, cleanupTestDatabase } from '../../helpers/database';

describe('AuthService', () => {
  let authService: AuthService;
  let prisma: PrismaClient;

  beforeEach(async () => {
    prisma = await createTestDatabase();
    authService = new AuthService();
    
    // Mock bcrypt for faster tests
    jest.spyOn(bcrypt, 'hash').mockResolvedValue('hashed-password' as never);
    jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
  });

  afterEach(async () => {
    await cleanupTestDatabase(prisma);
    jest.restoreAllMocks();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        name: 'New User'
      };

      const result = await authService.register(userData);

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.user.email).toBe(userData.email);
    });

    it('should throw error if email already exists', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        name: 'User'
      };

      // Register first user
      await authService.register(userData);

      // Try to register with same email
      await expect(authService.register(userData)).rejects.toThrow('Email already exists');
    });

    it('should validate password strength', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Weak password
        name: 'User'
      };

      await expect(authService.register(userData)).rejects.toThrow('Password does not meet requirements');
    });
  });

  describe('login', () => {
    beforeEach(async () => {
      // Create a test user
      await prisma.user.create({
        data: {
          id: 'test-user-id',
          email: '<EMAIL>',
          username: 'testuser',
          passwordHash: await bcrypt.hash('Test123!', 10),
          isActive: true,
          role: 'USER'
        }
      });
    });

    it('should login user with correct credentials', async () => {
      const result = await authService.login('<EMAIL>', 'Test123!');

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.user.email).toBe('<EMAIL>');
    });

    it('should throw error with incorrect password', async () => {
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      await expect(
        authService.login('<EMAIL>', 'WrongPassword')
      ).rejects.toThrow('Invalid email or password');
    });

    it('should throw error for non-existent user', async () => {
      await expect(
        authService.login('<EMAIL>', 'Test123!')
      ).rejects.toThrow('Invalid email or password');
    });

    it('should throw error for inactive user', async () => {
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { isActive: false }
      });

      await expect(
        authService.login('<EMAIL>', 'Test123!')
      ).rejects.toThrow('Account is not active');
    });
  });

  describe('refreshAuthTokens', () => {
    it('should refresh tokens with valid refresh token', async () => {
      // Create user and get initial tokens
      const userData = {
        email: '<EMAIL>',
        password: 'Test123!',
        name: 'Refresh User'
      };
      
      const { refreshToken } = await authService.register(userData);
      
      // Refresh tokens
      const result = await authService.refreshAuthTokens(refreshToken);

      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.refreshToken).not.toBe(refreshToken); // Should be new token
    });

    it('should throw error with invalid refresh token', async () => {
      await expect(
        authService.refreshAuthTokens('invalid-token')
      ).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('requestPasswordReset', () => {
    beforeEach(async () => {
      await prisma.user.create({
        data: {
          id: 'reset-user-id',
          email: '<EMAIL>',
          username: 'resetuser',
          passwordHash: await bcrypt.hash('OldPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });
    });

    it('should create password reset token', async () => {
      await authService.requestPasswordReset('<EMAIL>');

      const resetToken = await prisma.passwordReset.findFirst({
        where: { userId: 'reset-user-id' }
      });

      expect(resetToken).toBeTruthy();
      expect(resetToken?.used).toBe(false);
    });

    it('should not throw error for non-existent email', async () => {
      // Should not reveal if email exists
      await expect(
        authService.requestPasswordReset('<EMAIL>')
      ).resolves.not.toThrow();
    });
  });

  describe('resetPassword', () => {
    let resetToken: string;

    beforeEach(async () => {
      const user = await prisma.user.create({
        data: {
          id: 'reset-user-id',
          email: '<EMAIL>',
          username: 'resetuser',
          passwordHash: await bcrypt.hash('OldPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });

      // Create reset token
      resetToken = 'test-reset-token';
      await prisma.passwordReset.create({
        data: {
          id: 'reset-id',
          userId: user.id,
          tokenHash: await bcrypt.hash(resetToken, 10),
          expiresAt: new Date(Date.now() + 3600000), // 1 hour
          used: false
        }
      });
    });

    it('should reset password with valid token', async () => {
      await authService.resetPassword(resetToken, 'NewPass123!');

      const user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });

      const isValid = await bcrypt.compare('NewPass123!', user!.passwordHash);
      expect(isValid).toBe(true);
    });

    it('should mark token as used after reset', async () => {
      await authService.resetPassword(resetToken, 'NewPass123!');

      const token = await prisma.passwordReset.findFirst({
        where: { id: 'reset-id' }
      });

      expect(token?.used).toBe(true);
    });

    it('should throw error with expired token', async () => {
      await prisma.passwordReset.update({
        where: { id: 'reset-id' },
        data: { expiresAt: new Date(Date.now() - 3600000) } // Expired
      });

      await expect(
        authService.resetPassword(resetToken, 'NewPass123!')
      ).rejects.toThrow('Invalid or expired reset token');
    });

    it('should throw error with used token', async () => {
      await prisma.passwordReset.update({
        where: { id: 'reset-id' },
        data: { used: true }
      });

      await expect(
        authService.resetPassword(resetToken, 'NewPass123!')
      ).rejects.toThrow('Invalid or expired reset token');
    });
  });

  describe('changePassword', () => {
    beforeEach(async () => {
      await prisma.user.create({
        data: {
          id: 'change-user-id',
          email: '<EMAIL>',
          username: 'changeuser',
          passwordHash: await bcrypt.hash('CurrentPass123!', 10),
          isActive: true,
          role: 'USER'
        }
      });
    });

    it('should change password with correct current password', async () => {
      await authService.changePassword(
        'change-user-id',
        'CurrentPass123!',
        'NewPass123!'
      );

      const user = await prisma.user.findUnique({
        where: { id: 'change-user-id' }
      });

      const isValid = await bcrypt.compare('NewPass123!', user!.passwordHash);
      expect(isValid).toBe(true);
    });

    it('should throw error with incorrect current password', async () => {
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      await expect(
        authService.changePassword(
          'change-user-id',
          'WrongPass123!',
          'NewPass123!'
        )
      ).rejects.toThrow('Current password is incorrect');
    });

    it('should validate new password strength', async () => {
      await expect(
        authService.changePassword(
          'change-user-id',
          'CurrentPass123!',
          '123' // Weak password
        )
      ).rejects.toThrow('New password does not meet requirements');
    });
  });
});