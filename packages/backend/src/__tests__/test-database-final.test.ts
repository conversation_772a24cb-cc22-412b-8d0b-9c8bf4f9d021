/**
 * Final Test Database Verification
 * 
 * This test verifies that the SQLite test database setup is working correctly
 * for basic CRUD operations without complex clearing logic.
 */

import { PrismaClient } from '../../generated/prisma-test';

describe('Test Database Final', () => {
  let prisma: PrismaClient;

  beforeAll(() => {
    prisma = new PrismaClient({
      log: ['error'],
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || 'file:./test-data/test.db'
        }
      }
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  test('should connect to SQLite database', async () => {
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(Array.isArray(result)).toBe(true);
    expect(result).toHaveLength(1);
    expect((result as any)[0].test).toBe(1);
  });

  test('should create and retrieve a user', async () => {
    const userData = {
      email: `test-${Date.now()}@example.com`,
      username: `testuser${Date.now()}`,
      passwordHash: 'hashedpassword123',
      role: 'USER',
    };

    const user = await prisma.user.create({
      data: userData,
    });

    expect(user).toMatchObject({
      email: userData.email,
      username: userData.username,
      role: 'USER',
      isActive: true,
    });

    // Retrieve the user
    const retrievedUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    expect(retrievedUser).not.toBeNull();
    expect(retrievedUser?.email).toBe(userData.email);

    // Clean up
    await prisma.user.delete({ where: { id: user.id } });
  });

  test('should handle foreign key relationships', async () => {
    // Create user
    const user = await prisma.user.create({
      data: {
        email: `fktest-${Date.now()}@example.com`,
        username: `fkuser${Date.now()}`,
        passwordHash: 'hashedpassword123',
      },
    });

    // Create project for user
    const project = await prisma.project.create({
      data: {
        title: `Test Project ${Date.now()}`,
        userId: user.id,
      },
    });

    // Create image for project
    const image = await prisma.image.create({
      data: {
        name: `test-image-${Date.now()}.jpg`,
        storageFilename: `storage-${Date.now()}.jpg`,
        storagePath: `/test/storage/${Date.now()}.jpg`,
        projectId: project.id,
        userId: user.id,
      },
    });

    expect(image.projectId).toBe(project.id);
    expect(image.userId).toBe(user.id);

    // Test cascade delete - deleting project should delete image
    await prisma.project.delete({ where: { id: project.id } });

    const deletedImage = await prisma.image.findUnique({
      where: { id: image.id },
    });

    expect(deletedImage).toBeNull();

    // Clean up user
    await prisma.user.delete({ where: { id: user.id } });
  });

  test('should handle JSON fields as strings in SQLite', async () => {
    const user = await prisma.user.create({
      data: {
        email: `jsontest-${Date.now()}@example.com`,
        username: `jsonuser${Date.now()}`,
        passwordHash: 'hashedpassword123',
      },
    });
    
    // Create user profile with JSON-like data stored as string
    const profile = await prisma.userProfile.create({
      data: {
        userId: user.id,
        fullName: 'Test User',
        notificationPreferences: JSON.stringify({
          email: true,
          push: false,
          sms: false,
        }),
      },
    });

    expect(profile.notificationPreferences).toBeTruthy();
    
    // Parse JSON from string
    const preferences = JSON.parse(profile.notificationPreferences || '{}');
    expect(preferences).toEqual({
      email: true,
      push: false,
      sms: false,
    });

    // Clean up
    await prisma.userProfile.delete({ where: { id: profile.id } });
    await prisma.user.delete({ where: { id: user.id } });
  });

  test('should show database contains expected tables', async () => {
    const tables = await prisma.$queryRaw`
      SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name
    ` as Array<{ name: string }>;

    const expectedTables = [
      'access_requests',
      'email_verifications', 
      'images',
      'migrations',
      'password_reset_tokens',
      'project_duplication_tasks',
      'project_shares',
      'projects',
      'refresh_tokens',
      'segmentation_queue',
      'segmentation_results',
      'segmentations',
      'sessions',
      'user_profiles',
      'users'
    ];

    const tableNames = tables.map(t => t.name).sort();
    
    // Check that all expected tables exist
    expectedTables.forEach(expectedTable => {
      expect(tableNames).toContain(expectedTable);
    });

    console.log('✅ Database tables:', tableNames);
  });
});