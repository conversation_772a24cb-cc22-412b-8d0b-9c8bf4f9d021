/**
 * Email Service Test Suite
 *
 * This suite tests the critical email functionality including
 * project invitations, password resets, verification emails,
 * internationalization support, template generation, and error handling.
 */

// Mock i18n before importing the service
const mockTranslationFunction = (key: string, options?: any) => {
  // For simple keys, just return the key
  if (!options) {
    return key;
  }
  // For keys with interpolation, do simple replacement
  let result = key;
  if (options) {
    Object.keys(options).forEach((param) => {
      result = result.replace(`{{${param}}}`, options[param]);
    });
  }
  return result;
};

jest.mock('../../config/i18n', () => {
  const mockI18n = {
    getFixedT: jest.fn().mockReturnValue(mockTranslationFunction),
    t: jest.fn().mockImplementation(mockTranslationFunction),
    use: jest.fn().mockReturnThis(),
    init: jest.fn().mockReturnThis(),
  };

  return {
    __esModule: true,
    default: mockI18n,
  };
});

// Mock nodemailer module
const mockSendMail = jest.fn();
const mockVerify = jest.fn();
const mockTransporter = {
  sendMail: mockSendMail,
  verify: mockVerify,
};

jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => mockTransporter),
}));

// Mock logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

jest.mock('../../utils/logger', () => ({
  createLogger: jest.fn(() => mockLogger),
}));

// Mock config
jest.mock('../../config', () => ({
  __esModule: true,
  default: {
    email: {
      host: 'smtp.example.com',
      port: 587,
      secure: false,
      user: '<EMAIL>',
      pass: 'password',
      from: '<EMAIL>',
    },
    appUrl: 'https://app.spheroseg.com',
  },
}));

// emailService will be imported dynamically after mocks are set up

describe('Email Service', () => {
  let emailService: any;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();

    // Import emailService after mocks are reset
    emailService = require('../emailService');

    // Default successful transporter verification
    mockVerify.mockImplementation((callback) => {
      callback(null);
    });

    // Default successful email sending
    mockSendMail.mockResolvedValue({
      messageId: 'test-message-id',
    });
  });

  describe('Transporter Configuration', () => {
    it('should create transporter with correct configuration on first email send', async () => {
      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'test123',
      });

      const nodemailer = require('nodemailer');
      expect(nodemailer.createTransport).toHaveBeenCalledWith({
        host: 'smtp.example.com',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'password',
        },
      });
    });

    it('should verify transporter on creation', async () => {
      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'test123',
      });

      expect(mockVerify).toHaveBeenCalled();
    });

    it('should log successful verification', async () => {
      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'test123',
      });

      expect(mockLogger.info).toHaveBeenCalledWith('Email transporter is ready');
    });

    it('should handle verification errors', async () => {
      const error = new Error('SMTP connection failed');
      mockVerify.mockImplementation((callback) => {
        callback(error);
      });

      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'test123',
      });

      expect(mockLogger.error).toHaveBeenCalledWith('Email transporter verification failed:', {
        error,
      });
    });
  });

  describe('Project Invitation Email', () => {
    const mockParams = {
      to: '<EMAIL>',
      projectTitle: 'Test Project',
      ownerName: 'John Doe',
      invitationToken: 'abc123',
      permission: 'edit' as const,
      recipientLanguage: 'en',
    };

    it('should send project invitation email successfully', async () => {
      await emailService.sendProjectInvitation(mockParams);

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '"SpheroSeg" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'email.projectInvitation.subject',
        html: expect.stringContaining('email.projectInvitation.title'),
        text: expect.stringContaining('email.projectInvitation.title'),
      });

      expect(mockLogger.info).toHaveBeenCalledWith('Project invitation email sent', {
        to: '<EMAIL>',
        projectTitle: 'Test Project',
      });
    });

    it('should handle send failures gracefully', async () => {
      const error = new Error('SMTP send failed');
      mockSendMail.mockRejectedValue(error);

      await expect(emailService.sendProjectInvitation(mockParams)).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('Failed to send project invitation email', {
        error,
        to: '<EMAIL>',
      });
    });
  });

  describe('Password Reset Email', () => {
    it('should send password reset email successfully', async () => {
      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'reset123',
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '"SpheroSeg" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'email.passwordReset.subject',
        html: expect.stringContaining('email.passwordReset.title'),
        text: expect.stringContaining('email.passwordReset.title'),
      });

      expect(mockLogger.info).toHaveBeenCalledWith('Password reset email sent', {
        to: '<EMAIL>',
      });
    });

    it('should send password reset email with user name', async () => {
      await emailService.sendPasswordResetEmail({
        to: '<EMAIL>',
        resetToken: 'reset123',
        userName: 'John Doe',
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '"SpheroSeg" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'email.passwordReset.subject',
        html: expect.stringContaining('email.passwordReset.greetingWithName'),
        text: expect.stringContaining('email.passwordReset.greetingWithName'),
      });
    });

    it('should handle send failures gracefully', async () => {
      const error = new Error('SMTP send failed');
      mockSendMail.mockRejectedValue(error);

      await expect(
        emailService.sendPasswordResetEmail({
          to: '<EMAIL>',
          resetToken: 'reset123',
        })
      ).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('Failed to send password reset email', {
        error,
        to: '<EMAIL>',
      });
    });
  });

  describe('Verification Email', () => {
    it('should send verification email successfully', async () => {
      await emailService.sendVerificationEmail({
        to: '<EMAIL>',
        verificationToken: 'verify123',
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '"SpheroSeg" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'email.verification.subject',
        html: expect.stringContaining('email.verification.title'),
        text: expect.stringContaining('email.verification.title'),
      });

      expect(mockLogger.info).toHaveBeenCalledWith('Verification email sent', {
        to: '<EMAIL>',
      });
    });
  });

  describe('Default Export', () => {
    it('should export all email functions', () => {
      expect(emailService.default.sendProjectInvitation).toBe(emailService.sendProjectInvitation);
      expect(emailService.default.sendPasswordResetEmail).toBe(emailService.sendPasswordResetEmail);
      expect(emailService.default.sendVerificationEmail).toBe(emailService.sendVerificationEmail);
      expect(emailService.default.sendAccessRequestNotification).toBe(
        emailService.sendAccessRequestNotification
      );
      expect(emailService.default.sendInvitationAcceptedNotification).toBe(
        emailService.sendInvitationAcceptedNotification
      );
    });
  });
});
