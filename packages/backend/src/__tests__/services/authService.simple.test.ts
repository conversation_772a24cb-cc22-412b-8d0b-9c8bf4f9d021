/**
 * Simplified Authentication Service Test Suite
 *
 * A focused test suite that tests core authentication functionality
 * with simplified mocking to avoid memory and complexity issues.
 */

// Simple mock setup without complex configurations
const mockPool = {
  connect: jest.fn(),
};

const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

// Mock the db module before import
jest.mock('../../db', () => ({
  __esModule: true,
  default: {
    getPool: () => mockPool,
  },
}));

jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
}));

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-123'),
}));

jest.mock('../tokenService', () => ({
  __esModule: true,
  default: {
    createTokenResponse: jest.fn(),
    refreshToken: jest.fn(),
    revokeToken: jest.fn(),
    revokeAllUserTokens: jest.fn(),
  },
}));

jest.mock('../email/CentralizedEmailService', () => ({
  sendNewPasswordEmail: jest.fn(),
  sendVerificationEmail: jest.fn(),
}));

jest.mock('../cacheService', () => ({
  cacheService: {
    getUserCache: jest.fn(),
    setUserCache: jest.fn(),
    invalidateRelated: jest.fn(),
  },
}));

jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Import after mocks
import authService from '../authService';
import bcryptjs from 'bcryptjs';
import tokenService from '../tokenService';
import { ApiError } from '../../utils/errors';

describe('AuthService - Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPool.connect.mockResolvedValue(mockClient);
    mockClient.query.mockResolvedValue({ rows: [] });
  });

  describe('authenticateUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'password123';
    const mockUser = {
      id: 'user-123',
      email: mockEmail,
      password_hash: 'hashed-password',
      name: 'Test User',
      created_at: new Date(),
      updated_at: new Date(),
    };

    it('should authenticate valid credentials', async () => {
      // Setup mocks
      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // User found
        .mockResolvedValueOnce({ rows: [] }); // Profile query

      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Verify
      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        created_at: mockUser.created_at,
        updated_at: mockUser.updated_at,
        role: 'user', // Add the role field that authService returns
      });
      expect(bcryptjs.compare).toHaveBeenCalledWith(mockPassword, mockUser.password_hash);
    });

    it('should return null for non-existent user', async () => {
      // Setup - no user found
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Verify
      expect(result).toBeNull();
      expect(bcryptjs.compare).not.toHaveBeenCalled();
    });

    it('should return null for invalid password', async () => {
      // Setup mocks
      mockClient.query.mockResolvedValueOnce({ rows: [mockUser] });
      (bcryptjs.compare as jest.Mock).mockResolvedValue(false);

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Verify
      expect(result).toBeNull();
    });
  });

  describe('loginUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'password123';
    const mockUser = {
      id: 'user-123',
      email: mockEmail,
      password_hash: 'hashed-password',
      name: 'Test User',
      created_at: new Date(),
      updated_at: new Date(),
    };
    const mockTokenResponse = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      tokenType: 'Bearer',
    };

    it('should login user successfully', async () => {
      // Setup mocks - add role to mockUser
      const mockUserWithRole = { ...mockUser, role: 'user' };
      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUserWithRole] })
        .mockResolvedValueOnce({ rows: [] });

      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);
      (tokenService.createTokenResponse as jest.Mock).mockResolvedValue(mockTokenResponse);

      // Execute
      const result = await authService.loginUser(mockEmail, mockPassword);

      // Verify
      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
        }),
        ...mockTokenResponse,
      });
    });

    it('should throw error for invalid credentials', async () => {
      // Setup - no user found
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Execute & Verify
      await expect(authService.loginUser(mockEmail, mockPassword)).rejects.toThrow(
        'This email address is not registered'
      );
    });
  });

  describe('refreshAccessToken', () => {
    const mockRefreshToken = 'refresh-token';
    const mockNewTokens = {
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      tokenType: 'Bearer',
    };

    it('should refresh token successfully', async () => {
      // Setup
      (tokenService.refreshToken as jest.Mock).mockResolvedValue(mockNewTokens);

      // Execute
      const result = await authService.refreshAccessToken(mockRefreshToken);

      // Verify
      expect(result).toEqual(mockNewTokens);
      expect(tokenService.refreshToken).toHaveBeenCalledWith(mockRefreshToken);
    });

    it('should handle refresh failure', async () => {
      // Setup
      (tokenService.refreshToken as jest.Mock).mockRejectedValue(
        new ApiError('Invalid refresh token', 401)
      );

      // Execute & Verify
      await expect(authService.refreshAccessToken(mockRefreshToken)).rejects.toThrow(
        'Invalid refresh token'
      );
    });
  });

  describe('checkEmailExists', () => {
    const mockEmail = '<EMAIL>';

    it('should return true for existing email', async () => {
      // Setup
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: 'user-123' }] }) // User exists
        .mockResolvedValueOnce({ rows: [] }); // No access request

      // Execute
      const result = await authService.checkEmailExists(mockEmail);

      // Verify
      expect(result).toEqual({ exists: true, hasAccessRequest: false });
    });

    it('should return false for non-existent email', async () => {
      // Setup
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // No user
        .mockResolvedValueOnce({ rows: [] }); // No access request

      // Execute
      const result = await authService.checkEmailExists(mockEmail);

      // Verify
      expect(result).toEqual({ exists: false, hasAccessRequest: false });
    });
  });

  describe('logoutUser', () => {
    it('should logout with refresh token', async () => {
      const mockRefreshToken = 'refresh-token|jti-123';

      // Mock the db query for token revocation
      mockClient.query.mockResolvedValue({ rowCount: 1 });

      // Execute
      await authService.logoutUser(mockRefreshToken);

      // Verify that a query was made to revoke the token
      expect(mockClient.query).toHaveBeenCalled();
    });

    it('should logout with userId', async () => {
      const mockUserId = 'user-123';

      // Mock the db query for revoking all user tokens
      mockClient.query.mockResolvedValue({ rowCount: 5 });

      // Execute
      await authService.logoutUser(undefined, mockUserId);

      // Verify that a query was made to revoke all user tokens
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM refresh_tokens WHERE user_id = $1'),
        [mockUserId]
      );
    });
  });
});
