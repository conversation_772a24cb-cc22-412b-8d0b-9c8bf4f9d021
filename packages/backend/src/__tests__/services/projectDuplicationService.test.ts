import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { Pool, PoolClient } from 'pg';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('path');
jest.mock('@/utils/logger');

// Import the module under test (after mocks are set up)
import projectDuplicationService from '../projectDuplicationService';
const { duplicateProject, duplicateProjectViaApi, generateNewFilePaths, copyImageFiles } =
  projectDuplicationService;

// Setup mocks before each test
beforeEach(() => {
  jest.clearAllMocks();

  // Mock path functions
  (path.dirname as jest.Mock).mockImplementation((filePath: string) => {
    const parts = filePath.split('/');
    parts.pop();
    return parts.join('/');
  });

  (path.basename as jest.Mock).mockImplementation((filePath: string, ext?: string) => {
    const parts = filePath.split('/');
    const filename = parts[parts.length - 1];
    if (ext) {
      return filename.replace(ext, '');
    }
    return filename;
  });

  (path.extname as jest.Mock).mockImplementation((filePath: string) => {
    const parts = filePath.split('.');
    return parts.length > 1 ? `.${parts[parts.length - 1]}` : '';
  });

  (path.join as jest.Mock).mockImplementation((...args: string[]) => args.join('/'));

  // Mock fs functions
  (fs.copyFile as jest.Mock).mockResolvedValue(undefined);
});

describe('projectDuplicationService', () => {
  describe('generateNewFilePaths', () => {
    it('should generate new paths with timestamp', () => {
      // Mock Date.now for predictable test results
      const dateSpy = jest.spyOn(Date, 'now').mockReturnValue(12345);

      // Act
      const result = generateNewFilePaths([
        '/uploads/original-project/image.jpg',
        '/uploads/original-project/thumb-image.jpg',
      ]);

      // Assert
      expect(result).toEqual([
        {
          originalPath: '/uploads/original-project/image.jpg',
          newPath: '/uploads/original-project/image_copy_12345.jpg',
        },
        {
          originalPath: '/uploads/original-project/thumb-image.jpg',
          newPath: '/uploads/original-project/thumb-image_copy_12345.jpg',
        },
      ]);

      // Restore spy
      dateSpy.mockRestore();
    });

    it('should handle complex file names with multiple dots', () => {
      // Mock Date.now for predictable test results
      const dateSpy = jest.spyOn(Date, 'now').mockReturnValue(12345);

      // Act
      const result = generateNewFilePaths(['/uploads/original-project/complex.file.name.jpg']);

      // Assert
      expect(result).toEqual([
        {
          originalPath: '/uploads/original-project/complex.file.name.jpg',
          newPath: '/uploads/original-project/complex.file.name_copy_12345.jpg',
        },
      ]);

      // Restore spy
      dateSpy.mockRestore();
    });

    it('should handle files without extensions', () => {
      // Mock Date.now for predictable test results
      const dateSpy = jest.spyOn(Date, 'now').mockReturnValue(12345);

      // Act
      const result = generateNewFilePaths(['/uploads/original-project/README']);

      // Assert
      expect(result).toEqual([
        {
          originalPath: '/uploads/original-project/README',
          newPath: '/uploads/original-project/README_copy_12345',
        },
      ]);

      // Restore spy
      dateSpy.mockRestore();
    });
  });

  describe('copyImageFiles', () => {
    it('should copy files from source to target', async () => {
      // Arrange
      const filePaths = [
        { originalPath: '/uploads/old/image.jpg', newPath: '/uploads/new/image.jpg' },
        { originalPath: '/uploads/old/thumb.jpg', newPath: '/uploads/new/thumb.jpg' },
      ];

      // Act
      await copyImageFiles(filePaths);

      // Assert
      expect(fs.copyFile).toHaveBeenCalledTimes(2);
      expect(fs.copyFile).toHaveBeenCalledWith('/uploads/old/image.jpg', '/uploads/new/image.jpg');
      expect(fs.copyFile).toHaveBeenCalledWith('/uploads/old/thumb.jpg', '/uploads/new/thumb.jpg');
    });

    it('should handle file system errors', async () => {
      // Arrange
      (fs.copyFile as jest.Mock).mockRejectedValue(new Error('File system error'));
      const filePaths = [
        { originalPath: '/uploads/old/image.jpg', newPath: '/uploads/new/image.jpg' },
      ];

      // Act & Assert
      await expect(copyImageFiles(filePaths)).rejects.toThrow('File system error');
    });
  });

  describe('duplicateProject', () => {
    // Create mock pool and client
    const mockClient = {
      query: jest.fn(),
      release: jest.fn(),
    } as unknown as PoolClient;

    const mockPool = {
      connect: jest.fn(),
    } as unknown as Pool;

    beforeEach(() => {
      jest.clearAllMocks();

      // Reset mock pool connection
      (mockPool.connect as jest.Mock).mockResolvedValue(mockClient);
      (mockClient.release as jest.Mock).mockClear();

      // Mock client.query for different query patterns
      (mockClient.query as jest.Mock).mockImplementation((query, _params) => {
        if (query === 'BEGIN' || query === 'COMMIT' || query === 'ROLLBACK') {
          return Promise.resolve({});
        }

        if (query.includes('SELECT * FROM projects')) {
          return Promise.resolve({
            rows: [
              {
                id: 'original-project-id',
                name: 'Original Project',
                description: 'Test project description',
                created_by: 'original-user',
              },
            ],
          });
        }

        if (query.includes('INSERT INTO projects')) {
          return Promise.resolve({
            rows: [{ id: 'new-project-id' }],
          });
        }

        if (query.includes('SELECT * FROM images')) {
          return Promise.resolve({
            rows: [
              {
                id: 'image-1',
                project_id: 'original-project-id',
                filename: 'test-image.jpg',
                image_path: '/uploads/original/image.jpg',
                thumbnail_path: '/uploads/original/thumb.jpg',
                width: 800,
                height: 600,
                size: 1024,
                format: 'jpeg',
              },
            ],
          });
        }

        if (query.includes('INSERT INTO images')) {
          return Promise.resolve({ rows: [] });
        }

        if (query.includes('SELECT * FROM segmentation_results')) {
          return Promise.resolve({ rows: [] });
        }

        if (query.includes('INSERT INTO project_shares')) {
          return Promise.resolve({ rows: [] });
        }

        return Promise.resolve({ rows: [] });
      });
    });

    it('should duplicate a project with default options', async () => {
      // Act
      const result = await duplicateProject(mockPool, 'original-project-id', 'test-user');

      // Assert
      expect(mockPool.connect).toHaveBeenCalled();
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM projects'),
        ['original-project-id']
      );
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO projects'),
        expect.arrayContaining(['Original Project (Copy)', 'Test project description', 'test-user'])
      );
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(mockClient.release).toHaveBeenCalled();
      expect(result).toBe('new-project-id');
    });

    it('should duplicate a project with custom name', async () => {
      // Act
      const result = await duplicateProject(mockPool, 'original-project-id', 'test-user', {
        newName: 'Custom Project Name',
      });

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO projects'),
        expect.arrayContaining(['Custom Project Name', 'Test project description', 'test-user'])
      );
      expect(result).toBe('new-project-id');
    });

    it('should duplicate a project with images', async () => {
      // Act
      await duplicateProject(mockPool, 'original-project-id', 'test-user', {
        includeImages: true,
      });

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM images'),
        ['original-project-id']
      );
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO images'),
        expect.any(Array)
      );
      expect(fs.copyFile).toHaveBeenCalledTimes(2); // image and thumbnail
    });

    it('should duplicate a project with segmentations', async () => {
      // Arrange - Mock segmentation data
      (mockClient.query as jest.Mock).mockImplementation((query, _params) => {
        if (query === 'BEGIN' || query === 'COMMIT') return Promise.resolve({});

        if (query.includes('SELECT * FROM projects')) {
          return Promise.resolve({
            rows: [{ id: 'original-project-id', name: 'Original Project', description: 'Test' }],
          });
        }

        if (query.includes('INSERT INTO projects')) {
          return Promise.resolve({ rows: [{ id: 'new-project-id' }] });
        }

        if (query.includes('SELECT * FROM images')) {
          return Promise.resolve({
            rows: [
              {
                id: 'image-1',
                filename: 'test.jpg',
                image_path: '/uploads/old/image.jpg',
                thumbnail_path: '/uploads/old/thumb.jpg',
                width: 800,
                height: 600,
                size: 1024,
                format: 'jpeg',
              },
            ],
          });
        }

        if (query.includes('INSERT INTO images')) {
          return Promise.resolve({ rows: [] });
        }

        if (query.includes('SELECT') && query.includes('original_id')) {
          return Promise.resolve({
            rows: [{ original_id: 'image-1', new_id: 'new-image-1' }],
          });
        }

        if (query.includes('SELECT * FROM segmentation_results')) {
          return Promise.resolve({
            rows: [
              {
                id: 'seg-1',
                image_id: 'image-1',
                polygons: [
                  [
                    [0, 0],
                    [1, 1],
                    [2, 2],
                  ],
                ],
                processing_time: 1.5,
                ml_model_version: '1.0',
                parameters: {},
              },
            ],
          });
        }

        if (query.includes('INSERT INTO segmentation_results')) {
          return Promise.resolve({ rows: [{ id: 'new-seg-1' }] });
        }

        if (query.includes('SELECT * FROM cells')) {
          return Promise.resolve({ rows: [] });
        }

        return Promise.resolve({ rows: [] });
      });

      // Act
      await duplicateProject(mockPool, 'original-project-id', 'test-user', {
        includeImages: true,
        includeSegmentations: true,
      });

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM segmentation_results'),
        ['image-1']
      );
    });

    it('should handle database query errors', async () => {
      // Arrange
      (mockClient.query as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(duplicateProject(mockPool, 'original-project-id', 'test-user')).rejects.toThrow(
        'Database error'
      );

      // Verify transaction was rolled back
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should handle project not found', async () => {
      // Arrange
      (mockClient.query as jest.Mock).mockImplementation((query) => {
        if (query === 'BEGIN') return Promise.resolve({});
        if (query.includes('SELECT * FROM projects')) {
          return Promise.resolve({ rows: [] }); // No project found
        }
        return Promise.resolve({ rows: [] });
      });

      // Act & Assert
      await expect(duplicateProject(mockPool, 'original-project-id', 'test-user')).rejects.toThrow(
        'Project not found'
      );

      // Verify transaction was rolled back
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });

    it('should duplicate shared users', async () => {
      // Act
      await duplicateProject(mockPool, 'original-project-id', 'test-user', {
        includeSharedUsers: true,
      });

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO project_shares'),
        ['original-project-id', 'new-project-id']
      );
    });
  });

  describe('duplicateProjectViaApi', () => {
    it('should return stub response', async () => {
      // Act
      const result = await duplicateProjectViaApi('original-project-id');

      // Assert
      expect(result).toEqual({
        projectId: 'new-project-id',
        message: 'Project duplicated successfully',
      });
    });

    it('should accept options parameter', async () => {
      // Act
      const result = await duplicateProjectViaApi('original-project-id', {
        includeImages: true,
        newName: 'Custom Name',
      });

      // Assert
      expect(result).toEqual({
        projectId: 'new-project-id',
        message: 'Project duplicated successfully',
      });
    });
  });
});
