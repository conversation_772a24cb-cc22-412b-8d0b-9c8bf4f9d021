// Debug test to understand dynamic import issue

jest.mock('../projectService');

describe('Debug Dynamic Import', () => {
  it('should check what dynamic import returns', async () => {
    const projectService = await import('../projectService');
    console.log('Dynamic import result:', projectService);
    console.log('getProjectById:', projectService.getProjectById);

    const mockGetProjectById = projectService.getProjectById as jest.MockedFunction<any>;
    mockGetProjectById.mockResolvedValue({ id: 'test' });

    const result = await mockGetProjectById('pool', 'projectId', 'userId');
    console.log('Mock result:', result);

    expect(result).toEqual({ id: 'test' });
  });
});
