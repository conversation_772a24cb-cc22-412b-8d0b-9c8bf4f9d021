/**
 * Simplified Image Processing Service Test Suite
 *
 * Tests for the image processing service with simplified mocking
 * to avoid memory and complexity issues.
 */

// <PERSON>ck sharp before import
const mockSharpInstance = {
  resize: jest.fn().mockReturnThis(),
  toFormat: jest.fn().mockReturnThis(),
  toFile: jest.fn().mockResolvedValue({}),
  metadata: jest.fn().mockResolvedValue({
    width: 800,
    height: 600,
    format: 'jpeg',
    hasAlpha: false,
    orientation: 1,
  }),
  jpeg: jest.fn().mockReturnThis(),
  png: jest.fn().mockReturnThis(),
  webp: jest.fn().mockReturnThis(),
};

const mockSharp = jest.fn(() => mockSharpInstance);

jest.mock('sharp', () => ({
  __esModule: true,
  default: mockSharp,
}));

// Mock promisify functions directly
const mockStatAsync = jest.fn().mockResolvedValue({ size: 1024000, isFile: () => true });
const mockMkdirAsync = jest.fn().mockResolvedValue(undefined);
const mockExistsAsync = jest.fn().mockResolvedValue(true);

// Mock fs
jest.mock('fs', () => ({
  stat: jest.fn(),
  mkdir: jest.fn(),
  exists: jest.fn(),
  promises: {
    unlink: jest.fn().mockResolvedValue(undefined),
  },
}));

// Mock util promisify
jest.mock('util', () => ({
  promisify: (fn: any) => {
    const fnName = fn?.name;
    if (fnName === 'stat') return mockStatAsync;
    if (fnName === 'mkdir') return mockMkdirAsync;
    if (fnName === 'exists') return mockExistsAsync;
    return jest.fn();
  },
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Import after mocks
import imageProcessingService from '../imageProcessingService';
import { ApiError } from '../../utils/errors';

describe('ImageProcessingService - Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations to defaults
    mockSharpInstance.metadata.mockResolvedValue({
      width: 800,
      height: 600,
      format: 'jpeg',
      hasAlpha: false,
      orientation: 1,
    });
    mockSharpInstance.toFile.mockResolvedValue({});
    mockStatAsync.mockResolvedValue({ size: 1024000, isFile: () => true });
    mockExistsAsync.mockResolvedValue(true);
    mockMkdirAsync.mockResolvedValue(undefined);
  });

  describe('validateImage', () => {
    it('should validate a valid image', async () => {
      // Test validation passes
      const result = await imageProcessingService.validateImage('/uploads/test.jpg');

      expect(result).toBe(true);
      expect(mockSharp).toHaveBeenCalledWith('/uploads/test.jpg');
      expect(mockSharpInstance.metadata).toHaveBeenCalled();
    });

    it('should throw error for non-existent file', async () => {
      // Mock file doesn't exist
      mockExistsAsync.mockResolvedValueOnce(false);

      await expect(imageProcessingService.validateImage('/uploads/missing.jpg')).rejects.toThrow(
        ApiError
      );

      await expect(imageProcessingService.validateImage('/uploads/missing.jpg')).rejects.toThrow(
        'Soubor neexistuje'
      );
    });

    it('should throw error for unsupported format', async () => {
      // Mock unsupported format
      mockSharpInstance.metadata.mockResolvedValueOnce({
        width: 800,
        height: 600,
        format: 'gif', // GIF is not in SUPPORTED_FORMATS
      });

      await expect(imageProcessingService.validateImage('/uploads/test.gif')).rejects.toThrow(
        'Nepodporovaný formát obrázku'
      );
    });

    it('should throw error for oversized file', async () => {
      // Mock oversized file (>100MB)
      mockStatAsync.mockResolvedValueOnce({ size: 105 * 1024 * 1024, isFile: () => true }); // 105MB

      await expect(imageProcessingService.validateImage('/uploads/huge.jpg')).rejects.toThrow(
        'Soubor je příliš velký'
      );
    });

    it('should handle BMP format by converting', async () => {
      // Mock BMP file
      const result = await imageProcessingService.validateImage('/uploads/test.bmp');

      expect(result).toBe(true);
      // Should create a temp file and then validate it
      expect(mockSharp).toHaveBeenCalledTimes(2); // Once for conversion, once for validation
    });
  });

  describe('getImageMetadata', () => {
    it('should return image metadata', async () => {
      const metadata = await imageProcessingService.getImageMetadata('/uploads/test.jpg');

      expect(metadata).toEqual({
        width: 800,
        height: 600,
        format: 'jpeg',
        size: 1024000,
        hasAlpha: false,
        orientation: 1,
        exif: undefined,
      });

      expect(mockSharp).toHaveBeenCalledWith('/uploads/test.jpg');
      expect(mockSharpInstance.metadata).toHaveBeenCalled();
    });

    it('should throw error for non-existent file', async () => {
      mockExistsAsync.mockResolvedValueOnce(false);

      await expect(imageProcessingService.getImageMetadata('/uploads/missing.jpg')).rejects.toThrow(
        'Soubor neexistuje'
      );
    });

    it('should handle metadata extraction errors', async () => {
      mockSharpInstance.metadata.mockRejectedValueOnce(new Error('Corrupted file'));

      await expect(
        imageProcessingService.getImageMetadata('/uploads/corrupted.jpg')
      ).rejects.toThrow('Nelze získat metadata obrázku');
    });
  });

  describe('createThumbnail', () => {
    it('should create thumbnail with default options', async () => {
      const result = await imageProcessingService.createThumbnail(
        '/uploads/test.jpg',
        '/thumbnails/test_thumb.jpg'
      );

      expect(result).toBe('/thumbnails/test_thumb.jpg');
      expect(mockSharp).toHaveBeenCalledWith('/uploads/test.jpg');
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(300, 300, {
        fit: 'cover',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
        withoutEnlargement: true,
      });
      expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('jpeg', { quality: 80 });
      expect(mockSharpInstance.toFile).toHaveBeenCalledWith('/thumbnails/test_thumb.jpg');
    });

    it('should create thumbnail with custom options', async () => {
      const options = {
        width: 150,
        height: 150,
        fit: 'contain' as const,
        withoutEnlargement: false,
      };

      const result = await imageProcessingService.createThumbnail(
        '/uploads/test.jpg',
        '/thumbnails/test_custom.jpg',
        options
      );

      expect(result).toBe('/thumbnails/test_custom.jpg');
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(150, 150, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
        withoutEnlargement: false,
      });
    });

    it('should handle thumbnail creation errors', async () => {
      mockSharpInstance.toFile.mockRejectedValueOnce(new Error('Write error'));

      await expect(
        imageProcessingService.createThumbnail('/uploads/test.jpg', '/thumbnails/error.jpg')
      ).rejects.toThrow('Nelze vytvořit náhled');
    });

    it('should create directory if it does not exist', async () => {
      await imageProcessingService.createThumbnail('/uploads/test.jpg', '/new/dir/thumb.jpg');

      expect(mockMkdirAsync).toHaveBeenCalledWith('/new/dir', { recursive: true });
    });
  });

  describe('optimizeImage', () => {
    it('should optimize image with default settings', async () => {
      const result = await imageProcessingService.optimizeImage(
        '/uploads/original.jpg',
        '/optimized/output.jpg'
      );

      expect(result).toBe('/optimized/output.jpg');
      expect(mockSharp).toHaveBeenCalledWith('/uploads/original.jpg');
      expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('jpeg', { quality: 80 });
      expect(mockSharpInstance.toFile).toHaveBeenCalledWith('/optimized/output.jpg');
    });

    it('should optimize image with custom format and quality', async () => {
      const result = await imageProcessingService.optimizeImage(
        '/uploads/original.png',
        '/optimized/output.webp',
        'webp',
        90
      );

      expect(result).toBe('/optimized/output.webp');
      expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('webp', { quality: 90 });
    });

    it('should handle optimization errors', async () => {
      mockSharpInstance.toFile.mockRejectedValueOnce(new Error('Optimization failed'));

      await expect(
        imageProcessingService.optimizeImage('/uploads/test.jpg', '/optimized/error.jpg')
      ).rejects.toThrow('Nelze optimalizovat obrázek');
    });
  });

  describe('convertToStandardFormat', () => {
    it('should not convert JPEG files', async () => {
      const { convertToStandardFormat } = await import('../imageProcessingService');
      const result = await convertToStandardFormat('/uploads/test.jpg');

      expect(result).toBe('/uploads/test.jpg');
      expect(mockSharp).not.toHaveBeenCalled();
    });

    it('should convert BMP to JPEG', async () => {
      const { convertToStandardFormat } = await import('../imageProcessingService');
      mockSharpInstance.toFile.mockResolvedValueOnce({});

      const result = await convertToStandardFormat('/uploads/test.bmp');

      expect(result).toMatch(/test_\d+\.jpeg$/);
      expect(mockSharp).toHaveBeenCalledWith('/uploads/test.bmp');
      expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('jpeg');
    });

    it('should convert TIFF to JPEG', async () => {
      const { convertToStandardFormat } = await import('../imageProcessingService');
      mockSharpInstance.toFile.mockResolvedValueOnce({});

      const result = await convertToStandardFormat('/uploads/test.tiff');

      expect(result).toMatch(/test_\d+\.jpeg$/);
      expect(mockSharp).toHaveBeenCalledWith('/uploads/test.tiff');
    });

    it('should handle conversion errors', async () => {
      const { convertToStandardFormat } = await import('../imageProcessingService');
      mockSharpInstance.toFile.mockRejectedValueOnce(new Error('Conversion failed'));

      await expect(convertToStandardFormat('/uploads/test.bmp')).rejects.toThrow(
        'Nelze konvertovat soubor .bmp na jpeg'
      );
    });
  });
});
