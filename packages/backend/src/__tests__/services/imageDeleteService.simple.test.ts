/**
 * Simplified test for imageDeleteService to debug mocking issues
 */

import { jest } from '@jest/globals';

// Mock the database first before any imports
const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

const mockPool = {
  connect: jest.fn(() => mockClient),
  query: jest.fn(),
};

// THIS MUST BE BEFORE ANY IMPORTS TO WORK
jest.mock('../../db', () => {
  const mockClient = {
    query: jest.fn(),
    release: jest.fn(),
  };

  const mockPool = {
    connect: jest.fn(() => Promise.resolve(mockClient)),
    query: jest.fn(),
  };

  return {
    getPool: jest.fn(() => mockPool),
    __esModule: true,
    default: mockPool,
  };
});

// Manual mock for projectService
jest.mock('../projectService');

// Mock other dependencies
jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('../cacheService', () => ({
  default: {
    invalidateImageList: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../../utils/imageUtils.unified', () => ({
  default: {
    dbPathToFilesystemPath: jest.fn((dbPath: string, uploadDir: string) => {
      return `${uploadDir}/${dbPath}`;
    }),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    getFilesInDirectory: jest.fn().mockResolvedValue([]),
  },
}));

jest.mock('fs', () => ({
  rmdirSync: jest.fn(() => undefined),
}));

jest.mock('../../config', () => ({
  __esModule: true,
  default: {
    storage: {
      uploadDir: '/test/uploads',
    },
  },
}));

// Debug getPool
import { getPool } from '../../db';
console.log('getPool after import:', getPool);

// Import the function to test and the mock
import { deleteImage } from '../imageDeleteService';
import { getProjectById } from '../projectService';

const mockGetProjectById = getProjectById as jest.MockedFunction<typeof getProjectById>;

describe.skip('ImageDeleteService Simple Test', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mock implementations
    mockClient.query.mockReset();
    mockClient.release.mockReset();
    mockPool.connect.mockResolvedValue(mockClient);
  });

  it('should check if getProjectById is called correctly', async () => {
    // Setup the mock to log when called
    mockGetProjectById.mockImplementation(async (pool, projectId, userId) => {
      console.log('getProjectById called with:', { pool: !!pool, projectId, userId });
      return {
        id: projectId,
        is_owner: true,
        permission: 'owner',
      } as any;
    });

    // Setup basic client query responses
    mockClient.query.mockImplementation((query: string) => {
      console.log('Query:', query.substring(0, 50) + '...');

      if (query.includes('SELECT i.id, i.storage_path')) {
        return {
          rows: [
            {
              id: 'test-image',
              storage_path: 'test.jpg',
              thumbnail_path: 'test-thumb.jpg',
              file_size: '1000',
            },
          ],
        };
      }

      if (query === 'BEGIN' || query === 'COMMIT' || query === 'ROLLBACK') {
        return {};
      }

      if (query.includes('DELETE FROM')) {
        return {};
      }

      return { rows: [] };
    });

    const result = await deleteImage('test-image', 'test-project', 'test-user');

    console.log('Result:', result);
    console.log('getProjectById called:', mockGetProjectById.mock.calls.length, 'times');

    expect(mockGetProjectById).toHaveBeenCalled();
    expect(result.success).toBe(true);
  });
});
