/**
 * Unit tests for AuthService
 *
 * Tests all authentication service methods with proper mocking
 */

import { AuthService } from '../authService';
import db from '../../db';
import bcryptjs from 'bcryptjs';
import tokenService from '../tokenService';
import { sendVerificationEmail, sendNewPasswordEmail } from '../email/CentralizedEmailService';
import { cacheService } from '../cacheService';

// Mock all dependencies
jest.mock('../../db');
jest.mock('bcryptjs');
jest.mock('../tokenService');
jest.mock('../email/CentralizedEmailService');
jest.mock('../cacheService');
jest.mock('../../utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('AuthService Unit Tests', () => {
  let authService: AuthService;
  let mockClient: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create fresh instance
    authService = new AuthService();

    // Setup database mock
    mockClient = {
      query: jest.fn(),
      release: jest.fn(),
    };

    (db.connect as jest.Mock).mockResolvedValue(mockClient);
  });

  describe('registerUser', () => {
    const mockUserData = {
      email: '<EMAIL>',
      password: 'Test123!',
      full_name: 'Test User',
      username: 'testuser',
      preferred_language: 'en',
    };

    it('should successfully register a new user', async () => {
      // Setup mocks
      const hashedPassword = 'hashed_password';
      const userId = 'user-123';
      const mockUser = {
        id: userId,
        email: mockUserData.email,
        name: mockUserData.full_name,
        created_at: new Date(),
      };

      (bcryptjs.hash as jest.Mock).mockResolvedValue(hashedPassword);

      // Mock transaction queries
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({
          // INSERT user
          rows: [mockUser],
        })
        .mockResolvedValueOnce({ rows: [] }) // INSERT profile
        .mockResolvedValueOnce({ rows: [] }) // COMMIT
        .mockResolvedValueOnce({ rows: [] }); // Release

      (tokenService.generateAuthTokens as jest.Mock).mockReturnValue({
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });

      (sendVerificationEmail as jest.Mock).mockResolvedValue(undefined);

      // Execute
      const result = await authService.registerUser(
        mockUserData.email,
        mockUserData.password,
        mockUserData.full_name,
        mockUserData.username,
        mockUserData.preferred_language
      );

      // Assert
      expect(result).toEqual({
        user: mockUser,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        tokenType: 'Bearer',
      });

      expect(bcryptjs.hash).toHaveBeenCalledWith(mockUserData.password, 10);
      expect(tokenService.generateAuthTokens).toHaveBeenCalledWith(userId);
      expect(sendVerificationEmail).toHaveBeenCalledWith(mockUserData.email);
    });

    it('should throw error if email already exists', async () => {
      // Setup mocks
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce({ code: '23505' }); // Unique constraint violation

      // Execute & Assert
      await expect(
        authService.registerUser(
          mockUserData.email,
          mockUserData.password,
          mockUserData.full_name,
          mockUserData.username
        )
      ).rejects.toThrow('Email already in use');

      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });

    it('should continue registration even if verification email fails', async () => {
      // Setup mocks
      const hashedPassword = 'hashed_password';
      const userId = 'user-123';
      const mockUser = {
        id: userId,
        email: mockUserData.email,
        name: mockUserData.full_name,
        created_at: new Date(),
      };

      (bcryptjs.hash as jest.Mock).mockResolvedValue(hashedPassword);

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [mockUser] }) // INSERT user
        .mockResolvedValueOnce({ rows: [] }) // INSERT profile
        .mockResolvedValueOnce({ rows: [] }) // COMMIT
        .mockResolvedValueOnce({ rows: [] }); // Release

      (tokenService.generateAuthTokens as jest.Mock).mockReturnValue({
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });

      // Email service fails
      (sendVerificationEmail as jest.Mock).mockRejectedValue(new Error('Email service down'));

      // Execute
      const result = await authService.registerUser(
        mockUserData.email,
        mockUserData.password,
        mockUserData.full_name,
        mockUserData.username
      );

      // Assert - registration should still succeed
      expect(result).toEqual({
        user: mockUser,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        tokenType: 'Bearer',
      });
    });
  });

  describe('authenticateUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'Test123!';

    it('should authenticate valid user credentials', async () => {
      // Setup mocks
      const mockUser = {
        id: 'user-123',
        email: mockEmail,
        password: 'hashed_password',
        is_active: true,
        email_verified: true,
        name: 'Test User',
        created_at: new Date(),
        updated_at: new Date(),
      };

      const mockProfile = {
        preferred_language: 'en',
        avatar_url: null,
        bio: null,
        theme: 'light',
        notification_preferences: {},
      };

      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // SELECT user
        .mockResolvedValueOnce({ rows: [mockProfile] }); // SELECT profile

      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        created_at: mockUser.created_at,
        updated_at: mockUser.updated_at,
        role: 'user',
        preferred_language: mockProfile.preferred_language,
        avatar_url: mockProfile.avatar_url,
        bio: mockProfile.bio,
        theme: mockProfile.theme,
        notification_preferences: mockProfile.notification_preferences,
      });
    });

    it('should return null for non-existent email', async () => {
      // Setup mocks
      mockClient.query.mockResolvedValueOnce({ rows: [] }); // No user found

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for invalid password', async () => {
      // Setup mocks
      const mockUser = {
        id: 'user-123',
        email: mockEmail,
        password: 'hashed_password',
        is_active: true,
      };

      mockClient.query.mockResolvedValueOnce({ rows: [mockUser] });
      (bcryptjs.compare as jest.Mock).mockResolvedValue(false); // Wrong password

      // Execute
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('loginUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'Test123!';

    it('should successfully login user', async () => {
      // Setup mocks
      const mockUser = {
        id: 'user-123',
        email: mockEmail,
        name: 'Test User',
        created_at: new Date(),
        updated_at: new Date(),
        role: 'user',
      };

      // Mock authenticateUser method
      const authenticateSpy = jest
        .spyOn(authService, 'authenticateUser')
        .mockResolvedValue(mockUser);

      (tokenService.generateAuthTokens as jest.Mock).mockReturnValue({
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });

      (tokenService.saveRefreshToken as jest.Mock).mockResolvedValue(undefined);

      // Execute
      const result = await authService.loginUser(mockEmail, mockPassword);

      // Assert
      expect(result).toEqual({
        user: mockUser,
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        tokenType: 'Bearer',
      });

      expect(authenticateSpy).toHaveBeenCalledWith(mockEmail, mockPassword);
      expect(tokenService.generateAuthTokens).toHaveBeenCalledWith(mockUser.id);
      expect(tokenService.saveRefreshToken).toHaveBeenCalledWith(mockUser.id, 'refresh_token');
    });

    it('should throw error for invalid credentials', async () => {
      // Setup mocks
      jest.spyOn(authService, 'authenticateUser').mockResolvedValue(null);

      // Execute & Assert
      await expect(authService.loginUser(mockEmail, mockPassword)).rejects.toThrow(
        'Invalid email or password'
      );
    });
  });

  describe('refreshAccessToken', () => {
    const mockRefreshToken = 'refresh_token';

    it('should refresh access token successfully', async () => {
      // Setup mocks
      const mockPayload = { userId: 'user-123' };

      (tokenService.verifyRefreshToken as jest.Mock).mockResolvedValue(mockPayload);
      (tokenService.isRefreshTokenValid as jest.Mock).mockResolvedValue(true);
      (tokenService.generateAuthTokens as jest.Mock).mockReturnValue({
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
      });
      (tokenService.saveRefreshToken as jest.Mock).mockResolvedValue(undefined);
      (tokenService.revokeRefreshToken as jest.Mock).mockResolvedValue(undefined);

      // Execute
      const result = await authService.refreshAccessToken(mockRefreshToken);

      // Assert
      expect(result).toEqual({
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
        tokenType: 'Bearer',
      });
    });

    it('should throw error for invalid refresh token', async () => {
      // Setup mocks
      (tokenService.verifyRefreshToken as jest.Mock).mockRejectedValue(new Error('Invalid token'));

      // Execute & Assert
      await expect(authService.refreshAccessToken(mockRefreshToken)).rejects.toThrow(
        'Invalid refresh token'
      );
    });
  });

  describe('forgotPassword', () => {
    const mockEmail = '<EMAIL>';

    it('should handle forgot password for existing user', async () => {
      // Setup mocks
      const mockUser = { id: 'user-123', email: mockEmail };
      const mockResetToken = 'reset_token_123';

      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // SELECT user
        .mockResolvedValueOnce({ rows: [] }); // INSERT reset token

      jest.spyOn(global, 'crypto' as any, 'get').mockReturnValue({
        randomBytes: jest.fn().mockReturnValue({
          toString: jest.fn().mockReturnValue(mockResetToken),
        }),
      });

      (sendNewPasswordEmail as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.forgotPassword(mockEmail);

      // Assert
      expect(sendNewPasswordEmail).toHaveBeenCalledWith(mockEmail, mockResetToken);
    });

    it('should silently handle non-existent email', async () => {
      // Setup mocks
      mockClient.query.mockResolvedValueOnce({ rows: [] }); // No user found

      // Execute - should not throw
      await expect(authService.forgotPassword(mockEmail)).resolves.toBeUndefined();

      // Assert
      expect(sendNewPasswordEmail).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    const mockToken = 'reset_token_123';
    const mockNewPassword = 'NewPassword123!';

    it('should reset password successfully', async () => {
      // Setup mocks
      const mockTokenData = {
        user_id: 'user-123',
        expires_at: new Date(Date.now() + 3600000), // 1 hour from now
      };
      const hashedPassword = 'new_hashed_password';

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [mockTokenData] }) // SELECT token
        .mockResolvedValueOnce({ rows: [] }) // UPDATE password
        .mockResolvedValueOnce({ rows: [] }) // DELETE token
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      (bcryptjs.hash as jest.Mock).mockResolvedValue(hashedPassword);
      (tokenService.revokeAllUserTokens as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.resetPassword(mockToken, mockNewPassword);

      // Assert
      expect(bcryptjs.hash).toHaveBeenCalledWith(mockNewPassword, 10);
      expect(tokenService.revokeAllUserTokens).toHaveBeenCalledWith(mockTokenData.user_id);
    });

    it('should throw error for invalid token', async () => {
      // Setup mocks
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }); // No token found

      // Execute & Assert
      await expect(authService.resetPassword(mockToken, mockNewPassword)).rejects.toThrow(
        'Invalid or expired reset token'
      );
    });

    it('should throw error for expired token', async () => {
      // Setup mocks
      const mockTokenData = {
        user_id: 'user-123',
        expires_at: new Date(Date.now() - 3600000), // 1 hour ago
      };

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [mockTokenData] }); // SELECT expired token

      // Execute & Assert
      await expect(authService.resetPassword(mockToken, mockNewPassword)).rejects.toThrow(
        'Invalid or expired reset token'
      );
    });
  });

  describe('logoutUser', () => {
    it('should logout user with refresh token', async () => {
      // Setup mocks
      const mockRefreshToken = 'refresh_token';
      const mockUserId = 'user-123';

      (tokenService.verifyRefreshToken as jest.Mock).mockResolvedValue({ userId: mockUserId });
      (tokenService.revokeRefreshToken as jest.Mock).mockResolvedValue(undefined);
      (cacheService.clearUserCache as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.logoutUser(mockRefreshToken);

      // Assert
      expect(tokenService.revokeRefreshToken).toHaveBeenCalledWith(mockRefreshToken);
      expect(cacheService.clearUserCache).toHaveBeenCalledWith(mockUserId);
    });

    it('should logout user with userId', async () => {
      // Setup mocks
      const mockUserId = 'user-123';

      (cacheService.clearUserCache as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.logoutUser(undefined, mockUserId);

      // Assert
      expect(cacheService.clearUserCache).toHaveBeenCalledWith(mockUserId);
    });
  });

  describe('changePassword', () => {
    const mockUserId = 'user-123';
    const mockCurrentPassword = 'CurrentPass123!';
    const mockNewPassword = 'NewPass123!';

    it('should change password successfully', async () => {
      // Setup mocks
      const mockUser = {
        id: mockUserId,
        password: 'current_hashed_password',
      };
      const newHashedPassword = 'new_hashed_password';

      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // SELECT user
        .mockResolvedValueOnce({ rows: [] }); // UPDATE password

      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);
      (bcryptjs.hash as jest.Mock).mockResolvedValue(newHashedPassword);
      (tokenService.revokeAllUserTokens as jest.Mock).mockResolvedValue(undefined);
      (cacheService.clearUserCache as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.changePassword(mockUserId, mockCurrentPassword, mockNewPassword);

      // Assert
      expect(bcryptjs.compare).toHaveBeenCalledWith(mockCurrentPassword, mockUser.password);
      expect(bcryptjs.hash).toHaveBeenCalledWith(mockNewPassword, 10);
      expect(tokenService.revokeAllUserTokens).toHaveBeenCalledWith(mockUserId);
      expect(cacheService.clearUserCache).toHaveBeenCalledWith(mockUserId);
    });

    it('should throw error for incorrect current password', async () => {
      // Setup mocks
      const mockUser = {
        id: mockUserId,
        password: 'current_hashed_password',
      };

      mockClient.query.mockResolvedValueOnce({ rows: [mockUser] });
      (bcryptjs.compare as jest.Mock).mockResolvedValue(false);

      // Execute & Assert
      await expect(
        authService.changePassword(mockUserId, mockCurrentPassword, mockNewPassword)
      ).rejects.toThrow('Current password is incorrect');
    });
  });

  describe('verifyEmail', () => {
    const mockToken = 'verification_token';

    it('should verify email successfully', async () => {
      // Setup mocks
      const mockTokenData = {
        user_id: 'user-123',
        expires_at: new Date(Date.now() + 3600000), // 1 hour from now
      };

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [mockTokenData] }) // SELECT token
        .mockResolvedValueOnce({ rows: [] }) // UPDATE user
        .mockResolvedValueOnce({ rows: [] }) // DELETE token
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      (cacheService.clearUserCache as jest.Mock).mockResolvedValue(undefined);

      // Execute
      await authService.verifyEmail(mockToken);

      // Assert
      expect(cacheService.clearUserCache).toHaveBeenCalledWith(mockTokenData.user_id);
    });

    it('should throw error for invalid token', async () => {
      // Setup mocks
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }); // No token found

      // Execute & Assert
      await expect(authService.verifyEmail(mockToken)).rejects.toThrow(
        'Invalid or expired verification token'
      );
    });
  });

  describe('getCurrentUser', () => {
    const mockUserId = 'user-123';

    it('should get current user from cache', async () => {
      // Setup mocks
      const mockUser = {
        id: mockUserId,
        email: '<EMAIL>',
        name: 'Test User',
        created_at: new Date(),
        updated_at: new Date(),
      };

      (cacheService.getUserCache as jest.Mock).mockResolvedValue(mockUser);

      // Execute
      const result = await authService.getCurrentUser(mockUserId);

      // Assert
      expect(result).toEqual(mockUser);
      expect(mockClient.query).not.toHaveBeenCalled(); // Should not query DB
    });

    it('should get current user from database if not cached', async () => {
      // Setup mocks
      const mockUser = {
        id: mockUserId,
        email: '<EMAIL>',
        name: 'Test User',
        created_at: new Date(),
        updated_at: new Date(),
      };

      const mockProfile = {
        preferred_language: 'en',
        avatar_url: null,
      };

      (cacheService.getUserCache as jest.Mock).mockResolvedValue(null);

      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // SELECT user
        .mockResolvedValueOnce({ rows: [mockProfile] }); // SELECT profile

      (cacheService.setUserCache as jest.Mock).mockResolvedValue(undefined);

      // Execute
      const result = await authService.getCurrentUser(mockUserId);

      // Assert
      expect(result.id).toBe(mockUserId);
      expect(cacheService.setUserCache).toHaveBeenCalled();
    });

    it('should throw error if user not found', async () => {
      // Setup mocks
      (cacheService.getUserCache as jest.Mock).mockResolvedValue(null);
      mockClient.query.mockResolvedValueOnce({ rows: [] }); // No user found

      // Execute & Assert
      await expect(authService.getCurrentUser(mockUserId)).rejects.toThrow('User not found');
    });
  });

  describe('checkEmailExists', () => {
    it('should return true if email exists', async () => {
      // Setup mocks
      mockClient.query.mockResolvedValueOnce({ rows: [{ exists: true }] });

      // Execute
      const result = await authService.checkEmailExists('<EMAIL>');

      // Assert
      expect(result).toBe(true);
    });

    it('should return false if email does not exist', async () => {
      // Setup mocks
      mockClient.query.mockResolvedValueOnce({ rows: [{ exists: false }] });

      // Execute
      const result = await authService.checkEmailExists('<EMAIL>');

      // Assert
      expect(result).toBe(false);
    });
  });
});
