/**
 * Tests for AuthService.registerUser method
 */

describe('AuthService.registerUser', () => {
  // Mock modules
  const mockQuery = jest.fn();
  const mockRelease = jest.fn();
  const mockConnect = jest.fn(() =>
    Promise.resolve({
      query: mockQuery,
      release: mockRelease,
    })
  );

  beforeAll(() => {
    // Mock db module
    jest.doMock('../../db', () => ({
      default: {
        connect: mockConnect,
      },
    }));

    // Mock other dependencies
    jest.doMock('bcryptjs', () => ({
      hash: jest.fn(() => Promise.resolve('hashed_password')),
    }));

    jest.doMock('../tokenService', () => ({
      default: {
        generateAuthTokens: jest.fn(() => ({
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
        })),
      },
    }));

    jest.doMock('../email/CentralizedEmailService', () => ({
      sendVerificationEmail: jest.fn(() => Promise.resolve()),
    }));

    jest.doMock('../../utils/logger', () => ({
      default: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
      },
    }));

    jest.doMock('../cacheService', () => ({
      cacheService: {
        setUserCache: jest.fn(),
      },
    }));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should successfully register a new user', async () => {
    // Setup mocks
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      created_at: new Date(),
    };

    mockQuery
      .mockResolvedValueOnce({ rows: [] }) // BEGIN
      .mockResolvedValueOnce({ rows: [mockUser] }) // INSERT user
      .mockResolvedValueOnce({ rows: [] }) // INSERT profile
      .mockResolvedValueOnce({ rows: [] }); // COMMIT

    // Import after mocks are set up
    const authService = require('../authService').default;

    // Execute
    const result = await authService.registerUser(
      '<EMAIL>',
      'Test123!',
      'Test User',
      'testuser'
    );

    // Assert
    expect(result).toEqual({
      user: mockUser,
      accessToken: 'access_token',
      refreshToken: 'refresh_token',
      tokenType: 'Bearer',
    });
    expect(mockConnect).toHaveBeenCalled();
    expect(mockQuery).toHaveBeenCalledTimes(4);
    expect(mockRelease).toHaveBeenCalled();
  });

  it('should throw error if email already exists', async () => {
    // Setup mocks
    mockQuery
      .mockResolvedValueOnce({ rows: [] }) // BEGIN
      .mockRejectedValueOnce({ code: '23505' }); // Unique constraint violation

    // Import after mocks are set up
    const authService = require('../authService').default;

    // Execute & Assert
    await expect(
      authService.registerUser('<EMAIL>', 'Test123!', 'Test User', 'testuser')
    ).rejects.toThrow('Email already in use');
  });

  it('should handle database errors', async () => {
    // Setup mocks
    mockQuery
      .mockResolvedValueOnce({ rows: [] }) // BEGIN
      .mockRejectedValueOnce(new Error('Database error')); // Generic error

    // Import after mocks are set up
    const authService = require('../authService').default;

    // Execute & Assert
    await expect(
      authService.registerUser('<EMAIL>', 'Test123!', 'Test User', 'testuser')
    ).rejects.toThrow('Failed to register user');
  });
});
