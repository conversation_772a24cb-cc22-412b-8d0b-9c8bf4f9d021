/**
 * Token Service Test Suite
 *
 * This suite tests the critical JWT token management functionality including
 * token creation, validation, refresh, and security features.
 */

// Mock config/security first before any other mocks
jest.mock('../../config/security', () => ({
  securityConfig: {
    jwt: {
      secret: 'test-secret',
      algorithm: 'HS256' as const,
      audience: 'spheroseg-api',
      issuer: 'spheroseg-auth',
      refreshSecret: 'test-refresh-secret',
      expiresIn: '15m',
      refreshExpiresIn: '7d',
    },
    session: {
      secret: 'test-session-secret',
      name: 'spheroseg.sid',
      cookie: {
        secure: false,
        httpOnly: true,
        sameSite: 'strict' as const,
        maxAge: 86400000,
        domain: undefined,
      },
      resave: false,
      saveUninitialized: false,
      rolling: true,
    },
    cors: {
      origin: ['http://localhost:3000', 'http://localhost'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'],
      exposedHeaders: ['X-Total-Count', 'X-Page', 'X-Per-Page'],
      maxAge: 86400,
    },
    csrf: {
      enabled: true,
      secret: 'test-csrf-secret',
      cookieName: 'XSRF-TOKEN',
      headerName: 'X-CSRF-Token',
      paramName: '_csrf',
      cookie: {
        httpOnly: false,
        secure: false,
        sameSite: 'strict' as const,
      },
    },
    rateLimit: {
      windowMs: 900000,
      max: 100,
      standardHeaders: true,
      legacyHeaders: false,
      endpoints: {
        login: { windowMs: 900000, max: 5, skipSuccessfulRequests: true },
        signup: { windowMs: 3600000, max: 3 },
        api: { windowMs: 60000, max: 60 },
        upload: { windowMs: 3600000, max: 10 },
      },
    },
    headers: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
          fontSrc: ["'self'", 'https://fonts.gstatic.com'],
          imgSrc: ["'self'", 'data:', 'blob:', 'https:'],
          connectSrc: ["'self'", 'ws:', 'wss:', 'https:'],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          childSrc: ["'none'"],
        },
      },
      strictTransportSecurity: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      xContentTypeOptions: 'nosniff',
      xFrameOptions: 'DENY',
      xXssProtection: '1; mode=block',
      referrerPolicy: 'strict-origin-when-cross-origin',
      permissionsPolicy: {
        features: {
          camera: ["'none'"],
          microphone: ["'none'"],
          geolocation: ["'none'"],
          payment: ["'none'"],
        },
      },
    },
    password: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      bcryptRounds: 12,
      maxLoginAttempts: 5,
      lockoutDuration: 900000,
    },
    upload: {
      maxFileSize: 52428800,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/tiff', 'image/bmp'],
      allowedExtensions: ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp'],
      scanForViruses: false,
      storageEncryption: false,
    },
    api: {
      requireHttps: false,
      trustedProxies: ['127.0.0.1', '::1'],
      apiKeyHeader: 'X-API-Key',
      enableApiKeys: false,
    },
    audit: {
      enabled: true,
      logLevel: 'info',
      sensitiveFields: ['password', 'token', 'secret', 'credit_card', 'ssn'],
      retentionDays: 90,
    },
  },
  validateSecurityConfig: jest.fn(),
}));

// Mock all other dependencies
const mockPool = {
  query: jest.fn(),
};

const mockConfig = {
  auth: {
    jwtSecret: 'test-secret',
    jwtExpiresIn: '15m',
    refreshTokenExpiresIn: '7d',
    accessTokenExpiry: '15m',
    refreshTokenExpiry: '7d',
    accessTokenExpiresIn: '15m',
    refreshTokenExpiresIn: '7d',
    useKeyRotation: false,
    saltRounds: 10,
    sessionTimeout: 3600,
    allowMockUser: false,
    tokenSecurityMode: 'standard',
    secureCookies: false,
    trustProxy: false,
  },
  server: {
    port: 5001,
    host: 'localhost',
  },
  db: {
    host: 'localhost',
    port: 5432,
    database: 'test',
    user: 'test',
    password: 'test',
  },
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

// Set up remaining mocks
jest.mock('../../db', () => mockPool);
jest.mock('../../config', () => ({
  __esModule: true,
  default: mockConfig,
}));
jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: mockLogger,
}));

jest.mock('../../auth/jwtKeyRotation', () => ({
  getKeyManager: jest.fn().mockReturnValue({
    getCurrentKey: jest.fn().mockReturnValue({
      id: 'key-1',
      key: 'test-key',
      algorithm: 'HS256',
    }),
    getAllKeys: jest.fn().mockReturnValue([{ id: 'key-1', key: 'test-key', algorithm: 'HS256' }]),
    getPublicKey: jest.fn().mockResolvedValue(null),
  }),
  signJWTWithRotation: jest.fn(),
}));

jest.mock('jsonwebtoken');

jest.mock('crypto', () => {
  return {
    __esModule: true,
    default: {
      randomBytes: jest.fn((size) => {
        const buf = Buffer.from('a'.repeat(size));
        buf.toString = (encoding?: string) => {
          if (encoding === 'hex') return 'a'.repeat(size * 2);
          return 'a'.repeat(size);
        };
        return buf;
      }),
      createHash: jest.fn(() => ({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn(() => 'a'.repeat(64)),
      })),
    },
    randomBytes: jest.fn((size) => {
      const buf = Buffer.from('a'.repeat(size));
      buf.toString = (encoding?: string) => {
        if (encoding === 'hex') return 'a'.repeat(size * 2);
        return 'a'.repeat(size);
      };
      return buf;
    }),
    createHash: jest.fn(() => ({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn(() => 'a'.repeat(64)),
    })),
  };
});

// Don't mock ms - let it use the real module

// Isolate modules to ensure mocks are applied
jest.isolateModules(() => {
  require('../tokenService');
});

// Now import the modules after all mocks are set up
import tokenService, { TokenType } from '../tokenService';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// Remove the mock helper - use the actual tokenService methods

describe('Token Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mock implementations
    mockPool.query.mockReset();
    (jwt.sign as jest.Mock).mockReset();
    (jwt.verify as jest.Mock).mockReset();
    (crypto.randomBytes as jest.Mock).mockImplementation((size) => {
      const buf = Buffer.from('a'.repeat(size));
      buf.toString = (encoding?: string) => {
        if (encoding === 'hex') return 'a'.repeat(size * 2);
        return 'a'.repeat(size);
      };
      return buf;
    });

    // Reset createHash mock to return a new instance each time
    (crypto.createHash as jest.Mock).mockImplementation(() => ({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn(() => 'a'.repeat(64)),
    }));
  });

  describe('generateAccessToken', () => {
    it('should generate valid access token with user payload', () => {
      const mockToken = 'mock.jwt.token';
      (jwt.sign as jest.Mock).mockReturnValue(mockToken);

      const result = tokenService.generateAccessToken('1', '<EMAIL>');

      expect(result).toBe(mockToken);
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: '1',
          email: '<EMAIL>',
          type: TokenType.ACCESS,
        }),
        expect.any(String),
        expect.objectContaining({
          expiresIn: 900, // 15m = 900 seconds
        })
      );
    });

    it('should include token metadata in payload', () => {
      const mockToken = 'mock.jwt.token';
      (jwt.sign as jest.Mock).mockReturnValue(mockToken);

      tokenService.generateAccessToken('1', '<EMAIL>');

      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          type: TokenType.ACCESS,
          jti: expect.any(String),
        }),
        expect.any(String),
        expect.any(Object)
      );
    });

    it('should handle token generation errors', () => {
      (jwt.sign as jest.Mock).mockImplementation(() => {
        throw new Error('Token generation failed');
      });

      expect(() => tokenService.generateAccessToken('1', '<EMAIL>')).toThrow(
        'Token generation failed'
      );
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate valid refresh token', async () => {
      const mockToken = 'mock.refresh.token';
      const mockJti = 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa'; // 32 characters for 16 bytes hex
      (jwt.sign as jest.Mock).mockReturnValue(mockToken);

      mockPool.query.mockResolvedValue({
        rows: [],
      });

      const result = await tokenService.generateRefreshToken('1', '<EMAIL>');

      expect(result).toBe(`${mockToken}|${mockJti}`);
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: '1',
          email: '<EMAIL>',
          type: TokenType.REFRESH,
        }),
        expect.any(String),
        expect.objectContaining({
          expiresIn: 604800, // 7 days in seconds
        })
      );
    });

    it('should store refresh token in database', async () => {
      const mockToken = 'mock.refresh.token';
      (jwt.sign as jest.Mock).mockReturnValue(mockToken);

      mockPool.query.mockResolvedValue({
        rows: [],
      });

      await tokenService.generateRefreshToken('1', '<EMAIL>');

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO refresh_tokens'),
        expect.any(Array)
      );
    });

    it('should handle database errors during token storage', async () => {
      mockPool.query.mockRejectedValue(new Error('Database error'));

      await expect(tokenService.generateRefreshToken('1', '<EMAIL>')).rejects.toThrow(
        'Failed to generate refresh token'
      );
    });
  });

  describe('verifyToken', () => {
    it('should verify access token successfully', async () => {
      const mockToken = 'valid.jwt.token';
      const mockPayload = {
        userId: '1',
        email: '<EMAIL>',
        type: TokenType.ACCESS,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 900,
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);

      const result = await tokenService.verifyToken(mockToken, TokenType.ACCESS);

      expect(result).toEqual(mockPayload);
      expect(jwt.verify).toHaveBeenCalledWith(
        mockToken,
        expect.any(String),
        expect.objectContaining({
          audience: 'spheroseg-api',
          issuer: 'spheroseg-auth',
        })
      );
    });

    it('should reject invalid token type', async () => {
      const mockToken = 'valid.jwt.token';
      const mockPayload = {
        userId: '1',
        email: '<EMAIL>',
        type: TokenType.REFRESH,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 900,
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);

      await expect(tokenService.verifyToken(mockToken, TokenType.ACCESS)).rejects.toThrow(
        'Invalid token type'
      );
    });

    it('should handle expired tokens', async () => {
      const mockToken = 'expired.jwt.token';

      (jwt.verify as jest.Mock).mockImplementation(() => {
        const error = new Error('Token expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      await expect(tokenService.verifyToken(mockToken, TokenType.ACCESS)).rejects.toThrow(
        'Token expired'
      );
    });

    it('should handle malformed tokens', async () => {
      const mockToken = 'malformed.token';

      (jwt.verify as jest.Mock).mockImplementation(() => {
        const error = new Error('Malformed token');
        error.name = 'JsonWebTokenError';
        throw error;
      });

      await expect(tokenService.verifyToken(mockToken, TokenType.ACCESS)).rejects.toThrow(
        'Malformed token'
      );
    });
  });

  describe('rotateRefreshToken', () => {
    it('should successfully rotate valid refresh token', async () => {
      const mockJwtPart = 'valid.jwt.part';
      const mockJti = 'refresh-jti';
      const mockRefreshToken = `${mockJwtPart}.${mockJti}`;
      const mockPayload = {
        userId: '1',
        email: '<EMAIL>',
        type: TokenType.REFRESH,
        fid: 'family123',
        device: 'device123',
        jti: mockJti,
      };

      const mockNewRefreshToken = 'new.refresh.token';

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);

      // Mock database queries
      mockPool.query
        .mockResolvedValueOnce({
          rows: [
            {
              is_revoked: false,
              token_id: mockJti,
              family_id: 'family123',
              device_id: 'device123',
              user_id: '1',
              token_count: '2',
            },
          ],
        }) // verifyRefreshToken query
        .mockResolvedValueOnce({ rows: [] }) // Invalidate old token
        .mockResolvedValueOnce({ rows: [{ count: '2' }] }) // Count tokens in family
        .mockResolvedValueOnce({ rows: [] }); // Store new token

      (jwt.sign as jest.Mock).mockReturnValue(mockNewRefreshToken);

      const result = await tokenService.rotateRefreshToken(
        mockRefreshToken,
        '1',
        '<EMAIL>'
      );

      expect(result).toBe(`${mockNewRefreshToken}|aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa`);
    });

    it('should reject refresh token reuse attack', async () => {
      const mockJwtPart = 'reused.jwt.part';
      const mockJti = 'refresh-jti';
      const mockRefreshToken = `${mockJwtPart}.${mockJti}`;
      const mockPayload = {
        userId: '1',
        type: TokenType.REFRESH,
        fid: 'family123',
        device: 'device123',
        jti: mockJti,
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);

      // Mock token already used
      mockPool.query.mockResolvedValueOnce({
        rows: [
          {
            is_revoked: true,
            token_id: mockJti,
            family_id: 'family123',
            device_id: 'device123',
            user_id: '1',
            token_count: '2',
          },
        ],
      }); // Token is revoked

      await expect(
        tokenService.rotateRefreshToken(mockRefreshToken, '1', '<EMAIL>')
      ).rejects.toThrow('Failed to rotate refresh token');
    });

    it('should handle non-existent user during refresh', async () => {
      const mockRefreshToken = 'valid.refresh.token';
      const mockPayload = {
        userId: '999',
        type: TokenType.REFRESH,
        familyId: 'family123',
        jti: 'refresh-jti',
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);

      mockPool.query
        .mockResolvedValueOnce({ rows: [{ is_revoked: false }] }) // Token is valid
        .mockResolvedValueOnce({ rows: [] }); // User not found

      await expect(tokenService.rotateRefreshToken(mockRefreshToken)).rejects.toThrow();
    });
  });

  describe('revokeAllUserTokens', () => {
    it('should revoke all tokens for user', async () => {
      const userId = '1';

      mockPool.query.mockResolvedValue({ rowCount: 3 });

      await tokenService.revokeAllUserTokens(userId);

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE refresh_tokens SET revoked_at = NOW()'),
        expect.arrayContaining([userId])
      );
    });
  });

  describe('Security Features', () => {
    it('should use secure token generation', () => {
      tokenService.generateAccessToken('1', '<EMAIL>');

      // Verify secure random bytes for JTI
      expect(crypto.randomBytes).toHaveBeenCalledWith(16);
    });

    it('should properly invalidate token families on reuse', async () => {
      const mockJwtPart = 'reused.jwt';
      const mockJti = 'token-jti';
      const mockRefreshToken = `${mockJwtPart}.${mockJti}`;
      const mockPayload = {
        userId: '1',
        type: TokenType.REFRESH,
        fid: 'family123',
        device: 'device123',
        jti: mockJti,
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);
      mockPool.query.mockResolvedValueOnce({
        rows: [
          {
            is_revoked: true,
            token_id: mockJti,
            family_id: 'family123',
            device_id: 'device123',
            user_id: '1',
            token_count: '2',
          },
        ],
      }); // Token already used

      await expect(
        tokenService.rotateRefreshToken(mockRefreshToken, '1', '<EMAIL>')
      ).rejects.toThrow();
    });

    it('should enforce token expiration', async () => {
      const expiredToken = 'expired.token';

      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw expiredError;
      });

      await expect(tokenService.verifyToken(expiredToken, TokenType.ACCESS)).rejects.toThrow(
        'Token expired'
      );
    });
  });

  describe('Error Handling', () => {
    it('should log security events', async () => {
      const mockJwtPart = 'reused.jwt';
      const mockJti = 'token-jti';
      const mockRefreshToken = `${mockJwtPart}.${mockJti}`;
      const mockPayload = {
        userId: '1',
        type: TokenType.REFRESH,
        fid: 'family123',
        device: 'device123',
        jti: mockJti,
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockPayload);
      mockPool.query.mockResolvedValueOnce({
        rows: [
          {
            is_revoked: true,
            token_id: mockJti,
            family_id: 'family123',
            device_id: 'device123',
            user_id: '1',
            token_count: '2',
          },
        ],
      });

      await expect(
        tokenService.rotateRefreshToken(mockRefreshToken, '1', '<EMAIL>')
      ).rejects.toThrow();

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Refresh token has been revoked'),
        expect.any(Object)
      );
    });

    it('should handle database connection failures', async () => {
      mockPool.query.mockRejectedValue(new Error('Connection failed'));

      await expect(tokenService.generateRefreshToken('1', '<EMAIL>')).rejects.toThrow(
        'Failed to generate refresh token'
      );

      expect(mockLogger.error).toHaveBeenCalledWith(expect.any(String), expect.any(Object));
    });
  });
});
