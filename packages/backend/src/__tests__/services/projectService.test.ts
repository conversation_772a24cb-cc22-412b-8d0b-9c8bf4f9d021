/**
 * Tests for the Project Service
 */
// import { ApiError } from '../../utils/errors'; // Currently unused

// Create mock functions
const mockMkdir = jest.fn();
const mockExistsSync = jest.fn();
const mockMkdirSync = jest.fn();
const mockRmSync = jest.fn();

// Mock the file system operations
jest.mock('fs', () => ({
  existsSync: mockExistsSync,
  mkdirSync: mockMkdirSync,
  rmSync: mockRmSync,
  mkdir: mockMkdir,
}));

// Mock fs/promises separately - must be hoisted
jest.mock('fs/promises', () => ({
  mkdir: jest.fn().mockResolvedValue(undefined),
  rmdir: jest.fn().mockResolvedValue(undefined),
  rm: jest.fn().mockResolvedValue(undefined),
}));

// Mock the database pool and client
const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

const mockPool = {
  connect: jest.fn().mockResolvedValue(mockClient),
  query: jest.fn(),
  end: jest.fn(),
  on: jest.fn(),
};

jest.mock('pg', () => ({
  Pool: jest.fn(() => mockPool),
}));

// Mock logger
jest.mock('../../utils/logger', () => {
  const mockLogger = {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
    http: jest.fn(),
    silly: jest.fn(),
  };

  return {
    __esModule: true,
    default: mockLogger,
    createLogger: jest.fn().mockReturnValue(mockLogger),
  };
});

// Mock cache service
jest.mock('../cacheService', () => ({
  __esModule: true,
  default: {
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue(true),
    invalidateProjectCache: jest.fn(),
    invalidateProject: jest.fn().mockResolvedValue(true),
    cacheProject: jest.fn().mockResolvedValue(true),
    cacheUserProjects: jest.fn(),
    getCachedUserProjects: jest.fn().mockResolvedValue(null),
    delPattern: jest.fn().mockResolvedValue(true),
  },
}));

// Mock config
jest.mock('../../config', () => ({
  __esModule: true,
  default: {
    storage: {
      uploadDir: '/tmp/uploads',
    },
  },
}));

// Mock file cleanup service
jest.mock('../fileCleanupService', () => ({
  __esModule: true,
  default: {
    cleanupProjectFiles: jest
      .fn()
      .mockResolvedValue({ success: true, deletedFiles: [], failedFiles: [] }),
  },
}));

describe('Project Service', () => {
  let projectService: any;

  beforeEach(async () => {
    // Import service after mocks are set
    const module = await import('../projectService');
    projectService = module.default;
    jest.clearAllMocks();

    // Reset mock implementations
    mockMkdir.mockImplementation((path, options, callback) => {
      // Support both callback and promise-based usage
      if (typeof options === 'function') {
        callback = options;
        options = undefined;
      }
      if (callback) {
        callback(null);
      }
      return Promise.resolve();
    });

    mockExistsSync.mockReturnValue(false);
    mockMkdirSync.mockImplementation(() => {});
    mockRmSync.mockImplementation(() => {});

    // Reset client mock
    mockClient.query.mockReset();
    mockClient.release.mockReset();
    mockPool.connect.mockResolvedValue(mockClient);

    // Reset fs/promises mocks
    const fs = require('fs/promises');
    fs.mkdir.mockClear();
    fs.mkdir.mockResolvedValue(undefined);
    fs.rmdir.mockClear();
    fs.rm.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createProject', () => {
    const mockUserId = 'user123';
    const mockProjectData = {
      title: 'Test Project',
      description: 'Test Description',
      tags: ['tag1', 'tag2'],
      public: false,
    };

    it('should create a new project in the database and create project directories', async () => {
      const mockProjectId = 'project123';
      const mockCreatedProject = {
        id: mockProjectId,
        ...mockProjectData,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock UUID generation
      jest.spyOn(Math, 'random').mockReturnValue(0.5);

      // Mock database queries
      mockClient.query.mockImplementation((query: string) => {
        if (query === 'BEGIN') {
          return {};
        }

        // Check for existing project with same title
        if (query.includes('SELECT id FROM projects WHERE user_id = $1 AND title = $2')) {
          return { rows: [] }; // No existing project
        }

        // Check for tags column
        if (
          query.includes('information_schema.columns') &&
          query.includes("column_name = 'tags'")
        ) {
          return { rows: [{ exists: true }] };
        }

        // Check for public column
        if (
          query.includes('information_schema.columns') &&
          query.includes("column_name = 'is_public'")
        ) {
          return { rows: [{ exists: true }] };
        }

        if (query.includes('INSERT INTO projects')) {
          return { rows: [mockCreatedProject] };
        }

        if (query === 'COMMIT') {
          return {};
        }

        return { rows: [] };
      });

      // Call the service with pool as first parameter
      const result = await projectService.createProject(mockPool as any, {
        ...mockProjectData,
        userId: mockUserId,
      });

      // Verify the result
      expect(result).toEqual({
        ...mockCreatedProject,
        is_owner: true,
      });

      // Check that project directories were created
      const fs = require('fs/promises');
      expect(fs.mkdir).toHaveBeenCalledTimes(5);
      expect(fs.mkdir).toHaveBeenCalledWith(expect.stringContaining(mockProjectId), {
        recursive: true,
      });
    });

    it('should throw an ApiError if a project with the same title already exists', async () => {
      // Mock database query to simulate duplicate title
      mockClient.query.mockImplementation((query: string) => {
        if (query === 'BEGIN') {
          return {};
        }
        // Check for existing project - return one exists
        if (query.includes('SELECT id FROM projects WHERE user_id = $1 AND title = $2')) {
          return { rows: [{ id: 'existing-project-id' }] };
        }
        if (query === 'ROLLBACK') {
          return {};
        }
        return { rows: [] };
      });

      // Expect the service to throw an ApiError
      await expect(
        projectService.createProject(mockPool as any, {
          ...mockProjectData,
          userId: mockUserId,
        })
      ).rejects.toMatchObject({
        message: 'A project with the title "Test Project" already exists',
        statusCode: 409,
      });
    });

    it('should rollback transaction if directory creation fails', async () => {
      const mockProjectId = 'project123';
      const mockCreatedProject = {
        id: mockProjectId,
        ...mockProjectData,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database queries
      mockClient.query.mockImplementation((query: string) => {
        if (query.includes('INSERT INTO projects')) {
          return { rows: [mockCreatedProject] };
        }
        if (query.includes('BEGIN')) {
          return {};
        }
        if (query.includes('ROLLBACK')) {
          return {};
        }
        return { rows: [] };
      });

      // Mock fs operations to fail
      const fs = require('fs/promises');
      fs.mkdir.mockRejectedValueOnce(new Error('Directory creation failed'));

      // Expect the service to throw an ApiError
      await expect(
        projectService.createProject(mockPool as any, {
          ...mockProjectData,
          userId: mockUserId,
        })
      ).rejects.toThrow('Failed to create project directories');

      // Verify rollback was called
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('getProjectById', () => {
    const mockUserId = 'user123';
    const mockProjectId = 'project123';

    it('should return a project owned by the user', async () => {
      const mockProject = {
        id: mockProjectId,
        title: 'Test Project',
        description: 'Test Description',
        user_id: mockUserId,
        is_owner: true,
        permission: null,
      };

      // Mock database queries
      mockPool.query.mockImplementation((query) => {
        if (query.includes('SELECT * FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [mockProject] });
        }
        return Promise.resolve({ rows: [] });
      });

      const result = await projectService.getProjectById(
        mockPool as any,
        mockProjectId,
        mockUserId
      );

      expect(result).toEqual(mockProject);
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM projects WHERE id = $1 AND user_id = $2'),
        [mockProjectId, mockUserId]
      );
    });

    it('should return a project shared with the user', async () => {
      const mockSharedProject = {
        id: mockProjectId,
        title: 'Shared Project',
        description: 'Shared Description',
        user_id: 'otheruser123',
        is_owner: false,
        permission: 'edit',
      };

      // Mock database queries
      mockPool.query.mockImplementation((query) => {
        if (query.includes('SELECT * FROM projects WHERE id = $1 AND user_id = $2')) {
          // Owner query returns empty
          return Promise.resolve({ rows: [] });
        }
        if (query.includes('SELECT p.*, ps.permission, false as is_owner')) {
          // Shared project query returns the shared project
          return Promise.resolve({ rows: [mockSharedProject] });
        }
        return Promise.resolve({ rows: [] });
      });

      const result = await projectService.getProjectById(
        mockPool as any,
        mockProjectId,
        mockUserId
      );

      expect(result).toEqual(mockSharedProject);
    });

    it('should return null if the project is not found or user has no access', async () => {
      // Mock database queries to return empty results
      mockPool.query.mockResolvedValue({ rows: [] });

      const result = await projectService.getProjectById(
        mockPool as any,
        mockProjectId,
        mockUserId
      );

      expect(result).toBeNull();
    });
  });

  describe('getUserProjects', () => {
    const mockUserId = 'user123';

    it('should return owned and shared projects with count', async () => {
      // Mock database queries
      mockPool.query.mockImplementation((query, _params) => {
        // Mock table existence check
        if (query.includes('information_schema.tables')) {
          return {
            rows: [{ images_exists: true, shares_exists: true }],
          };
        }

        // Mock the CTE query for getUserProjects
        if (query.includes('WITH user_projects AS')) {
          return {
            rows: [
              {
                id: 'project1',
                title: 'Owned Project',
                user_id: mockUserId,
                is_owner: true,
                permission: null,
                image_count: 5,
                thumbnail_url: null,
              },
              {
                id: 'project2',
                title: 'Shared Project',
                user_id: 'otheruser',
                is_owner: false,
                permission: 'view',
                image_count: 0,
                thumbnail_url: null,
              },
            ],
          };
        }

        // Mock the count query
        if (query.includes('WITH project_counts AS')) {
          return { rows: [{ total: 2 }] };
        }

        return { rows: [] };
      });

      const result = await projectService.getUserProjects(
        mockPool as any,
        mockUserId,
        10, // limit
        0, // offset
        true // includeShared
      );

      expect(result.projects).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.projects[0].is_owner).toBe(true);
      expect(result.projects[1].is_owner).toBe(false);
    }, 15000); // Increase timeout

    it('should return only owned projects when includeShared is false', async () => {
      // Mock database queries
      mockPool.query.mockImplementation((query, _params) => {
        // Mock table existence check
        if (query.includes('information_schema.tables')) {
          return {
            rows: [{ images_exists: true, shares_exists: true }],
          };
        }

        // Mock the CTE query for getUserProjects with includeShared=false
        if (query.includes('WITH user_projects AS')) {
          return {
            rows: [
              {
                id: 'project1',
                title: 'Owned Project',
                user_id: mockUserId,
                is_owner: true,
                permission: null,
                image_count: 5,
                thumbnail_url: null,
              },
            ],
          };
        }

        // Mock the count query
        if (query.includes('WITH project_counts AS')) {
          return { rows: [{ total: 1 }] };
        }

        return { rows: [] };
      });

      const result = await projectService.getUserProjects(
        mockPool as any,
        mockUserId,
        10, // limit
        0, // offset
        false // includeShared
      );

      // Check results contain only owned project
      expect(result.projects).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.projects[0].is_owner).toBe(true);
    }, 15000); // Increase timeout
  });

  describe('updateProject', () => {
    const mockUserId = 'user123';
    const mockProjectId = 'project123';

    it('should update project title and description', async () => {
      const mockUpdates = {
        title: 'Updated Title',
        description: 'Updated Description',
      };

      const mockUpdatedProject = {
        id: mockProjectId,
        title: mockUpdates.title,
        description: mockUpdates.description,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database queries
      mockPool.query.mockImplementation((query, _params) => {
        // Check if user owns the project
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [{ id: mockProjectId }] });
        }
        // Check if title already exists for another project
        if (
          query.includes('SELECT id FROM projects WHERE user_id = $1 AND title = $2 AND id != $3')
        ) {
          return Promise.resolve({ rows: [] }); // No conflict
        }
        // Update query
        if (query.includes('UPDATE projects')) {
          return Promise.resolve({ rows: [mockUpdatedProject] });
        }
        return Promise.resolve({ rows: [] });
      });

      const result = await projectService.updateProject(
        mockPool as any,
        mockProjectId,
        mockUserId,
        mockUpdates
      );

      expect(result).toEqual({
        ...mockUpdatedProject,
        is_owner: true,
      });

      // Verify the queries were called correctly
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id FROM projects WHERE id = $1 AND user_id = $2'),
        [mockProjectId, mockUserId]
      );
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE projects'),
        expect.arrayContaining([mockProjectId, mockUpdates.title, mockUpdates.description])
      );
    });

    it('should update only title when description is not provided', async () => {
      const mockUpdates = {
        title: 'Updated Title Only',
      };

      const mockUpdatedProject = {
        id: mockProjectId,
        title: mockUpdates.title,
        description: 'Original Description',
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database queries
      mockPool.query.mockImplementation((query, _params) => {
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [{ id: mockProjectId }] });
        }
        if (
          query.includes('SELECT id FROM projects WHERE user_id = $1 AND title = $2 AND id != $3')
        ) {
          return Promise.resolve({ rows: [] });
        }
        if (query.includes('UPDATE projects')) {
          return Promise.resolve({ rows: [mockUpdatedProject] });
        }
        return Promise.resolve({ rows: [] });
      });

      const result = await projectService.updateProject(
        mockPool as any,
        mockProjectId,
        mockUserId,
        mockUpdates
      );

      expect(result.title).toBe(mockUpdates.title);
      expect(result.description).toBe('Original Description');
    });

    it('should update only description when title is not provided', async () => {
      const mockUpdates = {
        description: 'Updated Description Only',
      };

      const mockUpdatedProject = {
        id: mockProjectId,
        title: 'Original Title',
        description: mockUpdates.description,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database queries
      mockPool.query.mockImplementation((query, _params) => {
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [{ id: mockProjectId }] });
        }
        if (query.includes('UPDATE projects')) {
          return Promise.resolve({ rows: [mockUpdatedProject] });
        }
        return Promise.resolve({ rows: [] });
      });

      const result = await projectService.updateProject(
        mockPool as any,
        mockProjectId,
        mockUserId,
        mockUpdates
      );

      expect(result.title).toBe('Original Title');
      expect(result.description).toBe(mockUpdates.description);
    });

    it('should throw error if project not found or user has no permission', async () => {
      // Mock database to return no project
      mockPool.query.mockImplementation((query) => {
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [] }); // No project found
        }
        return Promise.resolve({ rows: [] });
      });

      await expect(
        projectService.updateProject(mockPool as any, mockProjectId, mockUserId, {
          title: 'New Title',
        })
      ).rejects.toMatchObject({
        message: 'Project not found or you do not have permission to update it',
        statusCode: 404,
      });
    });

    it('should throw error if title already exists for another project', async () => {
      const duplicateTitle = 'Duplicate Title';

      // Mock database queries
      mockPool.query.mockImplementation((query) => {
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [{ id: mockProjectId }] });
        }
        if (
          query.includes('SELECT id FROM projects WHERE user_id = $1 AND title = $2 AND id != $3')
        ) {
          return Promise.resolve({ rows: [{ id: 'other-project-id' }] }); // Title exists
        }
        return Promise.resolve({ rows: [] });
      });

      await expect(
        projectService.updateProject(mockPool as any, mockProjectId, mockUserId, {
          title: duplicateTitle,
        })
      ).rejects.toMatchObject({
        message: `A project with the title "${duplicateTitle}" already exists`,
        statusCode: 409,
      });
    });

    it('should throw error if no valid update fields provided', async () => {
      // Mock database queries
      mockPool.query.mockImplementation((query) => {
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return Promise.resolve({ rows: [{ id: mockProjectId }] });
        }
        return Promise.resolve({ rows: [] });
      });

      await expect(
        projectService.updateProject(
          mockPool as any,
          mockProjectId,
          mockUserId,
          {} // Empty updates object
        )
      ).rejects.toMatchObject({
        message: 'No valid update fields provided',
        statusCode: 400,
      });
    });
  });

  describe('deleteProject', () => {
    const mockUserId = 'user123';
    const mockProjectId = 'project123';

    it('should delete a project and its associated resources', async () => {
      // Mock pool.query for the initial check
      mockPool.query.mockResolvedValue({ rows: [{ id: mockProjectId }] });

      // Mock all pool queries that fileCleanupService might call
      mockPool.query.mockImplementation((_query: string) => {
        // For any queries on the pool itself (not inside transaction)
        return { rows: [] };
      });

      // Mock database queries in transaction
      mockClient.query.mockImplementation((query: string) => {
        if (query === 'BEGIN') {
          return {};
        }
        // First check if project exists
        if (query.includes('SELECT id, user_id FROM projects WHERE id = $1')) {
          return { rows: [{ id: mockProjectId, user_id: mockUserId }] };
        }
        // Then check if user owns it
        if (query.includes('SELECT id FROM projects WHERE id = $1 AND user_id = $2')) {
          return { rows: [{ id: mockProjectId }] };
        }
        // Handle images query
        if (query.includes('SELECT id FROM images WHERE project_id')) {
          return { rows: [] };
        }
        // Handle delete query
        if (query.includes('DELETE FROM projects WHERE id = $1 AND user_id = $2 RETURNING id')) {
          return { rowCount: 1, rows: [{ id: mockProjectId }] };
        }
        if (query === 'COMMIT') {
          return {};
        }
        return { rows: [] };
      });

      let result;
      try {
        result = await projectService.deleteProject(mockPool as any, mockProjectId, mockUserId);
      } catch (error) {
        console.error('Delete project error:', error);
        throw error;
      }
      expect(result).toBe(true);

      // Verify database operations
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM projects'),
        expect.any(Array)
      );
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');

      // Verify directory deletion was not called with fs but with file cleanup service
      const fileCleanupService = require('../fileCleanupService').default;
      expect(fileCleanupService.cleanupProjectFiles).toHaveBeenCalledWith(mockPool, mockProjectId);
    });

    it('should throw an ApiError if the project is not found or user has no access', async () => {
      // Mock database queries to simulate project not found
      mockClient.query.mockImplementation((query: string) => {
        if (query === 'BEGIN') {
          return {};
        }
        // Project not found
        if (query.includes('SELECT id, user_id FROM projects WHERE id = $1')) {
          return { rows: [] };
        }
        return { rows: [] };
      });

      await expect(
        projectService.deleteProject(mockPool as any, mockProjectId, mockUserId)
      ).rejects.toMatchObject({
        message: 'Project not found',
        statusCode: 404,
      });
    });
  });
});
