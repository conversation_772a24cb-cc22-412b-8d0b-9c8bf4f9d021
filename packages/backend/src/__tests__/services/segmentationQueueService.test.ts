/**
 * Segmentation Queue Service Test Suite
 *
 * Tests for the segmentation queue service that manages ML processing tasks.
 * Covers task management, queue processing, status updates, and error handling.
 */

// Mock winston-daily-rotate-file before any imports
jest.mock('winston-daily-rotate-file', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
  }));
});

// Mock the monitoring module
jest.mock('../../monitoring/unified', () => ({
  UnifiedMonitoringSystem: {
    getInstance: jest.fn().mockReturnValue({
      recordMetric: jest.fn(),
      incrementCounter: jest.fn(),
      trackCacheOperation: jest.fn(),
    }),
  },
  monitorQuery: jest.fn().mockImplementation((fn) => fn()),
}));

// Mock config at module level to ensure it's available when segmentationQueueService loads
const mockConfig = {
  ml: {
    serviceUrl: 'http://ml:5002',
    maxRetries: 3,
    retryDelay: 5000,
    maxConcurrentTasks: 2,
    healthCheckInterval: 60000,
    queueUpdateInterval: 5000,
  },
  rabbitmq: {
    url: 'amqp://rabbitmq:rabbitmq@rabbitmq:5672',
    queue: 'segmentation_tasks',
  },
};

jest.mock('../../config', () => ({
  default: mockConfig,
}));

// Mock socket.io
jest.mock('../../socket', () => ({
  getIO: jest.fn().mockReturnValue({
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
  }),
}));

// Mock axios
jest.mock('axios');

// Mock amqplib
jest.mock('amqplib', () => ({
  connect: jest.fn(),
}));

// Mock database
jest.mock('../../db');

// Mock logger
jest.mock('../../utils/logger');

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-123'),
}));

import db from '../../db';
import axios from 'axios';
import * as amqp from 'amqplib';
import logger from '../../utils/logger';
import { getIO } from '../../socket';
import { v4 as uuidv4 } from 'uuid';

// Type the mocks
const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedAmqp = amqp as jest.Mocked<typeof amqp>;
const mockedDb = db as jest.Mocked<typeof db>;
const mockedGetIO = getIO as jest.Mock;
const _mockedUuid = uuidv4 as jest.MockedFunction<typeof uuidv4>;

// Mock database client
const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

const mockPool = {
  connect: jest.fn().mockResolvedValue(mockClient),
  query: jest.fn(),
};

// Mock RabbitMQ connection and channel
const mockChannel = {
  assertQueue: jest.fn(),
  sendToQueue: jest.fn(),
  consume: jest.fn(),
  ack: jest.fn(),
  nack: jest.fn(),
  close: jest.fn(),
};

const mockConnection = {
  createChannel: jest.fn().mockResolvedValue(mockChannel),
  close: jest.fn(),
  on: jest.fn(),
};

describe('SegmentationQueueService', () => {
  let service: SegmentationQueueService;
  let mockIO: any;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();

    // Setup mocks
    mockIO = {
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
    };
    mockedGetIO.mockReturnValue(mockIO);

    // Setup database mock
    mockedDb.getPool.mockReturnValue(mockPool as any);
    mockClient.query.mockReset();
    mockClient.release.mockReset();
    mockPool.query.mockReset();

    // Setup RabbitMQ mock
    mockedAmqp.connect.mockResolvedValue(mockConnection as any);

    // Setup logger mocks
    logger.info = jest.fn();
    logger.error = jest.fn();
    logger.warn = jest.fn();
    logger.debug = jest.fn();

    // Create new service instance
    service = new SegmentationQueueService();
  });

  afterEach(() => {
    jest.useRealTimers();
    // Stop any intervals
    service.stop();
  });

  describe('init', () => {
    it('should initialize service successfully', async () => {
      // Arrange
      mockPool.query.mockResolvedValueOnce({ rows: [] }); // Table check
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });

      // Act
      await service.init();

      // Assert
      expect(logger.info).toHaveBeenCalledWith('Segmentation queue service initialized');
      expect(mockedAmqp.connect).toHaveBeenCalled();
      expect(mockChannel.assertQueue).toHaveBeenCalledWith('segmentation_tasks', {
        durable: true,
        arguments: {
          'x-max-priority': 10,
        },
      });
    });

    it('should create tasks table if not exists', async () => {
      // Arrange
      mockPool.query
        .mockResolvedValueOnce({
          rows: [{ to_regclass: null }], // Table doesn't exist
        })
        .mockResolvedValueOnce({ rows: [] }); // CREATE TABLE

      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });

      // Act
      await service.init();

      // Assert
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS segmentation_tasks')
      );
    });

    it('should handle RabbitMQ connection failure', async () => {
      // Arrange
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAmqp.connect.mockRejectedValueOnce(new Error('Connection failed'));
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });

      // Act
      await service.init();

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to connect to RabbitMQ',
        expect.any(Object)
      );
    });
  });

  describe('addTask', () => {
    beforeEach(async () => {
      // Initialize service
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });
      await service.init();
    });

    it('should add task successfully', async () => {
      // Arrange
      const imageId = 'image-123';
      const imagePath = '/path/to/image.jpg';
      const parameters = { model: 'default', threshold: 0.5 };
      const priority = TaskPriority.NORMAL;

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing task
        .mockResolvedValueOnce({
          rows: [
            {
              id: 'test-uuid-123',
              image_id: imageId,
              image_path: imagePath,
              parameters,
              priority,
              status: TaskStatus.QUEUED,
              created_at: new Date(),
            },
          ],
        }); // INSERT task

      mockChannel.sendToQueue.mockReturnValue(true);

      // Act
      const result = await service.addTask(imageId, imagePath, parameters, priority);

      // Assert
      expect(result).toEqual({
        id: 'test-uuid-123',
        imageId,
        imagePath,
        parameters,
        priority,
        status: TaskStatus.QUEUED,
      });
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO segmentation_tasks'),
        expect.arrayContaining([
          'test-uuid-123',
          imageId,
          imagePath,
          JSON.stringify(parameters),
          priority,
          TaskStatus.QUEUED,
        ])
      );
      expect(mockChannel.sendToQueue).toHaveBeenCalled();
      expect(mockIO.emit).toHaveBeenCalledWith('segmentation:queued', {
        imageId,
        status: TaskStatus.QUEUED,
      });
    });

    it('should handle existing task', async () => {
      // Arrange
      const imageId = 'image-123';
      mockClient.query.mockResolvedValueOnce({
        rows: [
          {
            id: 'existing-task',
            status: TaskStatus.PROCESSING,
          },
        ],
      });

      // Act
      const result = await service.addTask(imageId, '/path/to/image.jpg');

      // Assert
      expect(result).toEqual({
        id: 'existing-task',
        status: TaskStatus.PROCESSING,
      });
      expect(logger.info).toHaveBeenCalledWith('Task already exists for image', expect.any(Object));
    });

    it('should handle database error', async () => {
      // Arrange
      const imageId = 'image-123';
      mockClient.query
        .mockResolvedValueOnce({ rows: [] })
        .mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(service.addTask(imageId, '/path/to/image.jpg')).rejects.toThrow(
        'Failed to add task to queue'
      );
      expect(mockClient.release).toHaveBeenCalled();
    });
  });

  describe('cancelTask', () => {
    beforeEach(async () => {
      // Initialize service
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });
      await service.init();
    });

    it('should cancel queued task successfully', async () => {
      // Arrange
      const imageId = 'image-123';
      mockClient.query
        .mockResolvedValueOnce({
          rows: [
            {
              id: 'task-123',
              status: TaskStatus.QUEUED,
            },
          ],
        }) // Find task
        .mockResolvedValueOnce({ rowCount: 1 }); // UPDATE task

      // Act
      const result = await service.cancelTask(imageId);

      // Assert
      expect(result).toBe(true);
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE segmentation_tasks SET status = $1'),
        [TaskStatus.CANCELLED, 'task-123']
      );
      expect(mockIO.emit).toHaveBeenCalledWith('segmentation:cancelled', {
        imageId,
        status: TaskStatus.CANCELLED,
      });
    });

    it('should not cancel processing task', async () => {
      // Arrange
      const imageId = 'image-123';
      mockClient.query.mockResolvedValueOnce({
        rows: [
          {
            id: 'task-123',
            status: TaskStatus.PROCESSING,
          },
        ],
      });

      // Act
      const result = await service.cancelTask(imageId);

      // Assert
      expect(result).toBe(false);
      expect(logger.warn).toHaveBeenCalledWith(
        'Cannot cancel task in processing state',
        expect.any(Object)
      );
    });

    it('should handle non-existent task', async () => {
      // Arrange
      const imageId = 'image-123';
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act
      const result = await service.cancelTask(imageId);

      // Assert
      expect(result).toBe(false);
      expect(logger.warn).toHaveBeenCalledWith('Task not found for cancellation', { imageId });
    });
  });

  describe('getTaskStatus', () => {
    beforeEach(async () => {
      // Initialize service
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });
      await service.init();
    });

    it('should get task status successfully', async () => {
      // Arrange
      const taskId = 'task-123';
      const mockTask = {
        id: taskId,
        image_id: 'image-123',
        image_path: '/path/to/image.jpg',
        parameters: { model: 'default' },
        priority: TaskPriority.NORMAL,
        status: TaskStatus.COMPLETED,
        retries: 0,
        result: { polygons: [] },
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockClient.query.mockResolvedValueOnce({ rows: [mockTask] });

      // Act
      const result = await service.getTaskStatus(taskId);

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: taskId,
          imageId: 'image-123',
          status: TaskStatus.COMPLETED,
        })
      );
    });

    it('should return null for non-existent task', async () => {
      // Arrange
      const taskId = 'non-existent';
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act
      const result = await service.getTaskStatus(taskId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('ML service health check', () => {
    beforeEach(async () => {
      // Initialize service
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });
      await service.init();
      jest.clearAllMocks();
    });

    it('should detect ML service as online', async () => {
      // Arrange
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });

      // Act
      jest.advanceTimersByTime(60000); // Advance to health check interval
      await Promise.resolve(); // Let promises resolve

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://ml:5002/health',
        expect.objectContaining({ timeout: 5000 })
      );
      expect(logger.debug).toHaveBeenCalledWith('ML service is available');
    });

    it('should detect ML service as offline', async () => {
      // Arrange
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection refused'));

      // Act
      jest.advanceTimersByTime(60000);
      await Promise.resolve();

      // Assert
      expect(logger.warn).toHaveBeenCalledWith('ML service is not available', expect.any(Object));
    });
  });

  describe('getQueueStatus', () => {
    it('should return current queue status', async () => {
      // Arrange
      const expectedStatus = {
        pendingTasks: ['task-1', 'task-2'],
        runningTasks: ['task-3'],
        queueLength: 2,
        activeTasksCount: 1,
        mlServiceStatus: 'online' as const,
        lastUpdated: expect.any(Date),
      };

      // Set internal state
      (service as any).queueStatus = expectedStatus;

      // Act
      const result = service.getQueueStatus();

      // Assert
      expect(result).toEqual(expectedStatus);
    });
  });

  describe('stop', () => {
    it('should stop service and close connections', async () => {
      // Arrange
      mockPool.query.mockResolvedValueOnce({ rows: [] });
      mockedAxios.get.mockResolvedValueOnce({
        data: { status: 'healthy' },
        status: 200,
      });
      await service.init();

      // Act
      service.stop();

      // Assert
      expect(mockChannel.close).toHaveBeenCalled();
      expect(mockConnection.close).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Segmentation queue service stopped');
    });
  });
});
