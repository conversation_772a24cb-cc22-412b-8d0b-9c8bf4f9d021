/**
 * Authentication Service Test Suite
 *
 * Comprehensive tests for user authentication, registration, and session management.
 * Tests cover all critical authentication flows including edge cases and error scenarios.
 */

// Mock winston-daily-rotate-file before any imports
jest.mock('winston-daily-rotate-file', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
  }));
});

// Mock the monitoring module to avoid winston issues
jest.mock('../../monitoring/unified', () => ({
  UnifiedMonitoringSystem: {
    getInstance: jest.fn().mockReturnValue({
      recordMetric: jest.fn(),
      incrementCounter: jest.fn(),
      trackCacheOperation: jest.fn(),
    }),
  },
}));

// Mock config modules
jest.mock('../../config', () => ({
  default: {
    auth: {
      saltRounds: 10,
      jwtExpiresIn: '1h',
      refreshTokenExpiresIn: '7d',
    },
    database: {
      connectionString: 'mock-connection',
    },
  },
}));

jest.mock('../../config/security', () => ({
  securityConfig: {
    jwtSecret: 'test-jwt-secret',
    refreshTokenSecret: 'test-refresh-secret',
  },
}));

// Mock dependencies before imports
jest.mock('../../db', () => ({
  __esModule: true,
  default: {
    getPool: jest.fn(),
    query: jest.fn(),
    cachedQuery: jest.fn(),
    withTransaction: jest.fn(),
  },
  getPool: jest.fn(),
  query: jest.fn(),
  cachedQuery: jest.fn(),
  withTransaction: jest.fn(),
}));

jest.mock('bcryptjs');
jest.mock('uuid');
jest.mock('crypto');
jest.mock('../tokenService', () => ({
  __esModule: true,
  default: {
    createTokenResponse: jest.fn(),
    refreshToken: jest.fn(),
    revokeToken: jest.fn(),
    revokeAllUserTokens: jest.fn(),
  },
}));
jest.mock('../emailService');
jest.mock('../cacheService');
jest.mock('../../utils/logger');

import authService from '../authService';
import db from '../../db';
import bcryptjs from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import tokenService from '../tokenService';
import { sendNewPasswordEmail } from '../email/CentralizedEmailService';
import { cacheService } from '../cacheService';
import { ApiError } from '../../utils/errors';
import { ErrorCode } from '../../utils/ApiError';
import logger from '../../utils/logger';

// Create a factory function for mock clients
function createMockClient() {
  return {
    query: jest.fn(),
    release: jest.fn(),
  };
}

// Type the mocked modules
const mockedDb = db as jest.Mocked<typeof db>;
const mockedBcrypt = bcryptjs as jest.Mocked<typeof bcryptjs>;
const mockedUuid = uuidv4 as jest.MockedFunction<typeof uuidv4>;
const mockedTokenService = tokenService as jest.Mocked<typeof tokenService>;
const mockedCrypto = crypto as jest.Mocked<typeof crypto>;

describe('AuthService', () => {
  let mockClient: ReturnType<typeof createMockClient>;
  let mockPool: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create fresh mocks for each test
    mockClient = createMockClient();
    mockPool = {
      connect: jest.fn().mockResolvedValue(mockClient),
      query: jest.fn(),
      on: jest.fn(),
    };

    // Setup the db module mocks - db is the default export
    mockedDb.getPool = jest.fn().mockReturnValue(mockPool);
    mockedDb.query = jest.fn().mockImplementation((text, params) => mockPool.query(text, params));
    mockedDb.cachedQuery = jest
      .fn()
      .mockImplementation((text, params) => mockPool.query(text, params));
    mockedDb.withTransaction = jest.fn().mockImplementation(async (fn) => {
      const client = await mockPool.connect();
      try {
        await client.query('BEGIN');
        const result = await fn(client);
        await client.query('COMMIT');
        return result;
      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    });

    // Setup cacheService mocks
    (cacheService as any).getUserCache = jest.fn().mockResolvedValue(null);
    (cacheService as any).setUserCache = jest.fn().mockResolvedValue(true);
    (cacheService as any).invalidateRelated = jest.fn().mockResolvedValue(true);

    logger.info = jest.fn();
    logger.error = jest.fn();
    logger.warn = jest.fn();
    logger.debug = jest.fn();
  });

  describe('registerUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'SecurePassword123!';
    const mockName = 'Test User';
    const mockUserId = 'user-123';
    const mockHashedPassword = 'hashed-password';
    const mockTokenResponse = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      tokenType: 'Bearer',
    };

    beforeEach(() => {
      mockedUuid.mockReturnValue(mockUserId);
      mockedBcrypt.hash.mockResolvedValue(mockHashedPassword);
      mockedTokenService.createTokenResponse.mockResolvedValue(mockTokenResponse);
    });

    it('should successfully register a new user', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing user - not found
        .mockResolvedValueOnce({ rows: [] }) // BEGIN transaction
        .mockResolvedValueOnce({
          // INSERT user
          rows: [
            {
              id: mockUserId,
              email: mockEmail,
              name: mockName,
              created_at: new Date(),
            },
          ],
        })
        .mockResolvedValueOnce({ rows: [] }); // COMMIT transaction

      // Mock sendVerificationEmail as a method on authService
      jest.spyOn(authService, 'sendVerificationEmail').mockResolvedValue(undefined);

      // Act
      try {
        const result = await authService.registerUser(mockEmail, mockPassword, mockName);

        // Assert
        expect(result).toEqual({
          user: {
            id: mockUserId,
            email: mockEmail,
            name: mockName,
            created_at: expect.any(Date),
          },
          ...mockTokenResponse,
        });

        expect(mockClient.query).toHaveBeenCalledWith('SELECT id FROM users WHERE email = $1', [
          mockEmail,
        ]);
        expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
        expect(mockClient.query).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO users'),
          expect.arrayContaining([mockUserId, mockEmail, mockHashedPassword, mockName])
        );
        expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
        expect(authService.sendVerificationEmail).toHaveBeenCalledWith(mockEmail);
        expect(mockClient.release).toHaveBeenCalled();
      } catch (error) {
        // Log the actual error to understand what went wrong
        console.error('Test failed with error:', error);
        console.error('Logger error calls:', (logger.error as jest.Mock).mock.calls);
        console.error('mockClient.query calls:', mockClient.query.mock.calls);
        console.error(
          'tokenService.createTokenResponse:',
          mockedTokenService.createTokenResponse.mock.calls
        );
        throw error;
      }
    });

    it('should register user with preferred language', async () => {
      // Arrange
      const preferredLanguage = 'cs';
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing user
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({
          // INSERT user
          rows: [
            {
              id: mockUserId,
              email: mockEmail,
              name: mockName,
              created_at: new Date(),
            },
          ],
        })
        .mockResolvedValueOnce({ rows: [] }) // INSERT user_profiles
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      // Act
      const result = await authService.registerUser(
        mockEmail,
        mockPassword,
        mockName,
        preferredLanguage
      );

      // Assert
      expect(result.user.id).toBe(mockUserId);
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO user_profiles'),
        [mockUserId, preferredLanguage]
      );
    });

    it('should throw error if email already exists', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [{ id: 'existing-user' }] });

      // Act & Assert
      await expect(authService.registerUser(mockEmail, mockPassword)).rejects.toThrow(
        new ApiError('Email already in use', 409, ErrorCode.RESOURCE_CONFLICT)
      );
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should handle database connection failure', async () => {
      // Arrange
      mockPool.connect.mockRejectedValueOnce(new Error('Connection failed'));

      // Act & Assert
      await expect(authService.registerUser(mockEmail, mockPassword)).rejects.toThrow(
        'Database connection failed'
      );
    });

    it('should rollback transaction on error', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing user
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce(new Error('Insert failed')); // INSERT user fails

      // Act & Assert
      await expect(authService.registerUser(mockEmail, mockPassword)).rejects.toThrow(
        new ApiError('Failed to register user', 500, ErrorCode.INTERNAL_SERVER_ERROR)
      );
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should continue registration even if verification email fails', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({
          rows: [
            {
              id: mockUserId,
              email: mockEmail,
              name: mockName,
              created_at: new Date(),
            },
          ],
        })
        .mockResolvedValueOnce({ rows: [] });

      jest.spyOn(authService, 'sendVerificationEmail').mockRejectedValue(new Error('Email failed'));

      // Act
      const result = await authService.registerUser(mockEmail, mockPassword, mockName);

      // Assert
      expect(result.user.id).toBe(mockUserId);
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to send verification email after registration',
        expect.any(Object)
      );
    });
  });

  describe('authenticateUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'password123';
    const mockUserId = 'user-123';
    const mockHashedPassword = 'hashed-password';
    const mockUser = {
      id: mockUserId,
      email: mockEmail,
      password_hash: mockHashedPassword,
      name: 'Test User',
      created_at: new Date(),
      updated_at: new Date(),
    };

    it('should authenticate valid user credentials', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // SELECT user
        .mockResolvedValueOnce({ rows: [{ preferred_language: 'en' }] }); // SELECT profile

      mockedBcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toEqual({
        id: mockUserId,
        email: mockEmail,
        name: mockUser.name,
        created_at: mockUser.created_at,
        updated_at: mockUser.updated_at,
        preferred_language: 'en',
      });
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(mockPassword, mockHashedPassword);
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should return null for non-existent email', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toBeNull();
      expect(logger.warn).toHaveBeenCalledWith('Login attempt with non-existent email', {
        email: mockEmail,
      });
    });

    it('should return null for invalid password', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [mockUser] });
      mockedBcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toBeNull();
      expect(logger.warn).toHaveBeenCalledWith('Invalid password attempt', { email: mockEmail });
    });

    it('should handle user without profile', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] })
        .mockResolvedValueOnce({ rows: [] }); // No profile

      mockedBcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await authService.authenticateUser(mockEmail, mockPassword);

      // Assert
      expect(result).toEqual({
        id: mockUserId,
        email: mockEmail,
        name: mockUser.name,
        created_at: mockUser.created_at,
        updated_at: mockUser.updated_at,
      });
    });
  });

  describe('loginUser', () => {
    const mockEmail = '<EMAIL>';
    const mockPassword = 'password123';
    const mockUserId = 'user-123';
    const mockUser = {
      id: mockUserId,
      email: mockEmail,
      password_hash: 'hashed-password',
      name: 'Test User',
      created_at: new Date(),
      updated_at: new Date(),
    };
    const mockTokenResponse = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      tokenType: 'Bearer',
    };

    it('should successfully login user', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] })
        .mockResolvedValueOnce({ rows: [] });

      mockedBcrypt.compare.mockResolvedValue(true);
      mockedTokenService.createTokenResponse.mockResolvedValue(mockTokenResponse);

      // Act
      const result = await authService.loginUser(mockEmail, mockPassword, 'fingerprint123');

      // Assert
      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUserId,
          email: mockEmail,
          name: mockUser.name,
        }),
        ...mockTokenResponse,
      });
      expect(mockedTokenService.createTokenResponse).toHaveBeenCalledWith(
        mockUserId,
        mockEmail,
        'fingerprint123'
      );
    });

    it('should throw error for invalid credentials', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act & Assert
      await expect(authService.loginUser(mockEmail, mockPassword)).rejects.toThrow(
        new ApiError('Invalid email or password', 401, ErrorCode.INVALID_CREDENTIALS)
      );
    });
  });

  describe('refreshAccessToken', () => {
    const mockRefreshToken = 'refresh-token';
    const mockNewTokenResponse = {
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      tokenType: 'Bearer',
    };

    it('should refresh access token successfully', async () => {
      // Arrange
      mockedTokenService.refreshToken.mockResolvedValue(mockNewTokenResponse);

      // Act
      const result = await authService.refreshAccessToken(mockRefreshToken);

      // Assert
      expect(result).toEqual(mockNewTokenResponse);
      expect(mockedTokenService.refreshToken).toHaveBeenCalledWith(mockRefreshToken);
    });

    it('should handle token refresh failure', async () => {
      // Arrange
      mockedTokenService.refreshToken.mockRejectedValue(
        new ApiError('Invalid refresh token', 401, ErrorCode.INVALID_TOKEN)
      );

      // Act & Assert
      await expect(authService.refreshAccessToken(mockRefreshToken)).rejects.toThrow(
        new ApiError('Invalid refresh token', 401, ErrorCode.INVALID_TOKEN)
      );
    });
  });

  describe('forgotPassword', () => {
    const mockEmail = '<EMAIL>';
    const mockUserId = 'user-123';
    const mockResetToken = 'reset-token-123';

    beforeEach(() => {
      mockedCrypto.randomBytes.mockReturnValue({
        toString: jest.fn().mockReturnValue(mockResetToken),
      } as any);

      mockedCrypto.createHash.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('hashed-token'),
      } as any);
    });

    it('should handle forgot password for existing user', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: mockUserId }] }) // Find user
        .mockResolvedValueOnce({ rows: [] }); // Update user with reset token

      (sendNewPasswordEmail as jest.Mock).mockResolvedValue(undefined);

      // Act
      await authService.forgotPassword(mockEmail);

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET password_reset_token'),
        expect.arrayContaining(['hashed-token', expect.any(Date), mockUserId])
      );
      expect(sendNewPasswordEmail).toHaveBeenCalledWith(mockEmail, mockResetToken);
    });

    it('should silently handle non-existent email', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act
      await authService.forgotPassword(mockEmail);

      // Assert
      expect(logger.info).toHaveBeenCalledWith('Password reset requested for non-existent email', {
        email: mockEmail,
      });
      expect(sendNewPasswordEmail).not.toHaveBeenCalled();
    });

    it('should handle email service failure', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: mockUserId }] })
        .mockResolvedValueOnce({ rows: [] });

      (sendNewPasswordEmail as jest.Mock).mockRejectedValue(new Error('Email failed'));

      // Act & Assert
      await expect(authService.forgotPassword(mockEmail)).rejects.toThrow(
        new ApiError('Failed to send password reset email', 500, ErrorCode.EMAIL_SERVICE_ERROR)
      );
    });
  });

  describe('resetPassword', () => {
    const mockToken = 'reset-token';
    const mockNewPassword = 'NewSecurePassword123!';
    const mockUserId = 'user-123';
    const mockHashedPassword = 'new-hashed-password';

    beforeEach(() => {
      mockedCrypto.createHash.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('hashed-token'),
      } as any);

      mockedBcrypt.hash.mockResolvedValue(mockHashedPassword);
    });

    it('should reset password successfully', async () => {
      // Arrange
      const futureExpiry = new Date(Date.now() + 3600000); // 1 hour in future
      mockClient.query
        .mockResolvedValueOnce({
          rows: [
            {
              id: mockUserId,
              password_reset_expires: futureExpiry,
            },
          ],
        }) // Find user by token
        .mockResolvedValueOnce({ rows: [] }); // Update password

      // Act
      await authService.resetPassword(mockToken, mockNewPassword);

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET password_hash'),
        expect.arrayContaining([mockHashedPassword, mockUserId])
      );
      expect(mockedTokenService.revokeAllUserTokens).toHaveBeenCalledWith(mockUserId);
    });

    it('should throw error for invalid token', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act & Assert
      await expect(authService.resetPassword(mockToken, mockNewPassword)).rejects.toThrow(
        new ApiError('Invalid or expired reset token', 400, ErrorCode.INVALID_TOKEN)
      );
    });

    it('should throw error for expired token', async () => {
      // Arrange
      const pastExpiry = new Date(Date.now() - 3600000); // 1 hour in past
      mockClient.query.mockResolvedValueOnce({
        rows: [
          {
            id: mockUserId,
            password_reset_expires: pastExpiry,
          },
        ],
      });

      // Act & Assert
      await expect(authService.resetPassword(mockToken, mockNewPassword)).rejects.toThrow(
        new ApiError('Invalid or expired reset token', 400, ErrorCode.INVALID_TOKEN)
      );
    });
  });

  describe('logoutUser', () => {
    const mockRefreshToken = 'refresh-token';
    const mockUserId = 'user-123';

    it('should logout user with refresh token', async () => {
      // Act
      await authService.logoutUser(mockRefreshToken);

      // Assert
      expect(mockedTokenService.revokeToken).toHaveBeenCalledWith(mockRefreshToken);
    });

    it('should logout user with userId', async () => {
      // Act
      await authService.logoutUser(undefined, mockUserId);

      // Assert
      expect(mockedTokenService.revokeAllUserTokens).toHaveBeenCalledWith(mockUserId);
    });

    it('should clear user cache on logout', async () => {
      // Act
      await authService.logoutUser(undefined, mockUserId);

      // Assert
      expect(cacheService.invalidateRelated).toHaveBeenCalledWith('user', mockUserId);
    });

    it('should handle logout errors gracefully', async () => {
      // Arrange
      mockedTokenService.revokeToken.mockRejectedValue(new Error('Token error'));

      // Act & Assert
      await expect(authService.logoutUser(mockRefreshToken)).rejects.toThrow(
        new ApiError('Failed to logout', 500, ErrorCode.INTERNAL_SERVER_ERROR)
      );
    });
  });

  describe('changePassword', () => {
    const mockUserId = 'user-123';
    const mockCurrentPassword = 'currentPassword123';
    const mockNewPassword = 'newPassword123!';
    const mockHashedPassword = 'new-hashed-password';

    it('should change password successfully', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({
          rows: [{ password_hash: 'current-hash' }],
        }) // Get current password
        .mockResolvedValueOnce({ rows: [] }); // Update password

      mockedBcrypt.compare.mockResolvedValue(true);
      mockedBcrypt.hash.mockResolvedValue(mockHashedPassword);

      // Act
      await authService.changePassword(mockUserId, mockCurrentPassword, mockNewPassword);

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET password_hash'),
        [mockHashedPassword, mockUserId]
      );
      expect(mockedTokenService.revokeAllUserTokens).toHaveBeenCalledWith(mockUserId);
    });

    it('should throw error for incorrect current password', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({
        rows: [{ password_hash: 'current-hash' }],
      });
      mockedBcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(
        authService.changePassword(mockUserId, mockCurrentPassword, mockNewPassword)
      ).rejects.toThrow(
        new ApiError('Current password is incorrect', 401, ErrorCode.INVALID_CREDENTIALS)
      );
    });

    it('should throw error if user not found', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act & Assert
      await expect(
        authService.changePassword(mockUserId, mockCurrentPassword, mockNewPassword)
      ).rejects.toThrow(new ApiError('User not found', 404, ErrorCode.RESOURCE_NOT_FOUND));
    });
  });

  describe('deleteAccount', () => {
    const mockUserId = 'user-123';
    const mockUsername = 'testuser';
    const mockPassword = 'password123';

    it('should delete account successfully', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({
          rows: [
            {
              email: '<EMAIL>',
              password_hash: 'hashed-password',
            },
          ],
        }) // Get user
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // DELETE refresh_tokens
        .mockResolvedValueOnce({ rows: [] }) // DELETE user_profiles
        .mockResolvedValueOnce({ rows: [] }) // DELETE users
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      mockedBcrypt.compare.mockResolvedValue(true);

      // Act
      await authService.deleteAccount(mockUserId, mockUsername, mockPassword);

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        'DELETE FROM refresh_tokens WHERE user_id = $1',
        [mockUserId]
      );
      expect(mockClient.query).toHaveBeenCalledWith(
        'DELETE FROM user_profiles WHERE user_id = $1',
        [mockUserId]
      );
      expect(mockClient.query).toHaveBeenCalledWith('DELETE FROM users WHERE id = $1', [
        mockUserId,
      ]);
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(cacheService.invalidateRelated).toHaveBeenCalledWith('user', mockUserId);
    });

    it('should throw error for incorrect password', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({
        rows: [
          {
            email: '<EMAIL>',
            password_hash: 'hashed-password',
          },
        ],
      });
      mockedBcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(
        authService.deleteAccount(mockUserId, mockUsername, mockPassword)
      ).rejects.toThrow(new ApiError('Invalid password', 401, ErrorCode.INVALID_CREDENTIALS));
    });

    it('should rollback on deletion error', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({
          rows: [
            {
              email: '<EMAIL>',
              password_hash: 'hashed-password',
            },
          ],
        })
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce(new Error('Delete failed')); // DELETE fails

      mockedBcrypt.compare.mockResolvedValue(true);

      // Act & Assert
      await expect(
        authService.deleteAccount(mockUserId, mockUsername, mockPassword)
      ).rejects.toThrow(
        new ApiError('Failed to delete account', 500, ErrorCode.INTERNAL_SERVER_ERROR)
      );
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('verifyEmail', () => {
    const mockToken = 'verification-token';
    const mockUserId = 'user-123';

    beforeEach(() => {
      mockedCrypto.createHash.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('hashed-token'),
      } as any);
    });

    it('should verify email successfully', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({
          rows: [{ id: mockUserId }],
        }) // Find user by token
        .mockResolvedValueOnce({ rows: [] }); // Update user

      // Act
      await authService.verifyEmail(mockToken);

      // Assert
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET email_verified'),
        [mockUserId]
      );
    });

    it('should throw error for invalid verification token', async () => {
      // Arrange
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act & Assert
      await expect(authService.verifyEmail(mockToken)).rejects.toThrow(
        new ApiError('Invalid verification token', 400, ErrorCode.INVALID_TOKEN)
      );
    });
  });

  describe('checkEmailExists', () => {
    const mockEmail = '<EMAIL>';

    it('should return true if email exists', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ id: 'user-123' }] }) // Check user exists
        .mockResolvedValueOnce({ rows: [] }); // Check access request (no request)

      // Act
      const result = await authService.checkEmailExists(mockEmail);

      // Assert
      expect(result).toEqual({ exists: true, hasAccessRequest: false });
      expect(mockClient.query).toHaveBeenCalledWith('SELECT id FROM users WHERE email = $1', [
        mockEmail,
      ]);
    });

    it('should return false if email does not exist', async () => {
      // Arrange
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check user exists (no user)
        .mockResolvedValueOnce({ rows: [] }); // Check access request (no request)

      // Act
      const result = await authService.checkEmailExists(mockEmail);

      // Assert
      expect(result).toEqual({ exists: false, hasAccessRequest: false });
    });
  });

  describe('getCurrentUser', () => {
    const mockUserId = 'user-123';
    const mockUser = {
      id: mockUserId,
      email: '<EMAIL>',
      name: 'Test User',
      created_at: new Date(),
      updated_at: new Date(),
    };

    it('should get current user with cache', async () => {
      // Arrange
      const cachedUser = { ...mockUser, cached: true };
      cacheService.getUserCache = jest.fn().mockResolvedValue(cachedUser);

      // Act
      const result = await authService.getCurrentUser(mockUserId);

      // Assert
      expect(result).toEqual(cachedUser);
      expect(cacheService.getUserCache).toHaveBeenCalledWith(mockUserId);
      expect(mockClient.query).not.toHaveBeenCalled();
    });

    it('should get current user from database if not cached', async () => {
      // Arrange
      cacheService.getUserCache = jest.fn().mockResolvedValue(null);
      cacheService.setUserCache = jest.fn().mockResolvedValue(true);

      mockClient.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // Get user
        .mockResolvedValueOnce({ rows: [] }); // Get profile

      // Act
      const result = await authService.getCurrentUser(mockUserId);

      // Assert
      expect(result).toEqual(mockUser);
      expect(cacheService.setUserCache).toHaveBeenCalledWith(mockUserId, mockUser);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      cacheService.getUserCache = jest.fn().mockResolvedValue(null);
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      // Act & Assert
      await expect(authService.getCurrentUser(mockUserId)).rejects.toThrow(
        new ApiError('User not found', 404, ErrorCode.RESOURCE_NOT_FOUND)
      );
    });
  });
});
