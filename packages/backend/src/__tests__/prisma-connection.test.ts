/**
 * Simple Prisma Connection Test
 * 
 * Test the basic Prisma test client connection
 */

import { PrismaClient } from '../../generated/prisma-test';

describe('Prisma Connection Test', () => {
  let prisma: PrismaClient;

  beforeAll(() => {
    prisma = new PrismaClient({
      log: ['error'],
      datasources: {
        db: {
          url: 'file:./test-data/test.db'
        }
      }
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  test('should connect to test database', async () => {
    const result = await prisma.$executeRaw`SELECT 1`;
    expect(result).toBeDefined();
  });

  test('should show tables in database', async () => {
    try {
      const tables = await prisma.$queryRaw`
        SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `;
      console.log('Database tables:', tables);
    } catch (error) {
      console.log('Error querying tables:', error);
      throw error;
    }
  });
});