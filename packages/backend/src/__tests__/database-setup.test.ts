/**
 * Test Database Setup Verification
 * 
 * This test verifies that the SQLite test database setup is working correctly
 */

import { getTestPrismaClient, testDbUtils } from '../config/database.test';

describe('Test Database Setup', () => {
  let prisma: ReturnType<typeof getTestPrismaClient>;

  beforeAll(() => {
    prisma = getTestPrismaClient();
  });

  beforeEach(async () => {
    // Clear database before each test for this suite
    await testDbUtils.clearAll();
  });

  test('should connect to SQLite test database', async () => {
    // Test basic connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(result).toEqual([{ test: 1 }]);
  });

  test('should create and retrieve test user', async () => {
    // Create a test user
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      passwordHash: 'hashedpassword123',
      role: 'USER',
    };

    const user = await prisma.user.create({
      data: userData,
    });

    expect(user).toMatchObject({
      email: '<EMAIL>',
      username: 'testuser',
      role: 'USER',
      isActive: true,
    });

    // Retrieve the user
    const retrievedUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    expect(retrievedUser).not.toBeNull();
    expect(retrievedUser?.email).toBe('<EMAIL>');
  });

  test('should create and retrieve test project', async () => {
    // First create a user
    const user = await testDbUtils.createTestUser();

    // Create a project for the user
    const project = await testDbUtils.createTestProject(user.id, {
      title: 'Test Project',
      description: 'A test project',
    });

    expect(project).toMatchObject({
      title: 'Test Project',
      description: 'A test project',
      userId: user.id,
      public: false,
    });

    // Retrieve project with user relation
    const retrievedProject = await prisma.project.findUnique({
      where: { id: project.id },
      include: { user: true },
    });

    expect(retrievedProject).not.toBeNull();
    expect(retrievedProject?.user.email).toBe(user.email);
  });

  test('should handle foreign key relationships', async () => {
    const user = await testDbUtils.createTestUser();
    const project = await testDbUtils.createTestProject(user.id);
    
    // Create an image for the project
    const image = await testDbUtils.createTestImage(project.id, user.id, {
      name: 'test-image.jpg',
      format: 'jpeg',
      width: 800,
      height: 600,
    });

    expect(image).toMatchObject({
      name: 'test-image.jpg',
      projectId: project.id,
      userId: user.id,
      format: 'jpeg',
      width: 800,
      height: 600,
    });

    // Test cascade delete - deleting project should delete image
    await prisma.project.delete({ where: { id: project.id } });

    const deletedImage = await prisma.image.findUnique({
      where: { id: image.id },
    });

    expect(deletedImage).toBeNull();
  });

  test('should clear database between tests', async () => {
    // Create some data
    const user = await testDbUtils.createTestUser();
    const project = await testDbUtils.createTestProject(user.id);

    // Verify data exists
    const userCount = await prisma.user.count();
    const projectCount = await prisma.project.count();
    expect(userCount).toBeGreaterThan(0);
    expect(projectCount).toBeGreaterThan(0);

    // Clear all data
    await testDbUtils.clearAll();

    // Verify data is cleared
    const userCountAfter = await prisma.user.count();
    const projectCountAfter = await prisma.project.count();
    expect(userCountAfter).toBe(0);
    expect(projectCountAfter).toBe(0);
  });

  test('should handle JSON fields as strings in SQLite', async () => {
    const user = await testDbUtils.createTestUser();
    
    // Create user profile with JSON-like data stored as string
    const profile = await prisma.userProfile.create({
      data: {
        userId: user.id,
        fullName: 'Test User',
        notificationPreferences: JSON.stringify({
          email: true,
          push: false,
          sms: false,
        }),
      },
    });

    expect(profile.notificationPreferences).toBeTruthy();
    
    // Parse JSON from string
    const preferences = JSON.parse(profile.notificationPreferences || '{}');
    expect(preferences).toEqual({
      email: true,
      push: false,
      sms: false,
    });
  });
});