// Prisma Schema for SpheroSeg Application
// Complete migration from raw SQL to type-safe ORM

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  USER
  ADMIN
}

// User model
model User {
  id                  String    @id @default(uuid())
  email               String    @unique
  username            String    @unique
  passwordHash        String   @map("password_hash")
  role                UserRole  @default(USER)
  isActive            Boolean   @default(true) @map("is_active")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  
  // Relations
  profile             UserProfile?
  projects            Project[]
  images              Image[]
  segmentations       Segmentation[]
  projectShares       ProjectShare[]
  refreshTokens       RefreshToken[]
  segmentationQueue   SegmentationQueue[]
  duplicationTasks    ProjectDuplicationTask[]
  emailVerifications  EmailVerification[]
  passwordResetTokens PasswordResetToken[]
  accessRequests      AccessRequest[]
  processedRequests   AccessRequest[] @relation("ProcessedBy")
  
  @@index([createdAt])
  @@map("users")
}

// UserProfile model
model UserProfile {
  id                     String    @id @default(uuid())
  userId                 String    @unique @map("user_id")
  username               String?   @unique
  fullName               String?   @map("full_name")
  title                  String?
  organization           String?
  bio                    String?
  location               String?
  avatarUrl              String?   @map("avatar_url")
  preferredLanguage      String?   @map("preferred_language")
  themePreference        String?   @map("theme_preference")
  notificationPreferences Json?    @map("notification_preferences")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")
  
  // Relations
  user                   User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([createdAt])
  @@map("user_profiles")
}

// Project model
model Project {
  id          String    @id @default(uuid())
  userId      String    @map("user_id")
  title       String
  description String?
  tags        String[]  @default([])
  public      Boolean   @default(false)
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  images      Image[]
  shares      ProjectShare[]
  segmentations Segmentation[]
  segmentationQueue SegmentationQueue[]
  sourceDuplications ProjectDuplicationTask[] @relation("SourceProject")
  targetDuplications ProjectDuplicationTask[] @relation("TargetProject")
  
  @@index([userId])
  @@index([createdAt])
  @@index([userId, createdAt])
  @@map("projects")
}

// Image model
model Image {
  id                     String               @id @default(uuid())
  projectId              String?              @map("project_id")
  userId                 String?              @map("user_id")
  name                   String?
  storageFilename        String               @map("storage_filename")
  originalFilename       String?              @map("original_filename")
  storagePath            String               @map("storage_path")
  fileSize               BigInt?              @map("file_size")
  width                  Int?
  height                 Int?
  format                 String?
  thumbnailPath          String?              @map("thumbnail_path")
  metadata               Json?
  status                 String               @default("pending")
  segmentationStatus     String               @default("pending") @map("segmentation_status")
  error                  String?
  previewPath            String?              @map("preview_path")
  uploadDate             DateTime             @default(now()) @map("upload_date")
  processingStartedAt    DateTime?            @map("processing_started_at")
  processingCompletedAt  DateTime?            @map("processing_completed_at")
  processingTime         Int?                 @map("processing_time")
  createdAt              DateTime             @default(now()) @map("created_at")
  updatedAt              DateTime             @updatedAt @map("updated_at")
  
  // Relations
  project                Project?             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user                   User?                @relation(fields: [userId], references: [id], onDelete: Cascade)
  segmentations          Segmentation[]
  segmentationResults    SegmentationResult?
  segmentationQueue      SegmentationQueue[]
  
  @@index([projectId])
  @@index([userId])
  @@index([createdAt])
  @@index([projectId, createdAt])
  @@index([status])
  @@index([segmentationStatus])
  @@map("images")
}

// Segmentation model
model Segmentation {
  id          String    @id @default(uuid())
  imageId     String    @map("image_id")
  userId      String?   @map("user_id")
  projectId   String?   @map("project_id")
  name        String?
  modelType   String    @default("resunet") @map("model_type")
  modelVersion String?  @map("model_version")
  status      String    @default("pending")
  polygons    Json?
  maskPath    String?   @map("mask_path")
  metadata    Json?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  image       Image     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  user        User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
  project     Project?  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@index([imageId])
  @@index([projectId])
  @@index([userId])
  @@index([createdAt])
  @@index([status])
  @@map("segmentations")
}

// SegmentationResult model
model SegmentationResult {
  id          String    @id @default(uuid())
  imageId     String    @unique @map("image_id")
  status      String    @default("pending")
  resultData  Json?     @map("result_data")
  parameters  Json?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  image       Image     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  
  @@index([status])
  @@index([createdAt])
  @@map("segmentation_results")
}

// ProjectShare model
model ProjectShare {
  id              String    @id @default(uuid())
  projectId       String    @map("project_id")
  userId          String    @map("user_id")
  permission      String    @default("view") // view, edit, admin
  invitationToken String?   @unique @map("invitation_token")
  invitationEmail String?   @map("invitation_email")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  
  // Relations
  project         Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([projectId, userId])
  @@index([projectId])
  @@index([userId])
  @@index([createdAt])
  @@index([permission])
  @@map("project_shares")
}

// SegmentationQueue model
model SegmentationQueue {
  id            String    @id @default(uuid())
  imageId       String    @map("image_id")
  userId        String    @map("user_id")
  projectId     String?   @map("project_id")
  status        String    @default("pending")
  priority      Int       @default(0)
  startedAt     DateTime? @map("started_at")
  completedAt   DateTime? @map("completed_at")
  errorMessage  String?   @map("error_message")
  modelType     String    @default("resunet") @map("model_type")
  modelVersion  String?   @map("model_version")
  metadata      Json?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  
  // Relations
  image         Image     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  project       Project?  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@index([imageId])
  @@index([userId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([status, priority, createdAt])
  @@map("segmentation_queue")
}

// ProjectDuplicationTask model
model ProjectDuplicationTask {
  id                String    @id @default(uuid())
  sourceProjectId   String    @map("source_project_id")
  targetProjectId   String?   @map("target_project_id")
  userId            String    @map("user_id")
  status            String    @default("pending")
  progress          Float     @default(0)
  options           Json?
  startedAt         DateTime? @map("started_at")
  completedAt       DateTime? @map("completed_at")
  errorMessage      String?   @map("error_message")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  
  // Relations
  sourceProject     Project   @relation("SourceProject", fields: [sourceProjectId], references: [id], onDelete: Cascade)
  targetProject     Project?  @relation("TargetProject", fields: [targetProjectId], references: [id], onDelete: SetNull)
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@map("project_duplication_tasks")
}

// RefreshToken model
model RefreshToken {
  id          String    @id @default(uuid())
  userId      String    @map("user_id")
  tokenId     String    @unique @map("token_id")
  tokenFamily String?   @map("token_family")
  expiresAt   DateTime  @map("expires_at")
  isRevoked   Boolean   @default(false) @map("is_revoked")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([tokenId])
  @@index([expiresAt])
  @@index([isRevoked])
  @@index([tokenFamily])
  @@map("refresh_tokens")
}

// EmailVerification model
model EmailVerification {
  id          String    @id @default(uuid())
  userId      String    @map("user_id")
  token       String    @unique
  expiresAt   DateTime  @map("expires_at")
  usedAt      DateTime? @map("used_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  
  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([token])
  @@index([expiresAt])
  @@map("email_verifications")
}

// PasswordResetToken model
model PasswordResetToken {
  id          String    @id @default(uuid())
  userId      String    @map("user_id")
  tokenHash   String    @map("token_hash")
  expiresAt   DateTime  @map("expires_at")
  usedAt      DateTime? @map("used_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  
  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([expiresAt])
  @@map("password_reset_tokens")
}

// AccessRequest model
model AccessRequest {
  id          String    @id @default(uuid())
  email       String
  name        String?
  reason      String?
  status      String    @default("pending")
  processedBy String?   @map("processed_by")
  processedAt DateTime? @map("processed_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  processor   User?     @relation("ProcessedBy", fields: [processedBy], references: [id], onDelete: SetNull)
  requester   User?     @relation(fields: [email], references: [email])
  
  @@index([email])
  @@index([status])
  @@index([createdAt])
  @@index([processedBy])
  @@map("access_requests")
}

// Session model (for express-session)
model Session {
  sid    String   @id
  sess   Json
  expire DateTime
  
  @@index([expire])
  @@map("sessions")
}

// Migration tracking
model Migration {
  id         Int      @id @default(autoincrement())
  filename   String   @unique
  executedAt DateTime @default(now()) @map("executed_at")
  
  @@map("migrations")
}