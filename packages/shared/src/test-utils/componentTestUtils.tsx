import React from 'react';
import { render, RenderOptions } from '@testing-library/react';

interface ProvidersProps {
  children: React.ReactNode;
}

const AllTheProviders: React.FC<ProvidersProps> = ({ children }) => {
  return <>{children}</>;
};

export function renderWithProviders(
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  return render(ui, { wrapper: AllTheProviders, ...options });
}
