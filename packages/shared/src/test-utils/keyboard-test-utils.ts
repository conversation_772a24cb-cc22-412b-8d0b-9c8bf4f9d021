import { vi } from 'vitest';

export function createKeyboardTestProps() {
  return {
    editMode: 'view',
    setEditMode: vi.fn(),
    onUndo: vi.fn(),
    onRedo: vi.fn(),
    onDelete: vi.fn(),
    onSave: vi.fn(),
    canUndo: true,
    canRedo: true,
  };
}

export function simulateKeyPress(key: string, options?: KeyboardEventInit) {
  const event = new KeyboardEvent('keydown', {
    key,
    ...options,
  });
  document.dispatchEvent(event);
}

export function simulateKeyPressAndRelease(
  key: string,
  options?: KeyboardEventInit
) {
  simulateKeyPress(key, options);
  const upEvent = new KeyboardEvent('keyup', {
    key,
    ...options,
  });
  document.dispatchEvent(upEvent);
}

export function verifyEditModeWasSet(
  mockSetEditMode: any,
  expectedMode: string
) {
  expect(mockSetEditMode).toHaveBeenCalledWith(expectedMode);
}

export function verifyHandlerWasCalled(handler: any) {
  expect(handler).toHaveBeenCalled();
}

export function createFocusedInput() {
  const input = document.createElement('input');
  document.body.appendChild(input);
  input.focus();
  return input;
}

export function simulateKeyPressWithTarget(target: HTMLElement, key: string) {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
  });
  target.dispatchEvent(event);
}

export function cleanupInputElement(input: HTMLElement) {
  document.body.removeChild(input);
}
