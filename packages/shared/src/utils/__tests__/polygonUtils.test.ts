import { describe, it, expect } from 'vitest';
import { polygonUtils } from '../polygonUtils';

describe('polygonUtils', () => {
  describe('calculateArea', () => {
    it('should calculate area of a triangle', () => {
      const triangle = [
        { x: 0, y: 0 },
        { x: 4, y: 0 },
        { x: 2, y: 3 }
      ];
      
      const area = polygonUtils.calculateArea(triangle);
      expect(area).toBe(6); // Base = 4, Height = 3, Area = 0.5 * 4 * 3 = 6
    });

    it('should calculate area of a square', () => {
      const square = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];
      
      const area = polygonUtils.calculateArea(square);
      expect(area).toBe(100);
    });

    it('should calculate area of a complex polygon', () => {
      const polygon = [
        { x: 0, y: 0 },
        { x: 5, y: 0 },
        { x: 5, y: 3 },
        { x: 3, y: 3 },
        { x: 3, y: 5 },
        { x: 0, y: 5 }
      ];
      
      const area = polygonUtils.calculateArea(polygon);
      expect(area).toBe(21); // L-shaped polygon
    });

    it('should return 0 for degenerate polygons', () => {
      expect(polygonUtils.calculateArea([])).toBe(0);
      expect(polygonUtils.calculateArea([{ x: 0, y: 0 }])).toBe(0);
      expect(polygonUtils.calculateArea([{ x: 0, y: 0 }, { x: 1, y: 1 }])).toBe(0);
    });

    it('should handle negative coordinates', () => {
      const polygon = [
        { x: -5, y: -5 },
        { x: 5, y: -5 },
        { x: 5, y: 5 },
        { x: -5, y: 5 }
      ];
      
      const area = polygonUtils.calculateArea(polygon);
      expect(area).toBe(100);
    });
  });

  describe('calculatePerimeter', () => {
    it('should calculate perimeter of a triangle', () => {
      const triangle = [
        { x: 0, y: 0 },
        { x: 3, y: 0 },
        { x: 0, y: 4 }
      ];
      
      const perimeter = polygonUtils.calculatePerimeter(triangle);
      expect(perimeter).toBeCloseTo(12, 2); // 3 + 4 + 5
    });

    it('should calculate perimeter of a square', () => {
      const square = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];
      
      const perimeter = polygonUtils.calculatePerimeter(square);
      expect(perimeter).toBe(40);
    });

    it('should return 0 for empty or single point', () => {
      expect(polygonUtils.calculatePerimeter([])).toBe(0);
      expect(polygonUtils.calculatePerimeter([{ x: 0, y: 0 }])).toBe(0);
    });
  });

  describe('calculateCentroid', () => {
    it('should calculate centroid of a triangle', () => {
      const triangle = [
        { x: 0, y: 0 },
        { x: 6, y: 0 },
        { x: 3, y: 6 }
      ];
      
      const centroid = polygonUtils.calculateCentroid(triangle);
      expect(centroid.x).toBeCloseTo(3, 2);
      expect(centroid.y).toBeCloseTo(2, 2);
    });

    it('should calculate centroid of a square', () => {
      const square = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];
      
      const centroid = polygonUtils.calculateCentroid(square);
      expect(centroid.x).toBe(5);
      expect(centroid.y).toBe(5);
    });

    it('should handle empty polygon', () => {
      const centroid = polygonUtils.calculateCentroid([]);
      expect(centroid).toEqual({ x: 0, y: 0 });
    });
  });

  describe('isPointInPolygon', () => {
    const square = [
      { x: 0, y: 0 },
      { x: 10, y: 0 },
      { x: 10, y: 10 },
      { x: 0, y: 10 }
    ];

    it('should detect point inside polygon', () => {
      expect(polygonUtils.isPointInPolygon({ x: 5, y: 5 }, square)).toBe(true);
      expect(polygonUtils.isPointInPolygon({ x: 1, y: 1 }, square)).toBe(true);
      expect(polygonUtils.isPointInPolygon({ x: 9, y: 9 }, square)).toBe(true);
    });

    it('should detect point outside polygon', () => {
      expect(polygonUtils.isPointInPolygon({ x: -1, y: 5 }, square)).toBe(false);
      expect(polygonUtils.isPointInPolygon({ x: 11, y: 5 }, square)).toBe(false);
      expect(polygonUtils.isPointInPolygon({ x: 5, y: -1 }, square)).toBe(false);
      expect(polygonUtils.isPointInPolygon({ x: 5, y: 11 }, square)).toBe(false);
    });

    it('should handle points on edges', () => {
      expect(polygonUtils.isPointInPolygon({ x: 0, y: 5 }, square)).toBe(true);
      expect(polygonUtils.isPointInPolygon({ x: 5, y: 0 }, square)).toBe(true);
      expect(polygonUtils.isPointInPolygon({ x: 10, y: 5 }, square)).toBe(true);
    });

    it('should handle complex polygons', () => {
      const star = [
        { x: 50, y: 0 },
        { x: 61, y: 35 },
        { x: 98, y: 35 },
        { x: 68, y: 57 },
        { x: 79, y: 91 },
        { x: 50, y: 70 },
        { x: 21, y: 91 },
        { x: 32, y: 57 },
        { x: 2, y: 35 },
        { x: 39, y: 35 }
      ];

      expect(polygonUtils.isPointInPolygon({ x: 50, y: 50 }, star)).toBe(true);
      expect(polygonUtils.isPointInPolygon({ x: 0, y: 0 }, star)).toBe(false);
    });
  });

  describe('simplifyPolygon', () => {
    it('should simplify polygon with Douglas-Peucker algorithm', () => {
      const polygon = [
        { x: 0, y: 0 },
        { x: 1, y: 0.1 },
        { x: 2, y: -0.1 },
        { x: 3, y: 0 },
        { x: 4, y: 0 },
        { x: 5, y: 5 },
        { x: 0, y: 5 }
      ];

      const simplified = polygonUtils.simplifyPolygon(polygon, 0.5);
      expect(simplified.length).toBeLessThan(polygon.length);
      expect(simplified).toContainEqual({ x: 0, y: 0 });
      expect(simplified).toContainEqual({ x: 5, y: 5 });
    });

    it('should preserve important vertices', () => {
      const triangle = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 5, y: 10 }
      ];

      const simplified = polygonUtils.simplifyPolygon(triangle, 0.1);
      expect(simplified).toEqual(triangle);
    });

    it('should handle empty polygon', () => {
      expect(polygonUtils.simplifyPolygon([], 1)).toEqual([]);
    });
  });

  describe('getBoundingBox', () => {
    it('should calculate bounding box for polygon', () => {
      const polygon = [
        { x: 5, y: 10 },
        { x: 15, y: 20 },
        { x: 10, y: 5 },
        { x: 20, y: 15 }
      ];

      const bbox = polygonUtils.getBoundingBox(polygon);
      expect(bbox).toEqual({
        minX: 5,
        minY: 5,
        maxX: 20,
        maxY: 20,
        width: 15,
        height: 15
      });
    });

    it('should handle single point', () => {
      const point = [{ x: 5, y: 10 }];
      const bbox = polygonUtils.getBoundingBox(point);
      
      expect(bbox).toEqual({
        minX: 5,
        minY: 10,
        maxX: 5,
        maxY: 10,
        width: 0,
        height: 0
      });
    });

    it('should handle empty polygon', () => {
      const bbox = polygonUtils.getBoundingBox([]);
      expect(bbox).toEqual({
        minX: Infinity,
        minY: Infinity,
        maxX: -Infinity,
        maxY: -Infinity,
        width: 0,
        height: 0
      });
    });
  });

  describe('polygonsIntersect', () => {
    it('should detect intersecting polygons', () => {
      const poly1 = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];

      const poly2 = [
        { x: 5, y: 5 },
        { x: 15, y: 5 },
        { x: 15, y: 15 },
        { x: 5, y: 15 }
      ];

      expect(polygonUtils.polygonsIntersect(poly1, poly2)).toBe(true);
    });

    it('should detect non-intersecting polygons', () => {
      const poly1 = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];

      const poly2 = [
        { x: 20, y: 20 },
        { x: 30, y: 20 },
        { x: 30, y: 30 },
        { x: 20, y: 30 }
      ];

      expect(polygonUtils.polygonsIntersect(poly1, poly2)).toBe(false);
    });

    it('should detect touching polygons as intersecting', () => {
      const poly1 = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];

      const poly2 = [
        { x: 10, y: 0 },
        { x: 20, y: 0 },
        { x: 20, y: 10 },
        { x: 10, y: 10 }
      ];

      expect(polygonUtils.polygonsIntersect(poly1, poly2)).toBe(true);
    });
  });

  describe('calculateMetrics', () => {
    it('should calculate all metrics for a polygon', () => {
      const square = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }
      ];

      const metrics = polygonUtils.calculateMetrics(square);
      
      expect(metrics.area).toBe(100);
      expect(metrics.perimeter).toBe(40);
      expect(metrics.centroid).toEqual({ x: 5, y: 5 });
      expect(metrics.boundingBox).toEqual({
        minX: 0,
        minY: 0,
        maxX: 10,
        maxY: 10,
        width: 10,
        height: 10
      });
      expect(metrics.circularity).toBeCloseTo(0.785, 2); // 4π * area / perimeter²
      expect(metrics.aspectRatio).toBe(1); // width / height
    });

    it('should handle irregular polygons', () => {
      const triangle = [
        { x: 0, y: 0 },
        { x: 6, y: 0 },
        { x: 3, y: 8 }
      ];

      const metrics = polygonUtils.calculateMetrics(triangle);
      
      expect(metrics.area).toBe(24);
      expect(metrics.perimeter).toBeCloseTo(20, 1);
      expect(metrics.aspectRatio).toBe(0.75); // 6 / 8
    });
  });
});