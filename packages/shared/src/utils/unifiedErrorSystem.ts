/**
 * Unified Error System
 * 
 * Centralized error handling that provides consistent error types, codes, and structures
 * across both frontend and backend, following SSOT principle.
 */

// ===========================
// Error Types and Enums (Shared)
// ===========================

export enum ErrorType {
  // Client Errors (4xx)
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  
  // Server Errors (5xx)
  INTERNAL_SERVER = 'INTERNAL_SERVER',
  DATABASE = 'DATABASE',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  STORAGE = 'STORAGE',
  
  // Network Errors
  NETWORK = 'NETWORK',
  TIMEOUT = 'TIMEOUT',
  
  // Application-specific Errors
  IMAGE_OPERATION = 'IMAGE_OPERATION',
  PROJECT_OPERATION = 'PROJECT_OPERATION',
  SEGMENTATION_OPERATION = 'SEGMENTATION_OPERATION',
  
  // Unknown
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

export enum ErrorCode {
  // Client Errors (4xx)
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  FORBIDDEN = 'FORBIDDEN',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  // Server Errors (5xx)
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  SERVICE_UNAVAILABLE_ERROR = 'SERVICE_UNAVAILABLE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  
  // Network Errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // Application-specific
  IMAGE_PROCESSING_ERROR = 'IMAGE_PROCESSING_ERROR',
  PROJECT_ACCESS_ERROR = 'PROJECT_ACCESS_ERROR',
  SEGMENTATION_ERROR = 'SEGMENTATION_ERROR'
}

// ===========================
// Error Interfaces (Shared)
// ===========================

export interface ErrorDetails {
  field?: string;
  value?: unknown;
  constraint?: string;
  message?: string;
  context?: Record<string, unknown>;
}

export interface UnifiedErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  code: ErrorCode;
  message: string;
  statusCode?: number;
  details?: ErrorDetails[];
  timestamp: string;
  requestId?: string;
  userId?: string;
  context?: Record<string, unknown>;
  stack?: string;
}

export interface ErrorResponse {
  success: false;
  error: ErrorCode;
  message: string;
  statusCode: number;
  details?: ErrorDetails[];
  timestamp: string;
  requestId?: string;
}

// ===========================
// Error Mapping Utilities
// ===========================

/**
 * Map HTTP status codes to error types
 */
export function mapStatusToErrorType(status: number): ErrorType {
  switch (status) {
    case 400:
      return ErrorType.VALIDATION;
    case 401:
      return ErrorType.AUTHENTICATION;
    case 403:
      return ErrorType.AUTHORIZATION;
    case 404:
      return ErrorType.NOT_FOUND;
    case 409:
      return ErrorType.CONFLICT;
    case 429:
      return ErrorType.RATE_LIMIT;
    case 408:
      return ErrorType.TIMEOUT;
    case 500:
      return ErrorType.INTERNAL_SERVER;
    case 502:
    case 503:
    case 504:
      return ErrorType.SERVICE_UNAVAILABLE;
    default:
      return status >= 400 && status < 500 ? ErrorType.VALIDATION : ErrorType.UNKNOWN;
  }
}

/**
 * Map error types to HTTP status codes
 */
export function mapErrorTypeToStatus(type: ErrorType): number {
  switch (type) {
    case ErrorType.VALIDATION:
      return 400;
    case ErrorType.AUTHENTICATION:
      return 401;
    case ErrorType.AUTHORIZATION:
      return 403;
    case ErrorType.NOT_FOUND:
      return 404;
    case ErrorType.CONFLICT:
      return 409;
    case ErrorType.RATE_LIMIT:
      return 429;
    case ErrorType.TIMEOUT:
      return 408;
    case ErrorType.INTERNAL_SERVER:
    case ErrorType.DATABASE:
    case ErrorType.STORAGE:
      return 500;
    case ErrorType.EXTERNAL_SERVICE:
    case ErrorType.SERVICE_UNAVAILABLE:
      return 503;
    default:
      return 500;
  }
}

/**
 * Map error types to error codes
 */
export function mapErrorTypeToCode(type: ErrorType): ErrorCode {
  switch (type) {
    case ErrorType.VALIDATION:
      return ErrorCode.VALIDATION_ERROR;
    case ErrorType.AUTHENTICATION:
      return ErrorCode.AUTHENTICATION_REQUIRED;
    case ErrorType.AUTHORIZATION:
      return ErrorCode.INSUFFICIENT_PERMISSIONS;
    case ErrorType.NOT_FOUND:
      return ErrorCode.RESOURCE_NOT_FOUND;
    case ErrorType.CONFLICT:
      return ErrorCode.RESOURCE_CONFLICT;
    case ErrorType.RATE_LIMIT:
      return ErrorCode.RATE_LIMIT_EXCEEDED;
    case ErrorType.DATABASE:
      return ErrorCode.DATABASE_ERROR;
    case ErrorType.EXTERNAL_SERVICE:
      return ErrorCode.EXTERNAL_SERVICE_ERROR;
    case ErrorType.SERVICE_UNAVAILABLE:
      return ErrorCode.SERVICE_UNAVAILABLE_ERROR;
    case ErrorType.STORAGE:
      return ErrorCode.STORAGE_ERROR;
    case ErrorType.NETWORK:
      return ErrorCode.NETWORK_ERROR;
    case ErrorType.TIMEOUT:
      return ErrorCode.TIMEOUT_ERROR;
    case ErrorType.IMAGE_OPERATION:
      return ErrorCode.IMAGE_PROCESSING_ERROR;
    case ErrorType.PROJECT_OPERATION:
      return ErrorCode.PROJECT_ACCESS_ERROR;
    case ErrorType.SEGMENTATION_OPERATION:
      return ErrorCode.SEGMENTATION_ERROR;
    default:
      return ErrorCode.INTERNAL_SERVER_ERROR;
  }
}

/**
 * Determine error severity based on type
 */
export function getErrorSeverity(type: ErrorType): ErrorSeverity {
  switch (type) {
    case ErrorType.VALIDATION:
    case ErrorType.NOT_FOUND:
      return ErrorSeverity.WARNING;
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
    case ErrorType.NETWORK:
    case ErrorType.TIMEOUT:
      return ErrorSeverity.ERROR;
    case ErrorType.INTERNAL_SERVER:
    case ErrorType.DATABASE:
    case ErrorType.STORAGE:
      return ErrorSeverity.CRITICAL;
    default:
      return ErrorSeverity.ERROR;
  }
}

// ===========================
// Error Factory Functions
// ===========================

/**
 * Create a standardized error info object
 */
export function createErrorInfo(
  type: ErrorType,
  message: string,
  options: {
    code?: ErrorCode;
    statusCode?: number;
    details?: ErrorDetails[];
    context?: Record<string, unknown>;
    requestId?: string;
    userId?: string;
    stack?: string;
  } = {}
): UnifiedErrorInfo {
  const code = options.code || mapErrorTypeToCode(type);
  const statusCode = options.statusCode || mapErrorTypeToStatus(type);
  const severity = getErrorSeverity(type);

  return {
    type,
    severity,
    code,
    message,
    statusCode,
    details: options.details,
    timestamp: new Date().toISOString(),
    requestId: options.requestId,
    userId: options.userId,
    context: options.context,
    stack: options.stack
  };
}

/**
 * Create an error response for API endpoints
 */
export function createErrorResponse(errorInfo: UnifiedErrorInfo): ErrorResponse {
  return {
    success: false,
    error: errorInfo.code,
    message: errorInfo.message,
    statusCode: errorInfo.statusCode || 500,
    details: errorInfo.details,
    timestamp: errorInfo.timestamp,
    requestId: errorInfo.requestId
  };
}

// ===========================
// Common Error Templates
// ===========================

export const ERROR_MESSAGES = {
  // Authentication & Authorization
  AUTHENTICATION_REQUIRED: 'Authentication required. Please sign in.',
  INVALID_CREDENTIALS: 'Invalid credentials provided.',
  TOKEN_EXPIRED: 'Your session has expired. Please sign in again.',
  INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action.',
  
  // Validation
  VALIDATION_FAILED: 'Validation failed. Please check your input.',
  REQUIRED_FIELD_MISSING: 'Required field is missing.',
  INVALID_FORMAT: 'Invalid format provided.',
  
  // Resources
  RESOURCE_NOT_FOUND: 'The requested resource was not found.',
  RESOURCE_CONFLICT: 'Resource conflict detected.',
  DUPLICATE_RESOURCE: 'Resource already exists.',
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 'An internal server error occurred. Please try again later.',
  DATABASE_ERROR: 'A database error occurred. Please try again later.',
  SERVICE_UNAVAILABLE: 'Service is currently unavailable. Please try again later.',
  STORAGE_ERROR: 'A storage error occurred. Please try again later.',
  
  // Network
  NETWORK_ERROR: 'Network error. Please check your connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  
  // Application-specific
  IMAGE_PROCESSING_ERROR: 'Failed to process image. Please try again.',
  PROJECT_ACCESS_ERROR: 'Unable to access project. Please check your permissions.',
  SEGMENTATION_ERROR: 'Segmentation operation failed. Please try again.',
  
  // Generic
  UNKNOWN_ERROR: 'An unknown error occurred. Please try again.'
} as const;

// ===========================
// Error Validation
// ===========================

/**
 * Check if an object is a valid UnifiedErrorInfo
 */
export function isUnifiedErrorInfo(obj: unknown): obj is UnifiedErrorInfo {
  if (!obj || typeof obj !== 'object') return false;
  
  const error = obj as Record<string, unknown>;
  return (
    typeof error.type === 'string' &&
    typeof error.severity === 'string' &&
    typeof error.code === 'string' &&
    typeof error.message === 'string' &&
    typeof error.timestamp === 'string' &&
    Object.values(ErrorType).includes(error.type as ErrorType) &&
    Object.values(ErrorSeverity).includes(error.severity as ErrorSeverity) &&
    Object.values(ErrorCode).includes(error.code as ErrorCode)
  );
}

/**
 * Check if an object is a valid ErrorResponse
 */
export function isErrorResponse(obj: unknown): obj is ErrorResponse {
  if (!obj || typeof obj !== 'object') return false;
  
  const response = obj as Record<string, unknown>;
  return (
    response.success === false &&
    typeof response.error === 'string' &&
    typeof response.message === 'string' &&
    typeof response.statusCode === 'number' &&
    typeof response.timestamp === 'string' &&
    Object.values(ErrorCode).includes(response.error as ErrorCode)
  );
}

// ===========================
// Error Conversion Utilities
// ===========================

/**
 * Convert a standard Error to UnifiedErrorInfo
 */
export function fromError(
  error: Error,
  type: ErrorType = ErrorType.UNKNOWN,
  context?: Record<string, unknown>
): UnifiedErrorInfo {
  return createErrorInfo(type, error.message, {
    context,
    stack: error.stack
  });
}

/**
 * Convert an HTTP response error to UnifiedErrorInfo
 */
export function fromHttpError(
  status: number,
  message: string,
  details?: ErrorDetails[],
  context?: Record<string, unknown>
): UnifiedErrorInfo {
  const type = mapStatusToErrorType(status);
  return createErrorInfo(type, message, {
    statusCode: status,
    details,
    context
  });
}

// ===========================
// Export All
// ===========================

export default {
  // Enums
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  
  // Mapping functions
  mapStatusToErrorType,
  mapErrorTypeToStatus,
  mapErrorTypeToCode,
  getErrorSeverity,
  
  // Factory functions
  createErrorInfo,
  createErrorResponse,
  
  // Constants
  ERROR_MESSAGES,
  
  // Validation functions
  isUnifiedErrorInfo,
  isErrorResponse,
  
  // Conversion utilities
  fromError,
  fromHttpError
};