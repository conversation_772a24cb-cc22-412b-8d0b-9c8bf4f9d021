/**
 * Zoom handling utilities
 */

export interface ZoomConfig {
  minZoom: number;
  maxZoom: number;
  zoomStep: number;
}

export const defaultZoomConfig: ZoomConfig = {
  minZoom: 0.1,
  maxZoom: 10,
  zoomStep: 0.1,
};

export const handleZoomIn = (
  currentZoom: number,
  config: ZoomConfig = defaultZoomConfig
): number => {
  return Math.min(currentZoom + config.zoomStep, config.maxZoom);
};

export const handleZoomOut = (
  currentZoom: number,
  config: ZoomConfig = defaultZoomConfig
): number => {
  return Math.max(currentZoom - config.zoomStep, config.minZoom);
};

export const handleZoomReset = (): number => {
  return 1;
};

export const handleWheel = (
  delta: number,
  currentZoom: number,
  config: ZoomConfig = defaultZoomConfig
): number => {
  const zoomDelta = delta > 0 ? -config.zoomStep : config.zoomStep;
  return Math.max(
    config.minZoom,
    Math.min(config.maxZoom, currentZoom + zoomDelta)
  );
};

// Hook for React usage
export const useZoomHandlers = (config: ZoomConfig = defaultZoomConfig) => {
  return {
    handleZoomIn: (currentZoom: number) => handleZoomIn(currentZoom, config),
    handleZoomOut: (currentZoom: number) => handleZoomOut(currentZoom, config),
    handleZoomReset,
    handleWheel: (delta: number, currentZoom: number) =>
      handleWheel(delta, currentZoom, config),
  };
};
