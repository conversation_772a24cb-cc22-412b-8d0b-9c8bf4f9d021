/**
 * Polygon WASM Utilities
 *
 * Utilities for working with WebAssembly-based polygon operations
 */

export interface WasmPolygonModule {
  _malloc: (size: number) => number;
  _free: (ptr: number) => void;
  HEAP8: Int8Array;
  HEAP16: Int16Array;
  HEAP32: Int32Array;
  HEAPU8: Uint8Array;
  HEAPU16: Uint16Array;
  HEAPU32: Uint32Array;
  HEAPF32: Float32Array;
  HEAPF64: Float64Array;

  // Polygon operations
  _simplifyPolygon: (
    pointsPtr: number,
    numPoints: number,
    tolerance: number,
    resultPtr: number
  ) => number;
  _splitPolygon: (
    pointsPtr: number,
    numPoints: number,
    lineStartX: number,
    lineStartY: number,
    lineEndX: number,
    lineEndY: number,
    result1Ptr: number,
    result2Ptr: number
  ) => number;
  _calculatePolygonArea: (pointsPtr: number, numPoints: number) => number;
  _calculatePolygonPerimeter: (pointsPtr: number, numPoints: number) => number;
  _pointInPolygon: (
    pointX: number,
    pointY: number,
    polygonPtr: number,
    numPoints: number
  ) => number;
}

export class PolygonWasmProcessor {
  private module: WasmPolygonModule | null = null;

  async initialize(_wasmUrl: string): Promise<void> {
    // In a real implementation, this would load the WASM module
    // For testing, we'll create a mock
    this.module = {
      _malloc: (_size: number) => 0,
      _free: (_ptr: number) => {},
      HEAP8: new Int8Array(1024),
      HEAP16: new Int16Array(512),
      HEAP32: new Int32Array(256),
      HEAPU8: new Uint8Array(1024),
      HEAPU16: new Uint16Array(512),
      HEAPU32: new Uint32Array(256),
      HEAPF32: new Float32Array(256),
      HEAPF64: new Float64Array(128),
      _simplifyPolygon: () => 0,
      _splitPolygon: () => 0,
      _calculatePolygonArea: () => 0,
      _calculatePolygonPerimeter: () => 0,
      _pointInPolygon: () => 0,
    };
  }

  isInitialized(): boolean {
    return this.module !== null;
  }

  simplifyPolygon(
    points: Array<{ x: number; y: number }>,
    _tolerance: number
  ): Array<{ x: number; y: number }> {
    if (!this.module) {
      throw new Error('WASM module not initialized');
    }

    // Simplified implementation for testing
    if (points.length <= 3) {
      return points;
    }

    // Simple Douglas-Peucker algorithm mock
    return points.filter(
      (_, index) => index % 2 === 0 || index === points.length - 1
    );
  }

  splitPolygon(
    points: Array<{ x: number; y: number }>,
    _lineStart: { x: number; y: number },
    _lineEnd: { x: number; y: number }
  ): [Array<{ x: number; y: number }>, Array<{ x: number; y: number }>] | null {
    if (!this.module) {
      throw new Error('WASM module not initialized');
    }

    // Simple mock implementation
    if (points.length < 3) {
      return null;
    }

    const midIndex = Math.floor(points.length / 2);
    return [points.slice(0, midIndex + 1), points.slice(midIndex)];
  }

  calculateArea(points: Array<{ x: number; y: number }>): number {
    if (!this.module) {
      throw new Error('WASM module not initialized');
    }

    // Shoelace formula
    let area = 0;
    const n = points.length;
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const p1 = points[i];
      const p2 = points[j];
      if (p1 && p2) {
        area += p1.x * p2.y;
        area -= p2.x * p1.y;
      }
    }
    return Math.abs(area / 2);
  }

  calculatePerimeter(points: Array<{ x: number; y: number }>): number {
    if (!this.module) {
      throw new Error('WASM module not initialized');
    }

    let perimeter = 0;
    const n = points.length;
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const p1 = points[i];
      const p2 = points[j];
      if (p1 && p2) {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        perimeter += Math.sqrt(dx * dx + dy * dy);
      }
    }
    return perimeter;
  }

  pointInPolygon(
    point: { x: number; y: number },
    polygon: Array<{ x: number; y: number }>
  ): boolean {
    if (!this.module) {
      throw new Error('WASM module not initialized');
    }

    if (!polygon || !Array.isArray(polygon) || polygon.length === 0) {
      return false;
    }

    // Ray casting algorithm
    let inside = false;
    const n = polygon.length;
    let p1 = polygon[0];
    if (!p1) return false;

    for (let i = 1; i <= n; i++) {
      const p2 = polygon[i % n];
      if (!p2) continue;

      if (point.y > Math.min(p1.y, p2.y)) {
        if (point.y <= Math.max(p1.y, p2.y)) {
          if (point.x <= Math.max(p1.x, p2.x)) {
            const xinters =
              ((point.y - p1.y) * (p2.x - p1.x)) / (p2.y - p1.y) + p1.x;
            if (p1.x === p2.x || point.x <= xinters) {
              inside = !inside;
            }
          }
        }
      }
      p1 = p2;
    }

    return inside;
  }

  dispose(): void {
    this.module = null;
  }
}

// Singleton instance
export const polygonWasmProcessor = new PolygonWasmProcessor();

// Export with alias for backward compatibility
export const polygonWasm = polygonWasmProcessor;

// Helper functions
export async function initializeWasm(
  wasmUrl: string = '/polygon-ops.wasm'
): Promise<void> {
  await polygonWasmProcessor.initialize(wasmUrl);
}

export function isWasmInitialized(): boolean {
  return polygonWasmProcessor.isInitialized();
}

// Hook for React usage
export function usePolygonWasm() {
  // This is a simplified version - the actual implementation should be in the frontend
  return {
    load: async () => initializeWasm(),
    isPointInPolygon: (
      point: { x: number; y: number },
      polygon: Array<{ x: number; y: number }>
    ) => {
      try {
        return polygonWasmProcessor.pointInPolygon(point, polygon);
      } catch (error) {
        console.warn('WASM polygon operation failed, using fallback:', error);
        return false;
      }
    },
    distanceToSegment: (
      point: { x: number; y: number },
      segStart: { x: number; y: number },
      segEnd: { x: number; y: number }
    ) => {
      // Simple distance calculation
      const A = point.x - segStart.x;
      const B = point.y - segStart.y;
      const C = segEnd.x - segStart.x;
      const D = segEnd.y - segStart.y;

      const dot = A * C + B * D;
      const lenSq = C * C + D * D;
      let param = -1;

      if (lenSq !== 0) param = dot / lenSq;

      let xx, yy;

      if (param < 0) {
        xx = segStart.x;
        yy = segStart.y;
      } else if (param > 1) {
        xx = segEnd.x;
        yy = segEnd.y;
      } else {
        xx = segStart.x + param * C;
        yy = segStart.y + param * D;
      }

      const dx = point.x - xx;
      const dy = point.y - yy;
      return Math.sqrt(dx * dx + dy * dy);
    },
    calculateIntersection: () => null, // Not implemented in mock
    calculatePolygonArea: (polygon: Array<{ x: number; y: number }>) =>
      polygonWasmProcessor.calculateArea(polygon),
    calculatePolygonPerimeter: (polygon: Array<{ x: number; y: number }>) =>
      polygonWasmProcessor.calculatePerimeter(polygon),
    calculateBoundingBox: (polygon: Array<{ x: number; y: number }>) => {
      if (!polygon.length) return null;
      const firstPoint = polygon[0];
      if (!firstPoint) return null;
      let minX = firstPoint.x,
        maxX = firstPoint.x;
      let minY = firstPoint.y,
        maxY = firstPoint.y;

      for (const p of polygon) {
        minX = Math.min(minX, p.x);
        maxX = Math.max(maxX, p.x);
        minY = Math.min(minY, p.y);
        maxY = Math.max(maxY, p.y);
      }

      return { minX, minY, maxX, maxY };
    },
    doPolygonsIntersect: () => false, // Not implemented in mock
  };
}

export default polygonWasmProcessor;
