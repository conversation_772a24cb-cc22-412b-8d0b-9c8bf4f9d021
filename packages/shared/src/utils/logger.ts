/**
 * Shared Logger Utility
 *
 * Simple logger for shared package that works in both browser and Node.js environments
 */

export interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
}

export const LOG_LEVELS: LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
} as const;

export type LogLevelValue = LogLevel[keyof LogLevel];

class SharedLogger {
  private level: LogLevelValue = LOG_LEVELS.INFO;
  private context: string = 'shared';

  constructor(context?: string) {
    if (context) {
      this.context = context;
    }

    // Set log level from environment
    const envLevel =
      (typeof process !== 'undefined' && process.env?.['LOG_LEVEL']) || 'info';
    this.setLevel(envLevel);
  }

  setLevel(level: string): void {
    switch (level.toLowerCase()) {
      case 'debug':
        this.level = LOG_LEVELS.DEBUG;
        break;
      case 'info':
        this.level = LOG_LEVELS.INFO;
        break;
      case 'warn':
        this.level = LOG_LEVELS.WARN;
        break;
      case 'error':
        this.level = LOG_LEVELS.ERROR;
        break;
      default:
        this.level = LOG_LEVELS.INFO;
    }
  }

  private formatMessage(
    level: string,
    message: string,
    ...args: unknown[]
  ): void {
    if (typeof console === 'undefined') return;

    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.context}]`;
    const fullMessage = `${prefix} ${message}`;

    // Use appropriate console method based on level
    switch (level.toLowerCase()) {
      case 'error':
        // eslint-disable-next-line no-console
        console.error(fullMessage, ...args);
        break;
      case 'warn':
        // eslint-disable-next-line no-console
        console.warn(fullMessage, ...args);
        break;
      case 'debug':
        // eslint-disable-next-line no-console
        console.debug(fullMessage, ...args);
        break;
      default:
        // eslint-disable-next-line no-console
        console.log(fullMessage, ...args);
    }
  }

  debug(data: { message: string; [key: string]: any } | string, ...args: unknown[]): void {
    if (this.level <= LOG_LEVELS.DEBUG) {
      const message = typeof data === 'string' ? data : data.message;
      const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
      this.formatMessage('debug', message, ...extraArgs, ...args);
    }
  }

  info(data: { message: string; [key: string]: any } | string, ...args: unknown[]): void {
    if (this.level <= LOG_LEVELS.INFO) {
      const message = typeof data === 'string' ? data : data.message;
      const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
      this.formatMessage('info', message, ...extraArgs, ...args);
    }
  }

  warn(data: { message: string; [key: string]: any } | string, ...args: unknown[]): void {
    if (this.level <= LOG_LEVELS.WARN) {
      const message = typeof data === 'string' ? data : data.message;
      const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
      this.formatMessage('warn', message, ...extraArgs, ...args);
    }
  }

  error(data: { message: string; [key: string]: any } | string, ...args: unknown[]): void {
    if (this.level <= LOG_LEVELS.ERROR) {
      const message = typeof data === 'string' ? data : data.message;
      const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
      this.formatMessage('error', message, ...extraArgs, ...args);
    }
  }

  // Production-safe logging methods
  logInProduction(
    level: 'info' | 'warn' | 'error',
    message: string,
    ...args: unknown[]
  ): void {
    // Only log warnings and errors in production
    if (process.env.NODE_ENV === 'production' && level === 'info') {
      return;
    }

    this[level](message, ...args);
  }

  // Method for replacing console.log calls
  log(data: { message: string; [key: string]: any } | string, ...args: unknown[]): void {
    this.info(data, ...args);
  }
}

// Create default logger instance
const logger = new SharedLogger();

// Convenience functions for easy migration from console.*
export const log = (data: { message: string; [key: string]: any } | string, ...args: unknown[]) =>
  logger.log(typeof data === 'string' ? data : data.message, ...args);
export const info = (data: { message: string; [key: string]: any } | string, ...args: unknown[]) =>
  logger.info(data, ...args);
export const warn = (data: { message: string; [key: string]: any } | string, ...args: unknown[]) =>
  logger.warn(data, ...args);
export const error = (data: { message: string; [key: string]: any } | string, ...args: unknown[]) =>
  logger.error(data, ...args);
export const debug = (data: { message: string; [key: string]: any } | string, ...args: unknown[]) =>
  logger.debug(data, ...args);

export { SharedLogger };
export default logger;
