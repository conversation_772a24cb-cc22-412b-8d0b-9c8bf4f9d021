// API path constants for consistent endpoint definitions
// This file serves as the Single Source of Truth for all API paths

export const API_PATHS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    REFRESH_TOKEN: '/auth/refresh-token', // Alternative endpoint
    VERIFY_EMAIL: '/auth/verify-email',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    PROFILE: '/auth/profile',
    CHECK_EMAIL: '/auth/check-email',
    TEST: '/auth/test',
    CSRF_TOKEN: '/auth/csrf-token',
  },

  // User management endpoints
  USERS: {
    BASE: '/users',
    ME: '/users/me',
    PROFILE: '/users/me/profile',
    SETTINGS: '/users/me/settings',
    STATISTICS: '/users/statistics',
    EXTENDED_STATISTICS: '/users/extended-statistics',
    PASSWORD: '/users/me/password',
    AVATAR: '/users/me/avatar',
    PREFERENCES: '/users/preferences',
    BY_ID: (userId: string | number) => `/users/${userId}`,
  },

  // Project endpoints
  PROJECTS: {
    BASE: '/projects',
    BY_ID: (projectId: string | number) => `/projects/${projectId}`,
    IMAGES: (projectId: string | number) => `/projects/${projectId}/images`,
    SHARE: (projectId: string | number) => `/projects/${projectId}/share`,
    DOWNLOAD: (projectId: string | number) => `/projects/${projectId}/download`,
    EXPORT: (projectId: string | number) => `/projects/${projectId}/export`,
  },

  // Segmentation endpoints
  SEGMENTATION: {
    BASE: '/segmentation',
    PROCESS: '/segmentation/process',
    START: '/segmentation/start',
    BATCH: '/segmentation/batch',
    STATUS: (taskId: string) => `/segmentation/status/${taskId}`,
    RESULT: (taskId: string) => `/segmentation/result/${taskId}`,
    QUEUE: '/segmentation/queue',
    SEGMENTS: '/segmentation/segments',
    EXPORT: '/segmentation/export',
    IMPORT_MASK: '/segmentation/import/mask',
  },

  // Image endpoints
  IMAGES: {
    BASE: '/images',
    UPLOAD: '/images/upload',
    BY_ID: (imageId: string | number) => `/images/${imageId}`,
    DOWNLOAD: (imageId: string | number) => `/images/${imageId}/download`,
    SEGMENT: (imageId: string | number) => `/images/${imageId}/segment`,
    DELETE_MANY: '/images/delete-many',
    SEGMENT_MANY: '/images/segment-many',
  },

  // File upload endpoints
  UPLOADS: {
    BASE: '/uploads',
    IMAGES: '/uploads/images',
  },

  // Admin endpoints
  ADMIN: {
    BASE: '/admin',
    USERS: '/admin/users',
    METRICS: '/admin/metrics',
    LOGS: '/admin/logs',
  },

  // Access requests endpoints
  ACCESS_REQUESTS: {
    BASE: '/access-requests',
    CREATE: '/access-requests',
  },

  // Machine Learning service endpoints
  ML: {
    BASE: '/ml',
    PREDICT: '/ml/predict',
    STATUS: '/ml/status',
  },

  // User profile service endpoints
  USER_PROFILE: {
    BASE: '/user-profile',
  },

  // User statistics endpoints
  USER_STATS: {
    BASE: '/user-stats',
    STATS: '/user-stats/stats',
  },

  // Database metrics endpoints
  DB_METRICS: {
    BASE: '/db-metrics',
  },

  // System and health endpoints
  SYSTEM: {
    HEALTH: '/health',
    HEALTH_LIVE: '/health/live',
    HEALTH_READY: '/health/ready',
    HEALTH_DETAILED: '/health/detailed',
    HEALTH_DB: '/health/db',
    HEALTH_REDIS: '/health/redis',
    HEALTH_ML: '/health/ml',
    HEALTH_DB_METRICS: '/health/db/metrics',
    HEALTH_RABBITMQ: '/health/rabbitmq',
    STATUS: '/status',
    VERSION: '/version',
    METRICS: '/metrics',
    METRICS_PERFORMANCE: '/metrics/performance',
    METRICS_FRONTEND: '/metrics/frontend',
    METRICS_VITALS: '/metrics/vitals',
    METRICS_IMAGE_LOAD: '/metrics/image-load',
    METRICS_ROUTE_PREFETCH: '/metrics/route-prefetch',
  },

  // Analytics endpoints
  ANALYTICS: {
    BASE: '/analytics',
    EVENTS: '/analytics/events',
  },

  // CDN endpoints
  CDN: {
    PURGE: '/cdn/purge',
  },

  // Experiment endpoints
  EXPERIMENTS: {
    BASE: '/experiments',
  },

  // User geo endpoints
  USER: {
    GEO: '/user/geo',
  },
} as const;

// Export individual path groups for convenience
export const { 
  AUTH, 
  USERS, 
  PROJECTS, 
  SEGMENTATION, 
  IMAGES, 
  UPLOADS, 
  ADMIN,
  ACCESS_REQUESTS,
  ML,
  USER_PROFILE,
  USER_STATS,
  DB_METRICS,
  SYSTEM,
  ANALYTICS,
  CDN,
  EXPERIMENTS,
  USER
} = API_PATHS;

// Type for API paths
export type ApiPaths = typeof API_PATHS;

/**
 * Utility function to format API paths with /api prefix for full URLs
 * @param path - The API path to format
 * @returns The formatted API path with /api prefix
 */
export const formatApiPath = (path: string): string => {
  // If path already starts with /api, just return it
  if (path.startsWith('/api/')) {
    return path;
  }
  
  // Otherwise, ensure path starts with / and add /api prefix
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `/api${normalizedPath}`;
};

/**
 * Utility function for building URL parameters
 * @param params - The parameters to include in the URL
 * @returns A string of URL parameters
 */
export const buildUrlParams = (
  params: Record<string, string | number | boolean | undefined>,
): string => {
  const validParams = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`,
    );

  return validParams.length ? `?${validParams.join('&')}` : '';
};

// Default export for convenience
export default API_PATHS;