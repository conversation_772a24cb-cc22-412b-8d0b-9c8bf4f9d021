import React from 'react';

interface AuthFormLayoutProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
}

const AuthFormLayout: React.FC<AuthFormLayoutProps> = ({
  title,
  subtitle,
  children,
  footer,
}) => {
  return (
    <div className="auth-form-layout">
      <div className="auth-form-header">
        <h2>{title}</h2>
        {subtitle && <p>{subtitle}</p>}
      </div>
      <div className="auth-form-content">{children}</div>
      {footer && <div className="auth-form-footer">{footer}</div>}
    </div>
  );
};

export default AuthFormLayout;
