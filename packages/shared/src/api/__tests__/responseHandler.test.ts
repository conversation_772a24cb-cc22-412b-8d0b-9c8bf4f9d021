/// <reference types="vitest/globals" />
import { z, ZodError } from 'zod';
import { UnifiedResponseHandler } from '../responseHandler';
import { ApiResponse } from '../response.types';

describe('UnifiedResponseHandler', () => {
  describe('transform', () => {
    it('should transform raw data into standardized response', () => {
      const rawData = { id: '123', name: 'Test' };
      const result = UnifiedResponseHandler.transform(rawData);

      expect(result).toEqual({
        success: true,
        data: rawData,
        metadata: expect.objectContaining({
          timestamp: expect.any(String),
        }),
      });
    });

    it('should validate data with Zod schema', () => {
      const schema = z.object({
        id: z.string(),
        name: z.string(),
      });
      const rawData = { id: '123', name: 'Test' };
      const result = UnifiedResponseHandler.transform(rawData, schema);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(rawData);
    });

    it('should throw validation error for invalid data', () => {
      const schema = z.object({
        id: z.string(),
        name: z.string(),
      });
      const invalidData = { id: 123, name: 'Test' }; // id should be string

      expect(() => {
        UnifiedResponseHandler.transform(invalidData, schema);
      }).toThrow();
    });

    it('should handle already standardized responses', () => {
      const standardResponse = {
        success: true,
        data: { id: '123' },
        message: 'Test message',
      };
      const result = UnifiedResponseHandler.transform(standardResponse);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ id: '123' });
      expect(result.message).toBe('Test message');
    });
  });

  describe('handleError', () => {
    it('should handle ZodError', () => {
      const zodError = new ZodError([
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'number',
          path: ['id'],
          message: 'Expected string, received number',
        },
      ]);

      const result = UnifiedResponseHandler.handleError(zodError);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.message).toBe('Validation failed');
      expect(result.errors).toBeDefined();
      expect(result.errors).toHaveLength(1);
      expect(result.errors?.[0]?.code).toBe('invalid_type');
    });

    it('should handle standard Error', () => {
      const error = new Error('Something went wrong');
      const result = UnifiedResponseHandler.handleError(error);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.message).toBe('Something went wrong');
      expect(result.error).toEqual({
        code: 'INTERNAL_ERROR',
        message: 'Something went wrong',
        details: expect.objectContaining({
          name: 'Error',
        }),
      });
    });

    it('should handle HTTP errors', () => {
      const httpError = {
        status: 404,
        message: 'Resource not found',
      };

      const result = UnifiedResponseHandler.handleError(httpError);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Resource not found');
      expect(result.error).toEqual({
        code: 'HTTP_404',
        message: 'Resource not found',
        details: { status: 404 },
      });
    });

    it('should handle unknown errors', () => {
      const unknownError = 'Something unexpected';
      const result = UnifiedResponseHandler.handleError(unknownError);

      expect(result.success).toBe(false);
      expect(result.message).toBe('An unknown error occurred');
      expect(result.error).toEqual({
        code: 'UNKNOWN_ERROR',
        message: 'Something unexpected',
      });
    });

    it('should include context in error response', () => {
      const error = new Error('Test error');
      const context = { path: '/api/test', operation: 'GET /api/test' };
      const result = UnifiedResponseHandler.handleError(error, context);

      expect(result.error?.path).toBe('/api/test');
    });
  });

  describe('success', () => {
    it('should create a success response', () => {
      const data = { id: '123', name: 'Test' };
      const result = UnifiedResponseHandler.success(
        data,
        'Operation successful'
      );

      expect(result).toEqual({
        success: true,
        data,
        message: 'Operation successful',
        metadata: expect.objectContaining({
          timestamp: expect.any(String),
        }),
      });
    });

    it('should include custom metadata', () => {
      const data = { id: '123' };
      const metadata = { version: '1.0.0' };
      const result = UnifiedResponseHandler.success(data, undefined, metadata);

      expect(result.metadata).toEqual(
        expect.objectContaining({
          timestamp: expect.any(String),
          version: '1.0.0',
        })
      );
    });
  });

  describe('error', () => {
    it('should create an error response', () => {
      const result = UnifiedResponseHandler.error(
        'Validation failed',
        'VALIDATION_ERROR'
      );

      expect(result).toEqual({
        success: false,
        data: null,
        message: 'Validation failed',
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
        },
        metadata: expect.objectContaining({
          timestamp: expect.any(String),
        }),
      });
    });

    it('should include validation errors', () => {
      const validationErrors = [
        { field: 'email', message: 'Invalid email', code: 'invalid_email' },
      ];
      const result = UnifiedResponseHandler.error(
        'Validation failed',
        'VALIDATION_ERROR',
        validationErrors
      );

      expect(result.errors).toEqual(validationErrors);
    });
  });

  describe('paginated', () => {
    it('should create a paginated response', () => {
      const data = [
        { id: '1', name: 'Item 1' },
        { id: '2', name: 'Item 2' },
      ];
      const pagination = {
        page: 1,
        pageSize: 10,
        totalItems: 2,
      };

      const result = UnifiedResponseHandler.paginated(data, pagination);

      expect(result).toEqual({
        success: true,
        data,
        metadata: expect.objectContaining({
          timestamp: expect.any(String),
          pagination: {
            page: 1,
            pageSize: 10,
            totalPages: 1,
            totalItems: 2,
          },
        }),
      });
    });

    it('should calculate total pages correctly', () => {
      const data: any[] = [];
      const pagination = {
        page: 2,
        pageSize: 10,
        totalItems: 25,
      };

      const result = UnifiedResponseHandler.paginated(data, pagination);

      expect(result.metadata?.pagination?.totalPages).toBe(3);
    });

    it('should include message in paginated response', () => {
      const data: any[] = [];
      const pagination = {
        page: 1,
        pageSize: 10,
        totalItems: 0,
      };

      const result = UnifiedResponseHandler.paginated(
        data,
        pagination,
        'No items found'
      );

      expect(result.message).toBe('No items found');
    });
  });

  describe('Type guards', () => {
    it('isApiSuccess should correctly identify success responses', () => {
      const success: ApiResponse = {
        success: true,
        data: { id: '123' },
      };
      // Remove unused variable
      // const _error: ApiErrorResponse = {
      //   success: false,
      //   data: null,
      //   message: 'Error',
      // };

      expect(UnifiedResponseHandler.transform(success)).toMatchObject({
        success: true,
      });
      expect(UnifiedResponseHandler.handleError(new Error())).toMatchObject({
        success: false,
      });
    });
  });
});
