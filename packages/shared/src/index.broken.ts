// Simplified index file for @spheroseg/shared
// Only export existing and working modules

// Export utilities that exist
export * from './utils/polygonUtils';

// Export constants that exist
export * from './constants/segmentationStatus';
export * from './constants/apiPaths';

// Export monitoring utilities that exist
export * from './monitoring';

// Export utility functions that are needed by frontend
export const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const createWorkerRequest = (
  operation: string,
  data: unknown
): { id: string; operation: string; data: unknown } => {
  return {
    id: generateRequestId(),
    operation,
    data,
  };
};

export const createWorkerResponse = (
  id: string,
  result: unknown,
  error?: string
): { id: string; result?: unknown; error?: string } => {
  const response: { id: string; result?: unknown; error?: string } = {
    id,
    result,
  };

  if (error !== undefined) {
    response.error = error;
  }

  return response;
};
