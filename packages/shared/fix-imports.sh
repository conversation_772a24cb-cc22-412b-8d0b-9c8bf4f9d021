#!/bin/bash
# Fix .js extensions for ES module imports in compiled JavaScript files

echo "🔧 Fixing ES module imports in compiled JavaScript files..."

# Find all .js files and fix relative imports without .js extensions
find dist -name "*.js" -type f | while read -r file; do
  echo "Processing: $file"
  # Use a simpler approach - find lines with 'from ' followed by relative paths and add .js
  # First handle single quotes
  sed -i "s/from '\(\.[^']*\)';\$/from '\1.js';/" "$file"
  sed -i "s/from '\(\.[^']*\)'/from '\1.js'/g" "$file"
  # Then handle double quotes  
  sed -i 's/from "\(\.[^"]*\)";$/from "\1.js";/' "$file"
  sed -i 's/from "\(\.[^"]*\)"/from "\1.js"/g' "$file"
  # Remove any double .js extensions that might be created
  sed -i 's/\.js\.js/.js/g' "$file"
  # Fix specific case where ./utils should be ./utils/index.js
  sed -i "s/from '\.\/utils\.js'/from '.\/utils\/index.js'/g" "$file"
done

echo "✅ Fixed all relative imports to include .js extensions"