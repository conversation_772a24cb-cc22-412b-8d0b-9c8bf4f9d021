// Shared Types Package
// Consolidates common types used across frontend and backend

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Project types  
export interface Project {
  id: string;
  userId: string;
  name: string;
  description?: string;
  status: 'active' | 'archived' | 'deleted';
  imageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Image types
export interface Image {
  id: string;
  projectId: string;
  filename: string;
  originalFilename: string;
  fileSize: number;
  width: number;
  height: number;
  segmentationStatus: 'pending' | 'processing' | 'completed' | 'failed';
  uploadDate: Date;
  processingStartedAt?: Date;
  processingCompletedAt?: Date;
  processingTime?: number;
  errorMessage?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Segmentation types
export interface SegmentationResult {
  id: string;
  imageId: string;
  resultPath: string;
  confidence: number;
  cellCount: number;
  maskPath?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

// Email types
export type EmailType = 
  | 'password_reset'
  | 'project_share_invitation'
  | 'welcome'
  | 'notification';

export interface EmailData {
  to: string;
  subject?: string;
  name?: string;
  resetToken?: string;
  resetLink?: string;
  projectName?: string;
  inviteLink?: string;
  ownerName?: string;
  [key: string]: any;
}

// Queue types
export interface QueueJob {
  id: string;
  type: 'segmentation' | 'processing' | 'cleanup' | 'general';
  data: any;
  priority?: number;
  delay?: number;
  attempts?: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  error?: string;
}

// File upload types
export interface FileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer?: Buffer;
}

// Authentication types
export interface AuthToken {
  token: string;
  refreshToken?: string;
  expiresAt: Date;
  userId: string;
}

// Pagination types
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and sort types
export interface QueryFilters {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Project sharing types
export interface ProjectShare {
  id: string;
  projectId: string;
  email: string;
  role: 'viewer' | 'editor' | 'admin';
  status: 'pending' | 'accepted' | 'rejected';
  invitationToken?: string;
  createdAt: Date;
  acceptedAt?: Date;
}