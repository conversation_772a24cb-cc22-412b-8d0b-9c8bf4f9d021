/**
 * Generators Utility Module
 * Consolidated utility functions for generators
 */

/**
 * Get polygon style based on selection and type
 */
export const getPolygonStyle = (polygon: { id: string; type: 'internal' | 'external' }, selectedPolygonId?: string) => {
  const isSelected = polygon.id === selectedPolygonId;
  const baseColor = polygon.type === 'internal' ? 'blue' : 'red';
  
  const baseFillOpacity = isSelected ? 0.5 : 0.3;
  const baseFill = polygon.type === 'internal' 
    ? `rgba(0, 0, 255, ${baseFillOpacity})` 
    : `rgba(255, 0, 0, ${baseFillOpacity})`;

  return {
    fill: baseFill,
    stroke: baseColor,
    strokeWidth: isSelected ? 2 : 1,
  };
};

/**
 * Get user statistics
 */
export const getUserStats = async (userId: string, pool: any) => {
  const service = createUserStatsService(pool);
  return service.getUserStats(userId);
};

/**
 * Simulate window resize for testing
 */
export const simulateResize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', { writable: true, value: width });
  Object.defineProperty(window, 'innerHeight', { writable: true, value: height });
  window.dispatchEvent(new Event('resize'));
};

/**
 * Simulate drag over event for testing
 */
export const simulateDragOver = (element: HTMLElement) => {
  const dragOverEvent = new DragEvent('dragover', {
    bubbles: true,
    cancelable: true,
  });
  element.dispatchEvent(dragOverEvent);
};

// Helper function to create user stats service
function createUserStatsService(_pool: any) {
  return {
    getUserStats: async (_userId: string) => {
      // Mock implementation
      return {
        totalProjects: 0,
        totalImages: 0,
        totalSegmentations: 0,
      };
    },
  };
}