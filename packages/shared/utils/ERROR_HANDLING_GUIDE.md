# Error Handling Guide

## Standardized Error Patterns

### 1. Try/Catch Blocks
```typescript
import { safeExecute, standardErrorHandler } from './errorHandling';

// Standard pattern
try {
    const result = await riskyOperation();
    return result;
} catch (error) {
    standardErrorHandler(error, 'riskyOperation');
    throw error;
}

// Using wrapper
const result = await safeExecute(
    () => riskyOperation(),
    'riskyOperation',
    defaultValue
);
```

### 2. API Error Responses
```typescript
import { standardErrorResponse, ApiError } from './errorHandling';

app.use((error, req, res, next) => {
    standardErrorResponse(error, req, res);
});

// Throwing API errors
throw new ApiError('User not found', 404);
```

### 3. Validation Errors
```typescript
import { createValidationError } from './errorHandling';

if (!email) {
    throw createValidationError('email', 'Email is required');
}
```

## Migration from Legacy Patterns

Replace old patterns with new standardized ones:

- Old: `catch (err) { console.log(err); }`
- New: `catch (error) { standardErrorHandler(error, 'context'); }`

- Old: `res.status(500).json({ error: 'Error' });`
- New: `standardErrorResponse(error, req, res);`
