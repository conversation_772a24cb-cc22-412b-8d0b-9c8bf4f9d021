# API Development Guide

## Standardized API Patterns

### Response Helpers
```typescript
import { sendSuccess, sendError } from '@shared/utils/apiHelpers';

// Success responses
sendSuccess(res, data, 'Operation completed successfully', 200);

// Error responses
sendError(res, 'Invalid input', 400);
sendError(res, new Error('Database error'), 500);
```

### Authentication & Authorization
```typescript
import { authenticateUser, authorizeRole } from '@shared/utils/apiHelpers';

// Protect routes
app.use('/api/protected', authenticateUser);

// Role-based access
app.use('/api/admin', authenticateUser, authorizeRole(['admin']));
```

### CRUD Operations
```typescript
import { createCrudHandler } from '@shared/utils/apiHelpers';

const userHandler = createCrudHandler(UserModel, options);

router.post('/users', userHandler.create);
router.get('/users', userHandler.findAll);
router.get('/users/:id', userHandler.findById);
router.put('/users/:id', userHandler.update);
router.delete('/users/:id', userHandler.delete);
```

### File Uploads
```typescript
import { createUploadHandler } from '@shared/utils/apiHelpers';

const upload = createUploadHandler({
    destination: 'uploads/images/',
    allowedTypes: ['image/jpeg', 'image/png'],
    maxSize: 5 * 1024 * 1024 // 5MB
});

router.post('/upload', upload.single('image'), uploadController);
```

### Rate Limiting
```typescript
import { createRateLimit } from '@shared/utils/apiHelpers';

const apiLimiter = createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // 100 requests per IP
});

app.use('/api/', apiLimiter);
```

## Migration from Legacy Patterns

Replace old API patterns:

- Old: Manual res.status().json() everywhere
- New: Use sendSuccess() and sendError()

- Old: Duplicate auth middleware
- New: Use authenticateUser and authorizeRole

- Old: Custom CRUD implementations
- New: Use createCrudHandler()
