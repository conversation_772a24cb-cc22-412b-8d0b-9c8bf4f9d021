# Validation Guide

## Enhanced Validation System

### Basic Validation
```typescript
import { 
    isValidEmail, 
    isValidUrl, 
    validateRequired,
    validateStringLength 
} from '@shared/utils/validation';

// Email validation
if (!isValidEmail(email)) {
    throw new Error('Invalid email format');
}

// URL validation
if (!isValidUrl(website)) {
    throw new Error('Invalid URL format');
}

// Required fields
validateRequired(username, 'Username');

// String length
validateStringLength(password, 8, 128, 'Password');
```

### Advanced Validation
```typescript
import { 
    validateNumber,
    validateFileType,
    validateDate,
    validatePassword 
} from '@shared/utils/validation';

// Number validation
validateNumber(age, 0, 120, 'Age');

// File validation
validateFileType(uploadedFile, ['.jpg', '.png', '.gif'], 'Profile Image');

// Date validation
const validDate = validateDate('2023-12-25', 'YYYY-MM-DD', 'Birth Date');

// Password validation
validatePassword(newPassword, 8, true);
```

### Object Validation
```typescript
import { validateObject } from '@shared/utils/validation';

const validationRules = {
    email: (value) => {
        validateRequired(value, 'Email');
        if (!isValidEmail(value)) throw new Error('Invalid email format');
    },
    age: (value) => validateNumber(value, 0, 120, 'Age'),
    password: (value) => validatePassword(value)
};

const result = validateObject(userData, validationRules);
if (!result.isValid) {
    throw new Error(result.errors.join(', '));
}
```

## Migration from Legacy Patterns

Replace old validation patterns:

- Old: `if (!email.includes('@')) { ... }`
- New: `if (!isValidEmail(email)) { ... }`

- Old: `if (password.length < 8) { ... }`
- New: `validatePassword(password, 8);`
