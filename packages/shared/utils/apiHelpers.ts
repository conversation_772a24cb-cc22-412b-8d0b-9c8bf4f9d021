/**
 * API Helpers Module
 * Standardized API patterns and utilities for SpheroSeg
 * Created by Phase 5 Consolidator
 */

import { Request, Response, NextFunction } from 'express';
import { standardErrorResponse, ApiError } from './errorHandling';

// Response helpers for consistent API responses
export const sendSuccess = (
    res: Response,
    data: any = null,
    message: string = 'Success',
    statusCode: number = 200
) => {
    res.status(statusCode).json({
        success: true,
        message,
        data,
        timestamp: new Date().toISOString()
    });
};

export const sendError = (
    res: Response,
    error: string | Error,
    statusCode: number = 500,
    details?: any
) => {
    const errorMessage = error instanceof Error ? error.message : error;
    
    res.status(statusCode).json({
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? details : undefined,
        timestamp: new Date().toISOString()
    });
};

// Authentication middleware
export const authenticateUser = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return sendError(res, 'Authentication token required', 401);
        }
        
        // TODO: Implement actual token verification
        // const user = await verifyToken(token);
        // req.user = user;
        
        next();
    } catch (error) {
        standardErrorResponse(error as Error, req, res);
    }
};

// Role-based authorization
export const authorizeRole = (roles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
        try {
            const userRole = (req as any).user?.role;
            
            if (!userRole || !roles.includes(userRole)) {
                return sendError(res, 'Insufficient permissions', 403);
            }
            
            next();
        } catch (error) {
            standardErrorResponse(error as Error, req, res);
        }
    };
};

// Request validation middleware
export const validateRequest = (schema: any) => {
    return (req: Request, res: Response, next: NextFunction) => {
        try {
            // TODO: Implement schema validation (Joi, Yup, etc.)
            // const { error } = schema.validate(req.body);
            // if (error) {
            //     return sendError(res, error.details[0].message, 400);
            // }
            
            next();
        } catch (error) {
            standardErrorResponse(error as Error, req, res);
        }
    };
};

// CORS configuration
export const corsConfig = {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// Generic CRUD handler creator
export const createCrudHandler = (model: any, options: any = {}) => {
    return {
        // Create
        create: async (req: Request, res: Response) => {
            try {
                const data = await model.create(req.body);
                sendSuccess(res, data, 'Created successfully', 201);
            } catch (error) {
                standardErrorResponse(error as Error, req, res);
            }
        },
        
        // Read all
        findAll: async (req: Request, res: Response) => {
            try {
                const data = await model.findAll(req.query);
                sendSuccess(res, data, 'Retrieved successfully');
            } catch (error) {
                standardErrorResponse(error as Error, req, res);
            }
        },
        
        // Read one
        findById: async (req: Request, res: Response) => {
            try {
                const data = await model.findById(req.params.id);
                if (!data) {
                    return sendError(res, 'Not found', 404);
                }
                sendSuccess(res, data, 'Retrieved successfully');
            } catch (error) {
                standardErrorResponse(error as Error, req, res);
            }
        },
        
        // Update
        update: async (req: Request, res: Response) => {
            try {
                const data = await model.findByIdAndUpdate(req.params.id, req.body, { new: true });
                if (!data) {
                    return sendError(res, 'Not found', 404);
                }
                sendSuccess(res, data, 'Updated successfully');
            } catch (error) {
                standardErrorResponse(error as Error, req, res);
            }
        },
        
        // Delete
        delete: async (req: Request, res: Response) => {
            try {
                const data = await model.findByIdAndDelete(req.params.id);
                if (!data) {
                    return sendError(res, 'Not found', 404);
                }
                sendSuccess(res, null, 'Deleted successfully');
            } catch (error) {
                standardErrorResponse(error as Error, req, res);
            }
        }
    };
};

// Upload handler creator
export const createUploadHandler = (config: any = {}) => {
    const multer = require('multer');
    
    const storage = multer.diskStorage({
        destination: config.destination || 'uploads/',
        filename: (req: any, file: any, cb: any) => {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
        }
    });
    
    const fileFilter = (req: any, file: any, cb: any) => {
        const allowedTypes = config.allowedTypes || ['image/jpeg', 'image/png', 'image/gif'];
        
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new ApiError('Invalid file type', 400), false);
        }
    };
    
    return multer({
        storage,
        fileFilter,
        limits: {
            fileSize: config.maxSize || 5 * 1024 * 1024 // 5MB default
        }
    });
};

// Rate limiting helper
export const createRateLimit = (options: any = {}) => {
    const rateLimit = require('express-rate-limit');
    
    return rateLimit({
        windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes
        max: options.max || 100, // limit each IP to 100 requests per windowMs
        message: options.message || 'Too many requests from this IP',
        standardHeaders: true,
        legacyHeaders: false
    });
};

// Request logging middleware
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        console.log(`${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    });
    
    next();
};

// Health check endpoint
export const healthCheck = (req: Request, res: Response) => {
    sendSuccess(res, {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0'
    }, 'Service is healthy');
};
