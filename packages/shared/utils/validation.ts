/**
 * Enhanced Validation Module
 * Comprehensive validation utilities for SpheroSeg
 * Enhanced by Phase 4 Consolidator
 */

// Email validation with comprehensive pattern
export const isValidEmail = (email: string): boolean => {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email.trim());
};

// URL validation with protocol support
export const isValidUrl = (url: string): boolean => {
    if (!url || typeof url !== 'string') return false;
    
    try {
        const urlObj = new URL(url);
        return ['http:', 'https:', 'ftp:', 'ftps:'].includes(urlObj.protocol);
    } catch {
        return false;
    }
};

// Required field validation
export const validateRequired = (value: any, fieldName?: string): void => {
    if (value === null || value === undefined || value === '') {
        const field = fieldName || 'Field';
        throw new Error(`${field} is required`);
    }
    
    if (typeof value === 'string' && value.trim() === '') {
        const field = fieldName || 'Field';
        throw new Error(`${field} cannot be empty`);
    }
};

// String length validation
export const validateStringLength = (
    str: string, 
    minLength: number = 0, 
    maxLength: number = Infinity,
    fieldName?: string
): void => {
    if (typeof str !== 'string') {
        throw new Error(`${fieldName || 'Value'} must be a string`);
    }
    
    const length = str.trim().length;
    
    if (length < minLength) {
        throw new Error(`${fieldName || 'Value'} must be at least ${minLength} characters long`);
    }
    
    if (length > maxLength) {
        throw new Error(`${fieldName || 'Value'} must be no more than ${maxLength} characters long`);
    }
};

// Number validation with range support
export const validateNumber = (
    num: number,
    min: number = -Infinity,
    max: number = Infinity,
    fieldName?: string
): void => {
    if (typeof num !== 'number' || isNaN(num)) {
        throw new Error(`${fieldName || 'Value'} must be a valid number`);
    }
    
    if (num < min) {
        throw new Error(`${fieldName || 'Value'} must be at least ${min}`);
    }
    
    if (num > max) {
        throw new Error(`${fieldName || 'Value'} must be no more than ${max}`);
    }
};

// File type validation
export const validateFileType = (
    file: File | { name: string; type?: string },
    allowedTypes: string[],
    fieldName?: string
): void => {
    if (!file || !file.name) {
        throw new Error(`${fieldName || 'File'} is required`);
    }
    
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
    
    const isValidExtension = allowedTypes.some(type => 
        fileName.endsWith(type.toLowerCase()) || 
        fileExtension === type.toLowerCase()
    );
    
    if (!isValidExtension) {
        throw new Error(`${fieldName || 'File'} must be one of: ${allowedTypes.join(', ')}`);
    }
    
    // Check MIME type if available
    if (file.type) {
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg', 
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain'
        };
        
        const expectedMime = mimeTypes[fileExtension];
        if (expectedMime && file.type !== expectedMime) {
            throw new Error(`${fieldName || 'File'} type mismatch`);
        }
    }
};

// Date validation with format support
export const validateDate = (
    dateString: string,
    format: string = 'YYYY-MM-DD',
    fieldName?: string
): Date => {
    if (!dateString || typeof dateString !== 'string') {
        throw new Error(`${fieldName || 'Date'} is required`);
    }
    
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
        throw new Error(`${fieldName || 'Date'} must be a valid date`);
    }
    
    // Basic format validation for common formats
    if (format === 'YYYY-MM-DD' && !/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        throw new Error(`${fieldName || 'Date'} must be in YYYY-MM-DD format`);
    }
    
    return date;
};

// Input sanitization
export const sanitizeInput = (input: string): string => {
    if (typeof input !== 'string') return '';
    
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML
        .replace(/['"]/g, '') // Remove quotes
        .substring(0, 1000); // Limit length
};

// Password validation
export const validatePassword = (
    password: string,
    minLength: number = 8,
    requireSpecialChars: boolean = true
): void => {
    if (!password || typeof password !== 'string') {
        throw new Error('Password is required');
    }
    
    if (password.length < minLength) {
        throw new Error(`Password must be at least ${minLength} characters long`);
    }
    
    if (requireSpecialChars) {
        if (!/[A-Z]/.test(password)) {
            throw new Error('Password must contain at least one uppercase letter');
        }
        
        if (!/[a-z]/.test(password)) {
            throw new Error('Password must contain at least one lowercase letter');
        }
        
        if (!/[0-9]/.test(password)) {
            throw new Error('Password must contain at least one number');
        }
        
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            throw new Error('Password must contain at least one special character');
        }
    }
};

// Phone number validation (basic international format)
export const validatePhoneNumber = (phone: string): boolean => {
    if (!phone || typeof phone !== 'string') return false;
    
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Check if it's a reasonable length (7-15 digits)
    return cleaned.length >= 7 && cleaned.length <= 15;
};

// Validation result type
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

// Comprehensive validation function
export const validateObject = (
    obj: Record<string, any>,
    rules: Record<string, (value: any) => void>
): ValidationResult => {
    const errors: string[] = [];
    
    for (const [field, validator] of Object.entries(rules)) {
        try {
            validator(obj[field]);
        } catch (error) {
            errors.push(`${field}: ${(error as Error).message}`);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};
