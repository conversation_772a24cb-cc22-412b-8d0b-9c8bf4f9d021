/**
 * Helpers Utility Module
 * Consolidated utility functions for helpers
 */

/**
 * Logger utility for unified error logging
 */
export const error = (message: string, moduleName: string, meta?: Record<string, any>) => {
  const unifiedLogger = {
    error: (msg: string, options?: any) => {
      console.error(`[${moduleName}] ${msg}`, options);
    }
  };
  return unifiedLogger.error(message, { module: moduleName, ...meta });
};

/**
 * Get footer CSS classes based on theme
 */
export const getFooterClasses = (resolvedTheme: string) => {
  if (resolvedTheme === 'dark') {
    return 'bg-gray-900 text-white';
  }
  return 'bg-white text-gray-900';
};

/**
 * Simulate keyboard press event with target
 */
export const simulateKeyPressWithTarget = (
  target: HTMLElement, 
  key: string, 
  options?: KeyboardEventInit
) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  target.dispatchEvent(event);
};

/**
 * Get error metrics from error tracker
 */
export const getErrorMetrics = () => {
  const errorTracker = {
    getErrorStats: () => ({ total: 0, recent: 0 }),
    getHealthStatus: () => ({ status: 'healthy' })
  };
  
  return {
    stats: errorTracker.getErrorStats(),
    health: errorTracker.getHealthStatus(),
  };
};

/**
 * Clear all mocks, connections and force garbage collection
 */
export const clearAll = () => {
  const clearMocks = () => {
    // Mock clearing logic
  };
  
  const clearConnections = () => {
    // Connection clearing logic
  };
  
  const forceGC = () => {
    if (global.gc) {
      global.gc();
    }
  };
  
  clearMocks();
  clearConnections();
  forceGC();
};

/**
 * Deep clone utility
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};