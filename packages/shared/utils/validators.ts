/**
 * Validators Utility Module
 * Consolidated utility functions for validators
 */

/**
 * Create backend concurrent test runner
 */
export const createBackendConcurrentTestRunner = (maxConcurrency: number = 5) => {
  const queue: Array<() => Promise<void>> = [];
  let running = 0;

  const runNext = async (): Promise<void> => {
    if (queue.length === 0 || running >= maxConcurrency) return;

    running++;
    const test = queue.shift();
    if (test) {
      try {
        await test();
      } catch (error) {
        console.error('Test failed:', error);
      } finally {
        running--;
        runNext();
      }
    }
  };

  return {
    addTest: (test: () => Promise<void>) => {
      queue.push(test);
      runNext();
    },
    waitForCompletion: () => {
      return new Promise<void>((resolve) => {
        const checkComplete = () => {
          if (queue.length === 0 && running === 0) {
            resolve();
          } else {
            setTimeout(checkComplete, 100);
          }
        };
        checkComplete();
      });
    }
  };
};

/**
 * Test start handler
 */
export const onTestStart = (testName: string) => {
  console.log(`Test started: ${testName}`);
};

/**
 * Get contact email with validation
 */
export const getContactEmail = (): string => {
  const appConfig = {
    contact: {
      email: process.env.CONTACT_EMAIL || '<EMAIL>'
    }
  };
  
  const email = appConfig.contact.email;
  if (!isValidEmail(email)) {
    return '<EMAIL>'; // Fallback
  }
  return email;
};

/**
 * Optimize backend test memory
 */
export const optimizeBackendTestMemory = () => {
  const clearMocks = () => {
    // Clear all mocks if jest is available
    if (typeof jest !== 'undefined') {
      jest.clearAllMocks();
    }
  };
  
  const clearBackendMockCache = () => {
    // Clear backend mock cache
  };
  
  clearMocks();
  clearBackendMockCache();
};

/**
 * Database rollback utility
 */
export const rollback = async (client: any) => {
  await client.query('ROLLBACK TO SAVEPOINT test_transaction');
};

/**
 * Mock Auth Provider factory for testing
 */
export const createMockAuthProvider = () => {
  return {
    displayName: 'MockAuthProvider',
    testId: 'mock-auth-provider'
  };
};

/**
 * Email validation utility
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * URL validation utility
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};