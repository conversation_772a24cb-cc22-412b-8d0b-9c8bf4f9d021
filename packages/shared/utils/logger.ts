/**
 * Centralized Logging Utility
 * Replaces scattered console statements
 */

export const logger = {
    info: (message: string, ...args: any[]) => {
        if (process.env.NODE_ENV !== 'production') {
            console.log('[INFO]', message, ...args);
        }
    },
    
    error: (message: string, error?: Error) => {
        console.error('[ERROR]', message, error?.message || '');
        if (error?.stack && process.env.NODE_ENV === 'development') {
            console.error(error.stack);
        }
    },
    
    warn: (message: string, ...args: any[]) => {
        console.warn('[WARN]', message, ...args);
    },
    
    debug: (message: string, ...args: any[]) => {
        if (process.env.NODE_ENV === 'development') {
            console.debug('[DEBUG]', message, ...args);
        }
    }
};