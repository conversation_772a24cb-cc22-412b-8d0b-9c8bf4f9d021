/**
 * Enhanced Error Handling Module
 * Standardized error handling patterns for SpheroSeg
 * Enhanced by Phase 3 Consolidator
 */

export class ApiError extends Error {
    public statusCode: number;
    public isOperational: boolean;
    
    constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
        super(message);
        this.name = 'ApiError';
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        
        Error.captureStackTrace(this, this.constructor);
    }
}

export class ValidationError extends ApiError {
    public field?: string;
    
    constructor(message: string, field?: string) {
        super(message, 400);
        this.name = 'ValidationError';
        this.field = field;
    }
}

export const handleAsyncRoute = (fn: Function) => {
    return (req: any, res: any, next: any) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

export const standardErrorResponse = (error: Error, req?: any, res?: any) => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (res) {
        const statusCode = (error as any).statusCode || 500;
        
        return res.status(statusCode).json({
            success: false,
            error: error.name || 'Error',
            message: error.message,
            ...(isDevelopment && { 
                stack: error.stack,
                request: req ? {
                    method: req.method,
                    url: req.url,
                    params: req.params
                } : undefined
            })
        });
    }
    
    // Console logging fallback
    console.error('Error:', error.message);
    if (isDevelopment) {
        console.error(error.stack);
    }
};

export const createValidationError = (field: string, message: string): ValidationError => {
    return new ValidationError(`Validation failed for ${field}: ${message}`, field);
};

export const createApiError = (message: string, statusCode: number = 500): ApiError => {
    return new ApiError(message, statusCode);
};

export const standardErrorHandler = (error: Error, context?: string): void => {
    const errorContext = context ? ` in ${context}` : '';
    console.error(`Error${errorContext}:`, error.message);
    
    if (process.env.NODE_ENV === 'development') {
        console.error(error.stack);
    }
};

// Standard try/catch wrapper
export const safeExecute = async <T>(
    operation: () => Promise<T>,
    context?: string,
    defaultValue?: T
): Promise<T | undefined> => {
    try {
        return await operation();
    } catch (error) {
        standardErrorHandler(error as Error, context);
        return defaultValue;
    }
};

// Promise error handler
export const handlePromiseRejection = (promise: Promise<any>, context?: string) => {
    return promise.catch(error => {
        standardErrorHandler(error, context);
        throw error;
    });
};
