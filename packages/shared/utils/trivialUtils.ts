/**
 * Common Trivial Utilities
 * Consolidated from duplicate trivial functions
 */

export const noop = (): void => {
    // Intentionally empty function
};

export const identity = <T>(value: T): T => value;

export const isNullOrUndefined = (value: any): boolean => {
    return value === null || value === undefined;
};

export const safeGet = <T>(getter: () => T, defaultValue: T): T => {
    try {
        return getter();
    } catch {
        return defaultValue;
    }
};

export const standardErrorHandler = (error: Error, context?: string): void => {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error.message);
    if (process.env.NODE_ENV === 'development') {
        console.error(error.stack);
    }
};