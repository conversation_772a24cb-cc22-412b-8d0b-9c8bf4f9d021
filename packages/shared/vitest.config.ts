import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./src/testing/setup.ts'],
    include: [
      'src/**/*.{test,spec}.{ts,tsx}',
      'src/**/__tests__/**/*.{ts,tsx}'
    ],
    exclude: [
      'node_modules', 
      'dist', 
      '.turbo',
      'coverage',
      '**/*.d.ts',
      '**/*.js',
      '**/*.js.map'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov', 'json', 'json-summary', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
        '**/__tests__/**',
        '**/testing/**',
        '**/index.ts', // Exclude barrel exports
        '**/*.config.{js,ts}'
      ],
      thresholds: {
        branches: 80,
        functions: 85,
        lines: 90,
        statements: 90
      }
    },
    // Mock configuration
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,
    // Reporter configuration
    reporters: ['default', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    }
  },
  resolve: {
    alias: {
      '@shared': path.resolve(__dirname, './src'),
      '@': path.resolve(__dirname, './src')
    }
  }
});