/**
 * Custom API Error Class
 *
 * Standardized error handling for the API with proper HTTP status codes
 * and consistent error response format.
 */
export declare enum ErrorCode {
    VALIDATION_ERROR = "VALIDATION_ERROR",
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED",
    UNAUTHORIZED = "UNAUTHORIZED",
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
    FORBIDDEN = "FORBIDDEN",
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT",
    DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
    DATABASE_ERROR = "DATABASE_ERROR",
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR",
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
    STORAGE_ERROR = "STORAGE_ERROR"
}
export interface ErrorDetails {
    field?: string;
    value?: unknown;
    constraint?: string;
    message?: string;
}
export declare class ApiError extends Error {
    readonly statusCode: number;
    readonly code: ErrorCode;
    readonly details?: ErrorDetails[];
    readonly isOperational: boolean;
    readonly timestamp: string;
    constructor(message: string, statusCode?: number, code?: ErrorCode, details?: ErrorDetails[], isOperational?: boolean);
    /**
     * Create a validation error
     */
    static validation(message: string, details?: ErrorDetails[]): ApiError;
    /**
     * Create an authentication error
     */
    static unauthorized(message?: string): ApiError;
    /**
     * Create a forbidden error
     */
    static forbidden(message?: string): ApiError;
    /**
     * Create a not found error
     */
    static notFound(message?: string): ApiError;
    /**
     * Create a conflict error
     */
    static conflict(message: string, details?: ErrorDetails[]): ApiError;
    /**
     * Create a rate limit error
     */
    static rateLimit(message?: string): ApiError;
    /**
     * Create a database error
     */
    static database(message: string, originalError?: Error): ApiError;
    /**
     * Create a storage error
     */
    static storage(message: string): ApiError;
    /**
     * Create an external service error
     */
    static externalService(message: string, service?: string): ApiError;
    /**
     * Convert to JSON representation
     */
    toJSON(): {
        timestamp: string;
        details?: ErrorDetails[] | undefined;
        success: boolean;
        error: ErrorCode;
        message: string;
        statusCode: number;
    };
    /**
     * Check if error is operational (expected) vs programming error
     */
    static isOperational(error: Error): boolean;
}
export declare const HTTP_STATUS_CODES: {
    OK: number;
    CREATED: number;
    NO_CONTENT: number;
    BAD_REQUEST: number;
    UNAUTHORIZED: number;
    FORBIDDEN: number;
    NOT_FOUND: number;
    CONFLICT: number;
    UNPROCESSABLE_ENTITY: number;
    INTERNAL_SERVER_ERROR: number;
    SERVICE_UNAVAILABLE: number;
};
export declare class BadRequestError extends ApiError {
    constructor(message?: string);
}
export declare class NotFoundError extends ApiError {
    constructor(message?: string);
}
export declare class UnauthorizedError extends ApiError {
    constructor(message?: string);
}
export declare class ForbiddenError extends ApiError {
    constructor(message?: string);
}
export declare class UnprocessableEntityError extends ApiError {
    constructor(message?: string);
}
export declare class InternalServerError extends ApiError {
    constructor(message?: string);
}
