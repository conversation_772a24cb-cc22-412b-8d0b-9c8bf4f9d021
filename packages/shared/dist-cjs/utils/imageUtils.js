"use strict";
/**
 * Shared image utilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_IMAGE_MIME_TYPES = exports.SUPPORTED_IMAGE_EXTENSIONS = exports.IMAGE_FORMATS = void 0;
exports.isImageFormatSupported = isImageFormatSupported;
exports.getImageExtension = getImageExtension;
exports.isImage = isImage;
exports.validateImageFile = validateImageFile;
exports.getImageDimensions = getImageDimensions;
exports.getSupportedImageFormats = getSupportedImageFormats;
exports.isValidImageFormat = isValidImageFormat;
exports.IMAGE_FORMATS = {
    PNG: 'png',
    JPEG: 'jpeg',
    JPG: 'jpg',
    TIFF: 'tiff',
    TIF: 'tif',
    BMP: 'bmp',
};
exports.SUPPORTED_IMAGE_EXTENSIONS = Object.values(exports.IMAGE_FORMATS);
exports.SUPPORTED_IMAGE_MIME_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/tiff',
    'image/bmp',
];
function isImageFormatSupported(extension) {
    return exports.SUPPORTED_IMAGE_EXTENSIONS.includes(extension.toLowerCase());
}
function getImageExtension(filename) {
    const parts = filename.split('.');
    return parts[parts.length - 1]?.toLowerCase() || '';
}
function isImage(filename) {
    const ext = getImageExtension(filename);
    return isImageFormatSupported(ext);
}
function validateImageFile(file) {
    if (!file) {
        throw new Error('No file provided');
    }
    if (!isValidImageFormat(file.type)) {
        throw new Error(`Unsupported file type: ${file.type}`);
    }
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        throw new Error(`File too large: ${file.size} bytes. Maximum allowed: ${maxSize} bytes`);
    }
    // Check minimum file size (1KB)
    const minSize = 1024;
    if (file.size < minSize) {
        throw new Error(`File too small: ${file.size} bytes. Minimum required: ${minSize} bytes`);
    }
}
function getImageDimensions(width, height) {
    if (width < 0 || height < 0) {
        throw new Error('Image dimensions cannot be negative');
    }
    return { width, height };
}
function getSupportedImageFormats() {
    return [...exports.SUPPORTED_IMAGE_MIME_TYPES];
}
function isValidImageFormat(mimeType) {
    if (!mimeType || typeof mimeType !== 'string') {
        return false;
    }
    return exports.SUPPORTED_IMAGE_MIME_TYPES.includes(mimeType.toLowerCase());
}
const imageUtils = {
    getImagePath: (imagePath) => {
        return imagePath;
    },
    getImageDimensions: (width, height) => {
        return { width, height };
    },
    isImageFormatSupported,
    getImageExtension,
    isImage,
};
exports.default = imageUtils;
