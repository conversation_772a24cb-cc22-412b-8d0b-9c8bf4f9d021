"use strict";
/**
 * Shared Logger Utility
 *
 * Simple logger for shared package that works in both browser and Node.js environments
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedLogger = exports.debug = exports.error = exports.warn = exports.info = exports.log = exports.LOG_LEVELS = void 0;
exports.LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
};
class SharedLogger {
    constructor(context) {
        this.level = exports.LOG_LEVELS.INFO;
        this.context = 'shared';
        if (context) {
            this.context = context;
        }
        // Set log level from environment
        const envLevel = (typeof process !== 'undefined' && process.env?.['LOG_LEVEL']) || 'info';
        this.setLevel(envLevel);
    }
    setLevel(level) {
        switch (level.toLowerCase()) {
            case 'debug':
                this.level = exports.LOG_LEVELS.DEBUG;
                break;
            case 'info':
                this.level = exports.LOG_LEVELS.INFO;
                break;
            case 'warn':
                this.level = exports.LOG_LEVELS.WARN;
                break;
            case 'error':
                this.level = exports.LOG_LEVELS.ERROR;
                break;
            default:
                this.level = exports.LOG_LEVELS.INFO;
        }
    }
    formatMessage(level, message, ...args) {
        if (typeof console === 'undefined')
            return;
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.context}]`;
        const fullMessage = `${prefix} ${message}`;
        // Use appropriate console method based on level
        switch (level.toLowerCase()) {
            case 'error':
                // eslint-disable-next-line no-console
                console.error(fullMessage, ...args);
                break;
            case 'warn':
                // eslint-disable-next-line no-console
                console.warn(fullMessage, ...args);
                break;
            case 'debug':
                // eslint-disable-next-line no-console
                console.debug(fullMessage, ...args);
                break;
            default:
                // eslint-disable-next-line no-console
                console.log(fullMessage, ...args);
        }
    }
    debug(data, ...args) {
        if (this.level <= exports.LOG_LEVELS.DEBUG) {
            const message = typeof data === 'string' ? data : data.message;
            const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
            this.formatMessage('debug', message, ...extraArgs, ...args);
        }
    }
    info(data, ...args) {
        if (this.level <= exports.LOG_LEVELS.INFO) {
            const message = typeof data === 'string' ? data : data.message;
            const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
            this.formatMessage('info', message, ...extraArgs, ...args);
        }
    }
    warn(data, ...args) {
        if (this.level <= exports.LOG_LEVELS.WARN) {
            const message = typeof data === 'string' ? data : data.message;
            const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
            this.formatMessage('warn', message, ...extraArgs, ...args);
        }
    }
    error(data, ...args) {
        if (this.level <= exports.LOG_LEVELS.ERROR) {
            const message = typeof data === 'string' ? data : data.message;
            const extraArgs = typeof data === 'object' && data !== null ? [data] : [];
            this.formatMessage('error', message, ...extraArgs, ...args);
        }
    }
    // Production-safe logging methods
    logInProduction(level, message, ...args) {
        // Only log warnings and errors in production
        if (process.env.NODE_ENV === 'production' && level === 'info') {
            return;
        }
        this[level](message, ...args);
    }
    // Method for replacing console.log calls
    log(data, ...args) {
        this.info(data, ...args);
    }
}
exports.SharedLogger = SharedLogger;
// Create default logger instance
const logger = new SharedLogger();
// Convenience functions for easy migration from console.*
const log = (data, ...args) => logger.log(typeof data === 'string' ? data : data.message, ...args);
exports.log = log;
const info = (data, ...args) => logger.info(data, ...args);
exports.info = info;
const warn = (data, ...args) => logger.warn(data, ...args);
exports.warn = warn;
const error = (data, ...args) => logger.error(data, ...args);
exports.error = error;
const debug = (data, ...args) => logger.debug(data, ...args);
exports.debug = debug;
exports.default = logger;
