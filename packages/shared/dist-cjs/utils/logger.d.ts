/**
 * Shared Logger Utility
 *
 * Simple logger for shared package that works in both browser and Node.js environments
 */
export interface LogLevel {
    DEBUG: 0;
    INFO: 1;
    WARN: 2;
    ERROR: 3;
}
export declare const LOG_LEVELS: LogLevel;
export type LogLevelValue = LogLevel[keyof LogLevel];
declare class SharedLogger {
    private level;
    private context;
    constructor(context?: string);
    setLevel(level: string): void;
    private formatMessage;
    debug(data: {
        message: string;
        [key: string]: any;
    } | string, ...args: unknown[]): void;
    info(data: {
        message: string;
        [key: string]: any;
    } | string, ...args: unknown[]): void;
    warn(data: {
        message: string;
        [key: string]: any;
    } | string, ...args: unknown[]): void;
    error(data: {
        message: string;
        [key: string]: any;
    } | string, ...args: unknown[]): void;
    logInProduction(level: 'info' | 'warn' | 'error', message: string, ...args: unknown[]): void;
    log(data: {
        message: string;
        [key: string]: any;
    } | string, ...args: unknown[]): void;
}
declare const logger: SharedLogger;
export declare const log: (data: {
    message: string;
    [key: string]: any;
} | string, ...args: unknown[]) => void;
export declare const info: (data: {
    message: string;
    [key: string]: any;
} | string, ...args: unknown[]) => void;
export declare const warn: (data: {
    message: string;
    [key: string]: any;
} | string, ...args: unknown[]) => void;
export declare const error: (data: {
    message: string;
    [key: string]: any;
} | string, ...args: unknown[]) => void;
export declare const debug: (data: {
    message: string;
    [key: string]: any;
} | string, ...args: unknown[]) => void;
export { SharedLogger };
export default logger;
