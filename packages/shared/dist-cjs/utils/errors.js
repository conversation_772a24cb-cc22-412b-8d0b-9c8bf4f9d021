"use strict";
/**
 * Custom API Error Class
 *
 * Standardized error handling for the API with proper HTTP status codes
 * and consistent error response format.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InternalServerError = exports.UnprocessableEntityError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.BadRequestError = exports.HTTP_STATUS_CODES = exports.ApiError = exports.ErrorCode = void 0;
var ErrorCode;
(function (ErrorCode) {
    // Client Errors (4xx)
    ErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorCode["AUTHENTICATION_REQUIRED"] = "AUTHENTICATION_REQUIRED";
    ErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
    ErrorCode["INSUFFICIENT_PERMISSIONS"] = "INSUFFICIENT_PERMISSIONS";
    ErrorCode["FORBIDDEN"] = "FORBIDDEN";
    ErrorCode["RESOURCE_NOT_FOUND"] = "RESOURCE_NOT_FOUND";
    ErrorCode["RESOURCE_CONFLICT"] = "RESOURCE_CONFLICT";
    ErrorCode["DUPLICATE_RESOURCE"] = "DUPLICATE_RESOURCE";
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
    // Server Errors (5xx)
    ErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    ErrorCode["SERVICE_UNAVAILABLE"] = "SERVICE_UNAVAILABLE";
    ErrorCode["STORAGE_ERROR"] = "STORAGE_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class ApiError extends Error {
    constructor(message, statusCode = 500, code = ErrorCode.INTERNAL_SERVER_ERROR, details, isOperational = true) {
        super(message);
        this.name = 'ApiError';
        this.statusCode = statusCode;
        this.code = code;
        if (details !== undefined) {
            this.details = details;
        }
        this.isOperational = isOperational;
        this.timestamp = new Date().toISOString();
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ApiError);
        }
    }
    /**
     * Create a validation error
     */
    static validation(message, details) {
        return new ApiError(message, 400, ErrorCode.VALIDATION_ERROR, details);
    }
    /**
     * Create an authentication error
     */
    static unauthorized(message = 'Authentication required') {
        return new ApiError(message, 401, ErrorCode.AUTHENTICATION_REQUIRED);
    }
    /**
     * Create a forbidden error
     */
    static forbidden(message = 'Insufficient permissions') {
        return new ApiError(message, 403, ErrorCode.INSUFFICIENT_PERMISSIONS);
    }
    /**
     * Create a not found error
     */
    static notFound(message = 'Resource not found') {
        return new ApiError(message, 404, ErrorCode.RESOURCE_NOT_FOUND);
    }
    /**
     * Create a conflict error
     */
    static conflict(message, details) {
        return new ApiError(message, 409, ErrorCode.RESOURCE_CONFLICT, details);
    }
    /**
     * Create a rate limit error
     */
    static rateLimit(message = 'Rate limit exceeded') {
        return new ApiError(message, 429, ErrorCode.RATE_LIMIT_EXCEEDED);
    }
    /**
     * Create a database error
     */
    static database(message, originalError) {
        return new ApiError(message, 500, ErrorCode.DATABASE_ERROR, originalError ? [{ message: originalError.message }] : undefined);
    }
    /**
     * Create a storage error
     */
    static storage(message) {
        return new ApiError(message, 500, ErrorCode.STORAGE_ERROR);
    }
    /**
     * Create an external service error
     */
    static externalService(message, service) {
        return new ApiError(message, 502, ErrorCode.EXTERNAL_SERVICE_ERROR, service ? [{ field: 'service', value: service }] : undefined);
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            success: false,
            error: this.code,
            message: this.message,
            statusCode: this.statusCode,
            ...(this.details && { details: this.details }),
            timestamp: this.timestamp,
        };
    }
    /**
     * Check if error is operational (expected) vs programming error
     */
    static isOperational(error) {
        if (error instanceof ApiError) {
            return error.isOperational;
        }
        return false;
    }
}
exports.ApiError = ApiError;
// Common HTTP status codes for errors
exports.HTTP_STATUS_CODES = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,
};
// Legacy error classes that extend the unified ApiError
class BadRequestError extends ApiError {
    constructor(message = 'Bad Request') {
        super(message, exports.HTTP_STATUS_CODES.BAD_REQUEST, ErrorCode.VALIDATION_ERROR);
    }
}
exports.BadRequestError = BadRequestError;
class NotFoundError extends ApiError {
    constructor(message = 'Resource Not Found') {
        super(message, exports.HTTP_STATUS_CODES.NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND);
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends ApiError {
    constructor(message = 'Unauthorized') {
        super(message, exports.HTTP_STATUS_CODES.UNAUTHORIZED, ErrorCode.AUTHENTICATION_REQUIRED);
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends ApiError {
    constructor(message = 'Forbidden') {
        super(message, exports.HTTP_STATUS_CODES.FORBIDDEN, ErrorCode.INSUFFICIENT_PERMISSIONS);
    }
}
exports.ForbiddenError = ForbiddenError;
class UnprocessableEntityError extends ApiError {
    constructor(message = 'Unprocessable Entity') {
        super(message, exports.HTTP_STATUS_CODES.UNPROCESSABLE_ENTITY, ErrorCode.VALIDATION_ERROR);
    }
}
exports.UnprocessableEntityError = UnprocessableEntityError;
class InternalServerError extends ApiError {
    constructor(message = 'Internal Server Error') {
        super(message, exports.HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR, ErrorCode.INTERNAL_SERVER_ERROR);
    }
}
exports.InternalServerError = InternalServerError;
