/**
 * Centralized API path configuration
 * This file provides a single point of truth for all API endpoints
 */
export declare const API_PATHS: {
    AUTH: {
        LOGIN: string;
        REGISTER: string;
        LOGOUT: string;
        REFRESH: string;
        ME: string;
        FORGOT_PASSWORD: string;
        RESET_PASSWORD: string;
    };
    USERS: {
        ME: string;
        PROFILE: string;
        SETTINGS: string;
        STATS: string;
        BY_ID: (id: string) => string;
    };
    PROJECTS: {
        LIST: string;
        CREATE: string;
        BY_ID: (id: string) => string;
        IMAGES: (id: string) => string;
        EXPORT: (id: string) => string;
        SHARE: (id: string) => string;
    };
    IMAGES: {
        UPLOAD: string;
        BY_ID: (id: string) => string;
        SEGMENT: (id: string) => string;
    };
    SEGMENTATION: {
        START: string;
        STATUS: (id: string) => string;
        RESULT: (id: string) => string;
        BATCH: string;
    };
    ACCESS_REQUESTS: {
        CREATE: string;
    };
    SYSTEM: {
        HEALTH: string;
        STATUS: string;
        METRICS: string;
    };
};
/**
 * Ensures API path always uses the correct format
 * Handles both direct API calls and proxied calls
 *
 * @param path - The API path to format
 * @returns The formatted API path
 */
export declare const formatApiPath: (path: string) => string;
/**
 * Utility function for building URL parameters
 * @param params - The parameters to include in the URL
 * @returns A string of URL parameters
 */
export declare const buildUrlParams: (params: Record<string, string | number | boolean | undefined>) => string;
export default API_PATHS;
