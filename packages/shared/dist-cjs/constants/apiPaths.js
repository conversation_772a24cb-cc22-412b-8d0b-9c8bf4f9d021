"use strict";
/**
 * Centralized API path configuration
 * This file provides a single point of truth for all API endpoints
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildUrlParams = exports.formatApiPath = exports.API_PATHS = void 0;
// Core API endpoints (without /api prefix - added by API client)
exports.API_PATHS = {
    AUTH: {
        LOGIN: "/auth/login",
        REGISTER: "/auth/register",
        LOGOUT: "/auth/logout",
        REFRESH: "/auth/refresh",
        ME: "/users/me",
        FORGOT_PASSWORD: "/auth/forgot-password",
        RESET_PASSWORD: "/auth/reset-password",
    },
    USERS: {
        ME: "/users/me",
        PROFILE: "/users/me/profile",
        SETTINGS: "/users/me/settings",
        STATS: "/user-stats/stats",
        BY_ID: (id) => `/users/${id}`,
    },
    PROJECTS: {
        LIST: "/projects",
        CREATE: "/projects",
        BY_ID: (id) => `/projects/${id}`,
        IMAGES: (id) => `/projects/${id}/images`,
        EXPORT: (id) => `/projects/${id}/export`,
        SHARE: (id) => `/projects/${id}/share`,
    },
    IMAGES: {
        UPLOAD: "/images/upload",
        BY_ID: (id) => `/images/${id}`,
        SEGMENT: (id) => `/images/${id}/segment`,
    },
    SEGMENTATION: {
        START: "/segmentation/start",
        STATUS: (id) => `/segmentation/${id}/status`,
        RESULT: (id) => `/segmentation/${id}/result`,
        BATCH: "/segmentation/batch",
    },
    ACCESS_REQUESTS: {
        CREATE: "/access-requests",
    },
    SYSTEM: {
        HEALTH: "/health",
        STATUS: "/status",
        METRICS: "/metrics",
    },
};
/**
 * Ensures API path always uses the correct format
 * Handles both direct API calls and proxied calls
 *
 * @param path - The API path to format
 * @returns The formatted API path
 */
const formatApiPath = (path) => {
    // If path already starts with /api, just return it
    if (path.startsWith("/api/")) {
        return path;
    }
    // Otherwise, ensure path starts with / and add /api prefix
    const normalizedPath = path.startsWith("/") ? path : `/${path}`;
    return `/api${normalizedPath}`;
};
exports.formatApiPath = formatApiPath;
/**
 * Utility function for building URL parameters
 * @param params - The parameters to include in the URL
 * @returns A string of URL parameters
 */
const buildUrlParams = (params) => {
    const validParams = Object.entries(params)
        .filter(([_, value]) => value !== undefined && value !== null)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
    return validParams.length ? `?${validParams.join("&")}` : "";
};
exports.buildUrlParams = buildUrlParams;
exports.default = exports.API_PATHS;
