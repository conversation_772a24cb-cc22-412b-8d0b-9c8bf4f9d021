import { z } from 'zod';
/**
 * Entity-specific validation schemas
 *
 * These schemas define the structure of core entities in the application
 * and can be used for validation and type generation.
 */
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    email: z.ZodString;
    name: z.ZodString;
    preferredLanguage: z.<PERSON><z.ZodEnum<["en", "cs", "de", "es", "fr", "zh"]>>;
    emailVerified: z.<PERSON>odDefault<z.ZodBoolean>;
    role: z.<PERSON>odDefault<z.ZodEnum<["user", "admin"]>>;
    isActive: z.<PERSON><z.ZodBoolean>;
    lastLoginAt: z.<PERSON><z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    email: string;
    preferredLanguage: "en" | "cs" | "de" | "es" | "fr" | "zh";
    emailVerified: boolean;
    role: "user" | "admin";
    isActive: boolean;
    lastLoginAt?: string | undefined;
}, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    email: string;
    preferredLanguage?: "en" | "cs" | "de" | "es" | "fr" | "zh" | undefined;
    emailVerified?: boolean | undefined;
    role?: "user" | "admin" | undefined;
    isActive?: boolean | undefined;
    lastLoginAt?: string | undefined;
}>;
export declare const UserProfileSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    email: z.ZodString;
    name: z.ZodString;
    preferredLanguage: z.ZodEnum<["en", "cs", "de", "es", "fr", "zh"]>;
    emailVerified: z.ZodBoolean;
    role: z.ZodEnum<["user", "admin"]>;
    isActive: z.ZodBoolean;
    lastLoginAt: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    bio: z.ZodOptional<z.ZodString>;
    organization: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    email: string;
    preferredLanguage: "en" | "cs" | "de" | "es" | "fr" | "zh";
    emailVerified: boolean;
    role: "user" | "admin";
    isActive: boolean;
    lastLoginAt?: string | undefined;
    avatar?: string | undefined;
    bio?: string | undefined;
    organization?: string | undefined;
    location?: string | undefined;
}, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    email: string;
    preferredLanguage: "en" | "cs" | "de" | "es" | "fr" | "zh";
    emailVerified: boolean;
    role: "user" | "admin";
    isActive: boolean;
    lastLoginAt?: string | undefined;
    avatar?: string | undefined;
    bio?: string | undefined;
    organization?: string | undefined;
    location?: string | undefined;
}>;
export declare const UserLoginResponseSchema: z.ZodObject<{
    user: z.ZodObject<{
        id: z.ZodString;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
        email: z.ZodString;
        name: z.ZodString;
        preferredLanguage: z.ZodDefault<z.ZodEnum<["en", "cs", "de", "es", "fr", "zh"]>>;
        emailVerified: z.ZodDefault<z.ZodBoolean>;
        role: z.ZodDefault<z.ZodEnum<["user", "admin"]>>;
        isActive: z.ZodDefault<z.ZodBoolean>;
        lastLoginAt: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        email: string;
        preferredLanguage: "en" | "cs" | "de" | "es" | "fr" | "zh";
        emailVerified: boolean;
        role: "user" | "admin";
        isActive: boolean;
        lastLoginAt?: string | undefined;
    }, {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        email: string;
        preferredLanguage?: "en" | "cs" | "de" | "es" | "fr" | "zh" | undefined;
        emailVerified?: boolean | undefined;
        role?: "user" | "admin" | undefined;
        isActive?: boolean | undefined;
        lastLoginAt?: string | undefined;
    }>;
    tokens: z.ZodObject<{
        accessToken: z.ZodString;
        refreshToken: z.ZodString;
        expiresIn: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
    }, {
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
    }>;
}, "strip", z.ZodTypeAny, {
    user: {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        email: string;
        preferredLanguage: "en" | "cs" | "de" | "es" | "fr" | "zh";
        emailVerified: boolean;
        role: "user" | "admin";
        isActive: boolean;
        lastLoginAt?: string | undefined;
    };
    tokens: {
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
    };
}, {
    user: {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        email: string;
        preferredLanguage?: "en" | "cs" | "de" | "es" | "fr" | "zh" | undefined;
        emailVerified?: boolean | undefined;
        role?: "user" | "admin" | undefined;
        isActive?: boolean | undefined;
        lastLoginAt?: string | undefined;
    };
    tokens: {
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
    };
}>;
export declare const ProjectSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    userId: z.ZodString;
    imageCount: z.ZodNumber;
    isPublic: z.ZodBoolean;
    tags: z.ZodArray<z.ZodString, "many">;
    settings: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    userId: string;
    imageCount: number;
    isPublic: boolean;
    tags: string[];
    description?: string | undefined;
    settings?: any;
}, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    userId: string;
    imageCount: number;
    isPublic: boolean;
    tags: string[];
    description?: string | undefined;
    settings?: any;
}>;
export declare const ProjectStatsSchema: z.ZodObject<{
    totalImages: z.ZodNumber;
    segmentedImages: z.ZodNumber;
    totalCells: z.ZodNumber;
    averageCellsPerImage: z.ZodNumber;
    lastActivityAt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    totalImages: number;
    segmentedImages: number;
    totalCells: number;
    averageCellsPerImage: number;
    lastActivityAt?: string | undefined;
}, {
    totalImages: number;
    segmentedImages: number;
    totalCells: number;
    averageCellsPerImage: number;
    lastActivityAt?: string | undefined;
}>;
export declare const ImageSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    projectId: z.ZodString;
    name: z.ZodString;
    filename: z.ZodString;
    originalName: z.ZodString;
    mimetype: z.ZodString;
    size: z.ZodNumber;
    width: z.ZodOptional<z.ZodNumber>;
    height: z.ZodOptional<z.ZodNumber>;
    segmentationStatus: z.ZodEnum<["without_segmentation", "queued", "processing", "completed", "failed"]>;
    url: z.ZodString;
    thumbnailUrl: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
    projectId: string;
    segmentationStatus: "without_segmentation" | "queued" | "processing" | "completed" | "failed";
    width?: number | undefined;
    height?: number | undefined;
    thumbnailUrl?: string | undefined;
}, {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
    projectId: string;
    segmentationStatus: "without_segmentation" | "queued" | "processing" | "completed" | "failed";
    width?: number | undefined;
    height?: number | undefined;
    thumbnailUrl?: string | undefined;
}>;
export declare const SegmentationResultSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    imageId: z.ZodString;
    status: z.ZodEnum<["pending", "processing", "completed", "failed"]>;
    cellCount: z.ZodOptional<z.ZodNumber>;
    processingTime: z.ZodOptional<z.ZodNumber>;
    error: z.ZodOptional<z.ZodString>;
    metadata: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: "processing" | "completed" | "failed" | "pending";
    createdAt: string;
    updatedAt: string;
    imageId: string;
    error?: string | undefined;
    metadata?: any;
    cellCount?: number | undefined;
    processingTime?: number | undefined;
}, {
    id: string;
    status: "processing" | "completed" | "failed" | "pending";
    createdAt: string;
    updatedAt: string;
    imageId: string;
    error?: string | undefined;
    metadata?: any;
    cellCount?: number | undefined;
    processingTime?: number | undefined;
}>;
export declare const CellSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    segmentationResultId: z.ZodString;
    cellNumber: z.ZodNumber;
    area: z.ZodNumber;
    perimeter: z.ZodNumber;
    centroidX: z.ZodNumber;
    centroidY: z.ZodNumber;
    eccentricity: z.ZodNumber;
    solidity: z.ZodNumber;
    circularity: z.ZodNumber;
    polygon: z.ZodArray<z.ZodArray<z.ZodNumber, "many">, "many">;
    features: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    segmentationResultId: string;
    cellNumber: number;
    area: number;
    perimeter: number;
    centroidX: number;
    centroidY: number;
    eccentricity: number;
    solidity: number;
    circularity: number;
    polygon: number[][];
    features?: any;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    segmentationResultId: string;
    cellNumber: number;
    area: number;
    perimeter: number;
    centroidX: number;
    centroidY: number;
    eccentricity: number;
    solidity: number;
    circularity: number;
    polygon: number[][];
    features?: any;
}>;
export declare const SegmentationTaskSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    imageId: z.ZodString;
    priority: z.ZodNumber;
    taskStatus: z.ZodEnum<["queued", "processing", "completed", "failed"]>;
    startedAt: z.ZodOptional<z.ZodString>;
    completedAt: z.ZodOptional<z.ZodString>;
    error: z.ZodOptional<z.ZodString>;
    retryCount: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    imageId: string;
    priority: number;
    taskStatus: "queued" | "processing" | "completed" | "failed";
    retryCount: number;
    error?: string | undefined;
    startedAt?: string | undefined;
    completedAt?: string | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    imageId: string;
    priority: number;
    taskStatus: "queued" | "processing" | "completed" | "failed";
    retryCount: number;
    error?: string | undefined;
    startedAt?: string | undefined;
    completedAt?: string | undefined;
}>;
export declare const UserSettingSchema: z.ZodObject<{
    key: z.ZodString;
    value: z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodBoolean, z.ZodAny]>;
    category: z.ZodOptional<z.ZodString>;
    updatedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    key: string;
    updatedAt: string;
    value?: any;
    category?: string | undefined;
}, {
    key: string;
    updatedAt: string;
    value?: any;
    category?: string | undefined;
}>;
export declare const ExportRequestSchema: z.ZodObject<{
    projectId: z.ZodString;
    format: z.ZodEnum<["csv", "json", "excel"]>;
    includeImages: z.ZodBoolean;
    includeFeatures: z.ZodBoolean;
    imageIds: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    projectId: string;
    format: "csv" | "json" | "excel";
    includeImages: boolean;
    includeFeatures: boolean;
    imageIds?: string[] | undefined;
}, {
    projectId: string;
    format: "csv" | "json" | "excel";
    includeImages: boolean;
    includeFeatures: boolean;
    imageIds?: string[] | undefined;
}>;
export declare const BatchDeleteRequestSchema: z.ZodObject<{
    ids: z.ZodArray<z.ZodString, "many">;
    type: z.ZodEnum<["images", "projects", "cells"]>;
}, "strip", z.ZodTypeAny, {
    type: "images" | "projects" | "cells";
    ids: string[];
}, {
    type: "images" | "projects" | "cells";
    ids: string[];
}>;
export declare const BatchDeleteResponseSchema: z.ZodObject<{
    successful: z.ZodNumber;
    failed: z.ZodNumber;
    errors: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        error: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        error: string;
    }, {
        id: string;
        error: string;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    failed: number;
    successful: number;
    errors?: {
        id: string;
        error: string;
    }[] | undefined;
}, {
    failed: number;
    successful: number;
    errors?: {
        id: string;
        error: string;
    }[] | undefined;
}>;
export declare const ProjectShareSchema: z.ZodObject<{
    projectId: z.ZodString;
    sharedWithUserId: z.ZodString;
    permission: z.ZodEnum<["view", "edit", "admin"]>;
    sharedAt: z.ZodString;
    expiresAt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    projectId: string;
    sharedWithUserId: string;
    permission: "admin" | "view" | "edit";
    sharedAt: string;
    expiresAt?: string | undefined;
}, {
    projectId: string;
    sharedWithUserId: string;
    permission: "admin" | "view" | "edit";
    sharedAt: string;
    expiresAt?: string | undefined;
}>;
export declare const SearchQuerySchema: z.ZodObject<{
    query: z.ZodString;
    filters: z.ZodOptional<z.ZodObject<{
        projectId: z.ZodOptional<z.ZodString>;
        status: z.ZodOptional<z.ZodString>;
        dateFrom: z.ZodOptional<z.ZodString>;
        dateTo: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        status?: string | undefined;
        tags?: string[] | undefined;
        projectId?: string | undefined;
        dateFrom?: string | undefined;
        dateTo?: string | undefined;
    }, {
        status?: string | undefined;
        tags?: string[] | undefined;
        projectId?: string | undefined;
        dateFrom?: string | undefined;
        dateTo?: string | undefined;
    }>>;
    pagination: z.ZodOptional<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        pageSize: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        pageSize: number;
    }, {
        page?: number | undefined;
        pageSize?: number | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    query: string;
    filters?: {
        status?: string | undefined;
        tags?: string[] | undefined;
        projectId?: string | undefined;
        dateFrom?: string | undefined;
        dateTo?: string | undefined;
    } | undefined;
    pagination?: {
        page: number;
        pageSize: number;
    } | undefined;
}, {
    query: string;
    filters?: {
        status?: string | undefined;
        tags?: string[] | undefined;
        projectId?: string | undefined;
        dateFrom?: string | undefined;
        dateTo?: string | undefined;
    } | undefined;
    pagination?: {
        page?: number | undefined;
        pageSize?: number | undefined;
    } | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type UserLoginResponse = z.infer<typeof UserLoginResponseSchema>;
export type Project = z.infer<typeof ProjectSchema>;
export type ProjectStats = z.infer<typeof ProjectStatsSchema>;
export type Image = z.infer<typeof ImageSchema>;
export type SegmentationResult = z.infer<typeof SegmentationResultSchema>;
export type Cell = z.infer<typeof CellSchema>;
export type SegmentationTask = z.infer<typeof SegmentationTaskSchema>;
export type UserSetting = z.infer<typeof UserSettingSchema>;
export type ExportRequest = z.infer<typeof ExportRequestSchema>;
export type BatchDeleteRequest = z.infer<typeof BatchDeleteRequestSchema>;
export type BatchDeleteResponse = z.infer<typeof BatchDeleteResponseSchema>;
export type ProjectShare = z.infer<typeof ProjectShareSchema>;
export type SearchQuery = z.infer<typeof SearchQuerySchema>;
