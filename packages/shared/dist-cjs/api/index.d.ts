/**
 * Unified API Response Module
 *
 * This module exports all types, handlers, and schemas needed for
 * standardized API communication across the application.
 */
export * from './response.types';
export { UnifiedResponseHandler } from './responseHandler';
export * from './schemas';
export * from './entitySchemas';
export { UnifiedResponseHandler as ResponseHandler } from './responseHandler';
export { ApiResponseSchema, ApiErrorResponseSchema, createPaginatedSchema, } from './schemas';
