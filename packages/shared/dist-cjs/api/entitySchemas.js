"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchQuerySchema = exports.ProjectShareSchema = exports.BatchDeleteResponseSchema = exports.BatchDeleteRequestSchema = exports.ExportRequestSchema = exports.UserSettingSchema = exports.SegmentationTaskSchema = exports.CellSchema = exports.SegmentationResultSchema = exports.ImageSchema = exports.ProjectStatsSchema = exports.ProjectSchema = exports.UserLoginResponseSchema = exports.UserProfileSchema = exports.UserSchema = void 0;
const zod_1 = require("zod");
const schemas_1 = require("./schemas");
/**
 * Entity-specific validation schemas
 *
 * These schemas define the structure of core entities in the application
 * and can be used for validation and type generation.
 */
// User schemas
exports.UserSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    email: schemas_1.EmailSchema,
    name: zod_1.z.string(),
    preferredLanguage: zod_1.z.enum(['en', 'cs', 'de', 'es', 'fr', 'zh']).default('en'),
    emailVerified: zod_1.z.boolean().default(false),
    role: zod_1.z.enum(['user', 'admin']).default('user'),
    isActive: zod_1.z.boolean().default(true),
    lastLoginAt: schemas_1.TimestampSchema.optional(),
});
exports.UserProfileSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    email: schemas_1.EmailSchema,
    name: zod_1.z.string(),
    preferredLanguage: zod_1.z.enum(['en', 'cs', 'de', 'es', 'fr', 'zh']),
    emailVerified: zod_1.z.boolean(),
    role: zod_1.z.enum(['user', 'admin']),
    isActive: zod_1.z.boolean(),
    lastLoginAt: schemas_1.TimestampSchema.optional(),
    avatar: zod_1.z.string().optional(),
    bio: zod_1.z.string().optional(),
    organization: zod_1.z.string().optional(),
    location: zod_1.z.string().optional(),
});
exports.UserLoginResponseSchema = zod_1.z.object({
    user: exports.UserSchema,
    tokens: zod_1.z.object({
        accessToken: zod_1.z.string(),
        refreshToken: zod_1.z.string(),
        expiresIn: zod_1.z.number(),
    }),
});
// Project schemas
exports.ProjectSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    userId: schemas_1.IdSchema,
    imageCount: zod_1.z.number(),
    isPublic: zod_1.z.boolean(),
    tags: zod_1.z.array(zod_1.z.string()),
    settings: zod_1.z.any(),
});
exports.ProjectStatsSchema = zod_1.z.object({
    totalImages: zod_1.z.number(),
    segmentedImages: zod_1.z.number(),
    totalCells: zod_1.z.number(),
    averageCellsPerImage: zod_1.z.number(),
    lastActivityAt: schemas_1.TimestampSchema.optional(),
});
// Image schemas
exports.ImageSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    projectId: schemas_1.IdSchema,
    name: zod_1.z.string(),
    filename: zod_1.z.string(),
    originalName: zod_1.z.string(),
    mimetype: zod_1.z.string(),
    size: zod_1.z.number(),
    width: zod_1.z.number().optional(),
    height: zod_1.z.number().optional(),
    segmentationStatus: zod_1.z.enum([
        'without_segmentation',
        'queued',
        'processing',
        'completed',
        'failed',
    ]),
    url: zod_1.z.string(),
    thumbnailUrl: zod_1.z.string().optional(),
});
// Segmentation schemas
exports.SegmentationResultSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    imageId: schemas_1.IdSchema,
    status: zod_1.z.enum(['pending', 'processing', 'completed', 'failed']),
    cellCount: zod_1.z.number().optional(),
    processingTime: zod_1.z.number().optional(),
    error: zod_1.z.string().optional(),
    metadata: zod_1.z.any(),
});
exports.CellSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    segmentationResultId: schemas_1.IdSchema,
    cellNumber: zod_1.z.number().positive(),
    area: zod_1.z.number().positive(),
    perimeter: zod_1.z.number().positive(),
    centroidX: zod_1.z.number(),
    centroidY: zod_1.z.number(),
    eccentricity: zod_1.z.number().min(0).max(1),
    solidity: zod_1.z.number().min(0).max(1),
    circularity: zod_1.z.number().min(0).max(1),
    polygon: zod_1.z.array(zod_1.z.array(zod_1.z.number())),
    features: zod_1.z.any(),
});
// Task schemas
exports.SegmentationTaskSchema = zod_1.z.object({
    id: schemas_1.IdSchema,
    createdAt: schemas_1.TimestampSchema,
    updatedAt: schemas_1.TimestampSchema,
    imageId: schemas_1.IdSchema,
    priority: zod_1.z.number(),
    taskStatus: zod_1.z.enum(['queued', 'processing', 'completed', 'failed']),
    startedAt: schemas_1.TimestampSchema.optional(),
    completedAt: schemas_1.TimestampSchema.optional(),
    error: zod_1.z.string().optional(),
    retryCount: zod_1.z.number(),
});
// Settings schemas
exports.UserSettingSchema = zod_1.z.object({
    key: zod_1.z.string(),
    value: zod_1.z.union([zod_1.z.string(), zod_1.z.number(), zod_1.z.boolean(), zod_1.z.any()]),
    category: zod_1.z.string().optional(),
    updatedAt: schemas_1.TimestampSchema,
});
// Export data schemas
exports.ExportRequestSchema = zod_1.z.object({
    projectId: schemas_1.IdSchema,
    format: zod_1.z.enum(['csv', 'json', 'excel']),
    includeImages: zod_1.z.boolean(),
    includeFeatures: zod_1.z.boolean(),
    imageIds: zod_1.z.array(schemas_1.IdSchema).optional(),
});
// Batch operation schemas
exports.BatchDeleteRequestSchema = zod_1.z.object({
    ids: zod_1.z.array(schemas_1.IdSchema).min(1, 'At least one ID is required'),
    type: zod_1.z.enum(['images', 'projects', 'cells']),
});
exports.BatchDeleteResponseSchema = zod_1.z.object({
    successful: zod_1.z.number(),
    failed: zod_1.z.number(),
    errors: zod_1.z
        .array(zod_1.z.object({
        id: schemas_1.IdSchema,
        error: zod_1.z.string(),
    }))
        .optional(),
});
// Share/collaboration schemas
exports.ProjectShareSchema = zod_1.z.object({
    projectId: schemas_1.IdSchema,
    sharedWithUserId: schemas_1.IdSchema,
    permission: zod_1.z.enum(['view', 'edit', 'admin']),
    sharedAt: schemas_1.TimestampSchema,
    expiresAt: schemas_1.TimestampSchema.optional(),
});
// Search/filter schemas
exports.SearchQuerySchema = zod_1.z.object({
    query: zod_1.z.string(),
    filters: zod_1.z
        .object({
        projectId: schemas_1.IdSchema.optional(),
        status: zod_1.z.string().optional(),
        dateFrom: schemas_1.TimestampSchema.optional(),
        dateTo: schemas_1.TimestampSchema.optional(),
        tags: zod_1.z.array(zod_1.z.string()).optional(),
    })
        .optional(),
    pagination: zod_1.z
        .object({
        page: zod_1.z.number().positive().default(1),
        pageSize: zod_1.z.number().positive().max(100).default(20),
    })
        .optional(),
});
