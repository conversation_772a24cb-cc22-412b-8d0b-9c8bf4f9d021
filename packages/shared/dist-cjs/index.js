"use strict";
// Index file for @spheroseg/shared
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkerResponse = exports.createWorkerRequest = exports.generateRequestId = exports.createPaginatedSchema = exports.ApiErrorResponseSchema = exports.ApiResponseSchema = exports.ResponseHandler = exports.UnifiedResponseHandler = void 0;
// Export types
__exportStar(require("./types"), exports);
// Export monitoring utilities
__exportStar(require("./monitoring"), exports);
// Export segmentation status constants
__exportStar(require("./constants/segmentationStatus"), exports);
// Export API paths and utilities
__exportStar(require("./constants/apiPaths"), exports);
// Export error utilities
__exportStar(require("./utils/errors"), exports);
// Export image utilities
__exportStar(require("./utils/imageUtils"), exports);
// Export path utilities
__exportStar(require("./utils/pathUtils"), exports);
// Export API response handling utilities
var api_1 = require("./api");
Object.defineProperty(exports, "UnifiedResponseHandler", { enumerable: true, get: function () { return api_1.UnifiedResponseHandler; } });
Object.defineProperty(exports, "ResponseHandler", { enumerable: true, get: function () { return api_1.ResponseHandler; } });
var api_2 = require("./api");
Object.defineProperty(exports, "ApiResponseSchema", { enumerable: true, get: function () { return api_2.ApiResponseSchema; } });
Object.defineProperty(exports, "ApiErrorResponseSchema", { enumerable: true, get: function () { return api_2.ApiErrorResponseSchema; } });
Object.defineProperty(exports, "createPaginatedSchema", { enumerable: true, get: function () { return api_2.createPaginatedSchema; } });
// Export utility functions that are needed by frontend
const generateRequestId = () => {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};
exports.generateRequestId = generateRequestId;
const createWorkerRequest = (operation, data) => {
    return {
        id: (0, exports.generateRequestId)(),
        operation,
        data,
    };
};
exports.createWorkerRequest = createWorkerRequest;
const createWorkerResponse = (id, result, error) => {
    const response = {
        id,
        result,
    };
    if (error !== undefined) {
        response.error = error;
    }
    return response;
};
exports.createWorkerResponse = createWorkerResponse;
