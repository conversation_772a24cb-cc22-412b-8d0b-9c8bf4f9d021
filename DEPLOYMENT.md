# SpheroSeg Deployment Guide

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Domain name (for production)
- SSL certificate (for production)
- At least 4GB RAM
- 20GB free disk space

## Quick Start (Development)

```bash
# Clone the repository
git clone https://github.com/your-username/spheroseg.git
cd spheroseg

# Install dependencies
npm install

# Build the project
npm run build

# Start development environment
docker-compose --profile dev up
```

Access the application at: http://localhost:3000

## Production Deployment

### 1. Server Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Node.js (for building)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. SSL Certificate Setup

#### Option A: Let's Encrypt (Recommended)
```bash
./scripts/generate-ssl-cert.sh production your-domain.com
```

#### Option B: Self-signed (Testing only)
```bash
./scripts/generate-ssl-cert.sh self-signed
```

### 3. Environment Configuration

```bash
# Copy production environment template
cp .env.production.example .env.production

# Edit with your values
nano .env.production
```

Required environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Random secret key (min 32 characters)
- `APP_URL` - Your domain URL
- `CORS_ORIGIN` - Frontend URL

### 4. Database Setup

```bash
# Run database migrations
docker-compose -f docker-compose.prod.yml run --rm backend npx prisma migrate deploy

# (Optional) Seed initial data
docker-compose -f docker-compose.prod.yml run --rm backend npx prisma db seed
```

### 5. Deploy Application

```bash
# Using deployment script
./scripts/deploy.sh production

# Or manually
docker-compose -f docker-compose.prod.yml up -d
```

### 6. Verify Deployment

```bash
# Check service health
curl https://your-domain.com/api/health

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Check container status
docker-compose -f docker-compose.prod.yml ps
```

## Monitoring

### Health Checks

- Backend: `https://your-domain.com/api/health`
- Frontend: `https://your-domain.com`
- RabbitMQ: `http://your-domain.com:15672` (admin/admin)

### Logs

```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f nginx-prod
```

### Metrics

The application exposes Prometheus metrics at `/metrics` endpoint.

## Backup & Recovery

### Backup Database

```bash
# Create backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U postgres spheroseg > backup_$(date +%Y%m%d).sql

# Automated backup (cron)
0 2 * * * /path/to/spheroseg/scripts/backup/backup-database.sh
```

### Restore Database

```bash
# Restore from backup
docker-compose -f docker-compose.prod.yml exec -T db psql -U postgres spheroseg < backup_20240101.sql
```

### Backup Uploads

```bash
# Backup uploaded files
tar -czf uploads_$(date +%Y%m%d).tar.gz uploads/

# Restore uploads
tar -xzf uploads_20240101.tar.gz
```

## Scaling

### Horizontal Scaling

```bash
# Scale backend instances
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Scale ML service instances
docker-compose -f docker-compose.prod.yml up -d --scale ml=2
```

### Load Balancing

The nginx configuration automatically load balances between backend instances.

## Troubleshooting

### Common Issues

#### 1. Database Connection Errors
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec db pg_isready

# View database logs
docker-compose -f docker-compose.prod.yml logs db
```

#### 2. Redis Connection Issues
```bash
# Test Redis connection
docker-compose -f docker-compose.prod.yml exec redis redis-cli ping
```

#### 3. File Upload Issues
```bash
# Check permissions
ls -la uploads/

# Fix permissions
sudo chown -R www-data:www-data uploads/
```

#### 4. SSL Certificate Issues
```bash
# Verify certificate
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout

# Renew certificate
certbot renew
```

### Emergency Rollback

```bash
# Stop current deployment
docker-compose -f docker-compose.prod.yml down

# Checkout previous version
git checkout <previous-tag>

# Redeploy
./scripts/deploy.sh production
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **Firewall**: Configure firewall to only allow necessary ports
3. **Updates**: Regularly update dependencies and Docker images
4. **Backups**: Implement automated backup strategy
5. **Monitoring**: Set up alerts for service failures
6. **Rate Limiting**: Configure rate limiting in production
7. **HTTPS**: Always use HTTPS in production

## CI/CD Pipeline

The project includes GitHub Actions workflows for:
- Automated testing on pull requests
- Building Docker images
- Deploying to staging/production

See `.github/workflows/ci.yml` for details.

## Support

For issues or questions:
- Check logs: `docker-compose logs`
- GitHub Issues: https://github.com/your-username/spheroseg/issues
- Documentation: `/docs` directory