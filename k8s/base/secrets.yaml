apiVersion: v1
kind: Secret
metadata:
  name: spheroseg-secrets
  namespace: spheroseg
type: Opaque
stringData:
  # Database Passwords
  DB_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  DB_ROOT_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  
  # Redis Password
  REDIS_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  
  # RabbitMQ Password
  RABBITMQ_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  
  # JWT Secrets
  JWT_SECRET: "<CHANGE_ME_64_CHAR_RANDOM_STRING>"
  JWT_REFRESH_SECRET: "<CHANGE_ME_64_CHAR_RANDOM_STRING>"
  
  # Session Secret
  SESSION_SECRET: "<CHANGE_ME_64_CHAR_RANDOM_STRING>"
  
  # Email Password
  EMAIL_PASSWORD: "<APP_SPECIFIC_PASSWORD>"
  
  # ML Service API Key
  ML_SERVICE_API_KEY: "<CHANGE_ME_RANDOM_API_KEY>"
  
  # Monitoring Passwords
  GRAFANA_ADMIN_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  ELASTIC_PASSWORD: "<CHANGE_ME_STRONG_PASSWORD>"
  
  # External Services
  SENTRY_DSN: "<YOUR_SENTRY_DSN>"
  AWS_ACCESS_KEY_ID: "<YOUR_AWS_ACCESS_KEY>"
  AWS_SECRET_ACCESS_KEY: "<YOUR_AWS_SECRET_KEY>"
  CLOUDFLARE_API_TOKEN: "<YOUR_API_TOKEN>"

---
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: spheroseg
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <BASE64_ENCODED_DOCKER_CONFIG>

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: spheroseg
type: kubernetes.io/tls
data:
  tls.crt: <BASE64_ENCODED_TLS_CERT>
  tls.key: <BASE64_ENCODED_TLS_KEY>