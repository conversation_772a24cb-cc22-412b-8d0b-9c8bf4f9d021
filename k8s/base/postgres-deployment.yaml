apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: spheroseg
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
  selector:
    app: postgres

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: spheroseg
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: spheroseg
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsNonRoot: true
      containers:
      - name: postgres
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: spheroseg
        - name: POSTGRES_USER
          value: spheroseg
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: spheroseg-secrets
              key: DB_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        # Performance tuning
        - name: POSTGRES_MAX_CONNECTIONS
          value: "200"
        - name: POSTGRES_SHARED_BUFFERS
          value: "256MB"
        - name: POSTGRES_EFFECTIVE_CACHE_SIZE
          value: "1GB"
        - name: POSTGRES_MAINTENANCE_WORK_MEM
          value: "64MB"
        - name: POSTGRES_CHECKPOINT_COMPLETION_TARGET
          value: "0.9"
        - name: POSTGRES_WAL_BUFFERS
          value: "16MB"
        - name: POSTGRES_DEFAULT_STATISTICS_TARGET
          value: "100"
        - name: POSTGRES_RANDOM_PAGE_COST
          value: "1.1"
        - name: POSTGRES_EFFECTIVE_IO_CONCURRENCY
          value: "200"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-backup
          mountPath: /backup
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - spheroseg
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - spheroseg
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: postgres-backup
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 20Gi

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: spheroseg
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          securityContext:
            runAsUser: 999
            runAsGroup: 999
            fsGroup: 999
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            env:
            - name: PGHOST
              value: postgres-service
            - name: PGDATABASE
              value: spheroseg
            - name: PGUSER
              value: spheroseg
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: spheroseg-secrets
                  key: DB_PASSWORD
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: spheroseg-secrets
                  key: AWS_ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: spheroseg-secrets
                  key: AWS_SECRET_ACCESS_KEY
            command:
            - /bin/sh
            - -c
            - |
              DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="/backup/spheroseg_${DATE}.sql.gz"
              pg_dump -h $PGHOST -U $PGUSER $PGDATABASE | gzip > $BACKUP_FILE
              # Upload to S3
              aws s3 cp $BACKUP_FILE s3://spheroseg-backups/postgres/${DATE}/
              # Keep only last 30 days locally
              find /backup -type f -name "*.sql.gz" -mtime +30 -delete
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: postgres-backup-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-backup-pvc
  namespace: spheroseg
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard