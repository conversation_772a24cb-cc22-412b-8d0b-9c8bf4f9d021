apiVersion: v1
kind: ConfigMap
metadata:
  name: spheroseg-config
  namespace: spheroseg
data:
  # Application Configuration
  NODE_ENV: "production"
  APP_URL: "https://spherosegapp.utia.cas.cz"
  API_URL: "https://api.spherosegapp.utia.cas.cz"
  WS_URL: "wss://api.spherosegapp.utia.cas.cz"
  
  # Database Configuration
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "spheroseg"
  DB_USER: "spheroseg"
  DB_SSL: "true"
  DB_POOL_MIN: "5"
  DB_POOL_MAX: "20"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_KEY_PREFIX: "spheroseg:"
  
  # RabbitMQ Configuration
  RABBITMQ_HOST: "rabbitmq-service"
  RABBITMQ_PORT: "5672"
  RABBITMQ_USER: "admin"
  RABBITMQ_VHOST: "/"
  RABBITMQ_QUEUE: "segmentation_tasks"
  
  # ML Service Configuration
  ML_SERVICE_URL: "http://ml-service:5002"
  ML_SERVICE_TIMEOUT: "300000"
  ML_MODEL_PATH: "/app/models/checkpoint_epoch_9.pth.tar"
  
  # Performance Settings
  UV_THREADPOOL_SIZE: "8"
  NODE_OPTIONS: "--max-old-space-size=2048 --max-semi-space-size=256"
  CLUSTER_WORKERS: "4"
  OMP_NUM_THREADS: "4"
  MKL_NUM_THREADS: "4"
  TORCH_NUM_THREADS: "4"
  
  # Security Settings
  SECURE_COOKIES: "true"
  TRUST_PROXY: "true"
  HELMET_CSP: "true"
  CORS_ENABLED: "true"
  ALLOWED_ORIGINS: "https://spherosegapp.utia.cas.cz,https://www.spherosegapp.utia.cas.cz"
  
  # Rate Limiting
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  RATE_LIMIT_BLOCK_DURATION: "600"
  
  # Monitoring
  ENABLE_METRICS: "true"
  METRICS_PORT: "9091"
  METRICS_PATH: "/metrics"
  LOG_LEVEL: "warn"
  LOG_FORMAT: "json"
  
  # Email Configuration
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_SECURE: "false"
  EMAIL_FROM: "<EMAIL>"
  
  # File Upload
  UPLOAD_DIR: "/app/uploads"
  MAX_FILE_SIZE: "104857600"
  ALLOWED_FILE_TYPES: "image/jpeg,image/png,image/tiff,image/bmp,image/webp"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: spheroseg
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 4096;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;

        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/xml text/javascript 
                   application/json application/javascript application/xml+rss 
                   application/rss+xml application/atom+xml image/svg+xml;

        upstream backend {
            least_conn;
            server backend-service:5001 max_fails=3 fail_timeout=30s;
        }

        upstream frontend {
            least_conn;
            server frontend-service:3000 max_fails=3 fail_timeout=30s;
        }

        server {
            listen 80 default_server;
            server_name _;

            location /health {
                access_log off;
                return 200 'healthy\n';
                add_header Content-Type text/plain;
            }

            location / {
                proxy_pass http://frontend;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api {
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /ws {
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }
        }
    }