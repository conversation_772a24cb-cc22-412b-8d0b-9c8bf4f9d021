groups:
  - name: spheroseg_alerts
    interval: 30s
    rules:
      # ============================================
      # APPLICATION ALERTS
      # ============================================
      - alert: BackendDown
        expr: up{job="backend"} == 0
        for: 2m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "Backend service is down"
          description: "Backend instance {{ $labels.instance }} has been down for more than 2 minutes."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for the last 5 minutes (current: {{ $value | humanizePercentage }})"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 1s (current: {{ $value }}s)"

      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes{name="spheroseg-backend"} / container_spec_memory_limit_bytes{name="spheroseg-backend"}) > 0.9
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High memory usage on backend"
          description: "Backend memory usage is above 90% (current: {{ $value | humanizePercentage }})"

      # ============================================
      # DATABASE ALERTS
      # ============================================
      - alert: PostgresDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      - alert: PostgresHighConnections
        expr: pg_stat_database_numbackends{datname="spheroseg"} > 150
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High number of PostgreSQL connections"
          description: "PostgreSQL has {{ $value }} active connections (threshold: 150)"

      - alert: PostgresSlowQueries
        expr: rate(pg_stat_statements_mean_exec_time_seconds[5m]) > 1
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "Slow PostgreSQL queries detected"
          description: "Average query execution time is above 1s (current: {{ $value }}s)"

      - alert: PostgresReplicationLag
        expr: pg_replication_lag > 10
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "PostgreSQL replication lag detected"
          description: "Replication lag is {{ $value }} seconds"

      # ============================================
      # REDIS ALERTS
      # ============================================
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is above 90% (current: {{ $value | humanizePercentage }})"

      - alert: RedisHighKeyEviction
        expr: rate(redis_evicted_keys_total[5m]) > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis key eviction rate"
          description: "Redis is evicting {{ $value }} keys per second"

      # ============================================
      # RABBITMQ ALERTS
      # ============================================
      - alert: RabbitMQDown
        expr: up{job="rabbitmq"} == 0
        for: 1m
        labels:
          severity: critical
          service: rabbitmq
        annotations:
          summary: "RabbitMQ is down"
          description: "RabbitMQ message queue has been down for more than 1 minute."

      - alert: RabbitMQHighQueueDepth
        expr: rabbitmq_queue_messages > 1000
        for: 5m
        labels:
          severity: warning
          service: rabbitmq
        annotations:
          summary: "High RabbitMQ queue depth"
          description: "Queue {{ $labels.queue }} has {{ $value }} messages waiting"

      - alert: RabbitMQNoConsumers
        expr: rabbitmq_queue_consumers == 0
        for: 5m
        labels:
          severity: warning
          service: rabbitmq
        annotations:
          summary: "No consumers for RabbitMQ queue"
          description: "Queue {{ $labels.queue }} has no active consumers"

      # ============================================
      # ML SERVICE ALERTS
      # ============================================
      - alert: MLServiceDown
        expr: up{job="ml-service"} == 0
        for: 2m
        labels:
          severity: critical
          service: ml
        annotations:
          summary: "ML service is down"
          description: "ML segmentation service has been down for more than 2 minutes."

      - alert: MLServiceHighProcessingTime
        expr: histogram_quantile(0.95, rate(ml_processing_duration_seconds_bucket[5m])) > 60
        for: 5m
        labels:
          severity: warning
          service: ml
        annotations:
          summary: "ML service processing time is high"
          description: "95th percentile processing time is above 60s (current: {{ $value }}s)"

      - alert: MLServiceHighMemoryUsage
        expr: (container_memory_usage_bytes{name="spheroseg-ml"} / container_spec_memory_limit_bytes{name="spheroseg-ml"}) > 0.9
        for: 5m
        labels:
          severity: warning
          service: ml
        annotations:
          summary: "High memory usage on ML service"
          description: "ML service memory usage is above 90% (current: {{ $value | humanizePercentage }})"

      # ============================================
      # INFRASTRUCTURE ALERTS
      # ============================================
      - alert: HighCPUUsage
        expr: (100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
        for: 5m
        labels:
          severity: warning
          type: infrastructure
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% on {{ $labels.instance }} (current: {{ $value }}%)"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: warning
          type: infrastructure
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 90% on {{ $labels.instance }} (current: {{ $value }}%)"

      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: warning
          type: infrastructure
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is above 85% on {{ $labels.instance }} {{ $labels.mountpoint }} (current: {{ $value }}%)"

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100000000
        for: 5m
        labels:
          severity: info
          type: infrastructure
        annotations:
          summary: "High network traffic detected"
          description: "Network receive traffic is above 100MB/s on {{ $labels.instance }} (current: {{ $value | humanize }}B/s)"

      # ============================================
      # CERTIFICATE ALERTS
      # ============================================
      - alert: SSLCertificateExpiringSoon
        expr: probe_ssl_earliest_cert_expiry - time() < 7 * 24 * 3600
        for: 1h
        labels:
          severity: warning
          type: security
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"

      - alert: SSLCertificateExpired
        expr: probe_ssl_earliest_cert_expiry - time() < 0
        for: 1m
        labels:
          severity: critical
          type: security
        annotations:
          summary: "SSL certificate has expired"
          description: "SSL certificate for {{ $labels.instance }} has expired"

      # ============================================
      # ENDPOINT MONITORING
      # ============================================
      - alert: EndpointDown
        expr: probe_success == 0
        for: 2m
        labels:
          severity: critical
          type: endpoint
        annotations:
          summary: "Endpoint is down"
          description: "Endpoint {{ $labels.instance }} has been down for more than 2 minutes"

      - alert: EndpointHighResponseTime
        expr: probe_duration_seconds > 2
        for: 5m
        labels:
          severity: warning
          type: endpoint
        annotations:
          summary: "Endpoint response time is high"
          description: "Endpoint {{ $labels.instance }} response time is above 2s (current: {{ $value }}s)"

      # ============================================
      # BACKUP ALERTS
      # ============================================
      - alert: BackupFailed
        expr: backup_last_success_timestamp_seconds < time() - 86400
        for: 1h
        labels:
          severity: warning
          type: backup
        annotations:
          summary: "Backup has not run successfully"
          description: "Last successful backup was {{ $value | humanizeDuration }} ago"

      - alert: BackupStorageFull
        expr: backup_storage_usage_bytes / backup_storage_total_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          type: backup
        annotations:
          summary: "Backup storage is almost full"
          description: "Backup storage usage is above 90% (current: {{ $value | humanizePercentage }})"