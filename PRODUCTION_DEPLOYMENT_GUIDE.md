# SpheroSeg Production Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Infrastructure Setup](#infrastructure-setup)
3. [Security Configuration](#security-configuration)
4. [Deployment Process](#deployment-process)
5. [Monitoring Setup](#monitoring-setup)
6. [Backup & Recovery](#backup--recovery)
7. [Performance Tuning](#performance-tuning)
8. [Troubleshooting](#troubleshooting)
9. [Security Checklist](#security-checklist)
10. [Disaster Recovery](#disaster-recovery)

## Prerequisites

### System Requirements
- **OS**: Ubuntu 22.04 LTS or RHEL 8+
- **CPU**: Minimum 8 cores, recommended 16 cores
- **RAM**: Minimum 32GB, recommended 64GB
- **Storage**: 
  - System: 100GB SSD
  - Data: 500GB SSD (expandable)
  - Backup: 1TB HDD
- **Network**: 1Gbps connection, static IP
- **GPU**: NVIDIA GPU with 8GB+ VRAM (for ML service)

### Software Requirements
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install required tools
sudo apt-get update
sudo apt-get install -y \
    git \
    curl \
    wget \
    jq \
    htop \
    iotop \
    net-tools \
    nginx \
    certbot \
    python3-certbot-nginx \
    postgresql-client \
    redis-tools

# Install kubectl (for Kubernetes deployment)
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

### Domain & SSL Setup
1. Configure DNS A records:
   ```
   spherosegapp.utia.cas.cz    → YOUR_SERVER_IP
   www.spherosegapp.utia.cas.cz → YOUR_SERVER_IP
   api.spherosegapp.utia.cas.cz → YOUR_SERVER_IP
   monitoring.spherosegapp.utia.cas.cz → YOUR_SERVER_IP
   ```

2. Generate SSL certificates:
   ```bash
   # Using Let's Encrypt
   sudo certbot certonly --standalone \
     -d spherosegapp.utia.cas.cz \
     -d www.spherosegapp.utia.cas.cz \
     -d api.spherosegapp.utia.cas.cz \
     -d monitoring.spherosegapp.utia.cas.cz \
     --email <EMAIL> \
     --agree-tos \
     --non-interactive
   ```

## Infrastructure Setup

### 1. Directory Structure
```bash
# Create deployment directory structure
sudo mkdir -p /opt/spheroseg/{config,data,backup,logs,ssl}
sudo chown -R $USER:$USER /opt/spheroseg

# Create data directories
mkdir -p /opt/spheroseg/data/{postgres,redis,rabbitmq,uploads,ml-models}
mkdir -p /opt/spheroseg/logs/{nginx,backend,ml,monitoring}
mkdir -p /opt/spheroseg/backup/{database,files,config}
```

### 2. Network Configuration
```bash
# Configure firewall
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 9090/tcp  # Prometheus (restrict to monitoring IPs)
sudo ufw allow 3003/tcp  # Grafana (restrict to monitoring IPs)
sudo ufw enable

# Configure Docker networks
docker network create --driver bridge --subnet **********/24 frontend
docker network create --driver bridge --subnet **********/24 --internal backend
docker network create --driver bridge --subnet **********/24 --internal data
docker network create --driver bridge --subnet **********/24 --internal monitoring
```

### 3. Storage Setup
```bash
# Configure LVM for flexible storage management
sudo pvcreate /dev/sdb
sudo vgcreate data-vg /dev/sdb
sudo lvcreate -L 200G -n postgres-lv data-vg
sudo lvcreate -L 100G -n uploads-lv data-vg
sudo lvcreate -L 50G -n backup-lv data-vg

# Format and mount volumes
sudo mkfs.ext4 /dev/data-vg/postgres-lv
sudo mkfs.ext4 /dev/data-vg/uploads-lv
sudo mkfs.ext4 /dev/data-vg/backup-lv

# Add to /etc/fstab
echo "/dev/data-vg/postgres-lv /opt/spheroseg/data/postgres ext4 defaults 0 0" | sudo tee -a /etc/fstab
echo "/dev/data-vg/uploads-lv /opt/spheroseg/data/uploads ext4 defaults 0 0" | sudo tee -a /etc/fstab
echo "/dev/data-vg/backup-lv /opt/spheroseg/backup ext4 defaults 0 0" | sudo tee -a /etc/fstab

sudo mount -a
```

## Security Configuration

### 1. Environment Variables
```bash
# Copy and configure environment template
cp .env.production.template .env.production

# Generate secure passwords
openssl rand -base64 32  # For each password field
openssl rand -hex 32     # For JWT secrets

# Edit .env.production with secure values
nano .env.production
```

### 2. Docker Secrets
```bash
# Create Docker secrets
echo "your-secure-password" | docker secret create db_password -
echo "your-secure-password" | docker secret create redis_password -
echo "your-secure-password" | docker secret create rabbitmq_password -
echo "your-jwt-secret" | docker secret create jwt_secret -
echo "your-session-secret" | docker secret create session_secret -
echo "your-grafana-password" | docker secret create grafana_password -
echo "your-elastic-password" | docker secret create elastic_password -

# For SSL certificates
docker secret create ssl_cert /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem
docker secret create ssl_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem
```

### 3. System Hardening
```bash
# Kernel parameters for production
sudo tee /etc/sysctl.d/99-spheroseg.conf <<EOF
# Network security
net.ipv4.tcp_syncookies = 1
net.ipv4.ip_forward = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1
net.ipv4.tcp_timestamps = 0

# Performance tuning
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 8192
net.core.netdev_max_backlog = 16384
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.ip_local_port_range = 1024 65535

# Memory management
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

sudo sysctl -p /etc/sysctl.d/99-spheroseg.conf
```

## Deployment Process

### 1. Initial Deployment
```bash
# Clone repository
cd /opt/spheroseg
git clone https://github.com/your-org/spheroseg.git .
git checkout tags/v1.0.0  # Use specific version tag

# Load environment variables
source .env.production

# Build images (optional if using pre-built images)
docker-compose -f docker-compose.production.yml build

# Initialize database
docker-compose -f docker-compose.production.yml up -d postgres
sleep 10
docker-compose -f docker-compose.production.yml exec postgres psql -U spheroseg -c "CREATE DATABASE spheroseg;"

# Run migrations
docker-compose -f docker-compose.production.yml run --rm backend npm run migrate:up

# Start all services
docker-compose -f docker-compose.production.yml up -d

# Verify deployment
docker-compose -f docker-compose.production.yml ps
docker-compose -f docker-compose.production.yml logs --tail=50
```

### 2. Zero-Downtime Deployment
```bash
# Use the automated deployment script
chmod +x scripts/deployment/deploy-production-secure.sh
./scripts/deployment/deploy-production-secure.sh

# Or manual blue-green deployment
# Step 1: Scale up new version
docker-compose -f docker-compose.production.yml up -d --no-deps --scale backend=6 backend

# Step 2: Wait for health checks
sleep 30
for i in {1..6}; do
  docker exec spheroseg_backend_$i curl -f http://localhost:5001/api/health || exit 1
done

# Step 3: Remove old instances
docker-compose -f docker-compose.production.yml up -d --no-deps --scale backend=3 --remove-orphans backend
```

### 3. Kubernetes Deployment
```bash
# Create namespace and secrets
kubectl create namespace spheroseg
kubectl create secret generic spheroseg-secrets \
  --from-env-file=.env.production \
  -n spheroseg

# Apply configurations
kubectl apply -k k8s/base
kubectl apply -k k8s/overlays/production

# Monitor deployment
kubectl rollout status deployment/backend -n spheroseg
kubectl rollout status deployment/frontend -n spheroseg
kubectl rollout status deployment/ml -n spheroseg

# Check pods
kubectl get pods -n spheroseg
kubectl logs -f deployment/backend -n spheroseg
```

## Monitoring Setup

### 1. Prometheus & Grafana
```bash
# Start monitoring stack
docker-compose -f docker-compose.monitoring.yml up -d

# Access Grafana
# URL: https://monitoring.spherosegapp.utia.cas.cz
# Default: admin / <configured_password>

# Import dashboards
cd monitoring/grafana/dashboards
for dashboard in *.json; do
  curl -X POST https://monitoring.spherosegapp.utia.cas.cz/api/dashboards/db \
    -H "Authorization: Bearer $GRAFANA_API_KEY" \
    -H "Content-Type: application/json" \
    -d @$dashboard
done
```

### 2. Log Aggregation (ELK Stack)
```bash
# Start ELK stack
docker-compose -f docker-compose.logging.yml up -d

# Access Kibana
# URL: https://logs.spherosegapp.utia.cas.cz
# Default: elastic / <configured_password>

# Configure index patterns
curl -X POST "https://logs.spherosegapp.utia.cas.cz/api/saved_objects/index-pattern" \
  -H "kbn-xsrf: true" \
  -H "Content-Type: application/json" \
  -u elastic:$ELASTIC_PASSWORD \
  -d '{"attributes":{"title":"spheroseg-*","timeFieldName":"@timestamp"}}'
```

### 3. Alerting Configuration
```bash
# Configure AlertManager
cat > monitoring/alertmanager/alertmanager.yml <<EOF
global:
  resolve_timeout: 5m
  smtp_from: '<EMAIL>'
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '$EMAIL_PASSWORD'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'team-admins'
  routes:
  - match:
      severity: critical
    receiver: 'team-critical'
    continue: true

receivers:
- name: 'team-admins'
  email_configs:
  - to: '<EMAIL>'
    
- name: 'team-critical'
  email_configs:
  - to: '<EMAIL>'
  slack_configs:
  - api_url: '$SLACK_WEBHOOK_URL'
    channel: '#alerts-critical'
EOF

# Restart AlertManager
docker-compose -f docker-compose.monitoring.yml restart alertmanager
```

## Backup & Recovery

### 1. Automated Backups
```bash
# Create backup script
cat > /opt/spheroseg/scripts/backup/automated-backup.sh <<'EOF'
#!/bin/bash
BACKUP_DIR="/opt/spheroseg/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
docker-compose -f docker-compose.production.yml exec -T postgres \
  pg_dump -U spheroseg spheroseg | gzip > $BACKUP_DIR/database/spheroseg_$DATE.sql.gz

# Files backup
tar -czf $BACKUP_DIR/files/uploads_$DATE.tar.gz /opt/spheroseg/data/uploads

# Redis backup
docker-compose -f docker-compose.production.yml exec -T redis \
  redis-cli --rdb /backup/redis_$DATE.rdb BGSAVE

# Upload to S3
aws s3 sync $BACKUP_DIR s3://spheroseg-backups/$DATE/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -type f -mtime +30 -delete
EOF

chmod +x /opt/spheroseg/scripts/backup/automated-backup.sh

# Add to crontab
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/spheroseg/scripts/backup/automated-backup.sh") | crontab -
```

### 2. Recovery Procedures
```bash
# Database recovery
gunzip -c /backup/database/spheroseg_20240101_020000.sql.gz | \
  docker-compose -f docker-compose.production.yml exec -T postgres psql -U spheroseg spheroseg

# Files recovery
tar -xzf /backup/files/uploads_20240101_020000.tar.gz -C /

# Redis recovery
docker-compose -f docker-compose.production.yml exec -T redis \
  redis-cli --rdb /backup/redis_20240101_020000.rdb

# Full system recovery
./scripts/rollback/restore-full-system.sh --backup-date 20240101_020000
```

## Performance Tuning

### 1. Database Optimization
```sql
-- Connect to database
docker-compose -f docker-compose.production.yml exec postgres psql -U spheroseg

-- Analyze tables
ANALYZE;

-- Check slow queries
SELECT query, calls, mean_exec_time, max_exec_time 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;

-- Create indexes for common queries
CREATE INDEX idx_images_project_id ON images(project_id);
CREATE INDEX idx_images_created_at ON images(created_at);
CREATE INDEX idx_users_email ON users(email);

-- Configure autovacuum
ALTER TABLE images SET (autovacuum_vacuum_scale_factor = 0.1);
ALTER TABLE projects SET (autovacuum_vacuum_scale_factor = 0.1);
```

### 2. Redis Optimization
```bash
# Monitor Redis performance
docker-compose -f docker-compose.production.yml exec redis redis-cli INFO stats

# Configure memory optimization
docker-compose -f docker-compose.production.yml exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
docker-compose -f docker-compose.production.yml exec redis redis-cli CONFIG SET maxmemory 2gb

# Enable persistence with AOF
docker-compose -f docker-compose.production.yml exec redis redis-cli CONFIG SET appendonly yes
```

### 3. Application Performance
```bash
# Enable Node.js clustering
docker-compose -f docker-compose.production.yml exec backend \
  node --max-old-space-size=4096 --optimize-for-size dist/cluster.js

# Monitor memory usage
docker stats --no-stream

# Configure Nginx caching
docker-compose -f docker-compose.production.yml exec nginx \
  nginx -s reload
```

### 4. CDN Configuration
```bash
# CloudFlare setup
curl -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/purge_cache" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{"purge_everything":true}'

# Configure cache rules
curl -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/pagerules" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{
    "targets": [{"target": "url", "constraint": {"operator": "matches", "value": "*.spherosegapp.utia.cas.cz/static/*"}}],
    "actions": [{"id": "cache_level", "value": "cache_everything"}, {"id": "edge_cache_ttl", "value": 2678400}]
  }'
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Service Won't Start
```bash
# Check logs
docker-compose -f docker-compose.production.yml logs backend --tail=100

# Check resource usage
docker system df
df -h
free -h

# Clean up resources
docker system prune -af --volumes
```

#### 2. Database Connection Issues
```bash
# Test connection
docker-compose -f docker-compose.production.yml exec backend \
  psql *********************************************/spheroseg

# Check PostgreSQL logs
docker-compose -f docker-compose.production.yml logs postgres --tail=50

# Restart with fresh connection pool
docker-compose -f docker-compose.production.yml restart backend
```

#### 3. High Memory Usage
```bash
# Identify memory consumers
docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}"

# Restart specific service
docker-compose -f docker-compose.production.yml restart ml

# Adjust memory limits
docker-compose -f docker-compose.production.yml up -d --force-recreate backend
```

#### 4. SSL Certificate Issues
```bash
# Renew certificates
certbot renew --force-renewal

# Reload Nginx
docker-compose -f docker-compose.production.yml exec nginx nginx -s reload

# Verify certificates
openssl s_client -connect spherosegapp.utia.cas.cz:443 -servername spherosegapp.utia.cas.cz
```

## Security Checklist

### Pre-Deployment
- [ ] All passwords are strong (16+ characters, mixed case, numbers, symbols)
- [ ] Environment variables are properly configured
- [ ] SSL certificates are valid and installed
- [ ] Firewall rules are configured
- [ ] Docker secrets are created
- [ ] Backup system is tested

### Application Security
- [ ] JWT secrets are unique and secure
- [ ] Session secrets are unique and secure
- [ ] CORS is properly configured
- [ ] Rate limiting is enabled
- [ ] Input validation is working
- [ ] File upload restrictions are in place
- [ ] SQL injection protection verified
- [ ] XSS protection headers enabled
- [ ] CSRF protection enabled

### Infrastructure Security
- [ ] Docker images are scanned for vulnerabilities
- [ ] Containers run as non-root users
- [ ] Read-only root filesystems where possible
- [ ] Network segmentation is implemented
- [ ] Monitoring and alerting is active
- [ ] Log aggregation is working
- [ ] Intrusion detection is configured
- [ ] Regular security updates scheduled

### Data Security
- [ ] Database encryption at rest
- [ ] Encrypted connections (TLS/SSL)
- [ ] Backup encryption enabled
- [ ] GDPR compliance verified
- [ ] Data retention policies implemented
- [ ] Access logs are monitored

## Disaster Recovery

### Recovery Time Objectives (RTO)
- **Critical Services**: 15 minutes
- **Full System**: 1 hour
- **Data Recovery**: 4 hours

### Recovery Point Objectives (RPO)
- **Database**: 1 hour (continuous replication)
- **Files**: 6 hours (scheduled backups)
- **Configuration**: Real-time (version control)

### Disaster Recovery Procedures

#### 1. Complete System Failure
```bash
# 1. Provision new server
./scripts/infrastructure/provision-dr-server.sh

# 2. Restore from backup
./scripts/disaster-recovery/full-restore.sh --latest

# 3. Update DNS
./scripts/disaster-recovery/update-dns.sh --server $NEW_SERVER_IP

# 4. Verify services
./scripts/disaster-recovery/verify-services.sh
```

#### 2. Database Corruption
```bash
# 1. Stop affected services
docker-compose -f docker-compose.production.yml stop backend ml

# 2. Restore database from backup
./scripts/disaster-recovery/restore-database.sh --point-in-time "2024-01-01 12:00:00"

# 3. Verify data integrity
docker-compose -f docker-compose.production.yml exec postgres \
  psql -U spheroseg -c "SELECT COUNT(*) FROM images;"

# 4. Restart services
docker-compose -f docker-compose.production.yml start backend ml
```

#### 3. Security Breach
```bash
# 1. Isolate affected systems
./scripts/security/isolate-system.sh

# 2. Rotate all credentials
./scripts/security/rotate-all-credentials.sh

# 3. Audit logs
./scripts/security/audit-logs.sh --since "7 days ago"

# 4. Apply security patches
./scripts/security/apply-patches.sh

# 5. Restore from clean backup
./scripts/disaster-recovery/restore-from-clean-backup.sh
```

## Maintenance Procedures

### Daily Tasks
- Monitor application health dashboards
- Check backup completion
- Review error logs
- Verify SSL certificate validity

### Weekly Tasks
- Review performance metrics
- Update security patches
- Test backup restoration
- Clean up old logs and temporary files

### Monthly Tasks
- Full system backup
- Security audit
- Performance optimization review
- Capacity planning review
- Update documentation

### Quarterly Tasks
- Disaster recovery drill
- Security penetration testing
- Major version updates
- Infrastructure review

## Support and Contact

### Technical Support
- **Email**: <EMAIL>
- **Slack**: #spheroseg-ops
- **On-Call**: +420-XXX-XXX-XXX

### Escalation Path
1. L1 Support Team
2. DevOps Team Lead
3. System Architect
4. CTO

### Documentation
- **Wiki**: https://wiki.spherosegapp.utia.cas.cz
- **API Docs**: https://api.spherosegapp.utia.cas.cz/docs
- **Runbooks**: /opt/spheroseg/docs/runbooks/

## Version History
- v1.0.0 - Initial production deployment
- v1.1.0 - Added monitoring stack
- v1.2.0 - Enhanced security features
- v1.3.0 - Kubernetes support
- v1.4.0 - CDN integration

---

Last Updated: 2024-01-01
Author: SpheroSeg DevOps Team