# Security Policy

## Reporting Security Vulnerabilities

We take the security of SpheroSeg seriously. If you discover a security vulnerability, please follow these steps:

1. **DO NOT** create a public GitHub issue
2. Email security details to: <EMAIL>
3. Include:
   - Description of the vulnerability
   - Steps to reproduce
   - Potential impact
   - Your recommended fix (if any)

We will acknowledge receipt within 48 hours and provide a detailed response within 7 days.

## Security Best Practices

### 1. Secrets Management

#### Never Commit Secrets
- Never commit `.env` files or any file containing secrets
- Use `.env.example` files to document required variables
- All secrets must be loaded from environment variables

#### Secret Generation
```bash
# Generate JWT Secret (min 64 characters)
openssl rand -base64 64

# Generate Session Secret (min 32 characters)
openssl rand -base64 48

# Generate Encryption Key (32 bytes hex)
openssl rand -hex 32

# Generate strong password
openssl rand -base64 32
```

#### Secret Rotation
Regular secret rotation is critical for security:

```bash
# Rotate all secrets (development)
npm run rotate-secrets --dry-run  # Preview changes
npm run rotate-secrets             # Execute rotation

# Production rotation (requires confirmation)
npm run rotate-secrets --force --notify
```

#### Secure Storage Options
1. **HashiCorp Vault** (Recommended for production)
   ```bash
   vault kv put secret/spheroseg \
     JWT_SECRET="..." \
     SESSION_SECRET="..." \
     ENCRYPTION_KEY="..."
   ```

2. **AWS Secrets Manager**
   ```bash
   aws secretsmanager create-secret \
     --name spheroseg/production \
     --secret-string file://secrets.json
   ```

3. **Azure Key Vault**
   ```bash
   az keyvault secret set \
     --vault-name spheroseg-vault \
     --name JWT-SECRET \
     --value "..."
   ```

4. **Environment Variables** (Simple deployments)
   ```bash
   export JWT_SECRET="$(openssl rand -base64 64)"
   export SESSION_SECRET="$(openssl rand -base64 48)"
   ```

### 2. Git Security

#### Pre-commit Hooks
Pre-commit hooks are configured to prevent accidental secret commits:

```bash
# Install hooks
npm run prepare

# Manual scan
gitleaks detect --source . --config .gitleaks.toml
```

#### Clean Git History
If secrets were accidentally committed:

```bash
# Create backup first
./scripts/clean-git-history.sh --no-backup  # Dry run
./scripts/clean-git-history.sh              # Execute

# After cleaning
git push --force --all
git push --force --tags
```

### 3. Authentication & Authorization

#### JWT Configuration
- Tokens expire after 15 minutes (access) and 7 days (refresh)
- Refresh token families prevent token replay attacks
- All tokens are signed with RS256 algorithm

#### Session Security
- Sessions stored in Redis with encryption
- Automatic session invalidation on suspicious activity
- HTTP-only, secure, SameSite cookies in production

#### Password Requirements
- Minimum 12 characters
- Must contain: uppercase, lowercase, number, special character
- Bcrypt with 12 rounds for hashing
- Password history to prevent reuse

### 4. API Security

#### Rate Limiting
```typescript
// Default limits
const rateLimits = {
  global: 100,        // requests per minute
  auth: 5,            // login attempts per minute
  upload: 10,         // file uploads per hour
  api: 1000          // API calls per hour
};
```

#### CORS Configuration
```typescript
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(','),
  credentials: true,
  optionsSuccessStatus: 200
};
```

#### Input Validation
- All inputs sanitized with express-validator
- File uploads restricted by type and size
- SQL injection prevention via Prisma ORM
- XSS protection with DOMPurify

### 5. Infrastructure Security

#### Docker Security
```dockerfile
# Run as non-root user
USER node

# Security scanning
docker scan spheroseg:latest

# Read-only root filesystem
docker run --read-only spheroseg
```

#### SSL/TLS Configuration
```nginx
# Strong cipher suite
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;

# HSTS
add_header Strict-Transport-Security "max-age=63072000" always;
```

#### Database Security
- Encrypted connections (SSL/TLS)
- Principle of least privilege for database users
- Regular backups with encryption
- Connection pooling with limits

### 6. Monitoring & Logging

#### Security Events to Monitor
- Failed login attempts
- Token refresh anomalies
- Unusual API patterns
- File upload attempts
- Permission escalation attempts

#### Audit Logging
```typescript
// Critical events logged
const auditEvents = [
  'user.login',
  'user.logout',
  'token.refresh',
  'permission.change',
  'data.export',
  'secret.rotation'
];
```

#### Log Security
- Never log sensitive data (passwords, tokens, PII)
- Centralized logging with encryption
- Log retention policy (90 days)
- Regular log analysis

### 7. Dependency Security

#### Regular Updates
```bash
# Check for vulnerabilities
npm audit

# Update dependencies
npm update --save
npm audit fix

# Check with Snyk
snyk test
snyk monitor
```

#### Supply Chain Security
- Lock file committed (package-lock.json)
- Dependency review in pull requests
- Automated vulnerability scanning in CI/CD
- Regular dependency pruning

### 8. Security Headers

The application sets these security headers:

```typescript
// Helmet.js configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### 9. File Upload Security

#### Restrictions
- Max file size: 50MB
- Allowed types: JPEG, PNG, TIFF, BMP
- Virus scanning (when configured)
- Isolated storage directory

#### Validation
```typescript
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};
```

### 10. Incident Response

See [INCIDENT_RESPONSE.md](./INCIDENT_RESPONSE.md) for detailed procedures.

## Security Checklist

### Development
- [ ] Environment variables used for all secrets
- [ ] No hardcoded credentials in code
- [ ] Pre-commit hooks installed
- [ ] Dependencies up to date
- [ ] Security headers configured
- [ ] Input validation implemented
- [ ] Error messages don't expose sensitive info

### Deployment
- [ ] Production secrets rotated
- [ ] SSL/TLS configured
- [ ] Firewall rules configured
- [ ] Database encrypted
- [ ] Backups encrypted
- [ ] Monitoring enabled
- [ ] Rate limiting active

### Regular Maintenance
- [ ] Weekly: Review security logs
- [ ] Monthly: Update dependencies
- [ ] Quarterly: Rotate secrets
- [ ] Yearly: Security audit

## Compliance

The application is designed to help meet:
- GDPR requirements (data protection)
- OWASP Top 10 mitigation
- SOC 2 Type II preparation
- ISO 27001 alignment

## Resources

- [OWASP Security Guidelines](https://owasp.org/)
- [Node.js Security Best Practices](https://nodejs.org/en/docs/guides/security/)
- [Docker Security](https://docs.docker.com/engine/security/)
- [PostgreSQL Security](https://www.postgresql.org/docs/current/security.html)

## Security Tools

### Recommended Tools
- **Gitleaks**: Secret scanning
- **Snyk**: Dependency vulnerabilities
- **OWASP ZAP**: Security testing
- **Burp Suite**: Web vulnerability scanner
- **Trivy**: Container scanning

### Installation
```bash
# macOS
brew install gitleaks snyk trivy

# Linux
curl -sSfL https://raw.githubusercontent.com/gitleaks/gitleaks/master/scripts/install.sh | sh
```

## Contact

For security concerns, contact:
- Email: <EMAIL>
- Response time: 48 hours

---

Last updated: 2025-08-07
Version: 1.0.0