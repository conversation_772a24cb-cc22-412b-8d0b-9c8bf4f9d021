# Incident Response Plan

## Overview

This document outlines the procedures to follow in case of a security incident involving the SpheroSeg application.

## Incident Classification

### Severity Levels

| Level | Description | Response Time | Examples |
|-------|-------------|---------------|----------|
| **CRITICAL** | Immediate threat to data or systems | < 1 hour | Active breach, data exfiltration, ransomware |
| **HIGH** | Significant security vulnerability | < 4 hours | Exposed secrets, authentication bypass, SQL injection |
| **MEDIUM** | Security issue with limited impact | < 24 hours | XSS vulnerability, session fixation, information disclosure |
| **LOW** | Minor security concern | < 72 hours | Missing security headers, outdated dependencies |

## Response Team

### Core Team Roles

| Role | Responsibilities | Contact |
|------|-----------------|---------|
| **Incident Commander** | Overall incident coordination | On-call rotation |
| **Security Lead** | Technical investigation and remediation | <EMAIL> |
| **Communications Lead** | Internal and external communications | <EMAIL> |
| **Operations Lead** | System operations and recovery | <EMAIL> |
| **Legal/Compliance** | Legal and regulatory requirements | <EMAIL> |

### Escalation Path

1. On-call engineer (immediate)
2. Team Lead (< 15 minutes)
3. Security Lead (< 30 minutes)
4. CTO/Executive (< 1 hour for CRITICAL)

## Incident Response Procedures

### Phase 1: Detection & Analysis (0-2 hours)

#### Immediate Actions Checklist

- [ ] **Confirm the incident** - Verify it's not a false positive
- [ ] **Classify severity** - Use the severity matrix above
- [ ] **Document everything** - Start incident log with timestamp
- [ ] **Notify incident commander** - Initiate response team
- [ ] **Preserve evidence** - Take snapshots, save logs

#### Initial Assessment Questions

1. What type of incident? (breach, exposure, attack, etc.)
2. When was it discovered?
3. How was it discovered?
4. What systems are affected?
5. Is it ongoing or contained?
6. What data might be affected?
7. Are there indicators of compromise (IoCs)?

#### Evidence Collection

```bash
# Create incident directory
mkdir -p /incidents/$(date +%Y%m%d_%H%M%S)
cd /incidents/$(date +%Y%m%d_%H%M%S)

# Collect system state
docker ps -a > docker_state.txt
docker logs backend > backend_logs.txt
docker logs frontend > frontend_logs.txt
docker logs ml > ml_logs.txt

# Database audit
docker exec db pg_dump -U postgres spheroseg > database_backup.sql

# Network connections
netstat -an > network_connections.txt
ss -tulpn > listening_ports.txt

# Process list
ps auxf > process_list.txt

# Recent file changes
find /app -type f -mtime -1 > recent_changes.txt

# Git status
git log --oneline -20 > recent_commits.txt
git status > git_status.txt
```

### Phase 2: Containment (2-4 hours)

#### Short-term Containment

**For Secret Exposure:**
```bash
# 1. Rotate all secrets immediately
cd /home/<USER>/spheroseg/packages/backend
npm run rotate-secrets --force

# 2. Invalidate all existing sessions
docker exec redis redis-cli FLUSHALL

# 3. Force all users to re-authenticate
docker exec db psql -U postgres -d spheroseg -c "UPDATE users SET force_logout = true;"
```

**For Active Attack:**
```bash
# 1. Block attacker IP
iptables -A INPUT -s ATTACKER_IP -j DROP

# 2. Enable emergency rate limiting
docker exec backend npm run emergency-rate-limit

# 3. Disable affected features
docker exec backend npm run disable-feature --name=FEATURE_NAME
```

**For Data Breach:**
```bash
# 1. Isolate affected systems
docker-compose stop

# 2. Backup current state
docker commit backend backend_incident_backup
docker commit db db_incident_backup

# 3. Disable external access
iptables -A INPUT -p tcp --dport 80 -j DROP
iptables -A INPUT -p tcp --dport 443 -j DROP
```

#### Long-term Containment

1. Deploy patched version
2. Implement additional monitoring
3. Enhanced logging for affected components
4. Temporary access restrictions

### Phase 3: Eradication (4-24 hours)

#### Root Cause Analysis

Use the 5 Whys technique:
1. Why did the incident occur?
2. Why was that possible?
3. Why wasn't it prevented?
4. Why wasn't it detected earlier?
5. Why did our controls fail?

#### Removal Checklist

- [ ] Remove attacker access (accounts, backdoors, shells)
- [ ] Patch vulnerabilities
- [ ] Update security rules
- [ ] Clean infected systems
- [ ] Remove malicious files
- [ ] Reset compromised credentials

#### Verification

```bash
# Verify no backdoors
find / -name "*.php" -exec grep -l "eval\|base64_decode\|shell_exec" {} \;

# Check for unauthorized SSH keys
find /home -name "authorized_keys" -exec cat {} \;

# Audit user accounts
docker exec db psql -U postgres -d spheroseg -c "SELECT * FROM users WHERE created_at > NOW() - INTERVAL '24 hours';"

# Check for persistence mechanisms
crontab -l
systemctl list-timers
```

### Phase 4: Recovery (24-72 hours)

#### Recovery Steps

1. **Restore from clean backup**
   ```bash
   ./scripts/rollback/restore-database.sh
   ./scripts/rollback/rollback-app.sh
   ```

2. **Deploy patched version**
   ```bash
   git checkout main
   git pull origin main
   docker-compose build --no-cache
   docker-compose up -d
   ```

3. **Verify system integrity**
   ```bash
   npm run test:security
   npm run test:e2e
   ./scripts/health-check.sh
   ```

4. **Monitor closely**
   - Enable enhanced monitoring
   - Review logs every hour
   - Check for anomalies

#### Validation Checklist

- [ ] All services running normally
- [ ] Security patches applied
- [ ] Monitoring active
- [ ] Backups verified
- [ ] Access controls reviewed
- [ ] Logs being collected

### Phase 5: Lessons Learned (Within 1 week)

#### Post-Incident Review Meeting

**Agenda:**
1. Timeline review (15 min)
2. What went well (10 min)
3. What could be improved (20 min)
4. Action items (15 min)

#### Documentation Requirements

Create incident report including:
- Executive summary
- Timeline of events
- Technical details
- Impact assessment
- Response actions taken
- Root cause analysis
- Recommendations
- Lessons learned

#### Improvement Actions

- [ ] Update runbooks
- [ ] Improve monitoring
- [ ] Enhance security controls
- [ ] Update incident response plan
- [ ] Security awareness training
- [ ] Tool improvements

## Specific Incident Playbooks

### Playbook 1: Exposed Secrets in Git

**Detection:** Gitleaks alert, GitHub security alert, or manual discovery

**Immediate Response:**
```bash
# 1. Rotate affected secrets
./packages/backend/scripts/rotate-secrets.ts --force

# 2. Clean Git history
./scripts/clean-git-history.sh --force

# 3. Force push cleaned history
git push --force --all
git push --force --tags

# 4. Notify all users to re-clone
npm run notify-users --message "Security update: Please re-clone repository"
```

**Follow-up:**
- Review how secrets were exposed
- Enhance pre-commit hooks
- Security training for team

### Playbook 2: SQL Injection Attack

**Detection:** WAF alert, unusual database queries, error logs

**Immediate Response:**
```bash
# 1. Block attacking IP
iptables -A INPUT -s ATTACKER_IP -j DROP

# 2. Enable read-only mode
docker exec db psql -U postgres -c "ALTER DATABASE spheroseg SET default_transaction_read_only = on;"

# 3. Analyze attack patterns
docker logs backend | grep -E "SELECT.*UNION|OR.*1=1|DROP TABLE"

# 4. Patch vulnerability
# Deploy fixed code
```

**Recovery:**
- Restore database if corrupted
- Audit all database queries
- Implement prepared statements
- Add query monitoring

### Playbook 3: Ransomware Attack

**Detection:** Files encrypted, ransom note, system unavailable

**Immediate Response:**
```bash
# 1. Isolate affected systems
docker-compose stop
systemctl stop docker

# 2. DO NOT pay ransom

# 3. Preserve evidence
dd if=/dev/sda of=/backup/infected_disk.img

# 4. Notify law enforcement
# Contact local FBI/cybercrime unit
```

**Recovery:**
- Restore from clean backups
- Rebuild affected systems
- Implement additional controls
- Review backup procedures

### Playbook 4: Data Breach

**Detection:** Unauthorized data access, data exfiltration alerts

**Immediate Response:**
```bash
# 1. Document what data was accessed
docker exec db psql -U postgres -d spheroseg -c "SELECT * FROM audit_log WHERE timestamp > NOW() - INTERVAL '24 hours';"

# 2. Identify affected users
# Query based on breach specifics

# 3. Preserve evidence
tar -czf evidence.tar.gz /var/log /home/<USER>/spheroseg/logs

# 4. Legal notification requirements
# Check GDPR, state laws
```

**Legal Requirements:**
- GDPR: Notify within 72 hours
- CCPA: Without unreasonable delay
- Document all affected individuals
- Prepare breach notification

## Communication Templates

### Internal Communication

**Subject:** [SECURITY INCIDENT] [Severity] - [Brief Description]

```
Team,

We have detected a [type] security incident affecting [systems].

Status: [Investigating/Contained/Resolved]
Severity: [Critical/High/Medium/Low]
Impact: [Description of impact]

Actions Required:
- [Action 1]
- [Action 2]

Incident Commander: [Name]
Slack Channel: #incident-[number]

Updates every [frequency].
```

### User Notification

**Subject:** Important Security Update - Action Required

```
Dear User,

We recently discovered a security issue that may have affected your account.

What Happened:
[Brief, non-technical description]

What Information Was Involved:
[List of affected data types]

What We Are Doing:
[List of actions taken]

What You Should Do:
1. Change your password immediately
2. Review your account activity
3. Enable two-factor authentication

We take security seriously and apologize for any inconvenience.

Questions? Contact <EMAIL>
```

### Regulatory Notification

Follow legal requirements for:
- GDPR (EU)
- CCPA (California)
- HIPAA (if applicable)
- State breach notification laws

## Tools and Resources

### Incident Response Tools

```bash
# Install incident response toolkit
apt-get install -y \
  forensics-all \
  sleuthkit \
  volatility \
  wireshark \
  tcpdump \
  yara \
  clamav
```

### Key Contacts

| Contact | Purpose | Details |
|---------|---------|---------|
| AWS Support | Infrastructure | 1-800-xxx-xxxx |
| Cloudflare | DDoS Protection | <EMAIL> |
| Legal Counsel | Legal advice | <EMAIL> |
| Cyber Insurance | Insurance claims | <EMAIL> |
| FBI IC3 | Cybercrime | ic3.gov |

### Documentation Links

- [Security Policy](./SECURITY.md)
- [Backup Procedures](./scripts/backup/)
- [Rollback Procedures](./scripts/rollback/)
- [Monitoring Dashboard](http://monitoring.spheroseg.local)
- [Audit Logs](http://logs.spheroseg.local)

## Testing the Plan

### Quarterly Drills

Schedule and conduct:
1. Tabletop exercises
2. Simulated incidents
3. Recovery testing
4. Communication drills

### Metrics to Track

- Time to detect (TTD)
- Time to respond (TTR)
- Time to contain (TTC)
- Time to recover (TTR)
- Lessons learned implemented

## Version History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-08-07 | Initial version | Security Team |

---

**Remember:** In a real incident, stay calm, document everything, and follow the plan. When in doubt, escalate.