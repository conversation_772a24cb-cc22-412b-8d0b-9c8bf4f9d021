version: '3.8'

# Development Docker Compose Configuration
# Optimized for local development with hot reload and debugging

services:
  # ===========================================
  # PostgreSQL Database
  # ===========================================
  db:
    image: postgres:16-alpine
    container_name: spheroseg-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres-CHANGE-IN-PRODUCTION}
      POSTGRES_DB: ${POSTGRES_DB:-spheroseg_dev}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=en_US.utf8"
      POSTGRES_HOST_AUTH_METHOD: md5
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spheroseg_dev"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ===========================================
  # Redis Cache
  # ===========================================
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ===========================================
  # RabbitMQ Message Queue
  # ===========================================
  rabbitmq:
    image: rabbitmq:3.13-management-alpine
    container_name: spheroseg-rabbitmq-dev
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER:-guest}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS:-guest-CHANGE-IN-PRODUCTION}
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data_dev:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ===========================================
  # Backend API (Development with hot reload)
  # ===========================================
  backend:
    build:
      context: .
      dockerfile: ./packages/backend/Dockerfile.dev
      target: development
    container_name: spheroseg-backend-dev
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    environment:
      NODE_ENV: development
      PORT: 5001
      LOG_LEVEL: debug
      # Database
      DB_HOST: db
      DB_PORT: 5432
      DB_NAME: spheroseg_dev
      DB_USER: postgres
      DB_PASSWORD: ${POSTGRES_PASSWORD:-postgres-CHANGE-IN-PRODUCTION}
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres-CHANGE-IN-PRODUCTION}@db:5432/${POSTGRES_DB:-spheroseg_dev}?schema=public
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      # RabbitMQ
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
      # Secrets (use .env in production)
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-CHANGE-IN-PRODUCTION}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-dev-refresh-secret-CHANGE-IN-PRODUCTION}
      SESSION_SECRET: ${SESSION_SECRET:-dev-session-secret-CHANGE-IN-PRODUCTION}
      CSRF_SECRET: ${CSRF_SECRET:-dev-csrf-secret-CHANGE-IN-PRODUCTION}
      # ML Service
      ML_SERVICE_URL: http://ml:5002
      # CORS
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001
      # Development
      WATCH_MODE: "true"
      FORCE_COLOR: "1"
    ports:
      - "5001:5001"
      - "9230:9229" # Node.js debugger (mapped to different host port)
      - "9091:9090" # Metrics (mapped to different host port)
    volumes:
      # Source code for hot reload
      - ./packages/backend:/app/packages/backend
      - ./packages/shared:/app/packages/shared
      - ./packages/types:/app/packages/types
      # Preserve node_modules
      - backend_node_modules_dev:/app/node_modules
      - backend_package_node_modules_dev:/app/packages/backend/node_modules
      # Uploads directory
      - ./uploads:/app/uploads
    command: npm run dev --workspace=@spheroseg/backend
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # ===========================================
  # Frontend (Development with hot reload)
  # ===========================================
  frontend:
    build:
      context: .
      dockerfile: ./packages/frontend/Dockerfile.dev
      target: development
    container_name: spheroseg-frontend-dev
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:5001
      VITE_WS_URL: ws://localhost:5001
      CHOKIDAR_USEPOLLING: "true"
      FORCE_COLOR: "1"
    ports:
      - "3000:3000"
      - "3001:3001" # Vite HMR
    volumes:
      # Source code for hot reload
      - ./packages/frontend:/app/packages/frontend
      - ./packages/shared:/app/packages/shared
      - ./packages/types:/app/packages/types
      # Preserve node_modules
      - frontend_node_modules_dev:/app/node_modules
      - frontend_package_node_modules_dev:/app/packages/frontend/node_modules
    command: npm run dev --workspace=@spheroseg/frontend -- --host 0.0.0.0
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ===========================================
  # ML Service (Python)
  # ===========================================
  ml:
    build:
      context: ./ML
      dockerfile: Dockerfile.dev
    container_name: spheroseg-ml-dev
    restart: unless-stopped
    depends_on:
      rabbitmq:
        condition: service_healthy
    environment:
      FLASK_ENV: development
      FLASK_DEBUG: "1"
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: ${RABBITMQ_DEFAULT_USER:-guest}
      RABBITMQ_PASS: ${RABBITMQ_DEFAULT_PASS:-guest-CHANGE-IN-PRODUCTION}
      PYTHONUNBUFFERED: "1"
    ports:
      - "5002:5002"
      - "5678:5678" # Python debugger (debugpy)
    volumes:
      - ./ML:/app
      - ml_models_dev:/app/models
    command: python -m debugpy --listen 0.0.0.0:5678 --wait-for-client app.py
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ===========================================
  # Database Admin Tool (Development only)
  # ===========================================
  adminer:
    image: adminer:4.8.1
    container_name: spheroseg-adminer-dev
    restart: unless-stopped
    depends_on:
      - db
    environment:
      ADMINER_DESIGN: pepa-linha
      ADMINER_DEFAULT_SERVER: db
    ports:
      - "8080:8080"
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # ===========================================
  # Redis Commander (Development only)
  # ===========================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: spheroseg-redis-commander-dev
    restart: unless-stopped
    depends_on:
      - redis
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: ${REDIS_COMMANDER_USER:-admin}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin-CHANGE-IN-PRODUCTION}
    ports:
      - "8081:8081"
    networks:
      - spheroseg-dev
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

# ===========================================
# Networks
# ===========================================
networks:
  spheroseg-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===========================================
# Volumes
# ===========================================
volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  rabbitmq_data_dev:
    driver: local
  backend_node_modules_dev:
    driver: local
  backend_package_node_modules_dev:
    driver: local
  frontend_node_modules_dev:
    driver: local
  frontend_package_node_modules_dev:
    driver: local
  ml_models_dev:
    driver: local