#!/bin/bash

set -e

echo "🚀 Setting up test environment..."

# Stop any existing test containers
docker-compose -f docker-compose.test.yml down -v 2>/dev/null || true

# Start test services
echo "📦 Starting test services..."
docker-compose -f docker-compose.test.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
  if docker-compose -f docker-compose.test.yml ps | grep -q "unhealthy\|starting"; then
    echo -n "."
    sleep 1
    counter=$((counter + 1))
  else
    echo ""
    echo "✅ All services are healthy!"
    break
  fi
done

if [ $counter -eq $timeout ]; then
  echo "❌ Timeout waiting for services"
  docker-compose -f docker-compose.test.yml logs
  exit 1
fi

# Setup test database
echo "🗄️ Setting up test database..."
export DATABASE_URL="postgresql://testuser:testpass@localhost:5433/spheroseg_test"

# Run migrations
cd packages/backend
npx prisma migrate deploy || true
cd ../..

echo "✅ Test environment ready!"