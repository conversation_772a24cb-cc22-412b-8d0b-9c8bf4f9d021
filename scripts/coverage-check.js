#!/usr/bin/env node

/**
 * Coverage Report Aggregator and Validator
 * 
 * This script:
 * 1. Collects coverage reports from all packages
 * 2. Aggregates the results
 * 3. Validates against thresholds
 * 4. Generates a unified report
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Coverage thresholds for the entire project
const GLOBAL_THRESHOLDS = {
  branches: 75,
  functions: 80,
  lines: 85,
  statements: 85
};

// Target for >90% coverage
const TARGET_THRESHOLDS = {
  branches: 90,
  functions: 90,
  lines: 90,
  statements: 90
};

// Packages to check
const PACKAGES = ['backend', 'frontend', 'shared'];

/**
 * Load coverage summary from a package
 */
function loadCoverageSummary(packageName) {
  const summaryPath = path.join(
    rootDir,
    'packages',
    packageName,
    'coverage',
    'coverage-summary.json'
  );

  if (!fs.existsSync(summaryPath)) {
    console.warn(`⚠️  No coverage report found for ${packageName}`);
    return null;
  }

  try {
    const content = fs.readFileSync(summaryPath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ Failed to parse coverage for ${packageName}:`, error.message);
    return null;
  }
}

/**
 * Aggregate coverage data from all packages
 */
function aggregateCoverage() {
  const aggregated = {
    total: {
      lines: { total: 0, covered: 0, skipped: 0, pct: 0 },
      statements: { total: 0, covered: 0, skipped: 0, pct: 0 },
      functions: { total: 0, covered: 0, skipped: 0, pct: 0 },
      branches: { total: 0, covered: 0, skipped: 0, pct: 0 }
    },
    packages: {}
  };

  for (const packageName of PACKAGES) {
    const summary = loadCoverageSummary(packageName);
    if (!summary || !summary.total) continue;

    aggregated.packages[packageName] = summary.total;

    // Aggregate metrics
    for (const metric of ['lines', 'statements', 'functions', 'branches']) {
      aggregated.total[metric].total += summary.total[metric].total || 0;
      aggregated.total[metric].covered += summary.total[metric].covered || 0;
      aggregated.total[metric].skipped += summary.total[metric].skipped || 0;
    }
  }

  // Calculate percentages
  for (const metric of ['lines', 'statements', 'functions', 'branches']) {
    const total = aggregated.total[metric].total;
    const covered = aggregated.total[metric].covered;
    aggregated.total[metric].pct = total > 0 ? (covered / total) * 100 : 0;
  }

  return aggregated;
}

/**
 * Format percentage with color
 */
function formatPercentage(value, threshold, target) {
  const rounded = value.toFixed(2);
  if (value >= target) {
    return `\x1b[32m${rounded}%\x1b[0m`; // Green
  } else if (value >= threshold) {
    return `\x1b[33m${rounded}%\x1b[0m`; // Yellow
  } else {
    return `\x1b[31m${rounded}%\x1b[0m`; // Red
  }
}

/**
 * Print coverage report
 */
function printReport(coverage) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 COVERAGE REPORT');
  console.log('='.repeat(80));

  // Package-level coverage
  console.log('\n📦 Package Coverage:');
  console.log('-'.repeat(80));
  
  for (const [packageName, metrics] of Object.entries(coverage.packages)) {
    console.log(`\n${packageName}:`);
    console.log(`  Lines......: ${formatPercentage(metrics.lines.pct, GLOBAL_THRESHOLDS.lines, TARGET_THRESHOLDS.lines)} (${metrics.lines.covered}/${metrics.lines.total})`);
    console.log(`  Statements.: ${formatPercentage(metrics.statements.pct, GLOBAL_THRESHOLDS.statements, TARGET_THRESHOLDS.statements)} (${metrics.statements.covered}/${metrics.statements.total})`);
    console.log(`  Functions..: ${formatPercentage(metrics.functions.pct, GLOBAL_THRESHOLDS.functions, TARGET_THRESHOLDS.functions)} (${metrics.functions.covered}/${metrics.functions.total})`);
    console.log(`  Branches...: ${formatPercentage(metrics.branches.pct, GLOBAL_THRESHOLDS.branches, TARGET_THRESHOLDS.branches)} (${metrics.branches.covered}/${metrics.branches.total})`);
  }

  // Global coverage
  console.log('\n' + '='.repeat(80));
  console.log('🌍 GLOBAL COVERAGE:');
  console.log('-'.repeat(80));
  
  const total = coverage.total;
  console.log(`  Lines......: ${formatPercentage(total.lines.pct, GLOBAL_THRESHOLDS.lines, TARGET_THRESHOLDS.lines)} (${total.lines.covered}/${total.lines.total})`);
  console.log(`  Statements.: ${formatPercentage(total.statements.pct, GLOBAL_THRESHOLDS.statements, TARGET_THRESHOLDS.statements)} (${total.statements.covered}/${total.statements.total})`);
  console.log(`  Functions..: ${formatPercentage(total.functions.pct, GLOBAL_THRESHOLDS.functions, TARGET_THRESHOLDS.functions)} (${total.functions.covered}/${total.functions.total})`);
  console.log(`  Branches...: ${formatPercentage(total.branches.pct, GLOBAL_THRESHOLDS.branches, TARGET_THRESHOLDS.branches)} (${total.branches.covered}/${total.branches.total})`);
  
  console.log('\n' + '='.repeat(80));
  
  // Thresholds
  console.log('\n📏 Thresholds:');
  console.log(`  Minimum: Lines ${GLOBAL_THRESHOLDS.lines}% | Statements ${GLOBAL_THRESHOLDS.statements}% | Functions ${GLOBAL_THRESHOLDS.functions}% | Branches ${GLOBAL_THRESHOLDS.branches}%`);
  console.log(`  Target:  Lines ${TARGET_THRESHOLDS.lines}% | Statements ${TARGET_THRESHOLDS.statements}% | Functions ${TARGET_THRESHOLDS.functions}% | Branches ${TARGET_THRESHOLDS.branches}%`);
}

/**
 * Check if coverage meets thresholds
 */
function checkThresholds(coverage) {
  const total = coverage.total;
  const failures = [];
  
  for (const [metric, threshold] of Object.entries(GLOBAL_THRESHOLDS)) {
    if (total[metric].pct < threshold) {
      failures.push(`${metric}: ${total[metric].pct.toFixed(2)}% < ${threshold}%`);
    }
  }
  
  if (failures.length > 0) {
    console.log('\n❌ Coverage below minimum thresholds:');
    failures.forEach(f => console.log(`   - ${f}`));
    return false;
  }
  
  // Check target thresholds
  const belowTarget = [];
  for (const [metric, target] of Object.entries(TARGET_THRESHOLDS)) {
    if (total[metric].pct < target) {
      belowTarget.push(`${metric}: ${total[metric].pct.toFixed(2)}% < ${target}%`);
    }
  }
  
  if (belowTarget.length > 0) {
    console.log('\n⚠️  Coverage below target (>90%):');
    belowTarget.forEach(t => console.log(`   - ${t}`));
    console.log('\n💡 Tip: Add more tests to reach the target coverage of >90%');
  } else {
    console.log('\n✅ Excellent! Coverage exceeds 90% target!');
  }
  
  return true;
}

/**
 * Generate HTML report
 */
function generateHtmlReport(coverage) {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>SpheroSeg Coverage Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f2f2f2; }
    .good { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .bad { color: red; font-weight: bold; }
    .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
  </style>
</head>
<body>
  <h1>📊 SpheroSeg Coverage Report</h1>
  
  <div class="summary">
    <h2>Overall Coverage</h2>
    <ul>
      <li>Lines: ${coverage.total.lines.pct.toFixed(2)}%</li>
      <li>Statements: ${coverage.total.statements.pct.toFixed(2)}%</li>
      <li>Functions: ${coverage.total.functions.pct.toFixed(2)}%</li>
      <li>Branches: ${coverage.total.branches.pct.toFixed(2)}%</li>
    </ul>
  </div>
  
  <h2>Package Coverage</h2>
  <table>
    <tr>
      <th>Package</th>
      <th>Lines</th>
      <th>Statements</th>
      <th>Functions</th>
      <th>Branches</th>
    </tr>
    ${Object.entries(coverage.packages).map(([name, metrics]) => `
    <tr>
      <td>${name}</td>
      <td class="${metrics.lines.pct >= 90 ? 'good' : metrics.lines.pct >= 75 ? 'warning' : 'bad'}">${metrics.lines.pct.toFixed(2)}%</td>
      <td class="${metrics.statements.pct >= 90 ? 'good' : metrics.statements.pct >= 75 ? 'warning' : 'bad'}">${metrics.statements.pct.toFixed(2)}%</td>
      <td class="${metrics.functions.pct >= 90 ? 'good' : metrics.functions.pct >= 75 ? 'warning' : 'bad'}">${metrics.functions.pct.toFixed(2)}%</td>
      <td class="${metrics.branches.pct >= 90 ? 'good' : metrics.branches.pct >= 75 ? 'warning' : 'bad'}">${metrics.branches.pct.toFixed(2)}%</td>
    </tr>
    `).join('')}
  </table>
  
  <p>Generated: ${new Date().toISOString()}</p>
</body>
</html>
  `;
  
  const reportPath = path.join(rootDir, 'coverage-report.html');
  fs.writeFileSync(reportPath, html);
  console.log(`\n📄 HTML report generated: ${reportPath}`);
}

/**
 * Main function
 */
function main() {
  console.log('🔍 Checking coverage reports...\n');
  
  const coverage = aggregateCoverage();
  
  if (coverage.total.lines.total === 0) {
    console.error('❌ No coverage data found. Run tests with coverage first:');
    console.log('   npm run test:coverage');
    process.exit(1);
  }
  
  printReport(coverage);
  generateHtmlReport(coverage);
  
  const passed = checkThresholds(coverage);
  
  if (!passed) {
    console.log('\n💡 Tips to improve coverage:');
    console.log('   1. Write unit tests for uncovered functions');
    console.log('   2. Add edge case tests for branches');
    console.log('   3. Test error handling paths');
    console.log('   4. Use "npm run test:coverage" to see detailed reports');
    process.exit(1);
  }
  
  console.log('\n✅ Coverage check passed!');
}

// Run the script
main();