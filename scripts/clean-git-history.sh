#!/bin/bash

# Git History Cleanup Script
# This script removes sensitive files and secrets from Git history
# 
# Usage: ./scripts/clean-git-history.sh [--force] [--no-backup]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backups/git-history"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
FORCE_MODE=false
NO_BACKUP=false

# Parse arguments
for arg in "$@"; do
  case $arg in
    --force)
      FORCE_MODE=true
      shift
      ;;
    --no-backup)
      NO_BACKUP=true
      shift
      ;;
    *)
      ;;
  esac
done

# Functions
print_header() {
  echo -e "${GREEN}════════════════════════════════════════════════════════${NC}"
  echo -e "${GREEN}     Git History Cleanup - Security Enhancement${NC}"
  echo -e "${GREEN}════════════════════════════════════════════════════════${NC}"
  echo
}

print_warning() {
  echo -e "${YELLOW}⚠️  WARNING: This script will permanently modify Git history!${NC}"
  echo -e "${YELLOW}   - All sensitive files will be removed from all commits${NC}"
  echo -e "${YELLOW}   - All collaborators must re-clone the repository${NC}"
  echo -e "${YELLOW}   - This operation cannot be undone${NC}"
  echo
}

check_requirements() {
  echo "Checking requirements..."
  
  # Check if we're in a git repository
  if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ Not in a Git repository${NC}"
    exit 1
  fi
  
  # Check for BFG Repo-Cleaner
  if ! command -v bfg &> /dev/null && ! [ -f "$SCRIPT_DIR/bfg.jar" ]; then
    echo -e "${YELLOW}BFG Repo-Cleaner not found. Downloading...${NC}"
    wget -O "$SCRIPT_DIR/bfg.jar" https://repo1.maven.org/maven2/com/madgag/bfg/1.14.0/bfg-1.14.0.jar
    echo -e "${GREEN}✅ BFG downloaded${NC}"
  fi
  
  # Check for Java (required for BFG)
  if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ Java is required for BFG. Please install Java.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}✅ All requirements met${NC}"
}

create_backup() {
  if [ "$NO_BACKUP" = true ]; then
    echo -e "${YELLOW}Skipping backup (--no-backup flag)${NC}"
    return
  fi
  
  echo "Creating backup..."
  mkdir -p "$BACKUP_DIR"
  
  # Create a full backup of the repository
  BACKUP_FILE="$BACKUP_DIR/repo_backup_$TIMESTAMP.tar.gz"
  tar -czf "$BACKUP_FILE" -C "$PROJECT_ROOT" --exclude="node_modules" --exclude="backups" .
  
  echo -e "${GREEN}✅ Backup created: $BACKUP_FILE${NC}"
}

identify_sensitive_files() {
  echo
  echo "Identifying sensitive files in history..."
  
  # Create list of files to remove
  cat > "$SCRIPT_DIR/sensitive-files.txt" << EOF
# Environment files
.env
.env.*
*.env
.env.local
.env.production
.env.development
.env.staging
.env.test

# Key files
*.key
*.pem
*.p12
*.pfx
private.key
public.key
id_rsa
id_rsa.pub

# Certificate files
*.crt
*.cer
*.cert
*.ca-bundle

# Credential files
credentials
credentials.json
service-account.json
keyfile.json
auth.json

# AWS/Cloud credentials
.aws/credentials
.aws/config
aws-credentials.json

# Database dumps with potential passwords
*.sql
*.dump
*.sqlite
*.sqlite3
*.db

# Config files that might contain secrets
config/secrets.yml
config/database.yml
settings.json
appsettings.json

# Docker files with potential secrets
docker-compose.yml
docker-compose.*.yml

# Package directories
packages/backend/keys/
certbot/conf/
EOF

  echo -e "${GREEN}✅ Sensitive file patterns identified${NC}"
}

scan_for_secrets() {
  echo
  echo "Scanning for embedded secrets in code..."
  
  # Create patterns file for secret detection
  cat > "$SCRIPT_DIR/secret-patterns.txt" << 'EOF'
# JWT patterns
JWT_SECRET=.+
jwt_secret\s*=\s*["'].+["']

# API Keys
[Aa][Pp][Ii][-_]?[Kk][Ee][Yy]\s*=\s*["'].+["']
[Aa][Pp][Ii][-_]?[Ss][Ee][Cc][Rr][Ee][Tt]\s*=\s*["'].+["']

# Database passwords
[Pp][Aa][Ss][Ss][Ww][Oo][Rr][Dd]\s*=\s*["'].+["']
[Dd][Bb][-_]?[Pp][Aa][Ss][Ss]\s*=\s*["'].+["']
DATABASE_URL=.+@.+

# AWS credentials
[Aa][Ww][Ss][-_]?[Aa][Cc][Cc][Ee][Ss][Ss][-_]?[Kk][Ee][Yy][-_]?[Ii][Dd]\s*=\s*.+
[Aa][Ww][Ss][-_]?[Ss][Ee][Cc][Rr][Ee][Tt][-_]?[Aa][Cc][Cc][Ee][Ss][Ss][-_]?[Kk][Ee][Yy]\s*=\s*.+

# Generic secrets
[Ss][Ee][Cc][Rr][Ee][Tt]\s*=\s*["'].+["']
[Tt][Oo][Kk][Ee][Nn]\s*=\s*["'].+["']
[Kk][Ee][Yy]\s*=\s*["'].+["']

# Base64 encoded secrets (min 20 chars)
[A-Za-z0-9+/]{20,}={0,2}

# Hex encoded secrets (min 32 chars)
[0-9a-fA-F]{32,}
EOF

  # Search for potential secrets in history
  echo "Searching for hardcoded secrets..."
  git log --all --full-history -p | grep -E -f "$SCRIPT_DIR/secret-patterns.txt" > "$SCRIPT_DIR/found-secrets.txt" 2>/dev/null || true
  
  if [ -s "$SCRIPT_DIR/found-secrets.txt" ]; then
    echo -e "${YELLOW}⚠️  Found potential secrets in history (see found-secrets.txt)${NC}"
  else
    echo -e "${GREEN}✅ No obvious secrets found in commit messages${NC}"
  fi
}

clean_with_bfg() {
  echo
  echo "Cleaning Git history with BFG..."
  
  # Determine BFG location
  if [ -f "$SCRIPT_DIR/bfg.jar" ]; then
    BFG_CMD="java -jar $SCRIPT_DIR/bfg.jar"
  else
    BFG_CMD="bfg"
  fi
  
  # Remove files by pattern
  echo "Removing sensitive files..."
  while IFS= read -r pattern; do
    # Skip comments and empty lines
    [[ "$pattern" =~ ^#.*$ || -z "$pattern" ]] && continue
    
    echo "  Removing: $pattern"
    $BFG_CMD --delete-files "$pattern" --no-blob-protection . 2>/dev/null || true
  done < "$SCRIPT_DIR/sensitive-files.txt"
  
  # Remove directories
  echo "Removing sensitive directories..."
  $BFG_CMD --delete-folders "{keys,certbot}" --no-blob-protection . 2>/dev/null || true
  
  # Replace sensitive text patterns
  echo "Replacing sensitive text patterns..."
  cat > "$SCRIPT_DIR/replacements.txt" << 'EOF'
JWT_SECRET=.*==>JWT_SECRET=REMOVED_FROM_HISTORY
jwt_secret=.*==>jwt_secret=REMOVED_FROM_HISTORY
SESSION_SECRET=.*==>SESSION_SECRET=REMOVED_FROM_HISTORY
REFRESH_TOKEN_SECRET=.*==>REFRESH_TOKEN_SECRET=REMOVED_FROM_HISTORY
ENCRYPTION_KEY=.*==>ENCRYPTION_KEY=REMOVED_FROM_HISTORY
password=.*==>password=REMOVED_FROM_HISTORY
[Aa]pi[_-]?[Kk]ey=.*==>[REDACTED_API_KEY]
[Aa]ws[_-]?[Ss]ecret=.*==>[REDACTED_AWS_SECRET]
postgresql://.*@==>[REDACTED_DATABASE_URL]
EOF
  
  $BFG_CMD --replace-text "$SCRIPT_DIR/replacements.txt" --no-blob-protection . 2>/dev/null || true
  
  echo -e "${GREEN}✅ BFG cleaning completed${NC}"
}

clean_with_filter_branch() {
  echo
  echo "Alternative: Using git filter-branch (slower but more control)..."
  
  # This is an alternative if BFG is not available
  # Remove specific files
  git filter-branch --force --index-filter \
    'git rm -rf --cached --ignore-unmatch .env* *.key *.pem packages/backend/keys/ certbot/conf/' \
    --prune-empty --tag-name-filter cat -- --all
  
  echo -e "${GREEN}✅ git filter-branch completed${NC}"
}

finalize_cleanup() {
  echo
  echo "Finalizing cleanup..."
  
  # Clean up refs and reflogs
  rm -rf .git/refs/original/
  git reflog expire --expire=now --all
  git gc --prune=now --aggressive
  
  # Get repository size before and after
  SIZE_AFTER=$(du -sh .git | cut -f1)
  
  echo -e "${GREEN}✅ Repository cleaned and optimized${NC}"
  echo -e "${GREEN}   New size: $SIZE_AFTER${NC}"
}

create_gitignore() {
  echo
  echo "Updating .gitignore..."
  
  # Ensure critical patterns are in .gitignore
  cat >> "$PROJECT_ROOT/.gitignore" << 'EOF'

# Security - Never commit these
.env
.env.*
*.env
!.env.example
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.cert
credentials
credentials.json
service-account.json
keyfile.json
auth.json
.aws/
packages/backend/keys/
certbot/conf/
*.sql
*.dump
*.sqlite
*.sqlite3
*.db
backups/

# Temporary cleanup files
sensitive-files.txt
secret-patterns.txt
found-secrets.txt
replacements.txt
bfg.jar
EOF
  
  # Remove duplicates from .gitignore
  awk '!seen[$0]++' "$PROJECT_ROOT/.gitignore" > "$PROJECT_ROOT/.gitignore.tmp"
  mv "$PROJECT_ROOT/.gitignore.tmp" "$PROJECT_ROOT/.gitignore"
  
  echo -e "${GREEN}✅ .gitignore updated${NC}"
}

create_report() {
  echo
  echo "Creating cleanup report..."
  
  REPORT_FILE="$BACKUP_DIR/cleanup_report_$TIMESTAMP.txt"
  
  cat > "$REPORT_FILE" << EOF
Git History Cleanup Report
Generated: $(date)
Repository: $PROJECT_ROOT

Actions Performed:
- Removed all .env files from history
- Removed all key files (*.key, *.pem) from history
- Removed packages/backend/keys/ directory from history
- Removed certbot/conf/ directory from history
- Replaced sensitive text patterns with [REDACTED]
- Cleaned refs and reflogs
- Performed aggressive garbage collection

Repository Size: $SIZE_AFTER

Next Steps:
1. Review the changes locally
2. Force push to remote: git push --force --all
3. Force push tags: git push --force --tags
4. Notify all team members to re-clone the repository
5. Rotate all secrets that may have been exposed
6. Set up secret scanning in CI/CD pipeline

Security Recommendations:
- Use environment variables for all secrets
- Never commit .env files
- Use secret management tools (HashiCorp Vault, AWS Secrets Manager)
- Enable GitHub secret scanning
- Set up pre-commit hooks to prevent secret commits
EOF
  
  echo -e "${GREEN}✅ Report created: $REPORT_FILE${NC}"
}

main() {
  print_header
  print_warning
  
  if [ "$FORCE_MODE" != true ]; then
    read -p "Do you want to continue? (yes/no): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo -e "${RED}Operation cancelled${NC}"
      exit 1
    fi
  fi
  
  cd "$PROJECT_ROOT"
  
  check_requirements
  create_backup
  identify_sensitive_files
  scan_for_secrets
  
  # Use BFG if available, otherwise fall back to filter-branch
  if [ -f "$SCRIPT_DIR/bfg.jar" ] || command -v bfg &> /dev/null; then
    clean_with_bfg
  else
    clean_with_filter_branch
  fi
  
  finalize_cleanup
  create_gitignore
  create_report
  
  echo
  echo -e "${GREEN}════════════════════════════════════════════════════════${NC}"
  echo -e "${GREEN}✅ Git history cleanup completed successfully!${NC}"
  echo -e "${GREEN}════════════════════════════════════════════════════════${NC}"
  echo
  echo -e "${YELLOW}⚠️  IMPORTANT NEXT STEPS:${NC}"
  echo "1. Review the changes: git log --oneline"
  echo "2. Force push to remote: git push --force --all"
  echo "3. Force push tags: git push --force --tags"
  echo "4. Notify all team members to re-clone"
  echo "5. Rotate all potentially exposed secrets"
  echo
  echo -e "${YELLOW}See the full report at: $REPORT_FILE${NC}"
}

# Run the script
main