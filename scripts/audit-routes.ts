#!/usr/bin/env ts-node

/**
 * Route Audit Script
 * Analyzes frontend API calls and backend routes to find mismatches
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface RouteCall {
  method: string;
  path: string;
  file: string;
  line: number;
}

interface RouteDefinition {
  method: string;
  path: string;
  file: string;
  line: number;
}

async function findFrontendApiCalls(): Promise<RouteCall[]> {
  const calls: RouteCall[] = [];
  const frontendDir = path.join(__dirname, '../packages/frontend/src');
  
  // Patterns to match API calls
  const patterns = [
    /apiClient\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)/g,
    /api\.(auth|users|projects|images|segmentation|metadata|notifications|tasks|stats)\.\w+\s*\(/g,
    /fetch\s*\(\s*['"`](\/api[^'"`]+)/g,
  ];
  
  const files = await glob('**/*.{ts,tsx,js,jsx}', { cwd: frontendDir });
  
  for (const file of files) {
    const content = fs.readFileSync(path.join(frontendDir, file), 'utf-8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          if (match[1] && match[2]) {
            // apiClient.method() pattern
            calls.push({
              method: match[1].toUpperCase(),
              path: match[2].startsWith('/') ? match[2] : `/api/${match[2]}`,
              file,
              line: index + 1,
            });
          } else if (match[1] && pattern.source.includes('fetch')) {
            // fetch() pattern
            calls.push({
              method: 'GET', // Default to GET for fetch
              path: match[1],
              file,
              line: index + 1,
            });
          }
        }
      });
    });
  }
  
  // Also check the endpoints.ts file for defined endpoints
  const endpointsFile = path.join(frontendDir, 'services/api/endpoints.ts');
  if (fs.existsSync(endpointsFile)) {
    const endpointsContent = fs.readFileSync(endpointsFile, 'utf-8');
    const endpointPattern = /(\w+):\s*\([^)]*\)\s*=>\s*apiClient\.(get|post|put|delete|patch)\s*(?:<[^>]+>)?\s*\(\s*['"`]([^'"`]+)/g;
    
    let match;
    const lines = endpointsContent.split('\n');
    let lineNum = 0;
    
    for (const line of lines) {
      lineNum++;
      while ((match = endpointPattern.exec(line)) !== null) {
        calls.push({
          method: match[2].toUpperCase(),
          path: match[3].startsWith('/') ? `/api${match[3]}` : `/api/${match[3]}`,
          file: 'services/api/endpoints.ts',
          line: lineNum,
        });
      }
    }
  }
  
  return calls;
}

async function findBackendRoutes(): Promise<RouteDefinition[]> {
  const routes: RouteDefinition[] = [];
  const backendDir = path.join(__dirname, '../packages/backend/src');
  
  // Patterns to match route definitions
  const patterns = [
    /router\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)/g,
    /app\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)/g,
  ];
  
  const files = await glob('**/*.{ts,js}', { cwd: backendDir });
  
  for (const file of files) {
    const content = fs.readFileSync(path.join(backendDir, file), 'utf-8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          let path = match[2];
          
          // Determine base path from file location
          if (file.includes('routes/auth')) {
            path = `/api/auth${path}`;
          } else if (file.includes('routes/users')) {
            path = `/api/users${path}`;
          } else if (file.includes('routes/projects')) {
            path = `/api/projects${path}`;
          } else if (file.includes('routes/images')) {
            path = `/api/images${path}`;
          } else if (file.includes('routes/segmentation')) {
            path = `/api/segmentation${path}`;
          } else if (!path.startsWith('/api') && !path.startsWith('/health')) {
            path = `/api${path}`;
          }
          
          routes.push({
            method: match[1].toUpperCase(),
            path,
            file,
            line: index + 1,
          });
        }
      });
    });
  }
  
  return routes;
}

function normalizeRoutes(routes: (RouteCall | RouteDefinition)[]): (RouteCall | RouteDefinition)[] {
  return routes.map(route => ({
    ...route,
    path: route.path
      .replace(/\/+/g, '/') // Remove duplicate slashes
      .replace(/\/$/, '') // Remove trailing slash
      || '/',
  }));
}

function matchRoutes(calls: RouteCall[], definitions: RouteDefinition[]): {
  matched: Array<{ call: RouteCall; definition: RouteDefinition }>;
  unmatchedCalls: RouteCall[];
  unusedDefinitions: RouteDefinition[];
} {
  const matched: Array<{ call: RouteCall; definition: RouteDefinition }> = [];
  const unmatchedCalls: RouteCall[] = [];
  const usedDefinitions = new Set<number>();
  
  calls.forEach(call => {
    const matchIndex = definitions.findIndex((def, index) => {
      if (usedDefinitions.has(index)) return false;
      
      if (def.method !== call.method) return false;
      
      // Exact match
      if (def.path === call.path) return true;
      
      // Parameterized match
      const defSegments = def.path.split('/');
      const callSegments = call.path.split('/');
      
      if (defSegments.length !== callSegments.length) return false;
      
      for (let i = 0; i < defSegments.length; i++) {
        if (defSegments[i].startsWith(':')) continue;
        if (defSegments[i] !== callSegments[i]) return false;
      }
      
      return true;
    });
    
    if (matchIndex >= 0) {
      matched.push({ call, definition: definitions[matchIndex] });
      usedDefinitions.add(matchIndex);
    } else {
      unmatchedCalls.push(call);
    }
  });
  
  const unusedDefinitions = definitions.filter((_, index) => !usedDefinitions.has(index));
  
  return { matched, unmatchedCalls, unusedDefinitions };
}

async function main() {
  console.log('🔍 SpheroSeg Route Audit\n');
  
  console.log('Scanning frontend API calls...');
  const frontendCalls = normalizeRoutes(await findFrontendApiCalls()) as RouteCall[];
  
  console.log('Scanning backend route definitions...');
  const backendRoutes = normalizeRoutes(await findBackendRoutes()) as RouteDefinition[];
  
  console.log('\n📊 Summary:');
  console.log(`  Frontend API calls: ${frontendCalls.length}`);
  console.log(`  Backend routes: ${backendRoutes.length}`);
  
  const { matched, unmatchedCalls, unusedDefinitions } = matchRoutes(frontendCalls, backendRoutes);
  
  console.log(`  Matched routes: ${matched.length}`);
  console.log(`  Unmatched frontend calls: ${unmatchedCalls.length}`);
  console.log(`  Unused backend routes: ${unusedDefinitions.length}`);
  
  if (unmatchedCalls.length > 0) {
    console.log('\n❌ Frontend API calls without backend routes:');
    const grouped = unmatchedCalls.reduce((acc, call) => {
      const key = `${call.method} ${call.path}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(call);
      return acc;
    }, {} as Record<string, RouteCall[]>);
    
    Object.entries(grouped).forEach(([route, calls]) => {
      console.log(`\n  ${route}`);
      calls.forEach(call => {
        console.log(`    📄 ${call.file}:${call.line}`);
      });
    });
  }
  
  if (unusedDefinitions.length > 0) {
    console.log('\n⚠️  Backend routes not used by frontend:');
    const grouped = unusedDefinitions.reduce((acc, def) => {
      const key = `${def.method} ${def.path}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(def);
      return acc;
    }, {} as Record<string, RouteDefinition[]>);
    
    Object.entries(grouped).forEach(([route, defs]) => {
      console.log(`\n  ${route}`);
      defs.forEach(def => {
        console.log(`    📄 ${def.file}:${def.line}`);
      });
    });
  }
  
  // Generate detailed report
  const reportPath = path.join(__dirname, '../route-audit-report.md');
  let report = '# SpheroSeg Route Audit Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  
  report += '## Summary\n\n';
  report += `- Frontend API calls: ${frontendCalls.length}\n`;
  report += `- Backend routes: ${backendRoutes.length}\n`;
  report += `- Matched routes: ${matched.length}\n`;
  report += `- Unmatched frontend calls: ${unmatchedCalls.length}\n`;
  report += `- Unused backend routes: ${unusedDefinitions.length}\n\n`;
  
  if (unmatchedCalls.length > 0) {
    report += '## Frontend API calls without backend routes\n\n';
    const grouped = unmatchedCalls.reduce((acc, call) => {
      const key = `${call.method} ${call.path}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(call);
      return acc;
    }, {} as Record<string, RouteCall[]>);
    
    Object.entries(grouped).forEach(([route, calls]) => {
      report += `### ${route}\n\n`;
      calls.forEach(call => {
        report += `- ${call.file}:${call.line}\n`;
      });
      report += '\n';
    });
  }
  
  if (unusedDefinitions.length > 0) {
    report += '## Backend routes not used by frontend\n\n';
    const grouped = unusedDefinitions.reduce((acc, def) => {
      const key = `${def.method} ${def.path}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(def);
      return acc;
    }, {} as Record<string, RouteDefinition[]>);
    
    Object.entries(grouped).forEach(([route, defs]) => {
      report += `### ${route}\n\n`;
      defs.forEach(def => {
        report += `- ${def.file}:${def.line}\n`;
      });
      report += '\n';
    });
  }
  
  report += '## All matched routes\n\n';
  const matchedGrouped = matched.reduce((acc, { call, definition }) => {
    const key = `${call.method} ${call.path}`;
    if (!acc[key]) acc[key] = { calls: [], definitions: [] };
    acc[key].calls.push(call);
    acc[key].definitions.push(definition);
    return acc;
  }, {} as Record<string, { calls: RouteCall[]; definitions: RouteDefinition[] }>);
  
  Object.entries(matchedGrouped).sort().forEach(([route, { calls, definitions }]) => {
    report += `### ${route}\n\n`;
    report += 'Frontend calls:\n';
    calls.forEach(call => {
      report += `- ${call.file}:${call.line}\n`;
    });
    report += '\nBackend definitions:\n';
    definitions.forEach(def => {
      report += `- ${def.file}:${def.line}\n`;
    });
    report += '\n';
  });
  
  fs.writeFileSync(reportPath, report);
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  
  // Exit with error code if there are unmatched calls
  if (unmatchedCalls.length > 0) {
    process.exit(1);
  }
}

main().catch(console.error);