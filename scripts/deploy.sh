#!/bin/bash

# Deploy script for SpheroSeg application
# Usage: ./scripts/deploy.sh [staging|production]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-staging}

echo -e "${GREEN}🚀 Deploying SpheroSeg to ${ENVIRONMENT}${NC}"

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
    echo "Usage: $0 [staging|production]"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed${NC}"
    exit 1
fi

# Load environment variables
if [ "$ENVIRONMENT" == "production" ]; then
    ENV_FILE=".env.production"
    COMPOSE_FILE="docker-compose.prod.yml"
else
    ENV_FILE=".env.staging"
    COMPOSE_FILE="docker-compose.yml"
fi

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}⚠️  Environment file $ENV_FILE not found, using defaults${NC}"
    ENV_FILE=".env"
fi

# Pull latest changes
echo -e "${GREEN}📥 Pulling latest changes from Git${NC}"
git pull origin $(git rev-parse --abbrev-ref HEAD)

# Build and update containers
echo -e "${GREEN}🏗️  Building Docker images${NC}"
docker-compose -f $COMPOSE_FILE build

# Run database migrations
echo -e "${GREEN}🗄️  Running database migrations${NC}"
docker-compose -f $COMPOSE_FILE run --rm backend npx prisma migrate deploy

# Stop old containers
echo -e "${GREEN}🛑 Stopping old containers${NC}"
docker-compose -f $COMPOSE_FILE down

# Start new containers
echo -e "${GREEN}🚀 Starting new containers${NC}"
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be healthy
echo -e "${GREEN}⏳ Waiting for services to be healthy${NC}"
sleep 10

# Check service health
echo -e "${GREEN}🏥 Checking service health${NC}"

# Check backend health
BACKEND_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5001/health || echo "000")
if [ "$BACKEND_HEALTH" == "200" ]; then
    echo -e "${GREEN}✅ Backend is healthy${NC}"
else
    echo -e "${RED}❌ Backend health check failed (HTTP $BACKEND_HEALTH)${NC}"
    docker-compose -f $COMPOSE_FILE logs backend | tail -20
fi

# Check frontend health
FRONTEND_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 || echo "000")
if [ "$FRONTEND_HEALTH" == "200" ]; then
    echo -e "${GREEN}✅ Frontend is healthy${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend health check returned HTTP $FRONTEND_HEALTH${NC}"
fi

# Clean up old images
echo -e "${GREEN}🧹 Cleaning up old Docker images${NC}"
docker image prune -f

# Show running containers
echo -e "${GREEN}📦 Running containers:${NC}"
docker-compose -f $COMPOSE_FILE ps

echo -e "${GREEN}✨ Deployment complete!${NC}"

# Show access URLs
if [ "$ENVIRONMENT" == "production" ]; then
    echo -e "${GREEN}🌐 Application is available at: https://your-domain.com${NC}"
else
    echo -e "${GREEN}🌐 Application is available at: http://localhost:3000${NC}"
fi

echo -e "${GREEN}📊 Monitoring:${NC}"
echo "  - Logs: docker-compose -f $COMPOSE_FILE logs -f"
echo "  - Backend logs: docker-compose -f $COMPOSE_FILE logs -f backend"
echo "  - Frontend logs: docker-compose -f $COMPOSE_FILE logs -f frontend"