#!/bin/bash

# Script to migrate all type imports from @spheroseg/types to @spheroseg/shared

echo "🔄 Migrating type imports from @spheroseg/types to @spheroseg/shared..."

# Count current imports
BEFORE_COUNT=$(grep -r "@spheroseg/types" packages/frontend packages/backend --include="*.ts" --include="*.tsx" 2>/dev/null | wc -l)
echo "📊 Found $BEFORE_COUNT imports to migrate"

# Replace all imports in frontend
echo "📦 Migrating frontend imports..."
find packages/frontend -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i "s/@spheroseg\/types/@spheroseg\/shared/g" {} \;

# Replace all imports in backend  
echo "📦 Migrating backend imports..."
find packages/backend -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i "s/@spheroseg\/types/@spheroseg\/shared/g" {} \;

# Count after migration
AFTER_COUNT=$(grep -r "@spheroseg/types" packages/frontend packages/backend --include="*.ts" --include="*.tsx" 2>/dev/null | wc -l)
echo "✅ Migration complete! Remaining @spheroseg/types imports: $AFTER_COUNT"

# Show what was changed
echo ""
echo "📝 Changed files:"
git diff --name-only | head -20

echo ""
echo "🎯 Next steps:"
echo "1. Review changes with: git diff"
echo "2. Test build with: npm run build"
echo "3. Remove packages/types directory"
echo "4. Update package.json dependencies"