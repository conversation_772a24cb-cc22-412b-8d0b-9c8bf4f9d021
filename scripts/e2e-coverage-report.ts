#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import { coverageConfig, coverageHelpers } from '../e2e/coverage.config';

interface TestResult {
  file: string;
  title: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
}

interface CoverageReport {
  timestamp: string;
  duration: number;
  tests: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
  coverage: {
    features: number;
    interactions: number;
    errors: number;
    integration: number;
    browser: number;
    overall: number;
  };
  categories: Record<string, {
    tests: number;
    passed: number;
    coverage: number;
    critical: boolean;
  }>;
  uncovered: {
    features: string[];
    interactions: string[];
    errors: string[];
  };
}

class E2ECoverageReporter {
  private results: any;
  private outputDir: string;

  constructor(resultsPath: string, outputDir: string) {
    this.results = JSON.parse(fs.readFileSync(resultsPath, 'utf-8'));
    this.outputDir = outputDir;
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
  }

  generateReport(): CoverageReport {
    const report: CoverageReport = {
      timestamp: new Date().toISOString(),
      duration: this.calculateTotalDuration(),
      tests: this.extractTests(),
      summary: this.calculateSummary(),
      coverage: this.calculateCoverage(),
      categories: this.calculateCategoryCoverage(),
      uncovered: this.findUncoveredAreas()
    };

    return report;
  }

  private calculateTotalDuration(): number {
    return this.results.suites.reduce((total: number, suite: any) => 
      total + suite.specs.reduce((suiteTotal: number, spec: any) => 
        suiteTotal + (spec.duration || 0), 0), 0);
  }

  private extractTests(): TestResult[] {
    const tests: TestResult[] = [];
    
    this.results.suites.forEach((suite: any) => {
      suite.specs.forEach((spec: any) => {
        tests.push({
          file: suite.file,
          title: `${suite.title} > ${spec.title}`,
          status: spec.ok ? 'passed' : 'failed',
          duration: spec.duration || 0,
          error: spec.error
        });
      });
    });

    return tests;
  }

  private calculateSummary() {
    const tests = this.extractTests();
    return {
      total: tests.length,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      skipped: tests.filter(t => t.status === 'skipped').length
    };
  }

  private calculateCoverage() {
    const tests = this.extractTests();
    
    // Feature coverage
    const coveredFeatures = new Set<string>();
    Object.entries(coverageConfig.featureMap).forEach(([feature, config]) => {
      const hasTests = config.tests.some(pattern => 
        tests.some(t => t.file.includes(pattern.replace('*.spec.ts', '')))
      );
      if (hasTests) coveredFeatures.add(feature);
    });
    const featureCoverage = (coveredFeatures.size / Object.keys(coverageConfig.featureMap).length) * 100;

    // Interaction coverage
    const interactionTests = tests.filter(t => 
      Object.keys(coverageConfig.interactions).some(interaction => 
        t.title.toLowerCase().includes(interaction)
      )
    );
    const interactionCoverage = (interactionTests.length / tests.length) * 100;

    // Error coverage
    const errorTests = tests.filter(t => 
      t.title.toLowerCase().includes('error') ||
      t.title.toLowerCase().includes('fail') ||
      t.title.toLowerCase().includes('invalid')
    );
    const errorCoverage = errorTests.length > 0 ? 
      (errorTests.filter(t => t.status === 'passed').length / errorTests.length) * 100 : 0;

    // Integration coverage
    const integrationTests = tests.filter(t => t.file.includes('integration'));
    const integrationCoverage = integrationTests.length > 0 ?
      (integrationTests.filter(t => t.status === 'passed').length / integrationTests.length) * 100 : 0;

    // Browser coverage (simplified - would need actual browser data)
    const browserCoverage = 80; // Placeholder

    // Overall coverage
    const overall = coverageHelpers.calculateCoverage({ tests });

    return {
      features: Math.round(featureCoverage),
      interactions: Math.round(interactionCoverage),
      errors: Math.round(errorCoverage),
      integration: Math.round(integrationCoverage),
      browser: Math.round(browserCoverage),
      overall: Math.round(overall)
    };
  }

  private calculateCategoryCoverage() {
    const tests = this.extractTests();
    const categories: Record<string, any> = {};

    Object.entries(coverageConfig.categories).forEach(([name, config]) => {
      const categoryTests = tests.filter(t => 
        config.tests.some(pattern => {
          const cleanPattern = pattern.replace('*.spec.ts', '').replace('*', '');
          return t.file.includes(cleanPattern);
        })
      );

      const passed = categoryTests.filter(t => t.status === 'passed').length;
      const total = categoryTests.length;

      categories[name] = {
        tests: total,
        passed: passed,
        coverage: total > 0 ? Math.round((passed / total) * 100) : 0,
        critical: config.critical
      };
    });

    return categories;
  }

  private findUncoveredAreas() {
    const tests = this.extractTests();
    
    // Find uncovered features
    const uncoveredFeatures: string[] = [];
    Object.entries(coverageConfig.featureMap).forEach(([feature, config]) => {
      const hasTests = config.tests.some(pattern => 
        tests.some(t => t.file.includes(pattern.replace('*.spec.ts', '')))
      );
      if (!hasTests) uncoveredFeatures.push(feature);
    });

    // Find missing interaction types
    const testedInteractions = new Set<string>();
    tests.forEach(t => {
      Object.keys(coverageConfig.interactions).forEach(interaction => {
        if (t.title.toLowerCase().includes(interaction)) {
          testedInteractions.add(interaction);
        }
      });
    });
    const uncoveredInteractions = Object.keys(coverageConfig.interactions)
      .filter(i => !testedInteractions.has(i));

    // Find missing error scenarios
    const commonErrors = [
      'network error', 'timeout', 'permission denied', 
      'validation error', 'server error', '404 not found',
      'rate limit', 'quota exceeded', 'session expired'
    ];
    const testedErrors = new Set<string>();
    tests.forEach(t => {
      commonErrors.forEach(error => {
        if (t.title.toLowerCase().includes(error)) {
          testedErrors.add(error);
        }
      });
    });
    const uncoveredErrors = commonErrors.filter(e => !testedErrors.has(e));

    return {
      features: uncoveredFeatures,
      interactions: uncoveredInteractions,
      errors: uncoveredErrors
    };
  }

  generateHTMLReport(report: CoverageReport) {
    const html = `
<!DOCTYPE html>
<html>
<head>
  <title>E2E Test Coverage Report</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 { color: #333; margin-bottom: 30px; }
    h2 { color: #555; margin-top: 30px; }
    .summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .metric {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      text-align: center;
    }
    .metric h3 {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
      text-transform: uppercase;
    }
    .metric .value {
      font-size: 36px;
      font-weight: bold;
      margin: 10px 0;
    }
    .metric.good .value { color: #28a745; }
    .metric.warning .value { color: #ffc107; }
    .metric.danger .value { color: #dc3545; }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background: #f8f9fa;
      font-weight: 600;
    }
    .coverage-bar {
      width: 100%;
      height: 20px;
      background: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
    }
    .coverage-fill {
      height: 100%;
      background: #28a745;
      transition: width 0.3s;
    }
    .coverage-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 12px;
      font-weight: bold;
    }
    .critical { background: #fff3cd; }
    .uncovered {
      background: #f8d7da;
      padding: 15px;
      border-radius: 8px;
      margin: 10px 0;
    }
    .uncovered h3 {
      margin: 0 0 10px 0;
      color: #721c24;
    }
    .uncovered ul {
      margin: 5px 0;
      padding-left: 20px;
    }
    .timestamp {
      color: #6c757d;
      font-size: 14px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>E2E Test Coverage Report</h1>
    <div class="timestamp">Generated: ${new Date(report.timestamp).toLocaleString()}</div>
    
    <div class="summary">
      <div class="metric ${report.coverage.overall >= 85 ? 'good' : report.coverage.overall >= 70 ? 'warning' : 'danger'}">
        <h3>Overall Coverage</h3>
        <div class="value">${report.coverage.overall}%</div>
      </div>
      <div class="metric">
        <h3>Total Tests</h3>
        <div class="value">${report.summary.total}</div>
      </div>
      <div class="metric ${report.summary.passed / report.summary.total >= 0.95 ? 'good' : 'warning'}">
        <h3>Tests Passed</h3>
        <div class="value">${report.summary.passed}</div>
      </div>
      <div class="metric">
        <h3>Duration</h3>
        <div class="value">${(report.duration / 1000 / 60).toFixed(1)}m</div>
      </div>
    </div>

    <h2>Coverage by Type</h2>
    <table>
      <tr>
        <th>Type</th>
        <th>Coverage</th>
        <th>Status</th>
      </tr>
      <tr>
        <td>Features</td>
        <td>
          <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.features}%"></div>
            <div class="coverage-text">${report.coverage.features}%</div>
          </div>
        </td>
        <td>${report.coverage.features >= 85 ? '✅' : '⚠️'}</td>
      </tr>
      <tr>
        <td>User Interactions</td>
        <td>
          <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.interactions}%"></div>
            <div class="coverage-text">${report.coverage.interactions}%</div>
          </div>
        </td>
        <td>${report.coverage.interactions >= 80 ? '✅' : '⚠️'}</td>
      </tr>
      <tr>
        <td>Error Scenarios</td>
        <td>
          <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.errors}%"></div>
            <div class="coverage-text">${report.coverage.errors}%</div>
          </div>
        </td>
        <td>${report.coverage.errors >= 90 ? '✅' : '⚠️'}</td>
      </tr>
      <tr>
        <td>Integration</td>
        <td>
          <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.integration}%"></div>
            <div class="coverage-text">${report.coverage.integration}%</div>
          </div>
        </td>
        <td>${report.coverage.integration >= 85 ? '✅' : '⚠️'}</td>
      </tr>
      <tr>
        <td>Browser Support</td>
        <td>
          <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.browser}%"></div>
            <div class="coverage-text">${report.coverage.browser}%</div>
          </div>
        </td>
        <td>${report.coverage.browser >= 80 ? '✅' : '⚠️'}</td>
      </tr>
    </table>

    <h2>Coverage by Category</h2>
    <table>
      <tr>
        <th>Category</th>
        <th>Tests</th>
        <th>Passed</th>
        <th>Coverage</th>
        <th>Critical</th>
      </tr>
      ${Object.entries(report.categories).map(([name, data]) => `
        <tr class="${data.critical && data.coverage < 100 ? 'critical' : ''}">
          <td>${name}</td>
          <td>${data.tests}</td>
          <td>${data.passed}</td>
          <td>
            <div class="coverage-bar">
              <div class="coverage-fill" style="width: ${data.coverage}%; background: ${data.coverage >= 85 ? '#28a745' : data.coverage >= 70 ? '#ffc107' : '#dc3545'}"></div>
              <div class="coverage-text">${data.coverage}%</div>
            </div>
          </td>
          <td>${data.critical ? '⚠️ Critical' : '✅ Standard'}</td>
        </tr>
      `).join('')}
    </table>

    ${report.uncovered.features.length > 0 || report.uncovered.interactions.length > 0 || report.uncovered.errors.length > 0 ? `
    <h2>Uncovered Areas</h2>
    <div class="uncovered">
      ${report.uncovered.features.length > 0 ? `
        <h3>Missing Feature Tests</h3>
        <ul>
          ${report.uncovered.features.map(f => `<li>${f}</li>`).join('')}
        </ul>
      ` : ''}
      ${report.uncovered.interactions.length > 0 ? `
        <h3>Missing Interaction Tests</h3>
        <ul>
          ${report.uncovered.interactions.map(i => `<li>${i}</li>`).join('')}
        </ul>
      ` : ''}
      ${report.uncovered.errors.length > 0 ? `
        <h3>Missing Error Scenario Tests</h3>
        <ul>
          ${report.uncovered.errors.map(e => `<li>${e}</li>`).join('')}
        </ul>
      ` : ''}
    </div>
    ` : ''}
  </div>
</body>
</html>
    `;

    fs.writeFileSync(path.join(this.outputDir, 'index.html'), html);
  }

  generateMarkdownReport(report: CoverageReport) {
    const markdown = `# E2E Test Coverage Report

Generated: ${new Date(report.timestamp).toLocaleString()}

## Summary

- **Overall Coverage**: ${report.coverage.overall}% ${report.coverage.overall >= 85 ? '✅' : '⚠️'}
- **Total Tests**: ${report.summary.total}
- **Passed**: ${report.summary.passed} (${Math.round((report.summary.passed / report.summary.total) * 100)}%)
- **Failed**: ${report.summary.failed}
- **Duration**: ${(report.duration / 1000 / 60).toFixed(1)} minutes

## Coverage Breakdown

| Type | Coverage | Target | Status |
|------|----------|--------|--------|
| Features | ${report.coverage.features}% | 85% | ${report.coverage.features >= 85 ? '✅' : '❌'} |
| Interactions | ${report.coverage.interactions}% | 80% | ${report.coverage.interactions >= 80 ? '✅' : '❌'} |
| Error Scenarios | ${report.coverage.errors}% | 90% | ${report.coverage.errors >= 90 ? '✅' : '❌'} |
| Integration | ${report.coverage.integration}% | 85% | ${report.coverage.integration >= 85 ? '✅' : '❌'} |
| Browser Support | ${report.coverage.browser}% | 80% | ${report.coverage.browser >= 80 ? '✅' : '❌'} |

## Category Coverage

| Category | Tests | Passed | Coverage | Critical |
|----------|-------|--------|----------|----------|
${Object.entries(report.categories).map(([name, data]) => 
  `| ${name} | ${data.tests} | ${data.passed} | ${data.coverage}% | ${data.critical ? '⚠️ Yes' : 'No'} |`
).join('\n')}

${report.uncovered.features.length > 0 ? `
## Uncovered Features

${report.uncovered.features.map(f => `- ${f}`).join('\n')}
` : ''}

${report.uncovered.errors.length > 0 ? `
## Missing Error Scenarios

${report.uncovered.errors.map(e => `- ${e}`).join('\n')}
` : ''}

## Coverage Badges

![Overall Coverage](https://img.shields.io/badge/coverage-${report.coverage.overall}%25-${report.coverage.overall >= 85 ? 'brightgreen' : report.coverage.overall >= 70 ? 'yellow' : 'red'})
![Tests Passed](https://img.shields.io/badge/tests-${report.summary.passed}%2F${report.summary.total}-${report.summary.failed === 0 ? 'brightgreen' : 'red'})
    `;

    fs.writeFileSync(path.join(this.outputDir, 'COVERAGE.md'), markdown);
  }

  generateJSONReport(report: CoverageReport) {
    fs.writeFileSync(
      path.join(this.outputDir, 'coverage.json'),
      JSON.stringify(report, null, 2)
    );
  }

  checkThresholds(report: CoverageReport): boolean {
    let passed = true;
    const thresholds = coverageConfig.thresholds;

    // Check global thresholds
    if (report.coverage.features < thresholds.global.features) {
      console.error(`❌ Feature coverage ${report.coverage.features}% is below threshold ${thresholds.global.features}%`);
      passed = false;
    }

    if (report.coverage.interactions < thresholds.global.interactions) {
      console.error(`❌ Interaction coverage ${report.coverage.interactions}% is below threshold ${thresholds.global.interactions}%`);
      passed = false;
    }

    if (report.coverage.errors < thresholds.global.errors) {
      console.error(`❌ Error coverage ${report.coverage.errors}% is below threshold ${thresholds.global.errors}%`);
      passed = false;
    }

    // Check critical path coverage
    Object.entries(report.categories).forEach(([name, data]) => {
      if (data.critical && data.coverage < 100) {
        console.error(`❌ Critical category '${name}' has ${data.coverage}% coverage (requires 100%)`);
        passed = false;
      }
    });

    return passed;
  }

  run() {
    console.log('🔍 Generating E2E coverage report...\n');

    const report = this.generateReport();
    
    // Generate reports
    this.generateHTMLReport(report);
    this.generateMarkdownReport(report);
    this.generateJSONReport(report);

    // Display summary
    console.log('📊 Coverage Summary:');
    console.log(`   Overall: ${report.coverage.overall}%`);
    console.log(`   Features: ${report.coverage.features}%`);
    console.log(`   Interactions: ${report.coverage.interactions}%`);
    console.log(`   Errors: ${report.coverage.errors}%`);
    console.log(`   Integration: ${report.coverage.integration}%`);
    console.log('');

    // Check thresholds
    const passed = this.checkThresholds(report);
    
    console.log(`\n📁 Reports generated in: ${this.outputDir}`);
    console.log(`   - HTML: ${path.join(this.outputDir, 'index.html')}`);
    console.log(`   - Markdown: ${path.join(this.outputDir, 'COVERAGE.md')}`);
    console.log(`   - JSON: ${path.join(this.outputDir, 'coverage.json')}`);

    if (!passed) {
      console.error('\n❌ Coverage thresholds not met!');
      process.exit(1);
    } else {
      console.log('\n✅ All coverage thresholds passed!');
    }
  }
}

// Run if executed directly
if (require.main === module) {
  const resultsPath = process.argv[2] || 'test-results/results.json';
  const outputDir = process.argv[3] || 'coverage-report';

  if (!fs.existsSync(resultsPath)) {
    console.error(`❌ Results file not found: ${resultsPath}`);
    console.log('\nUsage: npm run e2e:coverage:report [results.json] [output-dir]');
    process.exit(1);
  }

  const reporter = new E2ECoverageReporter(resultsPath, outputDir);
  reporter.run();
}

export default E2ECoverageReporter;