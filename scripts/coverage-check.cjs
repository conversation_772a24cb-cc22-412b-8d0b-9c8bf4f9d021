#!/usr/bin/env node

/**
 * Coverage Check Script
 * 
 * Enforces test coverage thresholds and generates reports
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configuration
const COVERAGE_CONFIG = path.join(__dirname, '../jest.coverage.config.js');
const COVERAGE_DIR = path.join(__dirname, '../coverage');
const PACKAGES = ['backend', 'frontend', 'shared', 'types'];

// Coverage thresholds from config
const { coverageThreshold } = require(COVERAGE_CONFIG);

/**
 * Run coverage for a specific package
 */
function runPackageCoverage(packageName) {
  console.log(chalk.blue(`\n📦 Running coverage for ${packageName}...`));
  
  const packagePath = path.join(__dirname, `../packages/${packageName}`);
  
  try {
    // Run tests with coverage
    execSync(
      `npm run test:coverage`,
      {
        cwd: packagePath,
        stdio: 'inherit',
        env: {
          ...process.env,
          CI: 'true',
        },
      }
    );
    
    // Read coverage summary
    const summaryPath = path.join(packagePath, 'coverage/coverage-summary.json');
    if (fs.existsSync(summaryPath)) {
      const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf-8'));
      return {
        package: packageName,
        success: true,
        summary: summary.total,
      };
    }
    
    return {
      package: packageName,
      success: false,
      error: 'Coverage summary not found',
    };
  } catch (error) {
    return {
      package: packageName,
      success: false,
      error: error.message,
    };
  }
}

/**
 * Check if coverage meets thresholds
 */
function checkThresholds(packageName, summary) {
  const packagePath = `./packages/${packageName}/src/`;
  const thresholds = coverageThreshold[packagePath] || coverageThreshold.global;
  
  const results = {
    passed: true,
    failures: [],
  };
  
  ['branches', 'functions', 'lines', 'statements'].forEach(metric => {
    const actual = summary[metric].pct;
    const required = thresholds[metric];
    
    if (actual < required) {
      results.passed = false;
      results.failures.push({
        metric,
        actual,
        required,
        diff: required - actual,
      });
    }
  });
  
  return results;
}

/**
 * Generate combined coverage report
 */
function generateCombinedReport(results) {
  console.log(chalk.bold('\n📊 Coverage Report Summary\n'));
  console.log('='.repeat(80));
  
  let allPassed = true;
  
  results.forEach(result => {
    if (!result.success) {
      console.log(chalk.red(`\n❌ ${result.package}: Failed to run coverage`));
      console.log(chalk.gray(`   Error: ${result.error}`));
      allPassed = false;
      return;
    }
    
    const thresholdCheck = checkThresholds(result.package, result.summary);
    
    if (thresholdCheck.passed) {
      console.log(chalk.green(`\n✅ ${result.package}: All thresholds met`));
    } else {
      console.log(chalk.red(`\n❌ ${result.package}: Coverage below thresholds`));
      allPassed = false;
    }
    
    // Print coverage details
    const { branches, functions, lines, statements } = result.summary;
    console.log(chalk.gray('   Coverage:'));
    console.log(chalk.gray(`   - Branches:   ${formatPercent(branches.pct)}% (${branches.covered}/${branches.total})`));
    console.log(chalk.gray(`   - Functions:  ${formatPercent(functions.pct)}% (${functions.covered}/${functions.total})`));
    console.log(chalk.gray(`   - Lines:      ${formatPercent(lines.pct)}% (${lines.covered}/${lines.total})`));
    console.log(chalk.gray(`   - Statements: ${formatPercent(statements.pct)}% (${statements.covered}/${statements.total})`));
    
    // Print failures
    if (!thresholdCheck.passed) {
      console.log(chalk.yellow('   Failures:'));
      thresholdCheck.failures.forEach(failure => {
        console.log(chalk.yellow(
          `   - ${failure.metric}: ${formatPercent(failure.actual)}% < ${failure.required}% (need +${formatPercent(failure.diff)}%)`
        ));
      });
    }
  });
  
  console.log('\n' + '='.repeat(80));
  
  return allPassed;
}

/**
 * Format percentage
 */
function formatPercent(num) {
  return num.toFixed(2);
}

/**
 * Merge coverage reports
 */
function mergeCoverageReports() {
  console.log(chalk.blue('\n🔀 Merging coverage reports...'));
  
  try {
    // Install nyc if not available
    try {
      execSync('nyc --version', { stdio: 'ignore' });
    } catch {
      console.log(chalk.yellow('Installing nyc for coverage merging...'));
      execSync('npm install -g nyc', { stdio: 'inherit' });
    }
    
    // Create combined coverage directory
    if (!fs.existsSync(COVERAGE_DIR)) {
      fs.mkdirSync(COVERAGE_DIR, { recursive: true });
    }
    
    // Merge all lcov files
    const lcovFiles = PACKAGES
      .map(pkg => path.join(__dirname, `../packages/${pkg}/coverage/lcov.info`))
      .filter(file => fs.existsSync(file));
    
    if (lcovFiles.length > 0) {
      const mergedLcov = path.join(COVERAGE_DIR, 'lcov.info');
      execSync(
        `nyc merge ${lcovFiles.map(f => path.dirname(f)).join(' ')} ${COVERAGE_DIR}/temp && ` +
        `nyc report --reporter=lcov --report-dir=${COVERAGE_DIR}`,
        { stdio: 'inherit' }
      );
      
      console.log(chalk.green('✅ Coverage reports merged successfully'));
      console.log(chalk.gray(`   Combined report: ${COVERAGE_DIR}/lcov.info`));
    }
  } catch (error) {
    console.error(chalk.red('Failed to merge coverage reports:'), error.message);
  }
}

/**
 * Generate badges
 */
function generateBadges(results) {
  console.log(chalk.blue('\n🏷️  Generating coverage badges...'));
  
  const badges = {
    backend: { coverage: 0, color: 'red' },
    frontend: { coverage: 0, color: 'red' },
    shared: { coverage: 0, color: 'red' },
    types: { coverage: 0, color: 'red' },
    total: { coverage: 0, color: 'red' },
  };
  
  let totalLines = 0;
  let coveredLines = 0;
  
  results.forEach(result => {
    if (result.success && result.summary) {
      const coverage = result.summary.lines.pct;
      const color = coverage >= 90 ? 'brightgreen' :
                   coverage >= 80 ? 'green' :
                   coverage >= 70 ? 'yellow' :
                   coverage >= 60 ? 'orange' : 'red';
      
      badges[result.package] = { coverage, color };
      
      totalLines += result.summary.lines.total;
      coveredLines += result.summary.lines.covered;
    }
  });
  
  // Calculate total coverage
  if (totalLines > 0) {
    const totalCoverage = (coveredLines / totalLines) * 100;
    const totalColor = totalCoverage >= 90 ? 'brightgreen' :
                      totalCoverage >= 80 ? 'green' :
                      totalCoverage >= 70 ? 'yellow' :
                      totalCoverage >= 60 ? 'orange' : 'red';
    
    badges.total = { coverage: totalCoverage, color: totalColor };
  }
  
  // Save badges data
  const badgesPath = path.join(COVERAGE_DIR, 'badges.json');
  fs.writeFileSync(badgesPath, JSON.stringify(badges, null, 2));
  
  console.log(chalk.green('✅ Coverage badges generated'));
  console.log(chalk.gray(`   Badges data: ${badgesPath}`));
  
  // Print badge URLs
  console.log(chalk.gray('\n   Badge URLs:'));
  Object.entries(badges).forEach(([name, data]) => {
    const url = `https://img.shields.io/badge/coverage-${data.coverage.toFixed(1)}%25-${data.color}`;
    console.log(chalk.gray(`   - ${name}: ${url}`));
  });
}

/**
 * Main execution
 */
async function main() {
  console.log(chalk.bold('🧪 Running Test Coverage Enforcement\n'));
  
  const args = process.argv.slice(2);
  const packagesToTest = args.length > 0 ? args : PACKAGES;
  
  // Run coverage for each package
  const results = [];
  for (const packageName of packagesToTest) {
    if (PACKAGES.includes(packageName)) {
      const result = runPackageCoverage(packageName);
      results.push(result);
    } else {
      console.warn(chalk.yellow(`⚠️  Unknown package: ${packageName}`));
    }
  }
  
  // Generate report
  const allPassed = generateCombinedReport(results);
  
  // Merge coverage reports
  mergeCoverageReports();
  
  // Generate badges
  generateBadges(results);
  
  // Exit with appropriate code
  if (!allPassed) {
    console.log(chalk.red('\n❌ Coverage requirements not met!'));
    console.log(chalk.yellow('\nTo improve coverage:'));
    console.log(chalk.yellow('1. Add more unit tests for uncovered code'));
    console.log(chalk.yellow('2. Focus on critical paths and edge cases'));
    console.log(chalk.yellow('3. Use "npm run test:coverage" to see detailed reports'));
    process.exit(1);
  } else {
    console.log(chalk.green('\n✅ All coverage requirements met!'));
    process.exit(0);
  }
}

// Run the script
main().catch(error => {
  console.error(chalk.red('Script failed:'), error);
  process.exit(1);
});