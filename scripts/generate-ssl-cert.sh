#!/bin/bash

# Script to generate SSL certificates for HTTPS
# Can generate self-signed certificates for development or prepare for Let's Encrypt in production

set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SSL_DIR="$PROJECT_ROOT/ssl"

echo "🔐 SSL Certificate Generation Script"
echo "===================================="

# Create SSL directory if it doesn't exist
mkdir -p "$SSL_DIR"

# Check if certificates already exist
if [ -f "$SSL_DIR/cert.pem" ] && [ -f "$SSL_DIR/key.pem" ]; then
    echo "⚠️  SSL certificates already exist in $SSL_DIR"
    read -p "Do you want to regenerate them? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Keeping existing certificates."
        exit 0
    fi
fi

# Prompt for certificate type
echo "Select certificate type:"
echo "1) Self-signed (for development/testing)"
echo "2) Let's Encrypt (for production)"
read -p "Enter choice (1 or 2): " CERT_TYPE

if [ "$CERT_TYPE" == "1" ]; then
    echo ""
    echo "📝 Generating self-signed certificate..."
    
    # Get domain name
    read -p "Enter domain name (default: localhost): " DOMAIN
    DOMAIN=${DOMAIN:-localhost}
    
    # Generate private key
    openssl genrsa -out "$SSL_DIR/key.pem" 2048
    
    # Generate certificate signing request
    openssl req -new -key "$SSL_DIR/key.pem" \
        -out "$SSL_DIR/csr.pem" \
        -subj "/C=CZ/ST=Prague/L=Prague/O=SpheroSeg/CN=$DOMAIN"
    
    # Generate self-signed certificate (valid for 365 days)
    openssl x509 -req -days 365 \
        -in "$SSL_DIR/csr.pem" \
        -signkey "$SSL_DIR/key.pem" \
        -out "$SSL_DIR/cert.pem"
    
    # Clean up CSR
    rm "$SSL_DIR/csr.pem"
    
    echo "✅ Self-signed certificate generated successfully!"
    echo "   Certificate: $SSL_DIR/cert.pem"
    echo "   Private Key: $SSL_DIR/key.pem"
    echo ""
    echo "⚠️  Note: Browsers will show a security warning for self-signed certificates."
    
elif [ "$CERT_TYPE" == "2" ]; then
    echo ""
    echo "📝 Let's Encrypt Setup Instructions:"
    echo "===================================="
    echo ""
    echo "For production Let's Encrypt certificates, you'll need:"
    echo ""
    echo "1. A domain name pointing to your server"
    echo "2. Port 80 and 443 open on your firewall"
    echo "3. Certbot installed on your server"
    echo ""
    echo "Run these commands on your production server:"
    echo ""
    echo "# Install certbot (Ubuntu/Debian)"
    echo "sudo apt-get update"
    echo "sudo apt-get install certbot"
    echo ""
    echo "# Stop nginx if running"
    echo "docker-compose --profile prod down"
    echo ""
    echo "# Generate certificate"
    echo "sudo certbot certonly --standalone -d spherosegapp.utia.cas.cz"
    echo ""
    echo "# Copy certificates to project"
    echo "sudo cp /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem $SSL_DIR/cert.pem"
    echo "sudo cp /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem $SSL_DIR/key.pem"
    echo "sudo chown $(whoami):$(whoami) $SSL_DIR/*.pem"
    echo ""
    echo "# Set up auto-renewal (add to crontab)"
    echo "0 0 * * * certbot renew --quiet --post-hook 'docker-compose --profile prod restart nginx-prod'"
    echo ""
    echo "After completing these steps, update docker-compose.prod.yml to mount the certificates."
    
else
    echo "Invalid choice. Exiting."
    exit 1
fi

echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Update docker-compose file to mount SSL certificates:"
echo "   volumes:"
echo "     - ./ssl:/etc/nginx/ssl:ro"
echo ""
echo "2. Update nginx configuration to use HTTPS:"
echo "   - Use nginx-https.conf instead of nginx.conf"
echo ""
echo "3. Update environment variables:"
echo "   - Set APP_URL to https://your-domain.com"
echo "   - Update CORS_ORIGIN to include https URLs"
echo ""
echo "4. Restart services:"
echo "   docker-compose --profile prod up -d"