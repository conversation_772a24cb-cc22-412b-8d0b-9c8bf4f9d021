version: '3.8'

# Production Docker Compose with enterprise-grade security and performance
# Usage: docker-compose -f docker-compose.production.yml up -d

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "5"
    labels: "service,container"

x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s

networks:
  frontend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
  backend:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/24
  data:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/24
  monitoring:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/24

secrets:
  db_password:
    external: true
  db_root_password:
    external: true
  jwt_secret:
    external: true
  session_secret:
    external: true
  rabbitmq_password:
    external: true
  redis_password:
    external: true
  email_password:
    external: true
  ssl_cert:
    external: true
  ssl_key:
    external: true
  grafana_password:
    external: true
  elastic_password:
    external: true

volumes:
  postgres_data:
    driver: local
  postgres_backup:
    driver: local
  redis_data:
    driver: local
  redis_backup:
    driver: local
  rabbitmq_data:
    driver: local
  ml_models:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  nginx_cache:
    driver: local

services:
  # ============================================
  # REVERSE PROXY & LOAD BALANCER
  # ============================================
  nginx:
    image: nginx:1.25-alpine
    container_name: spheroseg-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.secure.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/modsecurity:/etc/nginx/modsecurity:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - ./certbot/www:/var/www/certbot:ro
      - nginx_cache:/var/cache/nginx
      - logs:/var/log/nginx
    secrets:
      - ssl_cert
      - ssl_key
    networks:
      - frontend
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/run
      - /var/cache/nginx
      - /tmp

  # ============================================
  # FRONTEND APPLICATION
  # ============================================
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile.prod
      args:
        NODE_ENV: production
    image: spheroseg/frontend:${VERSION:-latest}
    container_name: spheroseg-frontend
    restart: always
    environment:
      NODE_ENV: production
      VITE_API_URL: https://api.spherosegapp.utia.cas.cz
      VITE_WS_URL: wss://api.spherosegapp.utia.cas.cz
    networks:
      - frontend
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000"]
    logging: *default-logging
    user: "1000:1000"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/.next/cache

  # ============================================
  # BACKEND API
  # ============================================
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile.prod
      args:
        NODE_ENV: production
    image: spheroseg/backend:${VERSION:-latest}
    container_name: spheroseg-backend
    restart: always
    secrets:
      - db_password
      - jwt_secret
      - session_secret
      - redis_password
      - rabbitmq_password
      - email_password
    environment:
      NODE_ENV: production
      PORT: 5001
      LOG_LEVEL: warn
      ENABLE_SOURCE_MAPS: "false"
      
      # Performance settings
      UV_THREADPOOL_SIZE: 8
      NODE_OPTIONS: "--max-old-space-size=2048 --max-semi-space-size=256"
      CLUSTER_WORKERS: 4
      
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: spheroseg
      DB_USER: spheroseg
      DB_SSL: "true"
      DB_POOL_MIN: 5
      DB_POOL_MAX: 20
      DB_POOL_IDLE: 10000
      
      # Cache
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      REDIS_KEY_PREFIX: "spheroseg:"
      
      # Message Queue
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: admin
      RABBITMQ_VHOST: "/"
      
      # Security
      ALLOWED_ORIGINS: "https://spherosegapp.utia.cas.cz,https://www.spherosegapp.utia.cas.cz"
      SECURE_COOKIES: "true"
      TRUST_PROXY: "true"
      HELMET_CSP: "true"
      
      # Rate limiting
      RATE_LIMIT_ENABLED: "true"
      RATE_LIMIT_REQUESTS: 100
      RATE_LIMIT_WINDOW: 60
      RATE_LIMIT_BLOCK_DURATION: 600
      
      # File upload
      MAX_FILE_SIZE: 104857600
      ALLOWED_FILE_TYPES: "image/jpeg,image/png,image/tiff"
      UPLOAD_DIR: /app/uploads
      
      # Email
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_SECURE: "false"
      EMAIL_FROM: <EMAIL>
      
      # ML Service
      ML_SERVICE_URL: http://ml:5002
      ML_SERVICE_TIMEOUT: 300000
      
      # Monitoring
      ENABLE_METRICS: "true"
      METRICS_PORT: 9091
      
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - frontend
      - backend
      - data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2048M
          cpus: '2.0'
        reservations:
          memory: 1024M
          cpus: '1.0'
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/health"]
    logging: *default-logging
    user: "1000:1000"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETUID
      - SETGID

  # ============================================
  # MACHINE LEARNING SERVICE
  # ============================================
  ml:
    build:
      context: ./packages/ml
      dockerfile: Dockerfile.prod
    image: spheroseg/ml:${VERSION:-latest}
    container_name: spheroseg-ml
    restart: always
    secrets:
      - rabbitmq_password
    environment:
      PYTHONUNBUFFERED: 1
      PYTHONDONTWRITEBYTECODE: 1
      MODEL_PATH: /app/models/checkpoint_epoch_9.pth.tar
      DEBUG: "false"
      LOG_LEVEL: WARNING
      
      # RabbitMQ
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: admin
      RABBITMQ_QUEUE: segmentation_tasks
      RABBITMQ_PREFETCH_COUNT: 2
      
      # Performance
      OMP_NUM_THREADS: 4
      MKL_NUM_THREADS: 4
      TORCH_NUM_THREADS: 4
      
      # GPU (if available)
      CUDA_VISIBLE_DEVICES: 0
      
    volumes:
      - ml_models:/app/models:ro
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - backend
      - data
    depends_on:
      rabbitmq:
        condition: service_healthy
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 8192M
          cpus: '4.0'
        reservations:
          memory: 4096M
          cpus: '2.0'
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      start_period: 120s
    logging: *default-logging
    user: "1000:1000"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # ============================================
  # DATABASE
  # ============================================
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-postgres
    restart: always
    secrets:
      - db_password
      - db_root_password
    environment:
      POSTGRES_DB: spheroseg
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'
      PGDATA: /var/lib/postgresql/data/pgdata
      
      # Performance tuning
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: 0.9
      POSTGRES_WAL_BUFFERS: 16MB
      POSTGRES_DEFAULT_STATISTICS_TARGET: 100
      POSTGRES_RANDOM_PAGE_COST: 1.1
      POSTGRES_EFFECTIVE_IO_CONCURRENCY: 200
      POSTGRES_WORK_MEM: 4MB
      POSTGRES_MIN_WAL_SIZE: 1GB
      POSTGRES_MAX_WAL_SIZE: 4GB
      
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backup:/backup
      - ./scripts/postgres/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/postgres/backup.sh:/usr/local/bin/backup.sh:ro
    networks:
      - data
    deploy:
      resources:
        limits:
          memory: 2048M
          cpus: '2.0'
        reservations:
          memory: 1024M
          cpus: '1.0'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD-SHELL", "pg_isready -U spheroseg"]
    logging: *default-logging
    user: "999:999"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETUID
      - SETGID

  # ============================================
  # DATABASE BACKUP SERVICE
  # ============================================
  postgres-backup:
    image: postgres:15-alpine
    container_name: spheroseg-postgres-backup
    restart: always
    secrets:
      - db_password
    environment:
      PGHOST: postgres
      PGDATABASE: spheroseg
      PGUSER: spheroseg
      PGPASSFILE: /run/secrets/db_password
      BACKUP_SCHEDULE: "0 2 * * *"
      BACKUP_RETENTION_DAYS: 30
      BACKUP_DIR: /backup
    volumes:
      - postgres_backup:/backup
      - ./scripts/postgres/backup-cron.sh:/usr/local/bin/backup-cron.sh:ro
    networks:
      - data
    depends_on:
      postgres:
        condition: service_healthy
    command: ["/usr/local/bin/backup-cron.sh"]
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    logging: *default-logging
    user: "999:999"
    security_opt:
      - no-new-privileges:true

  # ============================================
  # REDIS CACHE
  # ============================================
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    restart: always
    secrets:
      - redis_password
    command: >
      sh -c '
        redis-server
        --requirepass "$$(cat /run/secrets/redis_password)"
        --maxmemory 512mb
        --maxmemory-policy allkeys-lru
        --save 60 1
        --save 300 10
        --save 900 100
        --appendonly yes
        --appendfilename "redis-aof.aof"
        --aof-use-rdb-preamble yes
        --protected-mode yes
        --tcp-backlog 511
        --timeout 0
        --tcp-keepalive 300
        --databases 16
        --always-show-logo no
        --bind 0.0.0.0
        --port 6379
      '
    volumes:
      - redis_data:/data
      - redis_backup:/backup
    networks:
      - data
    deploy:
      resources:
        limits:
          memory: 768M
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "redis-cli", "--no-auth-warning", "ping"]
    logging: *default-logging
    user: "999:999"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID

  # ============================================
  # MESSAGE QUEUE
  # ============================================
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: spheroseg-rabbitmq
    restart: always
    secrets:
      - rabbitmq_password
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS_FILE: /run/secrets/rabbitmq_password
      RABBITMQ_DEFAULT_VHOST: /
      RABBITMQ_VM_MEMORY_HIGH_WATERMARK: 0.4
      RABBITMQ_DISK_FREE_LIMIT: 5GB
      RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS: "-rabbit log [{console,[{level,warning}]}]"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - ./rabbitmq/enabled_plugins:/etc/rabbitmq/enabled_plugins:ro
    networks:
      - data
      - monitoring
    ports:
      - "15672:15672" # Management UI (restrict in production)
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
    logging: *default-logging
    user: "999:999"
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETUID
      - SETGID

  # ============================================
  # SSL CERTIFICATE MANAGEMENT
  # ============================================
  certbot:
    image: certbot/certbot:latest
    container_name: spheroseg-certbot
    restart: unless-stopped
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
      - logs:/var/log/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - frontend
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    logging: *default-logging
    security_opt:
      - no-new-privileges:true

  # ============================================
  # MONITORING - PROMETHEUS
  # ============================================
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: spheroseg-prometheus
    restart: always
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring
    ports:
      - "9090:9090" # Restrict in production
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/ready"]
    logging: *default-logging
    user: "65534:65534"
    security_opt:
      - no-new-privileges:true

  # ============================================
  # MONITORING - GRAFANA
  # ============================================
  grafana:
    image: grafana/grafana:10.1.5
    container_name: spheroseg-grafana
    restart: always
    secrets:
      - grafana_password
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD__FILE: /run/secrets/grafana_password
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,alexanderzobnin-zabbix-app
      GF_SERVER_ROOT_URL: https://monitoring.spherosegapp.utia.cas.cz
      GF_SECURITY_COOKIE_SECURE: "true"
      GF_SECURITY_STRICT_TRANSPORT_SECURITY: "true"
      GF_SECURITY_X_CONTENT_TYPE_OPTIONS: "true"
      GF_SECURITY_X_XSS_PROTECTION: "true"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - monitoring
      - frontend
    ports:
      - "3003:3000" # Restrict in production
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
    logging: *default-logging
    user: "472:472"
    security_opt:
      - no-new-privileges:true

  # ============================================
  # MONITORING - NODE EXPORTER
  # ============================================
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: spheroseg-node-exporter
    restart: always
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--path.rootfs=/rootfs'
      - '--collector.filesystem.mount-points-exclude=^/(dev|proc|sys|var/lib/docker/.+|var/lib/kubelet/pods/.+)($|/)'
      - '--collector.netclass.ignored-devices=^(veth.*)$'
    networks:
      - monitoring
    ports:
      - "9100:9100" # Restrict in production
    deploy:
      mode: global
      resources:
        limits:
          memory: 128M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.25'
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_TIME

  # ============================================
  # MONITORING - CADVISOR
  # ============================================
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: spheroseg-cadvisor
    restart: always
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    devices:
      - /dev/kmsg
    networks:
      - monitoring
    ports:
      - "8080:8080" # Restrict in production
    deploy:
      mode: global
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    logging: *default-logging
    security_opt:
      - no-new-privileges:true
    privileged: true

  # ============================================
  # MONITORING - ALERTMANAGER
  # ============================================
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: spheroseg-alertmanager
    restart: always
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ./monitoring/alertmanager/data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=https://alerts.spherosegapp.utia.cas.cz'
    networks:
      - monitoring
    ports:
      - "9093:9093" # Restrict in production
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9093/-/ready"]
    logging: *default-logging
    user: "65534:65534"
    security_opt:
      - no-new-privileges:true

  # ============================================
  # LOGGING - ELASTICSEARCH
  # ============================================
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.2
    container_name: spheroseg-elasticsearch
    restart: always
    secrets:
      - elastic_password
    environment:
      - node.name=spheroseg-es01
      - cluster.name=spheroseg-cluster
      - discovery.type=single-node
      - ELASTIC_PASSWORD_FILE=/run/secrets/elastic_password
      - bootstrap.memory_lock=true
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=true
      - xpack.security.http.ssl.key=/usr/share/elasticsearch/config/certs/es01.key
      - xpack.security.http.ssl.certificate=/usr/share/elasticsearch/config/certs/es01.crt
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./monitoring/elasticsearch/certs:/usr/share/elasticsearch/config/certs:ro
    networks:
      - monitoring
    ports:
      - "9200:9200" # Restrict in production
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '1.0'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD-SHELL", "curl -s --cacert config/certs/ca.crt https://localhost:9200 | grep -q 'missing authentication credentials'"]
    logging: *default-logging
    security_opt:
      - no-new-privileges:true

  # ============================================
  # LOGGING - LOGSTASH
  # ============================================
  logstash:
    image: docker.elastic.co/logstash/logstash:8.10.2
    container_name: spheroseg-logstash
    restart: always
    secrets:
      - elastic_password
    environment:
      - "LS_JAVA_OPTS=-Xms256m -Xmx256m"
      - ELASTIC_PASSWORD_FILE=/run/secrets/elastic_password
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - logs:/var/log/spheroseg:ro
    networks:
      - monitoring
    ports:
      - "5044:5044" # Beats input
      - "9600:9600" # Monitoring API
    depends_on:
      - elasticsearch
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "curl", "-f", "http://localhost:9600"]
    logging: *default-logging
    security_opt:
      - no-new-privileges:true

  # ============================================
  # LOGGING - KIBANA
  # ============================================
  kibana:
    image: docker.elastic.co/kibana/kibana:8.10.2
    container_name: spheroseg-kibana
    restart: always
    secrets:
      - elastic_password
    environment:
      - SERVERNAME=kibana
      - ELASTICSEARCH_HOSTS=https://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD_FILE=/run/secrets/elastic_password
      - ELASTICSEARCH_SSL_CERTIFICATEAUTHORITIES=/usr/share/kibana/config/certs/ca.crt
      - SERVER_SSL_ENABLED=true
      - SERVER_SSL_CERTIFICATE=/usr/share/kibana/config/certs/kibana.crt
      - SERVER_SSL_KEY=/usr/share/kibana/config/certs/kibana.key
    volumes:
      - ./monitoring/kibana/certs:/usr/share/kibana/config/certs:ro
      - ./monitoring/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    networks:
      - monitoring
      - frontend
    ports:
      - "5601:5601" # Restrict in production
    depends_on:
      - elasticsearch
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      <<: *healthcheck-defaults
      test: ["CMD", "curl", "-s", "-f", "http://localhost:5601/api/status"]
    logging: *default-logging
    security_opt:
      - no-new-privileges:true