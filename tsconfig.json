{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@spheroseg/shared": ["packages/shared/src"], "@spheroseg/types": ["packages/types/src"]}}, "exclude": ["node_modules", "dist", "build"]}