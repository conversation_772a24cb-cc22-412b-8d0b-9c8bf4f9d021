// Prisma Schema for SpheroSeg Application
// Replaces 1,444 raw SQL queries with type-safe ORM

generator client {
  provider = "prisma-client-js"
  output   = "../packages/backend/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id           String    @id @default(uuid())
  email        String    @unique
  username     String    @unique
  passwordHash String    @map("password_hash")
  role         User<PERSON>ole  @default(USER)
  isActive     Boolean   @default(true) @map("is_active")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  
  // Relations
  projects     Project[]
  
  @@map("users")
}

// Project model
model Project {
  id          String        @id @default(uuid())
  userId      String        @map("user_id")
  name        String
  description String?
  status      ProjectStatus @default(ACTIVE)
  imageCount  Int           @default(0) @map("image_count")
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")
  
  // Relations
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  images      Image[]
  shares      ProjectShare[]
  
  @@index([userId])
  @@map("projects")
}

// Image model
model Image {
  id                     String               @id @default(uuid())
  projectId              String               @map("project_id")
  filename               String
  originalFilename       String?              @map("original_filename")
  fileSize               BigInt?              @map("file_size")
  width                  Int?
  height                 Int?
  thumbnailPath          String?              @map("thumbnail_path")
  previewPath            String?              @map("preview_path")
  segmentationStatus     SegmentationStatus   @default(PENDING) @map("segmentation_status")
  segmentationResult     Json?                @map("segmentation_result")
  processingStartedAt    DateTime?            @map("processing_started_at")
  processingCompletedAt  DateTime?            @map("processing_completed_at")
  processingTime         Int?                 @map("processing_time")
  errorMessage           String?              @map("error_message")
  uploadDate             DateTime             @default(now()) @map("upload_date")
  createdAt              DateTime             @default(now()) @map("created_at")
  updatedAt              DateTime             @updatedAt @map("updated_at")
  
  // Relations
  project                Project              @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@index([projectId])
  @@index([segmentationStatus])
  @@map("images")
}

// ProjectShare model
model ProjectShare {
  id              String      @id @default(uuid())
  projectId       String      @map("project_id")
  email           String
  role            ShareRole   @default(VIEWER)
  status          ShareStatus @default(PENDING)
  invitationToken String?     @unique @map("invitation_token")
  createdAt       DateTime    @default(now()) @map("created_at")
  acceptedAt      DateTime?   @map("accepted_at")
  
  // Relations
  project         Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@index([projectId])
  @@index([email])
  @@map("project_shares")
}

// Session model (for express-session)
model Session {
  sid    String   @id
  sess   Json
  expire DateTime
  
  @@index([expire])
  @@map("sessions")
}

// QueueJob model
model QueueJob {
  id          String    @id @default(uuid())
  type        JobType
  data        Json?
  status      JobStatus @default(PENDING)
  attempts    Int       @default(0)
  error       String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  completedAt DateTime? @map("completed_at")
  
  @@index([status])
  @@index([type])
  @@map("queue_jobs")
}

// Migration tracking
model Migration {
  id         Int      @id @default(autoincrement())
  filename   String   @unique
  executedAt DateTime @default(now()) @map("executed_at")
  
  @@map("migrations")
}

// Enums
enum UserRole {
  USER
  ADMIN
  GUEST
}

enum ProjectStatus {
  ACTIVE
  ARCHIVED
  DELETED
}

enum SegmentationStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum ShareRole {
  VIEWER
  EDITOR
  ADMIN
}

enum ShareStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum JobType {
  SEGMENTATION
  PROCESSING
  CLEANUP
  GENERAL
}

enum JobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
