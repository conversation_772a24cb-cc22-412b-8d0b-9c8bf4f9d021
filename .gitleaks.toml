# Gitleaks Configuration for SpheroSeg
# This file configures secret detection patterns and rules

title = "SpheroSeg Gitleaks Configuration"

# Extend the base configuration
[extend]
useDefault = true

# Custom rules for project-specific patterns
[[rules]]
id = "jwt-secret"
description = "JWT Secret Detection"
regex = '''(?i)(jwt[_\-\s]?secret|jwt[_\-\s]?key)[\s]*[=:]+[\s]*["']?([a-zA-Z0-9+/]{32,})["']?'''
tags = ["jwt", "secret", "auth"]
secretGroup = 2

[[rules]]
id = "session-secret"
description = "Session Secret Detection"
regex = '''(?i)(session[_\-\s]?secret|session[_\-\s]?key)[\s]*[=:]+[\s]*["']?([a-zA-Z0-9+/]{32,})["']?'''
tags = ["session", "secret"]
secretGroup = 2

[[rules]]
id = "refresh-token-secret"
description = "Refresh Token Secret Detection"
regex = '''(?i)(refresh[_\-\s]?token[_\-\s]?secret)[\s]*[=:]+[\s]*["']?([a-zA-Z0-9+/]{32,})["']?'''
tags = ["token", "secret", "auth"]
secretGroup = 2

[[rules]]
id = "encryption-key"
description = "Encryption Key Detection"
regex = '''(?i)(encryption[_\-\s]?key|encrypt[_\-\s]?key)[\s]*[=:]+[\s]*["']?([0-9a-fA-F]{32,})["']?'''
tags = ["encryption", "key"]
secretGroup = 2

[[rules]]
id = "database-url-with-password"
description = "Database URL with Password"
regex = '''(?i)(database[_\-\s]?url|db[_\-\s]?url|postgres[_\-\s]?url|mysql[_\-\s]?url|mongodb[_\-\s]?url)[\s]*[=:]+[\s]*["']?(postgresql|postgres|mysql|mongodb)://[^:]+:([^@]+)@'''
tags = ["database", "password", "credential"]
secretGroup = 3

[[rules]]
id = "redis-password"
description = "Redis Password"
regex = '''(?i)(redis[_\-\s]?password|redis[_\-\s]?pass)[\s]*[=:]+[\s]*["']?([^"'\s]+)["']?'''
tags = ["redis", "password"]
secretGroup = 2

[[rules]]
id = "rabbitmq-credentials"
description = "RabbitMQ Credentials"
regex = '''(?i)amqp://([^:]+):([^@]+)@'''
tags = ["rabbitmq", "credential"]
secretGroup = 2

[[rules]]
id = "api-key-generic"
description = "Generic API Key"
regex = '''(?i)(api[_\-\s]?key|apikey)[\s]*[=:]+[\s]*["']?([a-zA-Z0-9]{32,})["']?'''
tags = ["api", "key"]
secretGroup = 2

[[rules]]
id = "aws-access-key"
description = "AWS Access Key ID"
regex = '''(?i)(aws[_\-\s]?access[_\-\s]?key[_\-\s]?id|aws[_\-\s]?key[_\-\s]?id)[\s]*[=:]+[\s]*["']?(AKIA[0-9A-Z]{16})["']?'''
tags = ["aws", "credential"]
secretGroup = 2

[[rules]]
id = "aws-secret-key"
description = "AWS Secret Access Key"
regex = '''(?i)(aws[_\-\s]?secret[_\-\s]?access[_\-\s]?key|aws[_\-\s]?secret[_\-\s]?key|aws[_\-\s]?secret)[\s]*[=:]+[\s]*["']?([a-zA-Z0-9+/]{40})["']?'''
tags = ["aws", "credential", "secret"]
secretGroup = 2

[[rules]]
id = "private-key"
description = "Private Key File Content"
regex = '''-----BEGIN (RSA |EC |DSA |OPENSSH )?PRIVATE KEY-----'''
tags = ["key", "private"]

[[rules]]
id = "base64-high-entropy"
description = "Base64 High Entropy String (potential secret)"
regex = '''(?:[A-Za-z0-9+/]{4}){8,}(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?'''
entropy = 4.5
tags = ["entropy", "base64"]

[[rules]]
id = "hex-high-entropy"
description = "Hex High Entropy String (potential secret)"
regex = '''(?:[0-9a-fA-F]{2}){16,}'''
entropy = 4.5
tags = ["entropy", "hex"]

# Allow list for acceptable patterns
[allowlist]
description = "Global allow list"
paths = [
  '''^\.env\.example$''',
  '''^.*\.example$''',
  '''^.*\.sample$''',
  '''^.*\.template$''',
  '''^docs/.*''',
  '''^README\.md$''',
  '''^SECURITY\.md$''',
  '''test.*\.ts$''',
  '''test.*\.js$''',
  '''.*\.test\.ts$''',
  '''.*\.test\.js$''',
  '''.*\.spec\.ts$''',
  '''.*\.spec\.js$''',
]

# Allow certain patterns in specific files
[[allowlist]]
description = "Allow example secrets in documentation"
regexes = [
  '''CHANGE_THIS_IN_PRODUCTION''',
  '''your[_\-]?secret[_\-]?here''',
  '''your[_\-]?password[_\-]?here''',
  '''example[_\-]?secret''',
  '''example[_\-]?key''',
  '''REMOVED_FROM_HISTORY''',
  '''REDACTED''',
]

[[allowlist]]
description = "Allow localhost and development URLs"
regexes = [
  '''localhost:[0-9]+''',
  '''127\.0\.0\.1:[0-9]+''',
  '''postgres://[^:]+:postgres@localhost''',
  '''redis://localhost:[0-9]+''',
  '''amqp://guest:guest@localhost''',
]

# Specific file paths to always scan (override gitignore)
[scan]
include = [
  "**/*.ts",
  "**/*.tsx",
  "**/*.js",
  "**/*.jsx",
  "**/*.json",
  "**/*.yml",
  "**/*.yaml",
  "**/*.env*",
  "**/*.config.*",
  "**/config/*",
  "**/*.sh",
  "**/Dockerfile*",
  "**/docker-compose*.yml",
]

# Specific paths to exclude from scanning
exclude = [
  "**/node_modules/**",
  "**/dist/**",
  "**/build/**",
  "**/coverage/**",
  "**/.git/**",
  "**/backups/**",
  "**/logs/**",
  "**/*.min.js",
  "**/*.map",
  "**/package-lock.json",
  "**/yarn.lock",
  "**/pnpm-lock.yaml",
]