import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('Project Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123!');
    await page.locator('button[type="submit"]').click();
    await expect(page).toHaveURL('/dashboard');
  });

  test('should create a new project', async ({ page }) => {
    // Navigate to create project page
    await page.locator('text=Create New Project').click();
    await expect(page).toHaveURL('/projects/new');
    
    // Fill project form
    await page.fill('input[name="title"]', 'E2E Test Project');
    await page.fill('textarea[name="description"]', 'This is a test project created by E2E tests');
    await page.fill('input[name="tags"]', 'test, e2e, automated');
    
    // Submit form
    await page.locator('button[type="submit"]').click();
    
    // Should redirect to project detail page
    await expect(page).toHaveURL(/\/projects\/[a-z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test Project');
  });

  test('should list all projects', async ({ page }) => {
    // Should be on dashboard with project list
    await expect(page.locator('[data-testid="project-list"]')).toBeVisible();
    
    // Check if projects are displayed
    const projectCards = page.locator('[data-testid="project-card"]');
    await expect(projectCards).toHaveCount(await projectCards.count());
  });

  test('should search for projects', async ({ page }) => {
    // Enter search term
    await page.fill('input[placeholder*="Search"]', 'Test');
    
    // Wait for search results
    await page.waitForTimeout(500); // Debounce delay
    
    // Check filtered results
    const projectCards = page.locator('[data-testid="project-card"]');
    const count = await projectCards.count();
    
    for (let i = 0; i < count; i++) {
      const text = await projectCards.nth(i).textContent();
      expect(text?.toLowerCase()).toContain('test');
    }
  });

  test('should edit project details', async ({ page }) => {
    // Click on first project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Wait for project detail page
    await expect(page.locator('h1')).toBeVisible();
    
    // Click edit button
    await page.locator('button:has-text("Edit")').click();
    
    // Update project details
    const titleInput = page.locator('input[name="title"]');
    await titleInput.clear();
    await titleInput.fill('Updated Project Title');
    
    const descInput = page.locator('textarea[name="description"]');
    await descInput.clear();
    await descInput.fill('Updated description');
    
    // Save changes
    await page.locator('button:has-text("Save")').click();
    
    // Verify changes
    await expect(page.locator('h1')).toContainText('Updated Project Title');
    await expect(page.locator('text=Updated description')).toBeVisible();
  });

  test('should delete a project', async ({ page }) => {
    // Navigate to project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Get project title for verification
    const projectTitle = await page.locator('h1').textContent();
    
    // Click delete button
    await page.locator('button:has-text("Delete")').click();
    
    // Confirm deletion in modal
    await page.locator('text=Are you sure').waitFor();
    await page.locator('button:has-text("Confirm Delete")').click();
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Project should not be in list
    await expect(page.locator(`text="${projectTitle}"`)).not.toBeVisible();
  });

  test('should handle project visibility toggle', async ({ page }) => {
    // Navigate to project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Find visibility toggle
    const visibilityToggle = page.locator('[data-testid="visibility-toggle"]');
    const isPublic = await visibilityToggle.isChecked();
    
    // Toggle visibility
    await visibilityToggle.click();
    
    // Wait for update
    await page.waitForTimeout(500);
    
    // Verify change
    expect(await visibilityToggle.isChecked()).toBe(!isPublic);
  });

  test('should duplicate a project', async ({ page }) => {
    // Navigate to project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Click duplicate button
    await page.locator('button:has-text("Duplicate")').click();
    
    // Wait for duplication modal
    await page.locator('text=Duplicate Project').waitFor();
    
    // Modify title
    await page.fill('input[name="title"]', 'Duplicated Project');
    
    // Confirm duplication
    await page.locator('button:has-text("Duplicate")').last().click();
    
    // Should redirect to new project
    await expect(page.locator('h1')).toContainText('Duplicated Project');
  });

  test('should share a project', async ({ page }) => {
    // Navigate to project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Click share button
    await page.locator('button:has-text("Share")').click();
    
    // Wait for share modal
    await page.locator('text=Share Project').waitFor();
    
    // Enter email to share with
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Select permission level
    await page.selectOption('select[name="permission"]', 'view');
    
    // Send invitation
    await page.locator('button:has-text("Send Invitation")').click();
    
    // Verify success message
    await expect(page.locator('text=Invitation sent')).toBeVisible();
  });

  test('should export project data', async ({ page }) => {
    // Navigate to project
    await page.locator('[data-testid="project-card"]').first().click();
    
    // Click export button
    await page.locator('button:has-text("Export")').click();
    
    // Select export format
    await page.locator('text=Export Project').waitFor();
    await page.selectOption('select[name="format"]', 'json');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.locator('button:has-text("Download")').click();
    
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toMatch(/project.*\.json$/);
  });

  test('should filter projects by date', async ({ page }) => {
    // Open filter dropdown
    await page.locator('button:has-text("Filter")').click();
    
    // Select date range
    await page.locator('input[name="startDate"]').fill('2024-01-01');
    await page.locator('input[name="endDate"]').fill('2024-12-31');
    
    // Apply filter
    await page.locator('button:has-text("Apply")').click();
    
    // Verify filtered results
    const projectCards = page.locator('[data-testid="project-card"]');
    await expect(projectCards).toHaveCount(await projectCards.count());
  });

  test('should sort projects', async ({ page }) => {
    // Open sort dropdown
    await page.locator('select[name="sort"]').selectOption('name-asc');
    
    // Get project titles
    const titles = await page.locator('[data-testid="project-title"]').allTextContents();
    
    // Verify alphabetical order
    const sortedTitles = [...titles].sort();
    expect(titles).toEqual(sortedTitles);
    
    // Sort by date
    await page.locator('select[name="sort"]').selectOption('date-desc');
    
    // Verify most recent first
    const dates = await page.locator('[data-testid="project-date"]').allTextContents();
    for (let i = 1; i < dates.length; i++) {
      const prevDate = new Date(dates[i - 1]);
      const currDate = new Date(dates[i]);
      expect(prevDate >= currDate).toBeTruthy();
    }
  });

  test('should paginate projects', async ({ page }) => {
    // Check if pagination exists
    const pagination = page.locator('[data-testid="pagination"]');
    
    if (await pagination.isVisible()) {
      // Click next page
      await page.locator('button[aria-label="Next page"]').click();
      
      // Verify page changed
      await expect(page.locator('[data-testid="page-indicator"]')).toContainText('2');
      
      // Click previous page
      await page.locator('button[aria-label="Previous page"]').click();
      
      // Verify back to first page
      await expect(page.locator('[data-testid="page-indicator"]')).toContainText('1');
    }
  });
});