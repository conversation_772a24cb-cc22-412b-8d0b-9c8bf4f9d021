import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('Image Upload and Segmentation', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123!');
    await page.locator('button[type="submit"]').click();
    await expect(page).toHaveURL('/dashboard');
    
    // Navigate to first project
    await page.locator('[data-testid="project-card"]').first().click();
  });

  test('should upload single image', async ({ page }) => {
    // Open upload dialog
    await page.locator('button:has-text("Upload Images")').click();
    
    // Select file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(path.join(__dirname, 'fixtures', 'test-cell.jpg'));
    
    // Start upload
    await page.locator('button:has-text("Upload")').click();
    
    // Wait for upload to complete
    await expect(page.locator('text=Upload complete')).toBeVisible({ timeout: 10000 });
    
    // Verify image appears in grid
    await expect(page.locator('[data-testid="image-card"]:last-child')).toContainText('test-cell.jpg');
  });

  test('should upload multiple images', async ({ page }) => {
    // Open upload dialog
    await page.locator('button:has-text("Upload Images")').click();
    
    // Select multiple files
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles([
      path.join(__dirname, 'fixtures', 'cell1.jpg'),
      path.join(__dirname, 'fixtures', 'cell2.jpg'),
      path.join(__dirname, 'fixtures', 'cell3.jpg')
    ]);
    
    // Start upload
    await page.locator('button:has-text("Upload")').click();
    
    // Wait for all uploads to complete
    await expect(page.locator('text=3 files uploaded')).toBeVisible({ timeout: 15000 });
    
    // Verify all images appear
    await expect(page.locator('text=cell1.jpg')).toBeVisible();
    await expect(page.locator('text=cell2.jpg')).toBeVisible();
    await expect(page.locator('text=cell3.jpg')).toBeVisible();
  });

  test('should reject invalid file types', async ({ page }) => {
    // Open upload dialog
    await page.locator('button:has-text("Upload Images")').click();
    
    // Try to select non-image file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(path.join(__dirname, 'fixtures', 'document.pdf'));
    
    // Should show error
    await expect(page.locator('text=Only image files are allowed')).toBeVisible();
  });

  test('should start segmentation for single image', async ({ page }) => {
    // Find pending image
    const pendingImage = page.locator('[data-testid="image-card"]:has-text("Pending")').first();
    
    if (await pendingImage.isVisible()) {
      // Click on image to open detail view
      await pendingImage.click();
      
      // Click segment button
      await page.locator('button:has-text("Start Segmentation")').click();
      
      // Should show processing status
      await expect(page.locator('text=Processing')).toBeVisible();
      
      // Wait for segmentation to complete (or timeout)
      await expect(page.locator('text=Completed')).toBeVisible({ timeout: 60000 });
    }
  });

  test('should batch segment multiple images', async ({ page }) => {
    // Select multiple images
    const checkboxes = page.locator('[data-testid="image-checkbox"]');
    const count = Math.min(await checkboxes.count(), 3);
    
    for (let i = 0; i < count; i++) {
      await checkboxes.nth(i).check();
    }
    
    // Click batch segment button
    await page.locator('button:has-text("Segment Selected")').click();
    
    // Confirm batch operation
    await page.locator('button:has-text("Start Batch Segmentation")').click();
    
    // Should show progress
    await expect(page.locator('[data-testid="batch-progress"]')).toBeVisible();
    
    // Wait for at least one to start processing
    await expect(page.locator('text=Processing')).toBeVisible({ timeout: 10000 });
  });

  test('should view segmentation results', async ({ page }) => {
    // Find completed segmentation
    const completedImage = page.locator('[data-testid="image-card"]:has-text("Completed")').first();
    
    if (await completedImage.isVisible()) {
      // Click to view details
      await completedImage.click();
      
      // Should show segmentation overlay
      await expect(page.locator('[data-testid="segmentation-canvas"]')).toBeVisible();
      
      // Check for polygons
      await expect(page.locator('[data-testid="polygon-count"]')).toBeVisible();
      const polygonCount = await page.locator('[data-testid="polygon-count"]').textContent();
      expect(parseInt(polygonCount || '0')).toBeGreaterThan(0);
    }
  });

  test('should edit segmentation results', async ({ page }) => {
    // Find completed segmentation
    const completedImage = page.locator('[data-testid="image-card"]:has-text("Completed")').first();
    
    if (await completedImage.isVisible()) {
      await completedImage.click();
      
      // Enter edit mode
      await page.locator('button:has-text("Edit")').click();
      
      // Select polygon tool
      await page.locator('[data-testid="tool-polygon"]').click();
      
      // Draw new polygon
      await page.locator('[data-testid="segmentation-canvas"]').click({ position: { x: 100, y: 100 } });
      await page.locator('[data-testid="segmentation-canvas"]').click({ position: { x: 200, y: 100 } });
      await page.locator('[data-testid="segmentation-canvas"]').click({ position: { x: 200, y: 200 } });
      await page.locator('[data-testid="segmentation-canvas"]').click({ position: { x: 100, y: 200 } });
      await page.locator('[data-testid="segmentation-canvas"]').click({ position: { x: 100, y: 100 } });
      
      // Save changes
      await page.locator('button:has-text("Save")').click();
      
      // Verify save success
      await expect(page.locator('text=Changes saved')).toBeVisible();
    }
  });

  test('should delete segmentation', async ({ page }) => {
    // Find completed segmentation
    const completedImage = page.locator('[data-testid="image-card"]:has-text("Completed")').first();
    
    if (await completedImage.isVisible()) {
      await completedImage.click();
      
      // Click delete segmentation
      await page.locator('button:has-text("Delete Segmentation")').click();
      
      // Confirm deletion
      await page.locator('button:has-text("Confirm Delete")').click();
      
      // Should revert to pending status
      await expect(page.locator('text=Pending')).toBeVisible();
    }
  });

  test('should export segmentation results', async ({ page }) => {
    // Find completed segmentation
    const completedImage = page.locator('[data-testid="image-card"]:has-text("Completed")').first();
    
    if (await completedImage.isVisible()) {
      await completedImage.click();
      
      // Click export button
      await page.locator('button:has-text("Export")').click();
      
      // Select format
      await page.selectOption('select[name="exportFormat"]', 'json');
      
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await page.locator('button:has-text("Download")').click();
      
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/segmentation.*\.json$/);
    }
  });

  test('should filter images by status', async ({ page }) => {
    // Filter by completed
    await page.selectOption('select[name="statusFilter"]', 'completed');
    
    // Verify only completed images shown
    const imageCards = page.locator('[data-testid="image-card"]');
    const count = await imageCards.count();
    
    for (let i = 0; i < count; i++) {
      await expect(imageCards.nth(i)).toContainText('Completed');
    }
    
    // Filter by pending
    await page.selectOption('select[name="statusFilter"]', 'pending');
    
    // Verify only pending images shown
    const pendingCards = page.locator('[data-testid="image-card"]');
    const pendingCount = await pendingCards.count();
    
    for (let i = 0; i < pendingCount; i++) {
      await expect(pendingCards.nth(i)).toContainText('Pending');
    }
  });

  test('should display image metadata', async ({ page }) => {
    // Click on any image
    await page.locator('[data-testid="image-card"]').first().click();
    
    // Check metadata panel
    await expect(page.locator('[data-testid="metadata-panel"]')).toBeVisible();
    
    // Verify metadata fields
    await expect(page.locator('text=Dimensions:')).toBeVisible();
    await expect(page.locator('text=File Size:')).toBeVisible();
    await expect(page.locator('text=Upload Date:')).toBeVisible();
    await expect(page.locator('text=Format:')).toBeVisible();
  });

  test('should zoom and pan image', async ({ page }) => {
    // Click on any image
    await page.locator('[data-testid="image-card"]').first().click();
    
    // Zoom in
    await page.locator('button[aria-label="Zoom in"]').click();
    await page.locator('button[aria-label="Zoom in"]').click();
    
    // Pan image
    const canvas = page.locator('[data-testid="image-viewer"]');
    await canvas.dragTo(canvas, {
      sourcePosition: { x: 200, y: 200 },
      targetPosition: { x: 300, y: 300 }
    });
    
    // Reset zoom
    await page.locator('button[aria-label="Reset zoom"]').click();
    
    // Verify zoom level reset
    await expect(page.locator('[data-testid="zoom-level"]')).toContainText('100%');
  });

  test('should delete image', async ({ page }) => {
    // Get initial image count
    const initialCount = await page.locator('[data-testid="image-card"]').count();
    
    if (initialCount > 0) {
      // Click on first image
      await page.locator('[data-testid="image-card"]').first().click();
      
      // Click delete button
      await page.locator('button:has-text("Delete Image")').click();
      
      // Confirm deletion
      await page.locator('button:has-text("Confirm Delete")').click();
      
      // Verify image removed
      const newCount = await page.locator('[data-testid="image-card"]').count();
      expect(newCount).toBe(initialCount - 1);
    }
  });

  test('should handle segmentation errors', async ({ page }) => {
    // Find image with error status if any
    const errorImage = page.locator('[data-testid="image-card"]:has-text("Error")').first();
    
    if (await errorImage.isVisible()) {
      await errorImage.click();
      
      // Should show error details
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      
      // Should have retry button
      await expect(page.locator('button:has-text("Retry")'));
      
      // Click retry
      await page.locator('button:has-text("Retry")').click();
      
      // Should start processing again
      await expect(page.locator('text=Processing')).toBeVisible();
    }
  });
});