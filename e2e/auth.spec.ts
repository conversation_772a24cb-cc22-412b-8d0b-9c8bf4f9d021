import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display login page', async ({ page }) => {
    await page.goto('/signin');
    
    await expect(page.locator('h1')).toContainText('Sign In');
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toContainText('Sign In');
  });

  test('should show validation errors for empty form', async ({ page }) => {
    await page.goto('/signin');
    
    await page.locator('button[type="submit"]').click();
    
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/signin');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.locator('button[type="submit"]').click();
    
    await expect(page.locator('text=Invalid email or password')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    await page.goto('/signin');
    
    // Use test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123!');
    await page.locator('button[type="submit"]').click();
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Welcome back')).toBeVisible();
  });

  test('should navigate to signup page', async ({ page }) => {
    await page.goto('/signin');
    
    await page.locator('text=Create an account').click();
    
    await expect(page).toHaveURL('/signup');
    await expect(page.locator('h1')).toContainText('Sign Up');
  });

  test('should register new user', async ({ page }) => {
    await page.goto('/signup');
    
    // Fill registration form
    await page.fill('input[name="name"]', 'New User');
    await page.fill('input[name="email"]', `user${Date.now()}@example.com`);
    await page.fill('input[name="password"]', 'SecurePass123!');
    await page.fill('input[name="confirmPassword"]', 'SecurePass123!');
    
    // Accept terms
    await page.locator('input[type="checkbox"]').check();
    
    // Submit form
    await page.locator('button[type="submit"]').click();
    
    // Should redirect to dashboard or verification page
    await expect(page).toHaveURL(/\/(dashboard|verify-email)/);
  });

  test('should show password strength indicator', async ({ page }) => {
    await page.goto('/signup');
    
    const passwordInput = page.locator('input[name="password"]');
    
    // Weak password
    await passwordInput.fill('123');
    await expect(page.locator('text=Weak')).toBeVisible();
    
    // Medium password
    await passwordInput.fill('Test123');
    await expect(page.locator('text=Medium')).toBeVisible();
    
    // Strong password
    await passwordInput.fill('SecurePass123!@#');
    await expect(page.locator('text=Strong')).toBeVisible();
  });

  test('should handle forgot password flow', async ({ page }) => {
    await page.goto('/signin');
    
    await page.locator('text=Forgot password?').click();
    
    await expect(page).toHaveURL('/forgot-password');
    await expect(page.locator('h1')).toContainText('Reset Password');
    
    // Submit email
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.locator('button[type="submit"]').click();
    
    // Should show success message
    await expect(page.locator('text=Password reset email sent')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // First login
    await page.goto('/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123!');
    await page.locator('button[type="submit"]').click();
    
    await expect(page).toHaveURL('/dashboard');
    
    // Now logout
    await page.locator('[data-testid="user-menu"]').click();
    await page.locator('text=Logout').click();
    
    // Should redirect to home
    await expect(page).toHaveURL('/');
    await expect(page.locator('text=Sign In')).toBeVisible();
  });

  test('should persist login across page refresh', async ({ page }) => {
    // Login
    await page.goto('/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Test123!');
    await page.locator('button[type="submit"]').click();
    
    await expect(page).toHaveURL('/dashboard');
    
    // Refresh page
    await page.reload();
    
    // Should still be logged in
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should redirect to login for protected routes', async ({ page }) => {
    // Try to access protected route without login
    await page.goto('/dashboard');
    
    // Should redirect to login
    await expect(page).toHaveURL('/signin');
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
  });
});