name: Production Deployment Pipeline

on:
  push:
    branches:
      - main
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
      skip_tests:
        description: 'Skip tests (emergency deployment only)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '20'
  DOCKER_BUILDKIT: 1

jobs:
  # ============================================
  # SECURITY SCAN
  # ============================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'

      - name: Upload Trivy results to GitHub Security
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # ============================================
  # BUILD & TEST
  # ============================================
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: security-scan
    strategy:
      matrix:
        service: [backend, frontend, ml]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        if: matrix.service != 'ml'
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        if: matrix.service == 'ml'
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install dependencies
        run: |
          if [ "${{ matrix.service }}" = "ml" ]; then
            cd packages/ml
            pip install -r requirements.txt
          else
            npm ci
          fi

      - name: Run linting
        run: |
          if [ "${{ matrix.service }}" = "ml" ]; then
            cd packages/ml
            pylint src/
            black --check src/
            mypy src/
          else
            npm run lint --workspace=@spheroseg/${{ matrix.service }}
          fi

      - name: Run unit tests
        if: ${{ !inputs.skip_tests }}
        run: |
          if [ "${{ matrix.service }}" = "ml" ]; then
            cd packages/ml
            pytest tests/ --cov=src --cov-report=xml
          else
            npm run test:coverage --workspace=@spheroseg/${{ matrix.service }}
          fi

      - name: Upload coverage
        if: ${{ !inputs.skip_tests }}
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/lcov.info
          flags: ${{ matrix.service }}
          name: ${{ matrix.service }}-coverage

      - name: Build Docker image
        run: |
          docker build \
            --cache-from ghcr.io/${{ github.repository }}/${{ matrix.service }}:latest \
            --tag ${{ matrix.service }}:${{ github.sha }} \
            --tag ${{ matrix.service }}:latest \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --file packages/${{ matrix.service }}/Dockerfile.prod \
            .

      - name: Scan Docker image
        run: |
          docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
            aquasec/trivy image --severity HIGH,CRITICAL \
            ${{ matrix.service }}:${{ github.sha }}

      - name: Save Docker image
        run: |
          docker save ${{ matrix.service }}:${{ github.sha }} | gzip > ${{ matrix.service }}.tar.gz

      - name: Upload Docker image artifact
        uses: actions/upload-artifact@v3
        with:
          name: docker-${{ matrix.service }}
          path: ${{ matrix.service }}.tar.gz
          retention-days: 1

  # ============================================
  # INTEGRATION TESTS
  # ============================================
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: build-and-test
    if: ${{ !inputs.skip_tests }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Docker images
        uses: actions/download-artifact@v3
        with:
          path: docker-images

      - name: Load Docker images
        run: |
          for service in backend frontend ml; do
            gunzip -c docker-images/docker-${service}/${service}.tar.gz | docker load
          done

      - name: Start test environment
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30

      - name: Run integration tests
        run: |
          npm run test:integration
          npm run e2e:integration

      - name: Collect logs on failure
        if: failure()
        run: |
          docker-compose -f docker-compose.test.yml logs > test-logs.txt
          
      - name: Upload test logs
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: test-logs
          path: test-logs.txt

      - name: Stop test environment
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down -v

  # ============================================
  # PUSH TO REGISTRY
  # ============================================
  push-to-registry:
    name: Push Docker Images
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        service: [backend, frontend, ml]
    steps:
      - name: Download Docker image
        uses: actions/download-artifact@v3
        with:
          name: docker-${{ matrix.service }}

      - name: Load Docker image
        run: |
          gunzip -c ${{ matrix.service }}.tar.gz | docker load

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Push Docker image
        run: |
          for tag in ${{ steps.meta.outputs.tags }}; do
            docker tag ${{ matrix.service }}:${{ github.sha }} $tag
            docker push $tag
          done

  # ============================================
  # DEPLOY TO STAGING
  # ============================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: push-to-registry
    if: github.ref == 'refs/heads/dev' || inputs.environment == 'staging'
    environment:
      name: staging
      url: https://staging.spherosegapp.utia.cas.cz
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.STAGING_KUBECONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=$(pwd)/kubeconfig

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k k8s/overlays/staging
          kubectl set image deployment/backend backend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }} -n spheroseg-staging
          kubectl set image deployment/frontend frontend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }} -n spheroseg-staging
          kubectl set image deployment/ml ml=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/ml:${{ github.sha }} -n spheroseg-staging
          kubectl rollout status deployment/backend -n spheroseg-staging
          kubectl rollout status deployment/frontend -n spheroseg-staging
          kubectl rollout status deployment/ml -n spheroseg-staging

      - name: Run smoke tests
        run: |
          npm run e2e:smoke -- --base-url=https://staging.spherosegapp.utia.cas.cz

      - name: Notify Slack
        if: always()
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "text": "Staging deployment ${{ job.status }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Staging Deployment*\nStatus: ${{ job.status }}\nCommit: ${{ github.sha }}\nAuthor: ${{ github.actor }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

  # ============================================
  # DEPLOY TO PRODUCTION
  # ============================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [push-to-registry, deploy-staging]
    if: (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')) && inputs.environment != 'staging'
    environment:
      name: production
      url: https://spherosegapp.utia.cas.cz
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PRODUCTION_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "${{ secrets.PRODUCTION_HOST_KEY }}" > ~/.ssh/known_hosts

      - name: Create deployment package
        run: |
          tar -czf deployment.tar.gz \
            docker-compose.production.yml \
            nginx/nginx.prod.secure.conf \
            monitoring/ \
            scripts/

      - name: Transfer deployment package
        run: |
          scp deployment.tar.gz ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }}:/opt/spheroseg/

      - name: Deploy to production
        run: |
          ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
            cd /opt/spheroseg
            tar -xzf deployment.tar.gz
            
            # Backup current deployment
            ./scripts/backup-production.sh
            
            # Pull new images
            docker-compose -f docker-compose.production.yml pull
            
            # Deploy with zero-downtime
            docker-compose -f docker-compose.production.yml up -d --no-deps --scale backend=6 backend
            sleep 30
            docker-compose -f docker-compose.production.yml up -d --no-deps --scale backend=3 --remove-orphans backend
            
            docker-compose -f docker-compose.production.yml up -d --no-deps --scale frontend=6 frontend
            sleep 30
            docker-compose -f docker-compose.production.yml up -d --no-deps --scale frontend=3 --remove-orphans frontend
            
            docker-compose -f docker-compose.production.yml up -d ml
            
            # Run database migrations
            docker-compose -f docker-compose.production.yml exec -T backend npm run migrate:up
            
            # Health check
            ./scripts/health-check-production.sh
          EOF

      - name: Run production tests
        run: |
          npm run e2e:production -- --base-url=https://spherosegapp.utia.cas.cz

      - name: Rollback on failure
        if: failure()
        run: |
          ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
            cd /opt/spheroseg
            ./scripts/rollback-production.sh
          EOF

      - name: Create release
        if: startsWith(github.ref, 'refs/tags/v')
        uses: softprops/action-gh-release@v1
        with:
          generate_release_notes: true
          files: |
            deployment.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Update monitoring
        run: |
          curl -X POST ${{ secrets.GRAFANA_URL }}/api/annotations \
            -H "Authorization: Bearer ${{ secrets.GRAFANA_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d '{
              "dashboardId": 1,
              "text": "Deployment: ${{ github.sha }}",
              "tags": ["deployment", "production"]
            }'

      - name: Notify teams
        if: always()
        uses: microsoft/teams-webhook-action@v1
        with:
          webhook-url: ${{ secrets.TEAMS_WEBHOOK }}
          title: "Production Deployment"
          text: |
            **Status**: ${{ job.status }}
            **Version**: ${{ github.ref }}
            **Commit**: ${{ github.sha }}
            **Author**: ${{ github.actor }}
            **URL**: https://spherosegapp.utia.cas.cz

  # ============================================
  # POST-DEPLOYMENT VALIDATION
  # ============================================
  post-deployment:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: deploy-production
    if: success()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run E2E tests
        run: |
          npm ci
          npm run e2e -- --base-url=https://spherosegapp.utia.cas.cz

      - name: Run performance tests
        run: |
          npm run test:performance -- --base-url=https://spherosegapp.utia.cas.cz

      - name: Security scan production
        run: |
          npm run test:security -- --base-url=https://spherosegapp.utia.cas.cz

      - name: Check monitoring metrics
        run: |
          ./scripts/check-production-metrics.sh

      - name: Generate deployment report
        run: |
          ./scripts/generate-deployment-report.sh > deployment-report.md

      - name: Upload deployment report
        uses: actions/upload-artifact@v3
        with:
          name: deployment-report
          path: deployment-report.md
          retention-days: 30