# SpheroSeg Development Setup

## 🚀 Rychlý start - Development prostředí

Tato dokumentace popisuje, jak zprovoznit development verzi aplikace SpheroSeg.

## Předpoklady

- Node.js v20+ (doporučeno použít NVM)
- Docker a Docker Compose 
- PostgreSQL klient (pro debugging)
- Git

## Struktura projektu

```
spheroseg/
├── packages/
│   ├── backend/         # Express API server
│   ├── frontend/        # React + Vite aplikace
│   ├── shared/          # Sdílené typy a utility
│   └── types/           # TypeScript definice
├── ML/                  # Python ML service
├── docker-compose.dev.yml  # Docker konfigurace pro development
└── .env                 # Environment proměnné
```

## 1. Příprava prostředí

### Klonování repozitáře
```bash
git clone <repository-url>
cd spheroseg
git checkout dev  # DŮLEŽITÉ: Vždy pracujte v dev branch!
```

### Instalace závislostí
```bash
npm install
```

### Nastavení environment proměnných
```bash
cp .env.example .env
# Upravte .env podle potřeby
```

## 2. Spuštění služeb

### Varianta A: Hybridní přístup (DOPORUČENO)

Spustíme databázi a pomocné služby v Dockeru, backend a frontend lokálně:

#### 1. Spusťte Docker služby
```bash
# Spustit pouze databázi, Redis a RabbitMQ
docker-compose -f docker-compose.dev.yml up -d db redis rabbitmq
```

#### 2. Nastavte databázi
```bash
cd packages/backend
npx prisma migrate dev  # Aplikovat migrace
npx prisma generate     # Vygenerovat Prisma client
```

#### 3. Spusťte backend
```bash
# V packages/backend
npm run dev

# Nebo jednoduší development server:
npm exec ts-node -- --transpile-only src/server-dev.ts
```

Backend poběží na: http://localhost:5001

#### 4. Spusťte frontend
```bash
# V packages/frontend
npm run dev
```

Frontend poběží na: http://localhost:3000

### Varianta B: Plný Docker (pokud preferujete)

```bash
docker-compose -f docker-compose.dev.yml up
```

## 3. Ověření funkčnosti

### Backend API
```bash
# Health check
curl http://localhost:5001/api/health

# API info
curl http://localhost:5001/api/info

# Registrace uživatele
curl -X POST http://localhost:5001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","username":"testuser","password":"test123"}'

# Přihlášení
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}'
```

### Frontend
Otevřete browser na: http://localhost:3000

### Databáze
```bash
# Přímý přístup k databázi
docker exec -it spheroseg-db-dev psql -U postgres -d spheroseg_dev

# Nebo přes Adminer (pokud běží)
# http://localhost:8080
# Server: db
# Username: postgres
# Password: postgres
# Database: spheroseg_dev
```

## 4. Development workflow

### Hot reload
- Backend i frontend mají nastavený hot reload
- Změny v kódu se automaticky projeví bez restartu

### Debugging

#### Backend debugging
- Port 9229 je otevřený pro Node.js debugger
- V VS Code použijte "Attach to Node" konfiguraci

#### Frontend debugging
- Použijte React DevTools v browseru
- Vite HMR na portu 3001

### Testování
```bash
# Unit testy
npm run test

# E2E testy
npm run e2e

# Coverage
npm run test:coverage
```

## 5. Časté problémy a řešení

### Port již používán
```bash
# Zkontrolovat, co používá port
lsof -i :5001  # nebo :3000

# Zastavit Docker kontejnery
docker-compose -f docker-compose.dev.yml down
```

### Databázové migrace
```bash
# Reset databáze
cd packages/backend
npx prisma migrate reset

# Nová migrace
npx prisma migrate dev --name <migration-name>
```

### TypeScript chyby
```bash
# Rebuild TypeScript
npm run build

# Vyčistit cache
rm -rf node_modules/.cache
npm run clean
npm install
```

### Merge konflikty
Pokud narazíte na merge konflikty (<<<<<<< HEAD):
1. Vyhledejte všechny konflikty: `grep -r "<<<<<<< HEAD" packages/`
2. Ručně je vyřešte
3. Znovu spusťte aplikaci

## 6. Struktura API

### Hlavní endpointy
- `/api/health` - Health check
- `/api/auth/*` - Autentizace (login, register, logout)
- `/api/projects/*` - Správa projektů
- `/api/images/*` - Upload a zpracování obrázků
- `/api/users/*` - Uživatelské profily

### WebSocket události
- `segmentation-update` - Aktualizace stavu segmentace
- `project-refresh` - Obnovení dat projektu

## 7. Monitoring a logy

### Logy aplikace
```bash
# Backend logy
docker logs spheroseg-backend-dev -f

# Frontend logy
# Zobrazují se přímo v terminálu kde běží npm run dev
```

### RabbitMQ Management
http://localhost:15672
- Username: guest
- Password: guest

### Redis Commander
http://localhost:8081
- Username: admin
- Password: admin

## 8. Produkční build

```bash
# Build všech packages
npm run build:prod

# Docker build pro produkci
docker-compose --profile prod build
```

## 9. Užitečné příkazy

```bash
# Vyčistit vše a začít znovu
npm run clean
docker-compose -f docker-compose.dev.yml down -v
rm -rf node_modules packages/*/node_modules
npm install

# Zkontrolovat TypeScript
npm run typecheck

# Formátování kódu
npm run format

# Linting
npm run lint:fix
```

## 10. Git workflow

**DŮLEŽITÉ**: Vždy pracujte v `dev` branch!

```bash
# Před začátkem práce
git checkout dev
git pull origin dev

# Po dokončení práce
git add .
git commit -m "feat: popis změny"
git push origin dev

# NIKDY necommitujte přímo do main!
```

## Kontakt a podpora

Pokud narazíte na problémy:
1. Zkontrolujte logy: `docker logs <container-name>`
2. Ověřte environment proměnné v `.env`
3. Ujistěte se, že všechny porty jsou volné
4. Restartujte Docker služby

---

Poslední aktualizace: 2025-08-09