version: '3.8'

services:
  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: spheroseg-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl-certs:/etc/nginx/ssl:ro
      - ./letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - backend
    networks:
      - spheroseg-network

  # Frontend
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile
    container_name: spheroseg-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://spherosegapp.utia.cas.cz/api
      - VITE_WS_URL=wss://spherosegapp.utia.cas.cz/ws
    networks:
      - spheroseg-network

  # Backend API
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile
    container_name: spheroseg-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5001
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD:-spheroseg123}@postgres:5432/spheroseg_prod
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - CORS_ORIGIN=https://spherosegapp.utia.cas.cz
      - ML_SERVICE_URL=http://ml:5002
      - ML_SERVICE_API_KEY=${ML_SERVICE_API_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - spheroseg-network

  # ML Service
  ml:
    build:
      context: ./packages/ml
      dockerfile: Dockerfile
    container_name: spheroseg-ml
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - MODEL_PATH=/app/models/checkpoint_epoch_9.pth.tar
    volumes:
      - ./models:/app/models:ro
      - ./uploads:/app/uploads
    depends_on:
      - redis
      - rabbitmq
    networks:
      - spheroseg-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=${DB_PASSWORD:-spheroseg123}
      - POSTGRES_DB=spheroseg_prod
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-""}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: spheroseg-rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=spheroseg
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-rabbitmq123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

networks:
  spheroseg-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data: