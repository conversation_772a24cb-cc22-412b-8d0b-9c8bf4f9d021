# Segmentation Import Guide

The SpheroSeg application now supports importing existing segmentation data in various formats. This guide explains how to use the import functionality and the supported formats.

## Accessing the Import Feature

1. Open the segmentation editor for any image
2. Look for the **Import** button (📤) in the left toolbar
3. Click the Import button to open the import dialog

## Supported Formats

### 1. JSON Format (.json)

Standard JSON format with polygon arrays.

**Format:**
```json
{
  "polygons": [
    {
      "id": "polygon_1",
      "points": [
        {"x": 100, "y": 200},
        {"x": 150, "y": 180}
      ],
      "type": "external",
      "class": "cell"
    }
  ]
}
```

**Required fields:**
- `polygons`: Array of polygon objects
- `points`: Array of point objects with `x` and `y` coordinates

**Optional fields:**
- `id`: Unique identifier (auto-generated if missing)
- `type`: "external" or "internal" (defaults to "external")
- `class`: Classification label
- `color`: Hex color code
- `parentId`: ID of parent polygon (for internal polygons)

### 2. COCO Format (.json)

Standard COCO dataset annotation format.

**Format:**
```json
{
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "segmentation": [
        [100, 200, 150, 180, 180, 220]
      ]
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "cell"
    }
  ]
}
```

**Features:**
- Automatically converts polygon coordinates
- Maps category names to polygon classes
- Supports multiple segments per annotation
- Handles RLE format (Run-Length Encoding) via backend processing

### 3. CSV Coordinates (.csv)

Comma-separated values with coordinate data.

**Format:**
```csv
polygon_id,x,y,type
polygon_1,100,200,external
polygon_1,150,180,external
polygon_2,300,150,external
```

**Required columns:**
- `x`: X coordinate
- `y`: Y coordinate

**Optional columns:**
- `polygon_id`: Polygon identifier (groups points into polygons)
- `type`: "external" or "internal"

**Features:**
- Auto-groups points by polygon_id
- Creates single polygon if no polygon_id specified
- Empty rows separate different polygons

### 4. Binary Mask (.png, .jpg, .jpeg, .bmp)

Binary mask images where white pixels represent foreground objects.

**Requirements:**
- White pixels (value > 127) = foreground
- Dark pixels (value ≤ 127) = background
- Processed server-side for better performance
- Converted to polygons using contour detection

**Features:**
- Automatic contour extraction
- Multi-object support (separate connected components)
- Polygon simplification to reduce point count
- Coordinate scaling to match target image dimensions

## Import Options

### Import Mode

**Merge with existing polygons:**
- Adds imported polygons to current polygons
- Resolves ID conflicts automatically
- Preserves existing work

**Replace existing polygons:**
- Removes all current polygons
- Replaces with imported polygons only
- ⚠️ **Warning:** This action cannot be undone

### Validation

**Validate polygons (recommended):**
- Checks for minimum 3 points per polygon
- Detects self-intersections
- Validates polygon structure
- Reports errors and warnings

## Preview and Selection

After processing, the import dialog shows a preview of all imported polygons:

- **Toggle visibility:** Click the eye icon to show/hide individual polygons
- **Bulk actions:** Show All / Hide All buttons
- **Polygon details:** View point count, type, and classification
- **Final count:** See total polygon count after import

## Error Handling

The import system provides detailed error messages:

**Common errors:**
- Invalid file format
- Insufficient points (< 3 per polygon)
- Self-intersecting polygons
- Missing required columns (CSV)
- Corrupted image files (binary masks)

**Warnings:**
- Duplicate points removed
- Polygon simplification applied
- Category mapping issues

## Tips and Best Practices

1. **File size limits:** Maximum 50MB per file
2. **Coordinate system:** Ensure coordinates match your image dimensions
3. **Testing:** Start with small test files to verify format compatibility
4. **Backup:** Save your work before importing (especially when replacing)
5. **Validation:** Always enable polygon validation for best results

## Sample Files

Example files for each format are available in the `test-data/` directory:
- `sample-polygons.json` - Standard JSON format
- `sample-coco.json` - COCO dataset format  
- `sample-coordinates.csv` - CSV coordinate format

## Troubleshooting

**Import button disabled?**
- Check that you're in the segmentation editor
- Verify the image has loaded properly

**Processing fails?**
- Check file format matches expected structure
- Verify coordinates are within image bounds
- Try with validation disabled if polygons have minor issues

**No polygons imported?**
- Check if polygons were hidden in preview
- Verify coordinate values are reasonable
- Check for empty polygon arrays

**Binary mask processing slow?**
- Large images take more time to process
- Complex masks with many objects increase processing time
- Consider resizing very large mask images before import

For additional support, check the application logs or contact the development team.